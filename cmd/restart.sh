# 重启应用
#!/bin/bash
APP_NAME=application.jar
echo "Restart application"
echo "Find PID"
pid=`ps -ef | grep $APP_NAME | grep -v grep | awk '{print $2}'`
if [ -n "$pid" ]
then
    echo "kill -9 ${pid}" 
    kill -9 $pid
    sleep 8
fi
BUILD_ID=dontKillMe 
nohup java -Xms512m -Xmx1024m -XX:MaxPermSize=128m -cp $APP_NAME:./lib/* org.springframework.boot.loader.JarLauncher -D -Dfile.encoding="utf-8" -Dspring.config.location=./config/ > /dev/null 2>&1 &

echo "Start success"

tailf ./logs/gateway.log
