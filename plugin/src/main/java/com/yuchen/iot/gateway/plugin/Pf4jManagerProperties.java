package com.yuchen.iot.gateway.plugin;

import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "spring.pf4j")
public class Pf4jManagerProperties {

    /**
     * 是否启用
     */
    private boolean enabled = true;

    /**
     * 插件目录，文件夹全路径
     */
    private List<String> path;

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public List<String> getPath() {
        return path;
    }

    public void setPath(List<String> path) {
        this.path = path;
    }
}
