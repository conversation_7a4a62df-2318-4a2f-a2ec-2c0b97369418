package com.yuchen.iot.gateway.plugin;

import com.yuchen.iot.gateway.plugin.config.SpringPluginManager;
import org.pf4j.PluginManager;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Configuration
@EnableConfigurationProperties(Pf4jManagerProperties.class)
@ConditionalOnProperty(value = "spring.pf4j.enabled", havingValue = "true", matchIfMissing = true)
public class Pf4jConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public PluginManager pluginManager(Pf4jManagerProperties pf4jManagerProperties) {
        List<Path> collect = pf4jManagerProperties.getPath().stream().map(p-> Paths.get(p).toAbsolutePath()).collect(Collectors.toList());
        return new SpringPluginManager(collect);
    }
}
