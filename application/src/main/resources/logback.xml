<?xml version="1.0" encoding="UTF-8" ?>
<configuration scan="true" scanPeriod="10 seconds">
    <statusListener class="ch.qos.logback.core.status.NopStatusListener"/>
    <contextName>logback</contextName>
    <!--日志路径:在yml文件中找到对应的配置项-->
    <!--<springProperty scope="context" name="LOG_HOME" source="log.path"/>-->
    <property name="LOG_HOME" value="./logs"/>

    <!--彩色日志依赖的渲染类-->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
                    converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>
    <!--彩色日志格式(日志文件主目录)-->
    <property name="CONSOLE_LOG_PATTERN"
              value="${CONSOLE_LOG_PATTERN:-%clr(%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>

    <!--输出到控制台-->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <!--此日志appender是为开发使用，只配置最低级别，控制台输出的日志级别是大于或等于此级别的日志信息-->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>debug</level>
        </filter>
        <encoder>
            <Pattern>${CONSOLE_LOG_PATTERN}</Pattern>
            <!--设置字符串-->
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!--输出到文档：配置磁盘文件滚动输出日志-->
    <!--level为 DEBUG 日志，时间滚动输出-->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--正在记录的日志文档的路径及文档名-->
        <file>${LOG_HOME}/gateway.log</file>
        <!--日志文档输出格式-->
        <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度，%msg：日志消息，%n：换行符-->
        <encoder>
            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</Pattern>
            <!--设置字符串-->
            <charset>UTF-8</charset>
        </encoder>
        <!--日志记录器的滚动策略，按日期,按大小记录-->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--日志文件路径：这里的%d{yyyy-MM-dd}表示按天分类日志-->
            <fileNamePattern>${LOG_HOME}/gateway-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <!--设置最大日志大小-->
                <maxFileSize>150MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <!--日志文档保留15天-->
            <maxHistory>15</maxHistory>
        </rollingPolicy>
    </appender>

    <!--多环境配置 按照active profile 选择分支-->
    <!--开发环境-->
    <root level="debug">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </root>

    <logger name="com.yuchen" level="debug"/>
    <logger name="com.thinkunion" level="debug"/>
    <logger name="com.yuchen.iot.gateway.dao.mapper" level="debug"/>


</configuration>