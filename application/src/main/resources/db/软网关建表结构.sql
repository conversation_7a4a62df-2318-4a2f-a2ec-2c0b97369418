-----软网关用户表--------------------------------------------------------------------
CREATE TABLE iot_gw_user (--用户表
	id bigint ( 20 ) PRIMARY KEY NOT NULL,-- 用户id
	username varchar (128) NOT NULL,-- 用户名
	password varchar (128) NOT NULL,-- 密码
	phone char (11) NOT NULL,-- 电话号码,后面找回密码功能会使用
	create_time char (10) NOT NULL,-- 创建时间,时间格式"yyyy-MM-dd HH:mm:ss"
    update_time char (10) NOT NULL-- 更新时间,时间格式"yyyy-MM-dd HH:mm:ss"
);
-----软网关系统配置表--------------------------------------------------------------------
CREATE TABLE iot_gw_config (-- 系统配置表
	id bigint (20) PRIMARY KEY NOT NULL,-- 配置id
	config_name varchar (64) NOT NULL,-- 配置名称
	config_info varchar (128) NOT NULL,-- 配置信息
	status int(4) NOT NULL,-- 0是禁用 1是启动
	is_delete int(1) DEFAULT 0 NOT NULL,-- 是否已删除[0-未删除, 1-已删除]
	create_at bigint (20) NOT NULL,-- 创建人
    update_at bigint (20) NOT NULL,-- 更新人
	create_time char (10) NOT NULL,-- 创建时间,时间格式"yyyy-MM-dd HH:mm:ss"
    update_time char (10) NOT NULL-- 更新时间,时间格式"yyyy-MM-dd HH:mm:ss"
);
-----软网关系统连接器表--------------------------------------------------------------------
CREATE TABLE iot_gw_access_connector (-- 接入连接器表
	id bigint (20) PRIMARY KEY NOT NULL,-- 连接器id
	connector_name varchar (64) NOT NULL,-- 连接器名称
	application_type varchar (64) NOT NULL,-- 应用类型
	protocol_type varchar(64) NOT NULL,-- 协议方式(HTTP,SDK,TCP)
	enable_status int DEFAULT 1 NOT NULL,-- 启用状态[0-未启用, 1-已启用]
	common_config varchar (1024) DEFAULT NULL,-- 连接器通用配置信息
	custom_config varchar (1024) DEFAULT NULL,-- 连接器自定义配置信息
	up_link_id bigint (20) DEFAULT NULL,-- 上行转换器id
	down_link_id bigint (20) DEFAULT NULL,-- 下行转换器id
	vendor_name varchar(64) NOT NULL,-- 所属厂商名称
	is_delete int(1) DEFAULT 0 NOT NULL,-- 是否已删除[0-未删除, 1-已删除]
	connector_desc varchar (64) DEFAULT NULL,-- 连接器描述
	create_at bigint (20) NOT NULL,-- 创建人
    update_at bigint (20) NOT NULL,-- 更新人
	create_time char (10) NOT NULL,-- 创建时间,时间格式"yyyy-MM-dd HH:mm:ss"
    update_time char (10) NOT NULL-- 更新时间,时间格式"yyyy-MM-dd HH:mm:ss"
);
-----软网关系统接入转换器表--------------------------------------------------------------------
CREATE TABLE iot_gw_access_converter (-- 接入转换器
	id bigint (20) PRIMARY KEY NOT NULL,-- 转换器id
	converter_name varchar (64) NOT NULL,-- 转换器名称
	converter_type varchar (16) NOT NULL,-- 转换器类型(UP_LINK:上行转换器,DOWN_LINK:下行转换器)
	analytic_mode varchar(16) NOT NULL,-- 解析方式(JS:JS脚本解析 JAR:动态jar包解析 内置：INTERNAL)
	analytic_code varchar(16) NOT NULL,-- 解析代码(不能的解析方式有不同的含义,解析的唯一标识,比如:tongye-parking-down-link)
	code_path varchar (64) DEFAULT NULL,-- 当是jar解析的时候，需要上传jar //todo 先保留
	converter_desc varchar (64) DEFAULT NULL,-- 转换器描述
	is_delete int(1) DEFAULT 0 NOT NULL,-- 是否已删除[0-未删除, 1-已删除]
	create_at bigint (20) NOT NULL,-- 创建人
    update_at bigint (20) NOT NULL,-- 更新人
	create_time char (10) NOT NULL,-- 创建时间,时间格式"yyyy-MM-dd HH:mm:ss"
    update_time char (10) NOT NULL-- 更新时间,时间格式"yyyy-MM-dd HH:mm:ss"
);
-----软网关系统设备连接器中间表--------------------------------------------------------------------
CREATE TABLE iot_gw_device_connector_rel (-- 软网关系统设备连接器中间表
	id bigint (20) PRIMARY KEY NOT NULL,-- 设备连接器中间表id
	connector_id bigint(20)  NOT NULL,-- 连接器id
	device_id bigint(20)  NOT NULL,-- 设备id
	create_at bigint (20) NOT NULL,-- 创建人
    update_at bigint (20) NOT NULL,-- 更新人
	create_time char (10) NOT NULL,-- 创建时间,时间格式"yyyy-MM-dd HH:mm:ss"
    update_time char (10) NOT NULL-- 更新时间,时间格式"yyyy-MM-dd HH:mm:ss"
);
CREATE UNIQUE INDEX idx_connector_device_id ON iot_gw_device_connector_rel(connector_id,device_id);

-----软网关系统设备信息表--------------------------------------------------------------------
CREATE TABLE iot_gw_device (-- 设备信息表
	id bigint (20) PRIMARY KEY NOT NULL,-- 连接器
	vendor_name VARCHAR(64) DEFAULT NULL,-- 所属厂商
	product_name varchar (64) DEFAULT NULL,-- 产品名称
	product_key varchar (64) NOT NULL,-- 产品key
	device_name char (20) NOT NULL,-- 设备名称
	device_sn varchar (64) DEFAULT NULL,-- 设备通信号(也叫集成号)
	device_status int (4) DEFAULT 1,-- 状态(1未激活,2在线,3离线)
	config_info varchar(1024) DEFAULT NULL,-- 设备配置信息
	is_delete int(1) DEFAULT 0,-- 是否已删除[0-未删除, 1-已删除]
	device_desc varchar (64) DEFAULT NULL,-- 设备描述
	create_at bigint (20) DEFAULT NULL,-- 创建人
    update_at bigint (20) DEFAULT NULL,-- 更新人
	create_time char (10) DEFAULT NULL,-- 创建时间,时间格式"yyyy-MM-dd HH:mm:ss"
    update_time char (10) DEFAULT NULL-- 更新时间,时间格式"yyyy-MM-dd HH:mm:ss"
);
CREATE INDEX idx_device_sn ON iot_gw_device(device_sn);

-----软网关系统设备离线缓存数据--------------------------------------------------------------------
CREATE TABLE iot_gw_device_offline_data (-- 设备离线缓存数据
	id bigint (20) PRIMARY KEY NOT NULL,-- 连接器
	device_id bigint(20) NOT NULL,-- 设备id
	data_content varchar (1024) NOT NULL,-- 数据内容JSON
	is_delete int(1) DEFAULT 0 NOT NULL,-- 是否已删除[0-未删除, 1-已删除]
	create_at bigint (20) NOT NULL,-- 创建人
    update_at bigint (20) NOT NULL,-- 更新人
	create_time char (10) NOT NULL,-- 创建时间,时间格式"yyyy-MM-dd HH:mm:ss"
    update_time char (10) NOT NULL-- 更新时间,时间格式"yyyy-MM-dd HH:mm:ss"
);
CREATE INDEX idx_device_id ON iot_gw_device_offline_data(device_id);


------------------------------增加用户---------------
INSERT INTO "main"."iot_gw_user"("id", "username", "password", "phone", "create_time", "update_time") VALUES (637451145438040064, 'admin', '92d7ddd2a010c59511dc2905b7e14f64', '18476573028', '1676272051', '1676272051');