persist:
  type: memory # 持久化方式
connectors:
  reinit:
    enabled: false # 是否扫描并启动失败的连接器
    frequency: 3600000 # 扫描间隔时间
  allowLocalNetworkHosts: true # 是否允许连接器绑定本机IP，如 127.0.0.1 等
  statistics:
    enabled: false # 是否开启指标统计
    persist:
      frequency: 3600000 # 指标持久化时间间隔
  callback:
    threadPoolSize: 2 # 回调线程池数量
  json:
    config:
      path: C:\iotGateway\config\jgzx.json

iot:
  gateway: # 连接 IoT 平台的信息
    productKey: P377jx5fX6WCIlMC
    productSecret: StO9ZHHobBAcODjJ
    deviceName: jgzx_edge
    deviceSecret: EYNaql7OcbeQHaab
    registerEndpoint: iot.console.fj-yuchen.com
    registerHttpSchema: HTTPS
    registerApiVersion: 1.0
    mqttEndpoint: tcp://**************:1883


js:
  local: # JS引擎配置
    use_js_sandbox: false
    monitor_thread_pool_size: 5
    max_cpu_time: 1
    max_errors: 10000
    max_black_list_duration_sec: 60
    max_requests_timeout: 0
    stats.enabled: true
    js_thread_pool_size: 50

minio: # 文件服务器配置
  endpoint: https://minio.api.fj-yuchen.com
  api-key: gNVbp48Z96GVsuKg
  api-secret: yn5cpRakCnO0EUXq7fSEsHaL6QiQOMVZ
  bucket: gateway
  preview-endpoint: https://minio.api.fj-yuchen.com

logging:
  config:
    classpath: logback.xml