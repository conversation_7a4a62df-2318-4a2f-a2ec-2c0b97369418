persist:
  type: memory # 持久化方式
connectors:
  reinit:
    enabled: false # 是否扫描并启动失败的连接器
    frequency: 3600000 # 扫描间隔时间
  allowLocalNetworkHosts: true # 是否允许连接器绑定本机IP，如 127.0.0.1 等
  statistics:
    enabled: false # 是否开启指标统计
    persist:
      frequency: 3600000 # 指标持久化时间间隔
  callback:
    threadPoolSize: 2 # 回调线程池数量
  json:
    config:
      path: D:\iotgateway\config\hktest.json

iot:
  gateway: # 连接 IoT 平台的信息
    productKey: P377jx5fX6WCIlMC
    productSecret: StO9ZHHobBAcODjJ
    deviceName: test_hik_sdk
    deviceSecret: E7Pm5G2nu8E0rCxH
    registerEndpoint: iot.console.fj-yuchen.com
    registerHttpSchema: HTTPS
    registerApiVersion: 1.0
    mqttEndpoint: tcp://**************:1883


js:
  local: # JS引擎配置
    use_js_sandbox: false
    monitor_thread_pool_size: 5
    max_cpu_time: 1
    max_errors: 10000
    max_black_list_duration_sec: 60
    max_requests_timeout: 0
    stats.enabled: true
    js_thread_pool_size: 50

minio: # 文件服务器配置
  endpoint: https://minio.api.fj-yuchen.com
  api-key: gNVbp48Z96GVsuKg
  api-secret: yn5cpRakCnO0EUXq7fSEsHaL6QiQOMVZ
  bucket: gateway
  preview-endpoint: https://minio.api.fj-yuchen.com

logging:
  config:
    classpath: logback.xml


#海康设备信息
hikvision:
  deviceList:
    # 车辆道闸车牌抓拍机
    - ip: **************
      username: admin
      password: xmrbi3967968
      port: 8000
      productKey: kWJUPZJ3vb9N7z0t
      deviceName: hik_sdk_dao_test
      # 进还是出[in - 进, out - 出]
      inOrOut: in
      # 车道编号
#      laneCode: lane1
#      ledIp: *************
#      ledPort: 10000
#      roadwayIndex: roadway_index_1
#      roadwayName: roadway_name_1
#      gateName: gate_name_1
#      modelNumber: DS-TCG405-E