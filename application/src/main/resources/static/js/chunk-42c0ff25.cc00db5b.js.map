{"version": 3, "sources": ["webpack:///./src/assets/images/index/edit-mini-icon.png", "webpack:///./src/components/copyright/index.vue?d278", "webpack:///./src/components/copyright/index.vue?89b0", "webpack:///src/components/copyright/index.vue", "webpack:///./src/components/copyright/index.vue?dab6", "webpack:///./src/components/copyright/index.vue", "webpack:///./node_modules/os-browserify/browser.js", "webpack:///./src/views/login/index.vue?8125", "webpack:///./src/components/topbar/index.vue?f0ac", "webpack:///./src/assets/images/index/wrong-icon.png", "webpack:///./src/assets/images/index/logo.png", "webpack:///./src/assets/images/index/user-icon.png", "webpack:///./node_modules/js-md5/src/md5.js", "webpack:///./src/components/topbar/index.vue?bb6e", "webpack:///./src/assets/images/index/poster.png", "webpack:///./src/views/login/index.vue?6206", "webpack:///./src/views/login/conf.js", "webpack:///src/views/login/index.vue", "webpack:///./src/views/login/index.vue?e0c5", "webpack:///./src/views/login/index.vue", "webpack:///./src/assets/images/index/user-mini-icon.png", "webpack:///./src/api/user.js", "webpack:///./src/components/topbar/index.vue?cbe2", "webpack:///src/components/topbar/index.vue", "webpack:///./src/components/topbar/index.vue?ad33", "webpack:///./src/components/topbar/index.vue"], "names": ["module", "exports", "render", "_vm", "this", "_h", "$createElement", "_self", "_c", "_m", "staticRenderFns", "staticClass", "_v", "component", "endianness", "hostname", "location", "loadavg", "uptime", "freemem", "Number", "MAX_VALUE", "totalmem", "cpus", "type", "release", "navigator", "appVersion", "networkInterfaces", "getNetworkInterfaces", "arch", "platform", "tmpdir", "tmpDir", "EOL", "homedir", "ERROR", "WINDOW", "window", "root", "JS_MD5_NO_WINDOW", "WEB_WORKER", "self", "NODE_JS", "JS_MD5_NO_NODE_JS", "process", "versions", "node", "global", "COMMON_JS", "JS_MD5_NO_COMMON_JS", "AMD", "ARRAY_BUFFER", "JS_MD5_NO_ARRAY_BUFFER", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HEX_CHARS", "split", "EXTRA", "SHIFT", "OUTPUT_TYPES", "BASE64_ENCODE_CHAR", "blocks", "buffer8", "buffer", "Uint8Array", "Uint32Array", "Array", "isArray", "obj", "Object", "prototype", "toString", "call", "JS_MD5_NO_ARRAY_BUFFER_IS_VIEW", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "createOutputMethod", "outputType", "message", "Md5", "update", "createMethod", "method", "nodeWrap", "create", "i", "length", "crypto", "eval", "<PERSON><PERSON><PERSON>", "nodeMethod", "createHash", "digest", "undefined", "sharedMemory", "h0", "h1", "h2", "h3", "start", "bytes", "hBytes", "finalized", "hashed", "first", "notString", "code", "index", "charCodeAt", "lastByteIndex", "hash", "finalize", "a", "b", "c", "d", "bc", "da", "hex", "array", "arrayBuffer", "base64", "v1", "v2", "v3", "base64Str", "md5", "attrs", "style", "opacity", "loginTips", "_s", "ref", "form", "rules", "on", "$event", "nameFocus", "model", "value", "callback", "$$v", "$set", "expression", "passwordFocus", "directives", "name", "rawName", "handleLogin", "data", "flag", "isVisible", "username", "password", "required", "trigger", "components", "topBar", "copyright", "mounted", "addEventListener", "keyDown", "$nextTick", "$refs", "focus", "methods", "handleRegister", "$router", "push", "path", "handleReset", "event", "keyCode", "validate", "valid", "params", "grant_type", "scope", "assign", "getLogin", "then", "res", "localStorage", "setItem", "token", "encrypt_tenant_id", "Encrypt", "encryptoByAES", "tenant_id", "userInfo", "JSON", "stringify", "$store", "dispatch", "setTimeout", "finally", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "baseServer", "BASE_SERVER", "auth", "user", "request", "url", "getLoginOut", "getUserInfo", "sendMessage", "checkPhoneMustExists", "checkCaptcha", "updatePassword", "modifyPassword", "slot", "handleToAccountInfo", "handleUpdatePwd", "loginOut", "_e", "props", "is<PERSON>ogin", "Boolean", "default", "computed", "query", "fn_sure"], "mappings": "mGAAAA,EAAOC,QAAU,kd,oCCAjB,W,oCCAA,IAAIC,EAAS,WAAa,IAAIC,EAAIC,KAASC,EAAGF,EAAIG,eAAsBH,EAAII,MAAMC,GAAO,OAAOL,EAAIM,GAAG,IACnGC,EAAkB,CAAC,WAAa,IAAIP,EAAIC,KAASC,EAAGF,EAAIG,eAAmBE,EAAGL,EAAII,MAAMC,IAAIH,EAAG,OAAOG,EAAG,MAAM,CAACG,YAAY,aAAa,CAACH,EAAG,OAAO,CAACL,EAAIS,GAAG,+ECShK,KCV8V,I,wBCQ1VC,EAAY,eACd,EACAX,EACAQ,GACA,EACA,KACA,WACA,MAIa,OAAAG,E,8BCnBfZ,EAAQa,WAAa,WAAc,MAAO,MAE1Cb,EAAQc,SAAW,WACf,MAAwB,qBAAbC,SACAA,SAASD,SAER,IAGhBd,EAAQgB,QAAU,WAAc,MAAO,IAEvChB,EAAQiB,OAAS,WAAc,OAAO,GAEtCjB,EAAQkB,QAAU,WACd,OAAOC,OAAOC,WAGlBpB,EAAQqB,SAAW,WACf,OAAOF,OAAOC,WAGlBpB,EAAQsB,KAAO,WAAc,MAAO,IAEpCtB,EAAQuB,KAAO,WAAc,MAAO,WAEpCvB,EAAQwB,QAAU,WACd,MAAyB,qBAAdC,UACAA,UAAUC,WAEd,IAGX1B,EAAQ2B,kBACN3B,EAAQ4B,qBACR,WAAc,MAAO,IAEvB5B,EAAQ6B,KAAO,WAAc,MAAO,cAEpC7B,EAAQ8B,SAAW,WAAc,MAAO,WAExC9B,EAAQ+B,OAAS/B,EAAQgC,OAAS,WAC9B,MAAO,QAGXhC,EAAQiC,IAAM,KAEdjC,EAAQkC,QAAU,WACjB,MAAO,M,kCC/CR,W,oCCAA,W,0CCAAnC,EAAOC,QAAU,0kB,4CCAjBD,EAAOC,QAAU,0uD,qBCAjBD,EAAOC,QAAU,s5D,mDCAjB;;;;;;;;;IASA,WACE,aAEA,IAAImC,MAAQ,wBACRC,OAA2B,kBAAXC,OAChBC,KAAOF,OAASC,OAAS,GACzBC,KAAKC,mBACPH,QAAS,GAEX,IAAII,YAAcJ,QAA0B,kBAATK,KAC/BC,SAAWJ,KAAKK,mBAAwC,kBAAZC,SAAwBA,QAAQC,UAAYD,QAAQC,SAASC,KACzGJ,QACFJ,KAAOS,OACEP,aACTF,KAAOG,MAET,IAAIO,WAAaV,KAAKW,qBAAyC,kBAAXlD,QAAuBA,OAAOC,QAC9EkD,IAAsC,4BACtCC,cAAgBb,KAAKc,wBAAiD,qBAAhBC,YACtDC,UAAY,mBAAmBC,MAAM,IACrCC,MAAQ,CAAC,IAAK,MAAO,SAAU,YAC/BC,MAAQ,CAAC,EAAG,EAAG,GAAI,IACnBC,aAAe,CAAC,MAAO,QAAS,SAAU,SAAU,cAAe,UACnEC,mBAAqB,mEAAmEJ,MAAM,IAE9FK,OAAS,GAAIC,QACjB,GAAIV,aAAc,CAChB,IAAIW,OAAS,IAAIT,YAAY,IAC7BQ,QAAU,IAAIE,WAAWD,QACzBF,OAAS,IAAII,YAAYF,SAGvBxB,KAAKK,mBAAsBsB,MAAMC,UACnCD,MAAMC,QAAU,SAAUC,GACxB,MAA+C,mBAAxCC,OAAOC,UAAUC,SAASC,KAAKJ,MAItChB,eAAiBb,KAAKkC,gCAAmCnB,YAAYoB,SACvEpB,YAAYoB,OAAS,SAAUN,GAC7B,MAAsB,kBAARA,GAAoBA,EAAIL,QAAUK,EAAIL,OAAOY,cAAgBrB,cA6D/E,IAAIsB,mBAAqB,SAAUC,GACjC,OAAO,SAAUC,GACf,OAAO,IAAIC,KAAI,GAAMC,OAAOF,GAASD,OAwBrCI,aAAe,WACjB,IAAIC,EAASN,mBAAmB,OAC5BjC,UACFuC,EAASC,SAASD,IAEpBA,EAAOE,OAAS,WACd,OAAO,IAAIL,KAEbG,EAAOF,OAAS,SAAUF,GACxB,OAAOI,EAAOE,SAASJ,OAAOF,IAEhC,IAAK,IAAIO,EAAI,EAAGA,EAAI1B,aAAa2B,SAAUD,EAAG,CAC5C,IAAI7D,EAAOmC,aAAa0B,GACxBH,EAAO1D,GAAQoD,mBAAmBpD,GAEpC,OAAO0D,GAGLC,SAAW,SAAUD,QACvB,IAAIK,OAASC,KAAK,qBACdC,OAASD,KAAK,4BACdE,WAAa,SAAUZ,GACzB,GAAuB,kBAAZA,EACT,OAAOS,OAAOI,WAAW,OAAOX,OAAOF,EAAS,QAAQc,OAAO,OAE/D,GAAgB,OAAZd,QAAgCe,IAAZf,EACtB,MAAM1C,MAKV,OAJa0C,EAAQH,cAAgBrB,cACjCwB,EAAU,IAAId,WAAWc,IAGzBZ,MAAMC,QAAQW,IAAYxB,YAAYoB,OAAOI,IAC/CA,EAAQH,cAAgBc,OACjBF,OAAOI,WAAW,OAAOX,OAAO,IAAIS,OAAOX,IAAUc,OAAO,OAE5DV,OAAOJ,IAGlB,OAAOY,YAST,SAASX,IAAIe,GACX,GAAIA,EACFjC,OAAO,GAAKA,OAAO,IAAMA,OAAO,GAAKA,OAAO,GAAKA,OAAO,GACxDA,OAAO,GAAKA,OAAO,GAAKA,OAAO,GAAKA,OAAO,GAC3CA,OAAO,GAAKA,OAAO,GAAKA,OAAO,IAAMA,OAAO,IAC5CA,OAAO,IAAMA,OAAO,IAAMA,OAAO,IAAMA,OAAO,IAAM,EACpDzD,KAAKyD,OAASA,OACdzD,KAAK0D,QAAUA,aAEf,GAAIV,aAAc,CAChB,IAAIW,EAAS,IAAIT,YAAY,IAC7BlD,KAAK0D,QAAU,IAAIE,WAAWD,GAC9B3D,KAAKyD,OAAS,IAAII,YAAYF,QAE9B3D,KAAKyD,OAAS,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAGnEzD,KAAK2F,GAAK3F,KAAK4F,GAAK5F,KAAK6F,GAAK7F,KAAK8F,GAAK9F,KAAK+F,MAAQ/F,KAAKgG,MAAQhG,KAAKiG,OAAS,EAChFjG,KAAKkG,UAAYlG,KAAKmG,QAAS,EAC/BnG,KAAKoG,OAAQ,EAYfzB,IAAIT,UAAUU,OAAS,SAAUF,GAC/B,IAAI1E,KAAKkG,UAAT,CAIA,IAAIG,EAAWjF,SAAcsD,EAC7B,GAAa,WAATtD,EAAmB,CACrB,GAAa,WAATA,EAWF,MAAMY,MAVN,GAAgB,OAAZ0C,EACF,MAAM1C,MACD,GAAIgB,cAAgB0B,EAAQH,cAAgBrB,YACjDwB,EAAU,IAAId,WAAWc,QACpB,IAAKZ,MAAMC,QAAQW,MACnB1B,eAAiBE,YAAYoB,OAAOI,IACvC,MAAM1C,MAMZqE,GAAY,EAEd,IAAIC,EAAiBrB,EAAXsB,EAAQ,EAAMrB,EAASR,EAAQQ,OAAQzB,EAASzD,KAAKyD,OAC3DC,EAAU1D,KAAK0D,QAEnB,MAAO6C,EAAQrB,EAAQ,CAUrB,GATIlF,KAAKmG,SACPnG,KAAKmG,QAAS,EACd1C,EAAO,GAAKA,EAAO,IACnBA,EAAO,IAAMA,EAAO,GAAKA,EAAO,GAAKA,EAAO,GAC5CA,EAAO,GAAKA,EAAO,GAAKA,EAAO,GAAKA,EAAO,GAC3CA,EAAO,GAAKA,EAAO,GAAKA,EAAO,IAAMA,EAAO,IAC5CA,EAAO,IAAMA,EAAO,IAAMA,EAAO,IAAMA,EAAO,IAAM,GAGlD4C,EACF,GAAIrD,aACF,IAAKiC,EAAIjF,KAAK+F,MAAOQ,EAAQrB,GAAUD,EAAI,KAAMsB,EAC/C7C,EAAQuB,KAAOP,EAAQ6B,QAGzB,IAAKtB,EAAIjF,KAAK+F,MAAOQ,EAAQrB,GAAUD,EAAI,KAAMsB,EAC/C9C,EAAOwB,GAAK,IAAMP,EAAQ6B,IAAUjD,MAAY,EAAN2B,UAI9C,GAAIjC,aACF,IAAKiC,EAAIjF,KAAK+F,MAAOQ,EAAQrB,GAAUD,EAAI,KAAMsB,EAC/CD,EAAO5B,EAAQ8B,WAAWD,GACtBD,EAAO,IACT5C,EAAQuB,KAAOqB,EACNA,EAAO,MAChB5C,EAAQuB,KAAO,IAAQqB,GAAQ,EAC/B5C,EAAQuB,KAAO,IAAe,GAAPqB,GACdA,EAAO,OAAUA,GAAQ,OAClC5C,EAAQuB,KAAO,IAAQqB,GAAQ,GAC/B5C,EAAQuB,KAAO,IAASqB,GAAQ,EAAK,GACrC5C,EAAQuB,KAAO,IAAe,GAAPqB,IAEvBA,EAAO,QAAoB,KAAPA,IAAiB,GAAqC,KAA9B5B,EAAQ8B,aAAaD,IACjE7C,EAAQuB,KAAO,IAAQqB,GAAQ,GAC/B5C,EAAQuB,KAAO,IAASqB,GAAQ,GAAM,GACtC5C,EAAQuB,KAAO,IAASqB,GAAQ,EAAK,GACrC5C,EAAQuB,KAAO,IAAe,GAAPqB,QAI3B,IAAKrB,EAAIjF,KAAK+F,MAAOQ,EAAQrB,GAAUD,EAAI,KAAMsB,EAC/CD,EAAO5B,EAAQ8B,WAAWD,GACtBD,EAAO,IACT7C,EAAOwB,GAAK,IAAMqB,GAAQhD,MAAY,EAAN2B,KACvBqB,EAAO,MAChB7C,EAAOwB,GAAK,KAAO,IAAQqB,GAAQ,IAAOhD,MAAY,EAAN2B,KAChDxB,EAAOwB,GAAK,KAAO,IAAe,GAAPqB,IAAiBhD,MAAY,EAAN2B,MACzCqB,EAAO,OAAUA,GAAQ,OAClC7C,EAAOwB,GAAK,KAAO,IAAQqB,GAAQ,KAAQhD,MAAY,EAAN2B,KACjDxB,EAAOwB,GAAK,KAAO,IAASqB,GAAQ,EAAK,KAAUhD,MAAY,EAAN2B,KACzDxB,EAAOwB,GAAK,KAAO,IAAe,GAAPqB,IAAiBhD,MAAY,EAAN2B,OAElDqB,EAAO,QAAoB,KAAPA,IAAiB,GAAqC,KAA9B5B,EAAQ8B,aAAaD,IACjE9C,EAAOwB,GAAK,KAAO,IAAQqB,GAAQ,KAAQhD,MAAY,EAAN2B,KACjDxB,EAAOwB,GAAK,KAAO,IAASqB,GAAQ,GAAM,KAAUhD,MAAY,EAAN2B,KAC1DxB,EAAOwB,GAAK,KAAO,IAASqB,GAAQ,EAAK,KAAUhD,MAAY,EAAN2B,KACzDxB,EAAOwB,GAAK,KAAO,IAAe,GAAPqB,IAAiBhD,MAAY,EAAN2B,MAK1DjF,KAAKyG,cAAgBxB,EACrBjF,KAAKgG,OAASf,EAAIjF,KAAK+F,MACnBd,GAAK,IACPjF,KAAK+F,MAAQd,EAAI,GACjBjF,KAAK0G,OACL1G,KAAKmG,QAAS,GAEdnG,KAAK+F,MAAQd,EAOjB,OAJIjF,KAAKgG,MAAQ,aACfhG,KAAKiG,QAAUjG,KAAKgG,MAAQ,YAAc,EAC1ChG,KAAKgG,MAAQhG,KAAKgG,MAAQ,YAErBhG,OAGT2E,IAAIT,UAAUyC,SAAW,WACvB,IAAI3G,KAAKkG,UAAT,CAGAlG,KAAKkG,WAAY,EACjB,IAAIzC,EAASzD,KAAKyD,OAAQwB,EAAIjF,KAAKyG,cACnChD,EAAOwB,GAAK,IAAM5B,MAAU,EAAJ4B,GACpBA,GAAK,KACFjF,KAAKmG,QACRnG,KAAK0G,OAEPjD,EAAO,GAAKA,EAAO,IACnBA,EAAO,IAAMA,EAAO,GAAKA,EAAO,GAAKA,EAAO,GAC5CA,EAAO,GAAKA,EAAO,GAAKA,EAAO,GAAKA,EAAO,GAC3CA,EAAO,GAAKA,EAAO,GAAKA,EAAO,IAAMA,EAAO,IAC5CA,EAAO,IAAMA,EAAO,IAAMA,EAAO,IAAMA,EAAO,IAAM,GAEtDA,EAAO,IAAMzD,KAAKgG,OAAS,EAC3BvC,EAAO,IAAMzD,KAAKiG,QAAU,EAAIjG,KAAKgG,QAAU,GAC/ChG,KAAK0G,SAGP/B,IAAIT,UAAUwC,KAAO,WACnB,IAAIE,EAAGC,EAAGC,EAAGC,EAAGC,EAAIC,EAAIxD,EAASzD,KAAKyD,OAElCzD,KAAKoG,OACPQ,EAAInD,EAAO,GAAK,UAChBmD,GAAKA,GAAK,EAAIA,IAAM,IAAM,WAAa,EACvCG,IAAM,WAAiB,WAAJH,GAAkBnD,EAAO,GAAK,UACjDsD,GAAKA,GAAK,GAAKA,IAAM,IAAMH,GAAK,EAChCE,IAAM,UAAaC,IAAU,UAALH,IAAoBnD,EAAO,GAAK,WACxDqD,GAAKA,GAAK,GAAKA,IAAM,IAAMC,GAAK,EAChCF,GAAKD,EAAKE,GAAKC,EAAIH,IAAOnD,EAAO,GAAK,WACtCoD,GAAKA,GAAK,GAAKA,IAAM,IAAMC,GAAK,IAEhCF,EAAI5G,KAAK2F,GACTkB,EAAI7G,KAAK4F,GACTkB,EAAI9G,KAAK6F,GACTkB,EAAI/G,KAAK8F,GACTc,IAAMG,EAAKF,GAAKC,EAAIC,IAAOtD,EAAO,GAAK,UACvCmD,GAAKA,GAAK,EAAIA,IAAM,IAAMC,GAAK,EAC/BE,IAAMD,EAAKF,GAAKC,EAAIC,IAAOrD,EAAO,GAAK,UACvCsD,GAAKA,GAAK,GAAKA,IAAM,IAAMH,GAAK,EAChCE,IAAMD,EAAKE,GAAKH,EAAIC,IAAOpD,EAAO,GAAK,UACvCqD,GAAKA,GAAK,GAAKA,IAAM,IAAMC,GAAK,EAChCF,IAAMD,EAAKE,GAAKC,EAAIH,IAAOnD,EAAO,GAAK,WACvCoD,GAAKA,GAAK,GAAKA,IAAM,IAAMC,GAAK,GAGlCF,IAAMG,EAAKF,GAAKC,EAAIC,IAAOtD,EAAO,GAAK,UACvCmD,GAAKA,GAAK,EAAIA,IAAM,IAAMC,GAAK,EAC/BE,IAAMD,EAAKF,GAAKC,EAAIC,IAAOrD,EAAO,GAAK,WACvCsD,GAAKA,GAAK,GAAKA,IAAM,IAAMH,GAAK,EAChCE,IAAMD,EAAKE,GAAKH,EAAIC,IAAOpD,EAAO,GAAK,WACvCqD,GAAKA,GAAK,GAAKA,IAAM,IAAMC,GAAK,EAChCF,IAAMD,EAAKE,GAAKC,EAAIH,IAAOnD,EAAO,GAAK,SACvCoD,GAAKA,GAAK,GAAKA,IAAM,IAAMC,GAAK,EAChCF,IAAMG,EAAKF,GAAKC,EAAIC,IAAOtD,EAAO,GAAK,WACvCmD,GAAKA,GAAK,EAAIA,IAAM,IAAMC,GAAK,EAC/BE,IAAMD,EAAKF,GAAKC,EAAIC,IAAOrD,EAAO,GAAK,WACvCsD,GAAKA,GAAK,GAAKA,IAAM,IAAMH,GAAK,EAChCE,IAAMD,EAAKE,GAAKH,EAAIC,IAAOpD,EAAO,IAAM,MACxCqD,GAAKA,GAAK,GAAKA,IAAM,IAAMC,GAAK,EAChCF,IAAMD,EAAKE,GAAKC,EAAIH,IAAOnD,EAAO,IAAM,WACxCoD,GAAKA,GAAK,GAAKA,IAAM,IAAMC,GAAK,EAChCF,IAAMG,EAAKF,GAAKC,EAAIC,IAAOtD,EAAO,IAAM,WACxCmD,GAAKA,GAAK,EAAIA,IAAM,IAAMC,GAAK,EAC/BE,IAAMD,EAAKF,GAAKC,EAAIC,IAAOrD,EAAO,IAAM,SACxCsD,GAAKA,GAAK,GAAKA,IAAM,IAAMH,GAAK,EAChCE,IAAMD,EAAKE,GAAKH,EAAIC,IAAOpD,EAAO,IAAM,WACxCqD,GAAKA,GAAK,GAAKA,IAAM,IAAMC,GAAK,EAChCF,IAAMD,EAAKE,GAAKC,EAAIH,IAAOnD,EAAO,IAAM,WACxCoD,GAAKA,GAAK,GAAKA,IAAM,IAAMC,GAAK,EAChCF,IAAME,EAAKC,GAAKF,EAAIC,IAAOrD,EAAO,GAAK,UACvCmD,GAAKA,GAAK,EAAIA,IAAM,IAAMC,GAAK,EAC/BE,IAAMF,EAAKC,GAAKF,EAAIC,IAAOpD,EAAO,GAAK,WACvCsD,GAAKA,GAAK,EAAIA,IAAM,IAAMH,GAAK,EAC/BE,IAAMF,EAAKC,GAAKE,EAAIH,IAAOnD,EAAO,IAAM,UACxCqD,GAAKA,GAAK,GAAKA,IAAM,IAAMC,GAAK,EAChCF,IAAME,EAAKH,GAAKE,EAAIC,IAAOtD,EAAO,GAAK,UACvCoD,GAAKA,GAAK,GAAKA,IAAM,IAAMC,GAAK,EAChCF,IAAME,EAAKC,GAAKF,EAAIC,IAAOrD,EAAO,GAAK,UACvCmD,GAAKA,GAAK,EAAIA,IAAM,IAAMC,GAAK,EAC/BE,IAAMF,EAAKC,GAAKF,EAAIC,IAAOpD,EAAO,IAAM,SACxCsD,GAAKA,GAAK,EAAIA,IAAM,IAAMH,GAAK,EAC/BE,IAAMF,EAAKC,GAAKE,EAAIH,IAAOnD,EAAO,IAAM,UACxCqD,GAAKA,GAAK,GAAKA,IAAM,IAAMC,GAAK,EAChCF,IAAME,EAAKH,GAAKE,EAAIC,IAAOtD,EAAO,GAAK,UACvCoD,GAAKA,GAAK,GAAKA,IAAM,IAAMC,GAAK,EAChCF,IAAME,EAAKC,GAAKF,EAAIC,IAAOrD,EAAO,GAAK,UACvCmD,GAAKA,GAAK,EAAIA,IAAM,IAAMC,GAAK,EAC/BE,IAAMF,EAAKC,GAAKF,EAAIC,IAAOpD,EAAO,IAAM,WACxCsD,GAAKA,GAAK,EAAIA,IAAM,IAAMH,GAAK,EAC/BE,IAAMF,EAAKC,GAAKE,EAAIH,IAAOnD,EAAO,GAAK,UACvCqD,GAAKA,GAAK,GAAKA,IAAM,IAAMC,GAAK,EAChCF,IAAME,EAAKH,GAAKE,EAAIC,IAAOtD,EAAO,GAAK,WACvCoD,GAAKA,GAAK,GAAKA,IAAM,IAAMC,GAAK,EAChCF,IAAME,EAAKC,GAAKF,EAAIC,IAAOrD,EAAO,IAAM,WACxCmD,GAAKA,GAAK,EAAIA,IAAM,IAAMC,GAAK,EAC/BE,IAAMF,EAAKC,GAAKF,EAAIC,IAAOpD,EAAO,GAAK,SACvCsD,GAAKA,GAAK,EAAIA,IAAM,IAAMH,GAAK,EAC/BE,IAAMF,EAAKC,GAAKE,EAAIH,IAAOnD,EAAO,GAAK,WACvCqD,GAAKA,GAAK,GAAKA,IAAM,IAAMC,GAAK,EAChCF,IAAME,EAAKH,GAAKE,EAAIC,IAAOtD,EAAO,IAAM,WACxCoD,GAAKA,GAAK,GAAKA,IAAM,IAAMC,GAAK,EAChCE,EAAKH,EAAIC,EACTF,IAAMI,EAAKD,GAAKtD,EAAO,GAAK,OAC5BmD,GAAKA,GAAK,EAAIA,IAAM,IAAMC,GAAK,EAC/BE,IAAMC,EAAKJ,GAAKnD,EAAO,GAAK,WAC5BsD,GAAKA,GAAK,GAAKA,IAAM,IAAMH,GAAK,EAChCK,EAAKF,EAAIH,EACTE,IAAMG,EAAKJ,GAAKpD,EAAO,IAAM,WAC7BqD,GAAKA,GAAK,GAAKA,IAAM,IAAMC,GAAK,EAChCF,IAAMI,EAAKH,GAAKrD,EAAO,IAAM,SAC7BoD,GAAKA,GAAK,GAAKA,IAAM,GAAKC,GAAK,EAC/BE,EAAKH,EAAIC,EACTF,IAAMI,EAAKD,GAAKtD,EAAO,GAAK,WAC5BmD,GAAKA,GAAK,EAAIA,IAAM,IAAMC,GAAK,EAC/BE,IAAMC,EAAKJ,GAAKnD,EAAO,GAAK,WAC5BsD,GAAKA,GAAK,GAAKA,IAAM,IAAMH,GAAK,EAChCK,EAAKF,EAAIH,EACTE,IAAMG,EAAKJ,GAAKpD,EAAO,GAAK,UAC5BqD,GAAKA,GAAK,GAAKA,IAAM,IAAMC,GAAK,EAChCF,IAAMI,EAAKH,GAAKrD,EAAO,IAAM,WAC7BoD,GAAKA,GAAK,GAAKA,IAAM,GAAKC,GAAK,EAC/BE,EAAKH,EAAIC,EACTF,IAAMI,EAAKD,GAAKtD,EAAO,IAAM,UAC7BmD,GAAKA,GAAK,EAAIA,IAAM,IAAMC,GAAK,EAC/BE,IAAMC,EAAKJ,GAAKnD,EAAO,GAAK,UAC5BsD,GAAKA,GAAK,GAAKA,IAAM,IAAMH,GAAK,EAChCK,EAAKF,EAAIH,EACTE,IAAMG,EAAKJ,GAAKpD,EAAO,GAAK,UAC5BqD,GAAKA,GAAK,GAAKA,IAAM,IAAMC,GAAK,EAChCF,IAAMI,EAAKH,GAAKrD,EAAO,GAAK,SAC5BoD,GAAKA,GAAK,GAAKA,IAAM,GAAKC,GAAK,EAC/BE,EAAKH,EAAIC,EACTF,IAAMI,EAAKD,GAAKtD,EAAO,GAAK,UAC5BmD,GAAKA,GAAK,EAAIA,IAAM,IAAMC,GAAK,EAC/BE,IAAMC,EAAKJ,GAAKnD,EAAO,IAAM,UAC7BsD,GAAKA,GAAK,GAAKA,IAAM,IAAMH,GAAK,EAChCK,EAAKF,EAAIH,EACTE,IAAMG,EAAKJ,GAAKpD,EAAO,IAAM,UAC7BqD,GAAKA,GAAK,GAAKA,IAAM,IAAMC,GAAK,EAChCF,IAAMI,EAAKH,GAAKrD,EAAO,GAAK,UAC5BoD,GAAKA,GAAK,GAAKA,IAAM,GAAKC,GAAK,EAC/BF,IAAME,GAAKD,GAAKE,IAAMtD,EAAO,GAAK,UAClCmD,GAAKA,GAAK,EAAIA,IAAM,IAAMC,GAAK,EAC/BE,IAAMF,GAAKD,GAAKE,IAAMrD,EAAO,GAAK,WAClCsD,GAAKA,GAAK,GAAKA,IAAM,IAAMH,GAAK,EAChCE,IAAMF,GAAKG,GAAKF,IAAMpD,EAAO,IAAM,WACnCqD,GAAKA,GAAK,GAAKA,IAAM,IAAMC,GAAK,EAChCF,IAAME,GAAKD,GAAKF,IAAMnD,EAAO,GAAK,SAClCoD,GAAKA,GAAK,GAAKA,IAAM,IAAMC,GAAK,EAChCF,IAAME,GAAKD,GAAKE,IAAMtD,EAAO,IAAM,WACnCmD,GAAKA,GAAK,EAAIA,IAAM,IAAMC,GAAK,EAC/BE,IAAMF,GAAKD,GAAKE,IAAMrD,EAAO,GAAK,WAClCsD,GAAKA,GAAK,GAAKA,IAAM,IAAMH,GAAK,EAChCE,IAAMF,GAAKG,GAAKF,IAAMpD,EAAO,IAAM,QACnCqD,GAAKA,GAAK,GAAKA,IAAM,IAAMC,GAAK,EAChCF,IAAME,GAAKD,GAAKF,IAAMnD,EAAO,GAAK,WAClCoD,GAAKA,GAAK,GAAKA,IAAM,IAAMC,GAAK,EAChCF,IAAME,GAAKD,GAAKE,IAAMtD,EAAO,GAAK,WAClCmD,GAAKA,GAAK,EAAIA,IAAM,IAAMC,GAAK,EAC/BE,IAAMF,GAAKD,GAAKE,IAAMrD,EAAO,IAAM,SACnCsD,GAAKA,GAAK,GAAKA,IAAM,IAAMH,GAAK,EAChCE,IAAMF,GAAKG,GAAKF,IAAMpD,EAAO,GAAK,WAClCqD,GAAKA,GAAK,GAAKA,IAAM,IAAMC,GAAK,EAChCF,IAAME,GAAKD,GAAKF,IAAMnD,EAAO,IAAM,WACnCoD,GAAKA,GAAK,GAAKA,IAAM,IAAMC,GAAK,EAChCF,IAAME,GAAKD,GAAKE,IAAMtD,EAAO,GAAK,UAClCmD,GAAKA,GAAK,EAAIA,IAAM,IAAMC,GAAK,EAC/BE,IAAMF,GAAKD,GAAKE,IAAMrD,EAAO,IAAM,WACnCsD,GAAKA,GAAK,GAAKA,IAAM,IAAMH,GAAK,EAChCE,IAAMF,GAAKG,GAAKF,IAAMpD,EAAO,GAAK,UAClCqD,GAAKA,GAAK,GAAKA,IAAM,IAAMC,GAAK,EAChCF,IAAME,GAAKD,GAAKF,IAAMnD,EAAO,GAAK,UAClCoD,GAAKA,GAAK,GAAKA,IAAM,IAAMC,GAAK,EAE5B9G,KAAKoG,OACPpG,KAAK2F,GAAKiB,EAAI,YAAc,EAC5B5G,KAAK4F,GAAKiB,EAAI,WAAa,EAC3B7G,KAAK6F,GAAKiB,EAAI,YAAc,EAC5B9G,KAAK8F,GAAKiB,EAAI,WAAa,EAC3B/G,KAAKoG,OAAQ,IAEbpG,KAAK2F,GAAK3F,KAAK2F,GAAKiB,GAAK,EACzB5G,KAAK4F,GAAK5F,KAAK4F,GAAKiB,GAAK,EACzB7G,KAAK6F,GAAK7F,KAAK6F,GAAKiB,GAAK,EACzB9G,KAAK8F,GAAK9F,KAAK8F,GAAKiB,GAAK,IAc7BpC,IAAIT,UAAUgD,IAAM,WAClBlH,KAAK2G,WAEL,IAAIhB,EAAK3F,KAAK2F,GAAIC,EAAK5F,KAAK4F,GAAIC,EAAK7F,KAAK6F,GAAIC,EAAK9F,KAAK8F,GAExD,OAAO3C,UAAWwC,GAAM,EAAK,IAAQxC,UAAe,GAALwC,GAC7CxC,UAAWwC,GAAM,GAAM,IAAQxC,UAAWwC,GAAM,EAAK,IACrDxC,UAAWwC,GAAM,GAAM,IAAQxC,UAAWwC,GAAM,GAAM,IACtDxC,UAAWwC,GAAM,GAAM,IAAQxC,UAAWwC,GAAM,GAAM,IACtDxC,UAAWyC,GAAM,EAAK,IAAQzC,UAAe,GAALyC,GACxCzC,UAAWyC,GAAM,GAAM,IAAQzC,UAAWyC,GAAM,EAAK,IACrDzC,UAAWyC,GAAM,GAAM,IAAQzC,UAAWyC,GAAM,GAAM,IACtDzC,UAAWyC,GAAM,GAAM,IAAQzC,UAAWyC,GAAM,GAAM,IACtDzC,UAAW0C,GAAM,EAAK,IAAQ1C,UAAe,GAAL0C,GACxC1C,UAAW0C,GAAM,GAAM,IAAQ1C,UAAW0C,GAAM,EAAK,IACrD1C,UAAW0C,GAAM,GAAM,IAAQ1C,UAAW0C,GAAM,GAAM,IACtD1C,UAAW0C,GAAM,GAAM,IAAQ1C,UAAW0C,GAAM,GAAM,IACtD1C,UAAW2C,GAAM,EAAK,IAAQ3C,UAAe,GAAL2C,GACxC3C,UAAW2C,GAAM,GAAM,IAAQ3C,UAAW2C,GAAM,EAAK,IACrD3C,UAAW2C,GAAM,GAAM,IAAQ3C,UAAW2C,GAAM,GAAM,IACtD3C,UAAW2C,GAAM,GAAM,IAAQ3C,UAAW2C,GAAM,GAAM,KAa1DnB,IAAIT,UAAUC,SAAWQ,IAAIT,UAAUgD,IAYvCvC,IAAIT,UAAUsB,OAAS,WACrBxF,KAAK2G,WAEL,IAAIhB,EAAK3F,KAAK2F,GAAIC,EAAK5F,KAAK4F,GAAIC,EAAK7F,KAAK6F,GAAIC,EAAK9F,KAAK8F,GACxD,MAAO,CACA,IAALH,EAAYA,GAAM,EAAK,IAAOA,GAAM,GAAM,IAAOA,GAAM,GAAM,IACxD,IAALC,EAAYA,GAAM,EAAK,IAAOA,GAAM,GAAM,IAAOA,GAAM,GAAM,IACxD,IAALC,EAAYA,GAAM,EAAK,IAAOA,GAAM,GAAM,IAAOA,GAAM,GAAM,IACxD,IAALC,EAAYA,GAAM,EAAK,IAAOA,GAAM,GAAM,IAAOA,GAAM,GAAM,MAcjEnB,IAAIT,UAAUiD,MAAQxC,IAAIT,UAAUsB,OAYpCb,IAAIT,UAAUkD,YAAc,WAC1BpH,KAAK2G,WAEL,IAAIhD,EAAS,IAAIT,YAAY,IACzBO,EAAS,IAAII,YAAYF,GAK7B,OAJAF,EAAO,GAAKzD,KAAK2F,GACjBlC,EAAO,GAAKzD,KAAK4F,GACjBnC,EAAO,GAAKzD,KAAK6F,GACjBpC,EAAO,GAAKzD,KAAK8F,GACVnC,GAcTgB,IAAIT,UAAUP,OAASgB,IAAIT,UAAUkD,YAYrCzC,IAAIT,UAAUmD,OAAS,WAErB,IADA,IAAIC,EAAIC,EAAIC,EAAIC,EAAY,GAAIzB,EAAQhG,KAAKmH,QACpClC,EAAI,EAAGA,EAAI,IAClBqC,EAAKtB,EAAMf,KACXsC,EAAKvB,EAAMf,KACXuC,EAAKxB,EAAMf,KACXwC,GAAajE,mBAAmB8D,IAAO,GACrC9D,mBAA0C,IAAtB8D,GAAM,EAAIC,IAAO,IACrC/D,mBAA0C,IAAtB+D,GAAM,EAAIC,IAAO,IACrChE,mBAAwB,GAALgE,GAMvB,OAJAF,EAAKtB,EAAMf,GACXwC,GAAajE,mBAAmB8D,IAAO,GACrC9D,mBAAoB8D,GAAM,EAAK,IAC/B,KACKG,GAGT,IAAI5H,QAAUgF,eAEVhC,UACFjD,OAAOC,QAAUA,SAmBjBsC,KAAKuF,IAAM7H,QACPkD,MACF,yCACE,OAAOlD,SACR,2IA9pBP,K,wGCTA,W,uBCAAD,EAAOC,QAAU,IAA0B,2B,kECA3C,IAAIC,EAAS,WAAa,IAAIC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBE,EAAGL,EAAII,MAAMC,IAAIH,EAAG,OAAOG,EAAG,MAAM,CAACG,YAAY,SAAS,CAACH,EAAG,UAAU,CAACuH,MAAM,CAAC,SAAU,KAASvH,EAAG,MAAM,CAACG,YAAY,gBAAgB,CAACR,EAAIM,GAAG,GAAGD,EAAG,MAAM,CAACG,YAAY,mBAAmB,CAACH,EAAG,KAAK,CAACL,EAAIS,GAAG,YAAYJ,EAAG,IAAI,CAACG,YAAY,kBAAkBqH,MAAM,CAAGC,QAAS9H,EAAI+H,UAAY,IAAM,MAAQ,CAAC1H,EAAG,MAAM,CAACuH,MAAM,CAAC,IAAM,EAAQ,QAAwC,IAAM,MAAMvH,EAAG,OAAO,CAACL,EAAIS,GAAGT,EAAIgI,GAAGhI,EAAI+H,gBAAgB1H,EAAG,UAAU,CAAC4H,IAAI,OAAOL,MAAM,CAAC,MAAQ5H,EAAIkI,KAAK,MAAQlI,EAAImI,QAAQ,CAAC9H,EAAG,eAAe,CAACuH,MAAM,CAAC,MAAQ,GAAG,KAAO,aAAa,CAACvH,EAAG,MAAM,CAACG,YAAY,aAAa,CAACH,EAAG,WAAW,CAAC4H,IAAI,WAAWL,MAAM,CAAC,UAAY,GAAG,YAAc,YAAYQ,GAAG,CAAC,MAAQ,SAASC,GAAQrI,EAAIsI,WAAY,GAAM,KAAO,SAASD,GAAQrI,EAAIsI,WAAY,IAAQC,MAAM,CAACC,MAAOxI,EAAIkI,KAAa,SAAEO,SAAS,SAAUC,GAAM1I,EAAI2I,KAAK3I,EAAIkI,KAAM,WAAYQ,IAAME,WAAW,oBAAoB,KAAKvI,EAAG,eAAe,CAACuH,MAAM,CAAC,MAAQ,GAAG,KAAO,aAAa,CAACvH,EAAG,MAAM,CAACG,YAAY,2BAA2B,CAACH,EAAG,WAAW,CAACuH,MAAM,CAAC,UAAY,GAAG,iBAAgB,EAAK,YAAc,MAAMQ,GAAG,CAAC,MAAQ,SAASC,GAAQrI,EAAI6I,eAAgB,GAAM,KAAO,SAASR,GAAQrI,EAAI6I,eAAgB,IAAQN,MAAM,CAACC,MAAOxI,EAAIkI,KAAa,SAAEO,SAAS,SAAUC,GAAM1I,EAAI2I,KAAK3I,EAAIkI,KAAM,WAAYQ,IAAME,WAAW,oBAAoB,MAAM,GAAGvI,EAAG,MAAM,CAACyI,WAAW,CAAC,CAACC,KAAK,WAAWC,QAAQ,aAAaR,MAAM,IAAMI,WAAW,QAAQpI,YAAY,eAAe4H,GAAG,CAAC,MAAQpI,EAAIiJ,cAAc,CAAEjJ,EAAQ,KAAEK,EAAG,OAAO,CAACL,EAAIS,GAAG,QAAQJ,EAAG,OAAO,CAACL,EAAIS,GAAG,eAAe,KAAKJ,EAAG,YAAY,CAACG,YAAY,eAAe,IACjoDD,EAAkB,CAAC,WAAa,IAAIP,EAAIC,KAASC,EAAGF,EAAIG,eAAmBE,EAAGL,EAAII,MAAMC,IAAIH,EAAG,OAAOG,EAAG,MAAM,CAACG,YAAY,OAAO,CAACH,EAAG,MAAM,CAACuH,MAAM,CAAC,IAAM,EAAQ,QAAoC,IAAM,GAAG,YAAc,uB,iFCGnN,GACXsB,KADW,WAEP,MAAO,CACHC,MAAM,EACNb,WAAW,EACXO,eAAe,EACfO,WAAW,EACXlB,KAAM,CACFmB,SAAU,GACVC,SAAU,IAEdnB,MAAO,CACHkB,SAAU,CACN,CACIE,UAAU,EACV5E,QAAS,aACT6E,QAAS,SAGjBF,SAAU,CAAC,CAAEC,UAAU,EAAM5E,QAAS,QAAS6E,QAAS,UAE5DzB,UAAW,KAGnB0B,WAAY,CAAEC,cAAQC,kBACtBC,QAzBW,WAyBA,WACPzH,OAAO0H,iBAAiB,UAAW5J,KAAK6J,SAAS,GACjD7J,KAAK8J,WAAU,WACX,EAAKC,MAAMX,SAASY,YAG5BC,QAAS,CAELC,eAFK,WAGDlK,KAAKmK,QAAQC,KAAK,CACdC,KAAM,eAGdC,YAPK,WAQDtK,KAAKmK,QAAQC,KAAK,CACdC,KAAM,YAGdR,QAZK,SAYIU,GACiB,KAAlBA,EAAMC,SACNxK,KAAKgJ,eAGbA,YAjBK,WAiBU,WACXhJ,KAAK+J,MAAM9B,KAAKwC,UAAS,SAACC,GACtB,IAAIA,EAgDA,OAAO,EA/CP,GAAK,EAAKxB,KAAV,CACA,EAAKA,MAAO,EACZ,IAAIyB,EAAS,CACTC,WAAY,WACZC,MAAO,OAEXF,EAAS1G,OAAO6G,OAAOH,EAAQ,EAAK1C,MAEpC0C,EAAOtB,SAAW3B,IAAIiD,EAAOtB,UAC7B0B,eAASJ,GACJK,MAAK,SAACC,GACH,GAAgB,KAAZA,EAAI3E,KAAa,CAEjB4E,aAAaC,QAAQ,eAAgBF,EAAIhC,KAAKmC,OAC9C,IAAIC,EAAoB,EAAKC,QAAQC,cACjCN,EAAIhC,KAAKuC,WAEbN,aAAaC,QAAQ,YAAaE,GAElC,IAAII,EAAW,iCACRR,EAAIhC,MADC,IAERuC,UAAWH,IAEfH,aAAaC,QAAQ,WAAYO,KAAKC,UAAUF,IAChD,EAAKG,OAAOC,SAAS,cAAeZ,EAAIhC,MACxC,EAAKkB,QAAQC,KAAK,CACdC,KAAM,2BAWV,EAAKvC,UAAYmD,EAAIvG,SAAW,YAChCoH,YAAW,WACP,EAAKhE,UAAY,KAClB,QAGViE,SAAQ,WACL,EAAK7C,MAAO,WAQpC8C,cAvGW,WAwGP9J,OAAO+J,oBAAoB,UAAWjM,KAAK6J,SAAS,KC5C5D,IChE8V,I,wBCQ1VpJ,EAAY,eACd,EACAX,EACAQ,GACA,EACA,KACA,WACA,MAIa,aAAAG,E,mDCnBfb,EAAOC,QAAU,kb,kCCAjB,4SAEMqM,EAAaC,OACbC,EAAOF,EACPG,EAAOH,EASAnB,EAAW,SAAC9B,GACrB,OAAOqD,eAAQ,CACXC,IAAK,GAAF,OAAKH,EAAL,eACHtH,OAAQ,OACRmE,UAUKuD,EAAc,SAAC7B,GACxB,OAAO2B,eAAQ,CACXC,IAAK,GAAF,OAAKH,EAAL,gBACHtH,OAAQ,MACR6F,YAUK8B,EAAc,SAAC9B,GACxB,OAAO2B,eAAQ,CACXC,IAAK,GAAF,OAAKF,EAAL,kBACHvH,OAAQ,MACR6F,YAyBK+B,EAAc,SAAC/B,GACxB,OAAO2B,eAAQ,CACXC,IAAK,GAAF,OAAKF,EAAL,yBACHvH,OAAQ,OACR6F,YAwBKgC,EAAuB,SAAChC,GACjC,OAAO2B,eAAQ,CACXC,IAAK,GAAF,OAAKF,EAAL,iCACHvH,OAAQ,OACR6F,YAsCKiC,EAAe,SAACjC,GACzB,OAAO2B,eAAQ,CACXC,IAAK,GAAF,OAAKF,EAAL,8BACHvH,OAAQ,OACR6F,YAUKkC,EAAiB,SAAC5D,GAC3B,OAAOqD,eAAQ,CACXC,IAAK,GAAF,OAAKF,EAAL,iCACHvH,OAAQ,OACRmE,UAiBK6D,EAAiB,SAAC7D,GAC3B,OAAOqD,eAAQ,CACXC,IAAK,GAAF,OAAKF,EAAL,uBACHvH,OAAQ,OACRmE,W,kCCnLR,IAAInJ,EAAS,WAAa,IAAIC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBE,EAAGL,EAAII,MAAMC,IAAIH,EAAG,OAAOG,EAAG,MAAM,CAACG,YAAY,gBAAgB,CAACR,EAAIM,GAAG,GAAIN,EAAW,QAAEK,EAAG,MAAM,CAACG,YAAY,aAAa,CAACH,EAAG,aAAa,CAACG,YAAY,OAAOoH,MAAM,CAAC,OAAS,QAAQ,UAAY,aAAa,eAAe,oBAAoB,CAACvH,EAAG,MAAM,CAACG,YAAY,UAAUoH,MAAM,CAAC,KAAO,WAAWoF,KAAK,WAAW,CAAC3M,EAAG,IAAI,CAACG,YAAY,SAAS,CAACR,EAAIS,GAAGT,EAAIgI,GAAGhI,EAAI0L,SAASrC,aAAahJ,EAAG,MAAM,CAACG,YAAY,aAAa,CAACH,EAAG,MAAM,CAACG,YAAY,YAAY4H,GAAG,CAAC,MAAQpI,EAAIiN,sBAAsB,CAAC5M,EAAG,MAAM,CAACuH,MAAM,CAAC,IAAM,EAAQ,QAA4C,IAAM,MAAMvH,EAAG,OAAO,CAACL,EAAIS,GAAG,YAAYJ,EAAG,MAAM,CAACG,YAAY,YAAY4H,GAAG,CAAC,MAAQpI,EAAIkN,kBAAkB,CAAC7M,EAAG,MAAM,CAACuH,MAAM,CAAC,IAAM,EAAQ,QAA4C,IAAM,MAAMvH,EAAG,OAAO,CAACL,EAAIS,GAAG,cAAcJ,EAAG,IAAI,CAACG,YAAY,MAAM4H,GAAG,CAAC,MAAQpI,EAAImN,WAAW,CAACnN,EAAIS,GAAG,YAAYJ,EAAG,MAAM,CAACG,YAAY,WAAWoH,MAAM,CAAC,IAAM,EAAQ,QAAuC,IAAM,SAAS,GAAG5H,EAAIoN,QACliC7M,EAAkB,CAAC,WAAa,IAAIP,EAAIC,KAASC,EAAGF,EAAIG,eAAmBE,EAAGL,EAAII,MAAMC,IAAIH,EAAG,OAAOG,EAAG,IAAI,CAACG,YAAY,QAAQ,CAACH,EAAG,MAAM,CAACuH,MAAM,CAAC,IAAM,EAAQ,QAAkC,IAAM,GAAG,YAAc,mBAAmBvH,EAAG,OAAO,CAACL,EAAIS,GAAG,gB,0DCgDpQ,G,UAAA,CACE4M,MAAO,CACLC,QAAS,CACPjM,KAAMkM,QACNC,SAAS,IAGbtE,KAPF,WAQI,MAAO,IAETuE,SAAU,OAAZ,OAAY,CAAZ,GACA,8BAeE7D,QA1BF,aA6BEM,QAAS,CACPiD,SADJ,WACA,WACM,OAAN,OAAM,GAAN,kBACwB,KAAZjC,EAAI3E,MACN,EAAV,4BACU,EAAV,kCAEU,EAAV,qCAII0G,oBAXJ,WAYMhN,KAAKmK,QAAQC,KAAK,CAChBC,KAAM,eACNoD,MAAO,CACLrM,KAAM,QAIZ6L,gBAnBJ,WAoBMjN,KAAKmK,QAAQC,KAAK,CAChBC,KAAM,eACNoD,MAAO,CACLrM,KAAM,QAKZsM,QA5BJ,gBC9E8V,I,kCCS1VjN,EAAY,eACd,EACAX,EACAQ,GACA,EACA,KACA,WACA,MAIa,OAAAG,E", "file": "js/chunk-42c0ff25.cc00db5b.js", "sourcesContent": ["module.exports = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAADfSURBVHgBjZENDYMwEIW5ggDmAAnMwRAArA42BZMwUDAcwBQ0AQFIGBKQgAHo3i1hacrP9hJy8HjfXTnI+SGlVOC6bktEVRzHOXv0D4Db4BMmyhikPQDFF0KUCIezP47jQexNwKWmabpqrTv2UXMp5UArgO95XovA3L1Hd4kGYZIkFRuLSXhZGgCLp55mYAHVdf1AOZseHwlAYXrf4zVNc0cgs4BnmqYXxxLtAB2Ao7MiAeBmA1CPrUXOhgSAwgawrYhXuwURPl7bACoD/hqAZr1nPA/8PziMFb+2IB70BtG9cveBkbn3AAAAAElFTkSuQmCC\"", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=a9e39914&lang=scss&scoped=true&\"", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _vm._m(0)}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"copyright\"},[_c('span',[_vm._v(\"© 2009-2021 福建钰辰微电子有限公司 Fujian Yuchen Microelectronics Co. ,Ltd. 版权所有\")])])}]\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"copyright\">\r\n    <span\r\n      >© 2009-2021 福建钰辰微电子有限公司 Fujian Yuchen Microelectronics Co.\r\n      ,Ltd. 版权所有</span\r\n    >\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.copyright {\r\n  // position: absolute;\r\n  padding: 20px 0 32px;\r\n  // left: 50%;\r\n  // transform: translateX(-50%);\r\n  text-align: center;\r\n  span {\r\n    text-align: center;\r\n    font-style: normal;\r\n    font-weight: normal;\r\n    font-size: 12px;\r\n    line-height: 14px;\r\n    color: #b2b5bc;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=a9e39914&scoped=true&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=a9e39914&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"a9e39914\",\n  null\n  \n)\n\nexport default component.exports", "exports.endianness = function () { return 'LE' };\n\nexports.hostname = function () {\n    if (typeof location !== 'undefined') {\n        return location.hostname\n    }\n    else return '';\n};\n\nexports.loadavg = function () { return [] };\n\nexports.uptime = function () { return 0 };\n\nexports.freemem = function () {\n    return Number.MAX_VALUE;\n};\n\nexports.totalmem = function () {\n    return Number.MAX_VALUE;\n};\n\nexports.cpus = function () { return [] };\n\nexports.type = function () { return 'Browser' };\n\nexports.release = function () {\n    if (typeof navigator !== 'undefined') {\n        return navigator.appVersion;\n    }\n    return '';\n};\n\nexports.networkInterfaces\n= exports.getNetworkInterfaces\n= function () { return {} };\n\nexports.arch = function () { return 'javascript' };\n\nexports.platform = function () { return 'browser' };\n\nexports.tmpdir = exports.tmpDir = function () {\n    return '/tmp';\n};\n\nexports.EOL = '\\n';\n\nexports.homedir = function () {\n\treturn '/'\n};\n", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=f20a1212&lang=scss&scoped=true&\"", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=1&lang=scss&\"", "module.exports = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAE4SURBVHgBlVLLTcNAEH1jzCkSSgmmAkIHCC6cIlNBgoArCRVAOjAc+UikAlZYSFzwGU7QAS7BEsoJ8DDrZddeEwR50srr8TzPzJtHaIA34i5WyhGYhvIamSBeQHLeaUL3Kre55Ej9nQG4TOTaxXwUQDmhNE0ckfuxVOEE/wEFQ7q9mRJvxxGW+dlV6nSA2cxP9mMF3mg1ENKxR7o8B7Y2a5K+65j+ZqB1GIcyfM9Nqv96cQWMD2viSO6nZ34XTINQSD2vrSwzT0vWpIcMLUQB5gqAPxHKyWF3Zmey7XGjcrOq7FZmpCmoEsgIsL/3s70DiT0+1XOKIYhjccsnv2KRdSzRekBKiSPoyCW1Se1YKdZTKq/EoVRdf5ML/A5dYJfuVG05N3MsLvooTyS81lhTXukQIjHdGXwBl090YudFKwIAAAAASUVORK5CYII=\"", "module.exports = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAASvSURBVHgBpZZ/TFtVFMfPfW1puwpsw804QCGCMlwgDKIhLBnGqA0z0z8GRF2IfxiVbRJHlKmMUX4JqBtI0KiJMUaNGQTmMJldNGayASJ0Y2PAAi0rv6KFrgXKaPto3/W8spZSWvpj36S5veedez/nnXvuvQ8gRMW9e25rbkuLAEIUAyEoRaEsiIiUDEZHx6qLu7oOQwgiwTinfNSxl4pEp3FQNt9/9vmtqw+ovbhh376GIKYCYSBOKe+17wSZtAb/vuEtUkoEMRCkmM2BF2UpFRdL4QGpmodCAKKUEoXm0Iv+/HyC91QoD4CMDgPHVQOFcN5msZjBbL7rc7KKsbwXqjX5/Qxlfq1U59+sVL+a7st3Q+aSFcqnhBz3CRCy32mz222gm50Bg2EOHol9DCIitjnszjWmAGfMC83fbouyDUkiKTDCdYQfOZYtU+z+RevOEboBt4sATlCOvo9QR0CUcqC/owO9XueA+9OyEbNiIiCLoiAOv2ekcJgRiV6pGs/7OIy1fHoiqcPEmx2pTq/7PVJIQYPrU4IROqBLS4ugVg+DTjfjguLzEbFE0rcZnENXk47A/CQBO+t6bQG+Q9nwLesNhULBuMDLJhZzRh15Y1csoJ0YdfysrOVe0GDE0n2Htp1KFoukWm/Ar3N+wnqAk+g9zfdtCDUi3DRLwPCfFf7qmIGbvfq4oaEh4gK7KyExEjKzYkAmE/NdK0b7GbULErm2k83gR005Z2ukAnEWpuZ7p826CDDStwDzc5Z1vl73cXLyLoiJ2Q6dnaP7x2rf6vXmEx0r9maG+ud+mMTm9SJl3gUM+iz4kM/tFBEhgZey99z2tO/YKWKfzgyHpN3StUkYxuzpR4FqYRMFdHK5KyXBVkQihHNYaEcJpUscIaetFtnnEKSCBjfI5QZsjh/r7W0ShoUZG9PS5iEEBQ12ihUcmW5OU61AiArpWixS5srF+oRrGR9mdKeVpGVACAoajNVaj8N+w834JBZQJiNg+tJL09+GIBXCGzNxnhYstMc9bSIRFYYMtsuYv4svX86FIFWtOfTa1keZn8Mf8rgw3LTBbFqw4/VHQSIh/M0ST4mw5Xh3j5JaobDxmUwtbKLK8fxUwnGNHCXZfJ+/KDiwwbLZssF3wxsbjTb4p2cRpiata0YKchIGt9/s6NjiDbhFKoqq0uR/CRwdoLAKXWE5GOk3wB9tE3gXrBY/LgldBx6pOTBBGfIyPhlzDFqhMHrLDN1XFmF21v+O2RUvK8A5C5396fEluHRuCkavG4Gzr7Lwpu2khGa1trba+b4r1YPl8vPYnE9VKLFC6SmM7WHzMgeDA3fhwR0ieCJJ6jcAk5GFGz160P+7doJiQLNIKVRVq9rdfTek+rpC/hVjpan4KfAFrrEj3Dtz7GLXFVPpNwcPLnsD8mnlgX+2T7mghBITLnAZMZD4q9VX2z3HeK25a7U5c9gcS6+60MDamAKGwHcYkBZ8aHLM5GlqtYGtZKB+wOeYTfeaqixHg005BCoKI/gBc6S/rv+SP9egz2rC4aoxnjw6j8ByVZ2qCQJU0CcXVucHzi8MBLI4wxkiJomq2sCh96Wjyvy9aaWhXRC8/geJoe8NORQ1DgAAAABJRU5ErkJggg==\"", "module.exports = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAUySURBVHgBlVddThxHEK6anYE1GLMJjqNYZNlJlChSJMO+RHL+hE9g3wByAuAENrmAwRcw3MA5ge285CUSWHkwDybDAoEkLGb5Z3+mK1X9MzsDy0Iahu7prq6v/mtAuMaIoqiQz+dH4zh+xK/jAFgCoII5xT1ev+HFslLqdbFYfHEdnngVoN/TM0UKphGhQETAM5lrBOZVVhmOa0i40Go1FsMwXPvfwBsbW1PM+gmzHSRNKBgaLHVbttrCsGBot2VZ4Wm2WBxeuDZwZX3zKbObYq7Zc2qDuWV7X29bIQBT1HMjxeGZK4GjyuZzniah8xBIdCwx0Q6spkjsZ55Qr+VAVEcPF8Li8E9pRt450KdMO6H4j3lAz6Rn60sxgmaq3xkIwNHHilBo0vf0FQWTq8y7o8bvovVJFu25IbbWsgazJk1p5tZ0wSSiqSXGlMWNC8ib+Ty8O58Av30blYJ88JKJS5AK07Yf0fnNuFKYotXeSEbWFPrMM2ZGF/Mo+8ZFtSNsheUwrPnCzM/7EzGDGsUSJlqtJFp5J+flYHDgBt7I90K+t+eC5VxUb2zvYKsVWyEBFfOz4IU+5U8zyRNNuLK6GfHJiLuszWVsjs6YgZ+jux8PIc9iTrhscHDB+tYOtGLljGZ4Gj8I3/2Tg1bor0R/PaRYlbQtbEoYYut/3gxyHghoT+B3BTUFBrVxlaKsNQyBrAv+TX/MV6143LkvHVjkzM7EgwP9kNa0EzilKosNaISkxGlGLvAwB/TQV4Bj7XKEJj9dgQAz3+zr7QraSQitsLFAEtpsBR07/DPu8d9RAmMeApezhOYyYQ49CgI/Ybr0524G5Py7DHPfqMMCkLwr5YzK+Q5U8lRMHxghk4QnZeNKXKLoXNm8kLsXczmObTSTCSwjB++7BbGfFbW9nwoAkBTQ2sekI1R8LKP82e0MyPn3RrMFLS6bhlUinPMTOQyPt2pi+7aWba3NL9Ju7Rhs3c0EUcI2dbZ3cGpKqDIW1GUVbBnVOPwA1DxQGFkC5xsh0rPzd5WBRZNOQOlHaITWJYiNLyeA84z8LHvM+PU5AkrW5iEx9bvNXag3mhe0dutGM4ZVpolZrUTbrBKk7DtjvuGyii9cdyFjYjQzWSuYIKk3YlipVGHj3304PWtmtNyqHvDZDtSbMcXOnNTuaClAu6de4dJSVGgGuYjPC+3KZctHqtOgCQx0Lcl+ckDybZK6Zy9nOlsSQPxp9M3XI6FfLoe13/6ozHNjfpzuiJDi43keDBX6cIALSV8+QJ9LaM5rt/ITtkC92YTDkwbsH55RvdlyRcHVetfGOMDiRbcBovVpzpNGUUg2WUA/hzh8ZxBuF/qurFxJOvBcrZ3AdvWQhWm19RDrKaqwLx7cL4drWmzRmr0660JR/BD4Hn5V+igBdc9lI00jd74oDoEvuW+DygQuzgqotqK7+O1oOMf5JY8m+pIv9ga5rgDdBJK74SeDSVSziZ/dv1dccOeZb67v743MxLFaFMVXKrtUb2Rz182dnjSNDI5wqPxz6HJ54bvRcDqjAHQYvy5V+MOMpiWS2Wz46Z0ByPf4V3YmAZac364ewd/vj3X+s6Gf/VDOgl4KLOPlUjTJresxL0siwK2+XvzwVh76bwTQnw9AIluGMJcc3z+uw3uO6P2jM9P6kP+1ofjnH8vhXCf+XVVg8BJPk/wvyYT9f6kjC93kIfmerBGpeXbi3AMJ2kvG1V3dCfF79Iipx/kTcpSr/hiD6NTj0rfHwBVW8RW3pV/Ah+VugG78B88ywyQqdJyOAAAAAElFTkSuQmCC\"", "/**\n * [js-md5]{@link https://github.com/emn178/js-md5}\n *\n * @namespace md5\n * @version 0.7.3\n * <AUTHOR> <PERSON><PERSON><PERSON> [<EMAIL>]\n * @copyright Chen, <PERSON><PERSON><PERSON><PERSON> 2014-2017\n * @license MIT\n */\n(function () {\n  'use strict';\n\n  var ERROR = 'input is invalid type';\n  var WINDOW = typeof window === 'object';\n  var root = WINDOW ? window : {};\n  if (root.JS_MD5_NO_WINDOW) {\n    WINDOW = false;\n  }\n  var WEB_WORKER = !WINDOW && typeof self === 'object';\n  var NODE_JS = !root.JS_MD5_NO_NODE_JS && typeof process === 'object' && process.versions && process.versions.node;\n  if (NODE_JS) {\n    root = global;\n  } else if (WEB_WORKER) {\n    root = self;\n  }\n  var COMMON_JS = !root.JS_MD5_NO_COMMON_JS && typeof module === 'object' && module.exports;\n  var AMD = typeof define === 'function' && define.amd;\n  var ARRAY_BUFFER = !root.JS_MD5_NO_ARRAY_BUFFER && typeof ArrayBuffer !== 'undefined';\n  var HEX_CHARS = '0123456789abcdef'.split('');\n  var EXTRA = [128, 32768, 8388608, -**********];\n  var SHIFT = [0, 8, 16, 24];\n  var OUTPUT_TYPES = ['hex', 'array', 'digest', 'buffer', 'arrayBuffer', 'base64'];\n  var BASE64_ENCODE_CHAR = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'.split('');\n\n  var blocks = [], buffer8;\n  if (ARRAY_BUFFER) {\n    var buffer = new ArrayBuffer(68);\n    buffer8 = new Uint8Array(buffer);\n    blocks = new Uint32Array(buffer);\n  }\n\n  if (root.JS_MD5_NO_NODE_JS || !Array.isArray) {\n    Array.isArray = function (obj) {\n      return Object.prototype.toString.call(obj) === '[object Array]';\n    };\n  }\n\n  if (ARRAY_BUFFER && (root.JS_MD5_NO_ARRAY_BUFFER_IS_VIEW || !ArrayBuffer.isView)) {\n    ArrayBuffer.isView = function (obj) {\n      return typeof obj === 'object' && obj.buffer && obj.buffer.constructor === ArrayBuffer;\n    };\n  }\n\n  /**\n   * @method hex\n   * @memberof md5\n   * @description Output hash as hex string\n   * @param {String|Array|Uint8Array|ArrayBuffer} message message to hash\n   * @returns {String} Hex string\n   * @example\n   * md5.hex('The quick brown fox jumps over the lazy dog');\n   * // equal to\n   * md5('The quick brown fox jumps over the lazy dog');\n   */\n  /**\n   * @method digest\n   * @memberof md5\n   * @description Output hash as bytes array\n   * @param {String|Array|Uint8Array|ArrayBuffer} message message to hash\n   * @returns {Array} Bytes array\n   * @example\n   * md5.digest('The quick brown fox jumps over the lazy dog');\n   */\n  /**\n   * @method array\n   * @memberof md5\n   * @description Output hash as bytes array\n   * @param {String|Array|Uint8Array|ArrayBuffer} message message to hash\n   * @returns {Array} Bytes array\n   * @example\n   * md5.array('The quick brown fox jumps over the lazy dog');\n   */\n  /**\n   * @method arrayBuffer\n   * @memberof md5\n   * @description Output hash as ArrayBuffer\n   * @param {String|Array|Uint8Array|ArrayBuffer} message message to hash\n   * @returns {ArrayBuffer} ArrayBuffer\n   * @example\n   * md5.arrayBuffer('The quick brown fox jumps over the lazy dog');\n   */\n  /**\n   * @method buffer\n   * @deprecated This maybe confuse with Buffer in node.js. Please use arrayBuffer instead.\n   * @memberof md5\n   * @description Output hash as ArrayBuffer\n   * @param {String|Array|Uint8Array|ArrayBuffer} message message to hash\n   * @returns {ArrayBuffer} ArrayBuffer\n   * @example\n   * md5.buffer('The quick brown fox jumps over the lazy dog');\n   */\n  /**\n   * @method base64\n   * @memberof md5\n   * @description Output hash as base64 string\n   * @param {String|Array|Uint8Array|ArrayBuffer} message message to hash\n   * @returns {String} base64 string\n   * @example\n   * md5.base64('The quick brown fox jumps over the lazy dog');\n   */\n  var createOutputMethod = function (outputType) {\n    return function (message) {\n      return new Md5(true).update(message)[outputType]();\n    };\n  };\n\n  /**\n   * @method create\n   * @memberof md5\n   * @description Create Md5 object\n   * @returns {Md5} Md5 object.\n   * @example\n   * var hash = md5.create();\n   */\n  /**\n   * @method update\n   * @memberof md5\n   * @description Create and update Md5 object\n   * @param {String|Array|Uint8Array|ArrayBuffer} message message to hash\n   * @returns {Md5} Md5 object.\n   * @example\n   * var hash = md5.update('The quick brown fox jumps over the lazy dog');\n   * // equal to\n   * var hash = md5.create();\n   * hash.update('The quick brown fox jumps over the lazy dog');\n   */\n  var createMethod = function () {\n    var method = createOutputMethod('hex');\n    if (NODE_JS) {\n      method = nodeWrap(method);\n    }\n    method.create = function () {\n      return new Md5();\n    };\n    method.update = function (message) {\n      return method.create().update(message);\n    };\n    for (var i = 0; i < OUTPUT_TYPES.length; ++i) {\n      var type = OUTPUT_TYPES[i];\n      method[type] = createOutputMethod(type);\n    }\n    return method;\n  };\n\n  var nodeWrap = function (method) {\n    var crypto = eval(\"require('crypto')\");\n    var Buffer = eval(\"require('buffer').Buffer\");\n    var nodeMethod = function (message) {\n      if (typeof message === 'string') {\n        return crypto.createHash('md5').update(message, 'utf8').digest('hex');\n      } else {\n        if (message === null || message === undefined) {\n          throw ERROR;\n        } else if (message.constructor === ArrayBuffer) {\n          message = new Uint8Array(message);\n        }\n      }\n      if (Array.isArray(message) || ArrayBuffer.isView(message) ||\n        message.constructor === Buffer) {\n        return crypto.createHash('md5').update(new Buffer(message)).digest('hex');\n      } else {\n        return method(message);\n      }\n    };\n    return nodeMethod;\n  };\n\n  /**\n   * Md5 class\n   * @class Md5\n   * @description This is internal class.\n   * @see {@link md5.create}\n   */\n  function Md5(sharedMemory) {\n    if (sharedMemory) {\n      blocks[0] = blocks[16] = blocks[1] = blocks[2] = blocks[3] =\n      blocks[4] = blocks[5] = blocks[6] = blocks[7] =\n      blocks[8] = blocks[9] = blocks[10] = blocks[11] =\n      blocks[12] = blocks[13] = blocks[14] = blocks[15] = 0;\n      this.blocks = blocks;\n      this.buffer8 = buffer8;\n    } else {\n      if (ARRAY_BUFFER) {\n        var buffer = new ArrayBuffer(68);\n        this.buffer8 = new Uint8Array(buffer);\n        this.blocks = new Uint32Array(buffer);\n      } else {\n        this.blocks = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];\n      }\n    }\n    this.h0 = this.h1 = this.h2 = this.h3 = this.start = this.bytes = this.hBytes = 0;\n    this.finalized = this.hashed = false;\n    this.first = true;\n  }\n\n  /**\n   * @method update\n   * @memberof Md5\n   * @instance\n   * @description Update hash\n   * @param {String|Array|Uint8Array|ArrayBuffer} message message to hash\n   * @returns {Md5} Md5 object.\n   * @see {@link md5.update}\n   */\n  Md5.prototype.update = function (message) {\n    if (this.finalized) {\n      return;\n    }\n\n    var notString, type = typeof message;\n    if (type !== 'string') {\n      if (type === 'object') {\n        if (message === null) {\n          throw ERROR;\n        } else if (ARRAY_BUFFER && message.constructor === ArrayBuffer) {\n          message = new Uint8Array(message);\n        } else if (!Array.isArray(message)) {\n          if (!ARRAY_BUFFER || !ArrayBuffer.isView(message)) {\n            throw ERROR;\n          }\n        }\n      } else {\n        throw ERROR;\n      }\n      notString = true;\n    }\n    var code, index = 0, i, length = message.length, blocks = this.blocks;\n    var buffer8 = this.buffer8;\n\n    while (index < length) {\n      if (this.hashed) {\n        this.hashed = false;\n        blocks[0] = blocks[16];\n        blocks[16] = blocks[1] = blocks[2] = blocks[3] =\n        blocks[4] = blocks[5] = blocks[6] = blocks[7] =\n        blocks[8] = blocks[9] = blocks[10] = blocks[11] =\n        blocks[12] = blocks[13] = blocks[14] = blocks[15] = 0;\n      }\n\n      if (notString) {\n        if (ARRAY_BUFFER) {\n          for (i = this.start; index < length && i < 64; ++index) {\n            buffer8[i++] = message[index];\n          }\n        } else {\n          for (i = this.start; index < length && i < 64; ++index) {\n            blocks[i >> 2] |= message[index] << SHIFT[i++ & 3];\n          }\n        }\n      } else {\n        if (ARRAY_BUFFER) {\n          for (i = this.start; index < length && i < 64; ++index) {\n            code = message.charCodeAt(index);\n            if (code < 0x80) {\n              buffer8[i++] = code;\n            } else if (code < 0x800) {\n              buffer8[i++] = 0xc0 | (code >> 6);\n              buffer8[i++] = 0x80 | (code & 0x3f);\n            } else if (code < 0xd800 || code >= 0xe000) {\n              buffer8[i++] = 0xe0 | (code >> 12);\n              buffer8[i++] = 0x80 | ((code >> 6) & 0x3f);\n              buffer8[i++] = 0x80 | (code & 0x3f);\n            } else {\n              code = 0x10000 + (((code & 0x3ff) << 10) | (message.charCodeAt(++index) & 0x3ff));\n              buffer8[i++] = 0xf0 | (code >> 18);\n              buffer8[i++] = 0x80 | ((code >> 12) & 0x3f);\n              buffer8[i++] = 0x80 | ((code >> 6) & 0x3f);\n              buffer8[i++] = 0x80 | (code & 0x3f);\n            }\n          }\n        } else {\n          for (i = this.start; index < length && i < 64; ++index) {\n            code = message.charCodeAt(index);\n            if (code < 0x80) {\n              blocks[i >> 2] |= code << SHIFT[i++ & 3];\n            } else if (code < 0x800) {\n              blocks[i >> 2] |= (0xc0 | (code >> 6)) << SHIFT[i++ & 3];\n              blocks[i >> 2] |= (0x80 | (code & 0x3f)) << SHIFT[i++ & 3];\n            } else if (code < 0xd800 || code >= 0xe000) {\n              blocks[i >> 2] |= (0xe0 | (code >> 12)) << SHIFT[i++ & 3];\n              blocks[i >> 2] |= (0x80 | ((code >> 6) & 0x3f)) << SHIFT[i++ & 3];\n              blocks[i >> 2] |= (0x80 | (code & 0x3f)) << SHIFT[i++ & 3];\n            } else {\n              code = 0x10000 + (((code & 0x3ff) << 10) | (message.charCodeAt(++index) & 0x3ff));\n              blocks[i >> 2] |= (0xf0 | (code >> 18)) << SHIFT[i++ & 3];\n              blocks[i >> 2] |= (0x80 | ((code >> 12) & 0x3f)) << SHIFT[i++ & 3];\n              blocks[i >> 2] |= (0x80 | ((code >> 6) & 0x3f)) << SHIFT[i++ & 3];\n              blocks[i >> 2] |= (0x80 | (code & 0x3f)) << SHIFT[i++ & 3];\n            }\n          }\n        }\n      }\n      this.lastByteIndex = i;\n      this.bytes += i - this.start;\n      if (i >= 64) {\n        this.start = i - 64;\n        this.hash();\n        this.hashed = true;\n      } else {\n        this.start = i;\n      }\n    }\n    if (this.bytes > 4294967295) {\n      this.hBytes += this.bytes / 4294967296 << 0;\n      this.bytes = this.bytes % 4294967296;\n    }\n    return this;\n  };\n\n  Md5.prototype.finalize = function () {\n    if (this.finalized) {\n      return;\n    }\n    this.finalized = true;\n    var blocks = this.blocks, i = this.lastByteIndex;\n    blocks[i >> 2] |= EXTRA[i & 3];\n    if (i >= 56) {\n      if (!this.hashed) {\n        this.hash();\n      }\n      blocks[0] = blocks[16];\n      blocks[16] = blocks[1] = blocks[2] = blocks[3] =\n      blocks[4] = blocks[5] = blocks[6] = blocks[7] =\n      blocks[8] = blocks[9] = blocks[10] = blocks[11] =\n      blocks[12] = blocks[13] = blocks[14] = blocks[15] = 0;\n    }\n    blocks[14] = this.bytes << 3;\n    blocks[15] = this.hBytes << 3 | this.bytes >>> 29;\n    this.hash();\n  };\n\n  Md5.prototype.hash = function () {\n    var a, b, c, d, bc, da, blocks = this.blocks;\n\n    if (this.first) {\n      a = blocks[0] - 680876937;\n      a = (a << 7 | a >>> 25) - 271733879 << 0;\n      d = (-1732584194 ^ a & 2004318071) + blocks[1] - 117830708;\n      d = (d << 12 | d >>> 20) + a << 0;\n      c = (-271733879 ^ (d & (a ^ -271733879))) + blocks[2] - 1126478375;\n      c = (c << 17 | c >>> 15) + d << 0;\n      b = (a ^ (c & (d ^ a))) + blocks[3] - 1316259209;\n      b = (b << 22 | b >>> 10) + c << 0;\n    } else {\n      a = this.h0;\n      b = this.h1;\n      c = this.h2;\n      d = this.h3;\n      a += (d ^ (b & (c ^ d))) + blocks[0] - 680876936;\n      a = (a << 7 | a >>> 25) + b << 0;\n      d += (c ^ (a & (b ^ c))) + blocks[1] - 389564586;\n      d = (d << 12 | d >>> 20) + a << 0;\n      c += (b ^ (d & (a ^ b))) + blocks[2] + 606105819;\n      c = (c << 17 | c >>> 15) + d << 0;\n      b += (a ^ (c & (d ^ a))) + blocks[3] - 1044525330;\n      b = (b << 22 | b >>> 10) + c << 0;\n    }\n\n    a += (d ^ (b & (c ^ d))) + blocks[4] - 176418897;\n    a = (a << 7 | a >>> 25) + b << 0;\n    d += (c ^ (a & (b ^ c))) + blocks[5] + 1200080426;\n    d = (d << 12 | d >>> 20) + a << 0;\n    c += (b ^ (d & (a ^ b))) + blocks[6] - 1473231341;\n    c = (c << 17 | c >>> 15) + d << 0;\n    b += (a ^ (c & (d ^ a))) + blocks[7] - 45705983;\n    b = (b << 22 | b >>> 10) + c << 0;\n    a += (d ^ (b & (c ^ d))) + blocks[8] + 1770035416;\n    a = (a << 7 | a >>> 25) + b << 0;\n    d += (c ^ (a & (b ^ c))) + blocks[9] - 1958414417;\n    d = (d << 12 | d >>> 20) + a << 0;\n    c += (b ^ (d & (a ^ b))) + blocks[10] - 42063;\n    c = (c << 17 | c >>> 15) + d << 0;\n    b += (a ^ (c & (d ^ a))) + blocks[11] - 1990404162;\n    b = (b << 22 | b >>> 10) + c << 0;\n    a += (d ^ (b & (c ^ d))) + blocks[12] + 1804603682;\n    a = (a << 7 | a >>> 25) + b << 0;\n    d += (c ^ (a & (b ^ c))) + blocks[13] - 40341101;\n    d = (d << 12 | d >>> 20) + a << 0;\n    c += (b ^ (d & (a ^ b))) + blocks[14] - 1502002290;\n    c = (c << 17 | c >>> 15) + d << 0;\n    b += (a ^ (c & (d ^ a))) + blocks[15] + 1236535329;\n    b = (b << 22 | b >>> 10) + c << 0;\n    a += (c ^ (d & (b ^ c))) + blocks[1] - 165796510;\n    a = (a << 5 | a >>> 27) + b << 0;\n    d += (b ^ (c & (a ^ b))) + blocks[6] - 1069501632;\n    d = (d << 9 | d >>> 23) + a << 0;\n    c += (a ^ (b & (d ^ a))) + blocks[11] + 643717713;\n    c = (c << 14 | c >>> 18) + d << 0;\n    b += (d ^ (a & (c ^ d))) + blocks[0] - 373897302;\n    b = (b << 20 | b >>> 12) + c << 0;\n    a += (c ^ (d & (b ^ c))) + blocks[5] - 701558691;\n    a = (a << 5 | a >>> 27) + b << 0;\n    d += (b ^ (c & (a ^ b))) + blocks[10] + 38016083;\n    d = (d << 9 | d >>> 23) + a << 0;\n    c += (a ^ (b & (d ^ a))) + blocks[15] - 660478335;\n    c = (c << 14 | c >>> 18) + d << 0;\n    b += (d ^ (a & (c ^ d))) + blocks[4] - 405537848;\n    b = (b << 20 | b >>> 12) + c << 0;\n    a += (c ^ (d & (b ^ c))) + blocks[9] + 568446438;\n    a = (a << 5 | a >>> 27) + b << 0;\n    d += (b ^ (c & (a ^ b))) + blocks[14] - 1019803690;\n    d = (d << 9 | d >>> 23) + a << 0;\n    c += (a ^ (b & (d ^ a))) + blocks[3] - 187363961;\n    c = (c << 14 | c >>> 18) + d << 0;\n    b += (d ^ (a & (c ^ d))) + blocks[8] + 1163531501;\n    b = (b << 20 | b >>> 12) + c << 0;\n    a += (c ^ (d & (b ^ c))) + blocks[13] - 1444681467;\n    a = (a << 5 | a >>> 27) + b << 0;\n    d += (b ^ (c & (a ^ b))) + blocks[2] - 51403784;\n    d = (d << 9 | d >>> 23) + a << 0;\n    c += (a ^ (b & (d ^ a))) + blocks[7] + 1735328473;\n    c = (c << 14 | c >>> 18) + d << 0;\n    b += (d ^ (a & (c ^ d))) + blocks[12] - 1926607734;\n    b = (b << 20 | b >>> 12) + c << 0;\n    bc = b ^ c;\n    a += (bc ^ d) + blocks[5] - 378558;\n    a = (a << 4 | a >>> 28) + b << 0;\n    d += (bc ^ a) + blocks[8] - 2022574463;\n    d = (d << 11 | d >>> 21) + a << 0;\n    da = d ^ a;\n    c += (da ^ b) + blocks[11] + 1839030562;\n    c = (c << 16 | c >>> 16) + d << 0;\n    b += (da ^ c) + blocks[14] - 35309556;\n    b = (b << 23 | b >>> 9) + c << 0;\n    bc = b ^ c;\n    a += (bc ^ d) + blocks[1] - 1530992060;\n    a = (a << 4 | a >>> 28) + b << 0;\n    d += (bc ^ a) + blocks[4] + 1272893353;\n    d = (d << 11 | d >>> 21) + a << 0;\n    da = d ^ a;\n    c += (da ^ b) + blocks[7] - 155497632;\n    c = (c << 16 | c >>> 16) + d << 0;\n    b += (da ^ c) + blocks[10] - 1094730640;\n    b = (b << 23 | b >>> 9) + c << 0;\n    bc = b ^ c;\n    a += (bc ^ d) + blocks[13] + 681279174;\n    a = (a << 4 | a >>> 28) + b << 0;\n    d += (bc ^ a) + blocks[0] - 358537222;\n    d = (d << 11 | d >>> 21) + a << 0;\n    da = d ^ a;\n    c += (da ^ b) + blocks[3] - 722521979;\n    c = (c << 16 | c >>> 16) + d << 0;\n    b += (da ^ c) + blocks[6] + 76029189;\n    b = (b << 23 | b >>> 9) + c << 0;\n    bc = b ^ c;\n    a += (bc ^ d) + blocks[9] - 640364487;\n    a = (a << 4 | a >>> 28) + b << 0;\n    d += (bc ^ a) + blocks[12] - 421815835;\n    d = (d << 11 | d >>> 21) + a << 0;\n    da = d ^ a;\n    c += (da ^ b) + blocks[15] + 530742520;\n    c = (c << 16 | c >>> 16) + d << 0;\n    b += (da ^ c) + blocks[2] - 995338651;\n    b = (b << 23 | b >>> 9) + c << 0;\n    a += (c ^ (b | ~d)) + blocks[0] - 198630844;\n    a = (a << 6 | a >>> 26) + b << 0;\n    d += (b ^ (a | ~c)) + blocks[7] + 1126891415;\n    d = (d << 10 | d >>> 22) + a << 0;\n    c += (a ^ (d | ~b)) + blocks[14] - 1416354905;\n    c = (c << 15 | c >>> 17) + d << 0;\n    b += (d ^ (c | ~a)) + blocks[5] - 57434055;\n    b = (b << 21 | b >>> 11) + c << 0;\n    a += (c ^ (b | ~d)) + blocks[12] + 1700485571;\n    a = (a << 6 | a >>> 26) + b << 0;\n    d += (b ^ (a | ~c)) + blocks[3] - 1894986606;\n    d = (d << 10 | d >>> 22) + a << 0;\n    c += (a ^ (d | ~b)) + blocks[10] - 1051523;\n    c = (c << 15 | c >>> 17) + d << 0;\n    b += (d ^ (c | ~a)) + blocks[1] - 2054922799;\n    b = (b << 21 | b >>> 11) + c << 0;\n    a += (c ^ (b | ~d)) + blocks[8] + 1873313359;\n    a = (a << 6 | a >>> 26) + b << 0;\n    d += (b ^ (a | ~c)) + blocks[15] - 30611744;\n    d = (d << 10 | d >>> 22) + a << 0;\n    c += (a ^ (d | ~b)) + blocks[6] - 1560198380;\n    c = (c << 15 | c >>> 17) + d << 0;\n    b += (d ^ (c | ~a)) + blocks[13] + 1309151649;\n    b = (b << 21 | b >>> 11) + c << 0;\n    a += (c ^ (b | ~d)) + blocks[4] - 145523070;\n    a = (a << 6 | a >>> 26) + b << 0;\n    d += (b ^ (a | ~c)) + blocks[11] - 1120210379;\n    d = (d << 10 | d >>> 22) + a << 0;\n    c += (a ^ (d | ~b)) + blocks[2] + 718787259;\n    c = (c << 15 | c >>> 17) + d << 0;\n    b += (d ^ (c | ~a)) + blocks[9] - 343485551;\n    b = (b << 21 | b >>> 11) + c << 0;\n\n    if (this.first) {\n      this.h0 = a + 1732584193 << 0;\n      this.h1 = b - 271733879 << 0;\n      this.h2 = c - 1732584194 << 0;\n      this.h3 = d + 271733878 << 0;\n      this.first = false;\n    } else {\n      this.h0 = this.h0 + a << 0;\n      this.h1 = this.h1 + b << 0;\n      this.h2 = this.h2 + c << 0;\n      this.h3 = this.h3 + d << 0;\n    }\n  };\n\n  /**\n   * @method hex\n   * @memberof Md5\n   * @instance\n   * @description Output hash as hex string\n   * @returns {String} Hex string\n   * @see {@link md5.hex}\n   * @example\n   * hash.hex();\n   */\n  Md5.prototype.hex = function () {\n    this.finalize();\n\n    var h0 = this.h0, h1 = this.h1, h2 = this.h2, h3 = this.h3;\n\n    return HEX_CHARS[(h0 >> 4) & 0x0F] + HEX_CHARS[h0 & 0x0F] +\n      HEX_CHARS[(h0 >> 12) & 0x0F] + HEX_CHARS[(h0 >> 8) & 0x0F] +\n      HEX_CHARS[(h0 >> 20) & 0x0F] + HEX_CHARS[(h0 >> 16) & 0x0F] +\n      HEX_CHARS[(h0 >> 28) & 0x0F] + HEX_CHARS[(h0 >> 24) & 0x0F] +\n      HEX_CHARS[(h1 >> 4) & 0x0F] + HEX_CHARS[h1 & 0x0F] +\n      HEX_CHARS[(h1 >> 12) & 0x0F] + HEX_CHARS[(h1 >> 8) & 0x0F] +\n      HEX_CHARS[(h1 >> 20) & 0x0F] + HEX_CHARS[(h1 >> 16) & 0x0F] +\n      HEX_CHARS[(h1 >> 28) & 0x0F] + HEX_CHARS[(h1 >> 24) & 0x0F] +\n      HEX_CHARS[(h2 >> 4) & 0x0F] + HEX_CHARS[h2 & 0x0F] +\n      HEX_CHARS[(h2 >> 12) & 0x0F] + HEX_CHARS[(h2 >> 8) & 0x0F] +\n      HEX_CHARS[(h2 >> 20) & 0x0F] + HEX_CHARS[(h2 >> 16) & 0x0F] +\n      HEX_CHARS[(h2 >> 28) & 0x0F] + HEX_CHARS[(h2 >> 24) & 0x0F] +\n      HEX_CHARS[(h3 >> 4) & 0x0F] + HEX_CHARS[h3 & 0x0F] +\n      HEX_CHARS[(h3 >> 12) & 0x0F] + HEX_CHARS[(h3 >> 8) & 0x0F] +\n      HEX_CHARS[(h3 >> 20) & 0x0F] + HEX_CHARS[(h3 >> 16) & 0x0F] +\n      HEX_CHARS[(h3 >> 28) & 0x0F] + HEX_CHARS[(h3 >> 24) & 0x0F];\n  };\n\n  /**\n   * @method toString\n   * @memberof Md5\n   * @instance\n   * @description Output hash as hex string\n   * @returns {String} Hex string\n   * @see {@link md5.hex}\n   * @example\n   * hash.toString();\n   */\n  Md5.prototype.toString = Md5.prototype.hex;\n\n  /**\n   * @method digest\n   * @memberof Md5\n   * @instance\n   * @description Output hash as bytes array\n   * @returns {Array} Bytes array\n   * @see {@link md5.digest}\n   * @example\n   * hash.digest();\n   */\n  Md5.prototype.digest = function () {\n    this.finalize();\n\n    var h0 = this.h0, h1 = this.h1, h2 = this.h2, h3 = this.h3;\n    return [\n      h0 & 0xFF, (h0 >> 8) & 0xFF, (h0 >> 16) & 0xFF, (h0 >> 24) & 0xFF,\n      h1 & 0xFF, (h1 >> 8) & 0xFF, (h1 >> 16) & 0xFF, (h1 >> 24) & 0xFF,\n      h2 & 0xFF, (h2 >> 8) & 0xFF, (h2 >> 16) & 0xFF, (h2 >> 24) & 0xFF,\n      h3 & 0xFF, (h3 >> 8) & 0xFF, (h3 >> 16) & 0xFF, (h3 >> 24) & 0xFF\n    ];\n  };\n\n  /**\n   * @method array\n   * @memberof Md5\n   * @instance\n   * @description Output hash as bytes array\n   * @returns {Array} Bytes array\n   * @see {@link md5.array}\n   * @example\n   * hash.array();\n   */\n  Md5.prototype.array = Md5.prototype.digest;\n\n  /**\n   * @method arrayBuffer\n   * @memberof Md5\n   * @instance\n   * @description Output hash as ArrayBuffer\n   * @returns {ArrayBuffer} ArrayBuffer\n   * @see {@link md5.arrayBuffer}\n   * @example\n   * hash.arrayBuffer();\n   */\n  Md5.prototype.arrayBuffer = function () {\n    this.finalize();\n\n    var buffer = new ArrayBuffer(16);\n    var blocks = new Uint32Array(buffer);\n    blocks[0] = this.h0;\n    blocks[1] = this.h1;\n    blocks[2] = this.h2;\n    blocks[3] = this.h3;\n    return buffer;\n  };\n\n  /**\n   * @method buffer\n   * @deprecated This maybe confuse with Buffer in node.js. Please use arrayBuffer instead.\n   * @memberof Md5\n   * @instance\n   * @description Output hash as ArrayBuffer\n   * @returns {ArrayBuffer} ArrayBuffer\n   * @see {@link md5.buffer}\n   * @example\n   * hash.buffer();\n   */\n  Md5.prototype.buffer = Md5.prototype.arrayBuffer;\n\n  /**\n   * @method base64\n   * @memberof Md5\n   * @instance\n   * @description Output hash as base64 string\n   * @returns {String} base64 string\n   * @see {@link md5.base64}\n   * @example\n   * hash.base64();\n   */\n  Md5.prototype.base64 = function () {\n    var v1, v2, v3, base64Str = '', bytes = this.array();\n    for (var i = 0; i < 15;) {\n      v1 = bytes[i++];\n      v2 = bytes[i++];\n      v3 = bytes[i++];\n      base64Str += BASE64_ENCODE_CHAR[v1 >>> 2] +\n        BASE64_ENCODE_CHAR[(v1 << 4 | v2 >>> 4) & 63] +\n        BASE64_ENCODE_CHAR[(v2 << 2 | v3 >>> 6) & 63] +\n        BASE64_ENCODE_CHAR[v3 & 63];\n    }\n    v1 = bytes[i];\n    base64Str += BASE64_ENCODE_CHAR[v1 >>> 2] +\n      BASE64_ENCODE_CHAR[(v1 << 4) & 63] +\n      '==';\n    return base64Str;\n  };\n\n  var exports = createMethod();\n\n  if (COMMON_JS) {\n    module.exports = exports;\n  } else {\n    /**\n     * @method md5\b\n     * @description Md5 hash function, export to global in browsers.\n     * @param {String|Array|Uint8Array|ArrayBuffer} message message to hash\n     * @returns {String} md5 hashes\n     * @example\n     * md5(''); // d41d8cd98f00b204e9800998ecf8427e\n     * md5('The quick brown fox jumps over the lazy dog'); // 9e107d9d372bb6826bd81d3542a419d6\n     * md5('The quick brown fox jumps over the lazy dog.'); // e4d909c290d0fb1ca068ffaddf22cbd0\n     *\n     * // It also supports UTF-8 encoding\n     * md5('中文'); // a7bac2239fcdcb3a067903d8077c4a07\n     *\n     * // It also supports byte `Array`, `Uint8Array`, `ArrayBuffer`\n     * md5([]); // d41d8cd98f00b204e9800998ecf8427e\n     * md5(new Uint8Array([])); // d41d8cd98f00b204e9800998ecf8427e\n     */\n    root.md5 = exports;\n    if (AMD) {\n      define(function () {\n        return exports;\n      });\n    }\n  }\n})();\n", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=2233f412&lang=scss&scoped=true&\"", "module.exports = __webpack_public_path__ + \"img/poster.91669c4a.png\";", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"login\"},[_c('top-bar',{attrs:{\"isLogin\":false}}),_c('div',{staticClass:\"content flex\"},[_vm._m(0),_c('div',{staticClass:\"login-form flex\"},[_c('h4',[_vm._v(\"登录您的账号\")]),_c('p',{staticClass:\"login-tips flex\",style:({ opacity: _vm.loginTips ? '1' : '0' })},[_c('img',{attrs:{\"src\":require(\"@/assets/images/index/wrong-icon.png\"),\"alt\":\"\"}}),_c('span',[_vm._v(_vm._s(_vm.loginTips))])]),_c('el-form',{ref:\"form\",attrs:{\"model\":_vm.form,\"rules\":_vm.rules}},[_c('el-form-item',{attrs:{\"label\":\"\",\"prop\":\"username\"}},[_c('div',{staticClass:\"form-item\"},[_c('el-input',{ref:\"username\",attrs:{\"clearable\":\"\",\"placeholder\":\"手机号码/用户名\"},on:{\"focus\":function($event){_vm.nameFocus = true},\"blur\":function($event){_vm.nameFocus = false}},model:{value:(_vm.form.username),callback:function ($$v) {_vm.$set(_vm.form, \"username\", $$v)},expression:\"form.username\"}})],1)]),_c('el-form-item',{attrs:{\"label\":\"\",\"prop\":\"password\"}},[_c('div',{staticClass:\"form-item form-password\"},[_c('el-input',{attrs:{\"clearable\":\"\",\"show-password\":true,\"placeholder\":\"密码\"},on:{\"focus\":function($event){_vm.passwordFocus = true},\"blur\":function($event){_vm.passwordFocus = false}},model:{value:(_vm.form.password),callback:function ($$v) {_vm.$set(_vm.form, \"password\", $$v)},expression:\"form.password\"}})],1)])],1),_c('div',{directives:[{name:\"throttle\",rawName:\"v-throttle\",value:(500),expression:\"500\"}],staticClass:\"login-button\",on:{\"click\":_vm.handleLogin}},[(_vm.flag)?_c('span',[_vm._v(\"登录\")]):_c('span',[_vm._v(\"登录中...\")])])],1)]),_c('copyright',{staticClass:\"copyright\"})],1)}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"pic\"},[_c('img',{attrs:{\"src\":require(\"@/assets/images/index/poster.png\"),\"alt\":\"\",\"ondragstart\":\"return false;\"}})])}]\n\nexport { render, staticRenderFns }", "import md5 from \"js-md5\";\r\nimport topBar from \"@/components/topbar\";\r\nimport copyright from \"@/components/copyright\";\r\nimport { getLogin, childList } from \"@/api/user\";\r\nexport default {\r\n    data () {\r\n        return {\r\n            flag: true,\r\n            nameFocus: false,\r\n            passwordFocus: false,\r\n            isVisible: false, //是否为明文输入框\r\n            form: {\r\n                username: \"\",\r\n                password: \"\",\r\n            },\r\n            rules: {\r\n                username: [\r\n                    {\r\n                        required: true,\r\n                        message: \"请输入手机号或用户名\",\r\n                        trigger: \"blur\",\r\n                    },\r\n                ],\r\n                password: [{ required: true, message: \"请输入密码\", trigger: \"blur\" }],\r\n            },\r\n            loginTips: \"\", //用户名或密码不正确\r\n        };\r\n    },\r\n    components: { topBar, copyright },\r\n    mounted () {\r\n        window.addEventListener(\"keydown\", this.keyDown, true);\r\n        this.$nextTick(() => {\r\n            this.$refs.username.focus();\r\n        });\r\n    },\r\n    methods: {\r\n        // test 提交\r\n        handleRegister () {\r\n            this.$router.push({\r\n                path: \"/register\",\r\n            });\r\n        },\r\n        handleReset () {\r\n            this.$router.push({\r\n                path: \"/reset\",\r\n            });\r\n        },\r\n        keyDown (event) {\r\n            if (event.keyCode === 13) {\r\n                this.handleLogin();\r\n            }\r\n        },\r\n        handleLogin () {\r\n            this.$refs.form.validate((valid) => {\r\n                if (valid) {\r\n                    if (!this.flag) return;\r\n                    this.flag = false;\r\n                    let params = {\r\n                        grant_type: \"password\",\r\n                        scope: \"all\",\r\n                    };\r\n                    params = Object.assign(params, this.form);\r\n                    // 暂时只要md5        this.$getRsaCode(md5(params.password))\r\n                    params.password = md5(params.password);\r\n                    getLogin(params)\r\n                        .then((res) => {\r\n                            if (res.code == 200) {\r\n                                debugger;\r\n                                localStorage.setItem(\"access_token\", res.data.token);\r\n                                let encrypt_tenant_id = this.Encrypt.encryptoByAES(\r\n                                    res.data.tenant_id\r\n                                );\r\n                                localStorage.setItem(\"tenant_id\", encrypt_tenant_id);\r\n\r\n                                let userInfo = {\r\n                                    ...res.data,\r\n                                    tenant_id: encrypt_tenant_id,\r\n                                };\r\n                                localStorage.setItem(\"userInfo\", JSON.stringify(userInfo));\r\n                                this.$store.dispatch(\"setUserInfo\", res.data);\r\n                                this.$router.push({\r\n                                    path: \"/device/connector\",\r\n                                });\r\n                                // childList({\r\n                                //   tenant_id: res.data.tenant_id,\r\n                                //   parentId: \"\",\r\n                                // }).then((res) => {\r\n                                //   console.log(\"/cc\", res);\r\n                                // });\r\n                                // window.location.reload();\r\n                            } else {\r\n                                // this.$message.warning(res.message);\r\n                                this.loginTips = res.message || \"用户名或密码不正确\";\r\n                                setTimeout(() => {\r\n                                    this.loginTips = \"\";\r\n                                }, 3000);\r\n                            }\r\n                        })\r\n                        .finally(() => {\r\n                            this.flag = true;\r\n                        });\r\n                } else {\r\n                    return false;\r\n                }\r\n            });\r\n        },\r\n    },\r\n    beforeDestroy () {\r\n        window.removeEventListener(\"keydown\", this.keyDown, true);\r\n    },\r\n};\r\n", "<template>\r\n  <div class=\"login\">\r\n    <top-bar :isLogin=\"false\" />\r\n    <div class=\"content flex\">\r\n      <div class=\"pic\">\r\n        <img src=\"~@/assets/images/index/poster.png\"\r\n             alt=\"\"\r\n             ondragstart=\"return false;\" />\r\n      </div>\r\n      <div class=\"login-form flex\">\r\n        <h4>登录您的账号</h4>\r\n        <p class=\"login-tips flex\"\r\n           :style=\"{ opacity: loginTips ? '1' : '0' }\">\r\n          <img src=\"~@/assets/images/index/wrong-icon.png\"\r\n               alt=\"\" />\r\n          <span>{{ loginTips }}</span>\r\n        </p>\r\n        <el-form ref=\"form\"\r\n                 :model=\"form\"\r\n                 :rules=\"rules\">\r\n          <el-form-item label=\"\"\r\n                        prop=\"username\">\r\n            <div class=\"form-item\">\r\n              <!-- <p>用户名称</p> -->\r\n              <el-input ref=\"username\"\r\n                        v-model=\"form.username\"\r\n                        clearable\r\n                        placeholder=\"手机号码/用户名\"\r\n                        @focus=\"nameFocus = true\"\r\n                        @blur=\"nameFocus = false\" />\r\n            </div>\r\n          </el-form-item>\r\n          <el-form-item label=\"\"\r\n                        prop=\"password\">\r\n            <div class=\"form-item form-password\">\r\n              <!-- <p>登录密码</p> -->\r\n              <el-input v-model=\"form.password\"\r\n                        clearable\r\n                        :show-password=\"true\"\r\n                        placeholder=\"密码\"\r\n                        @focus=\"passwordFocus = true\"\r\n                        @blur=\"passwordFocus = false\"></el-input>\r\n            </div>\r\n          </el-form-item>\r\n        </el-form>\r\n        <!-- @click=\"handleSubmit\" -->\r\n        <div class=\"login-button\"\r\n             v-throttle=\"500\"\r\n             @click=\"handleLogin\">\r\n          <span v-if=\"flag\">登录</span>\r\n          <span v-else>登录中...</span>\r\n        </div>\r\n        <!-- <div class=\"login-action flex\">\r\n          <span @click=\"handleRegister\">立即注册</span>\r\n          <span @click=\"handleReset\">忘记密码</span>\r\n        </div> -->\r\n      </div>\r\n    </div>\r\n    <copyright class=\"copyright\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport conf from './conf'\r\nexport default conf\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.login {\r\n  min-height: 100vh;\r\n  padding-top: 52px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n  .content {\r\n    width: 1020px;\r\n    height: 420px;\r\n    margin: 148px auto 0;\r\n    border-radius: 4px;\r\n    .pic {\r\n      width: 540px;\r\n      height: 420px;\r\n      flex-shrink: 0;\r\n      img {\r\n        width: 100%;\r\n      }\r\n    }\r\n    .login-form {\r\n      width: calc(100% - 540px);\r\n      padding: 72px 90px 0;\r\n      flex-direction: column;\r\n      background: #ffffff;\r\n      border-top-right-radius: 4px;\r\n      border-bottom-right-radius: 4px;\r\n      position: relative;\r\n      h4 {\r\n        text-align: center;\r\n        color: #333333;\r\n        font-weight: 500;\r\n        font-size: 28px;\r\n        line-height: 16px;\r\n        padding-bottom: 58px;\r\n      }\r\n      .login-tips {\r\n        width: 300px;\r\n        height: 36px;\r\n        align-items: center;\r\n        background: rgba(255, 237, 237, 0.8);\r\n        border: 1px solid rgba(255, 77, 79, 0.24);\r\n        box-sizing: border-box;\r\n        backdrop-filter: blur(8px);\r\n        color: #f53e3e;\r\n        font-size: 14px;\r\n        line-height: 16px;\r\n        padding: 0px 20px;\r\n        position: absolute;\r\n        left: 90px;\r\n        top: 104px;\r\n        transition: all 0.3s;\r\n        img {\r\n          width: 14px;\r\n          height: 14px;\r\n          flex-shrink: 0;\r\n        }\r\n        span {\r\n          padding-left: 10px;\r\n          overflow: hidden;\r\n          text-overflow: ellipsis;\r\n          white-space: nowrap;\r\n        }\r\n      }\r\n      .form-item {\r\n        position: relative;\r\n\r\n        p {\r\n          color: #333333;\r\n          font-size: 14px;\r\n          line-height: 16px;\r\n          padding-bottom: 8px;\r\n          font-family: H_Medium;\r\n        }\r\n\r\n        /deep/ .el-input {\r\n          input {\r\n            color: #333333;\r\n            outline: none;\r\n            border: 1px solid #e4e7ec;\r\n          }\r\n          input:hover {\r\n            border: 1px solid #018aff;\r\n          }\r\n          input:focus {\r\n            border: 1px solid #018aff;\r\n            box-shadow: 0px 0px 0px 2px #f2f9ff;\r\n          }\r\n          input::placeholder {\r\n            color: #bfbfbf;\r\n          }\r\n          .el-input__clear {\r\n            font-family: 'tenant' !important;\r\n            font-size: 18px;\r\n            color: #888888;\r\n          }\r\n          .el-icon-circle-close::before {\r\n            content: '\\e644' !important;\r\n          }\r\n          .el-icon-view::before {\r\n            content: '\\e642' !important;\r\n          }\r\n          .el-input__icon:after {\r\n            // content: \"\\e643\";\r\n          }\r\n        }\r\n      }\r\n      /deep/ .el-form {\r\n        .el-form-item {\r\n          margin-bottom: 34px;\r\n        }\r\n        .el-input__inner {\r\n          border-radius: 0;\r\n          height: 42px !important;\r\n          font-family: 'Courier New', Courier, monospace;\r\n        }\r\n        .el-form-item__error {\r\n          color: #f53e3e;\r\n          font-size: 14px;\r\n        }\r\n      }\r\n      .login-button {\r\n        height: 42px;\r\n        line-height: 42px;\r\n        text-align: center;\r\n        cursor: pointer;\r\n        background: linear-gradient(180deg, #0088fe 0%, #006eff 100%);\r\n        span {\r\n          color: #ffffff;\r\n          font-size: 16px;\r\n        }\r\n      }\r\n      .login-action {\r\n        justify-content: center;\r\n        align-items: center;\r\n        font-size: 14px;\r\n        line-height: 16px;\r\n        padding-top: 42px;\r\n        span {\r\n          color: #0088fe;\r\n          padding: 0 18px;\r\n          position: relative;\r\n          cursor: pointer;\r\n        }\r\n        span::before {\r\n          content: '';\r\n          position: absolute;\r\n          top: 50%;\r\n          transform: translateY(-50%);\r\n          right: 0;\r\n          width: 1px;\r\n          height: 14px;\r\n          background: #ededed;\r\n        }\r\n        span:last-child::before {\r\n          display: none;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  // .copyright {\r\n  //   position: absolute;\r\n  //   bottom: 32px;\r\n  //   left: 50%;\r\n  //   transform: translateX(-50%);\r\n  //   padding: 0;\r\n  //   span {\r\n  //     text-align: center;\r\n  //     font-style: normal;\r\n  //     font-weight: normal;\r\n  //     font-size: 12px;\r\n  //     line-height: 14px;\r\n  //     color: #b2b5bc;\r\n  //   }\r\n  // }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=f20a1212&scoped=true&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=f20a1212&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"f20a1212\",\n  null\n  \n)\n\nexport default component.exports", "module.exports = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAOCAYAAAAbvf3sAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAADFSURBVHgBpZFhDYMwEIXbUgGTgAQkMAMsOGAKGBLmgClgUwDBwCYFCRig3bukJV27BhLer+N636P3ypmjvu/TJEk6lJlpDcuyNGVZznZGesNvlKnjUaFH32fbEGshxM0btsphlgcAdGIRmb8EwBQDsMcUAEqpVmv9Dxqw9CcAKAlAtJw9nGFwh/uVHRG3BZKgCC9wzZzzmXNOb/AqiuK5AuM40mNVG+YtoIYb547tUyOklDXbr1p4d95SSsAjkv+PaIZi/gKYT1H8MvGR6QAAAABJRU5ErkJggg==\"", "import request from \"./index\";\r\nimport { BASE_SERVER } from \"../conf/env\";\r\nconst baseServer = BASE_SERVER;\r\nconst auth = baseServer;\r\nconst user = baseServer;\r\nconst system = baseServer;\r\n\r\n/**\r\n * @desc 登录\r\n * @params params\r\n * @returns\r\n */\r\n\r\nexport const getLogin = (data) => {\r\n    return request({\r\n        url: `${auth}/user/login`,\r\n        method: \"post\",\r\n        data,\r\n    });\r\n};\r\n\r\n/**\r\n * @desc 登录\r\n * @params params\r\n * @returns\r\n */\r\n\r\nexport const getLoginOut = (params) => {\r\n    return request({\r\n        url: `${auth}/user/logout`,\r\n        method: \"get\",\r\n        params,\r\n    });\r\n};\r\n\r\n/**\r\n * @desc 账号信息\r\n * @params params\r\n * @returns\r\n */\r\n\r\nexport const getUserInfo = (params) => {\r\n    return request({\r\n        url: `${user}/user/userInfo`,\r\n        method: \"get\",\r\n        params,\r\n    });\r\n};\r\n\r\n/**\r\n * @desc\r\n *\r\n * @params params\r\n * @returns\r\n */\r\n\r\nexport const getRegister = (data) => {\r\n    return request({\r\n        url: `${user}/user/account/register`,\r\n        method: \"post\",\r\n        data,\r\n    });\r\n};\r\n\r\n/**\r\n * @desc 发送短信验证码\r\n * @params params\r\n * @returns\r\n */\r\n\r\nexport const sendMessage = (params) => {\r\n    return request({\r\n        url: `${user}/user/account/captcha`,\r\n        method: \"post\",\r\n        params,\r\n    });\r\n};\r\n\r\n/**\r\n * @desc 注册 字段验证\r\n * @params params\r\n * @returns\r\n */\r\n\r\nexport const checkFieldPhone = (params) => {\r\n    return request({\r\n        url: `${user}/user/account/isPhoneExist`,\r\n        method: \"post\",\r\n        params,\r\n    });\r\n};\r\n\r\n/**\r\n * @desc 找回密码 字段验证\r\n * @params params\r\n * @returns\r\n */\r\n\r\nexport const checkPhoneMustExists = (params) => {\r\n    return request({\r\n        url: `${user}/user/account/phoneMustExists`,\r\n        method: \"post\",\r\n        params,\r\n    });\r\n};\r\n\r\n/**\r\n * @desc 注册 字段验证\r\n * @params params\r\n * @returns\r\n */\r\n\r\nexport const checkPhoneMustNotExists = (params) => {\r\n    return request({\r\n        url: `${user}/user/account/phoneMustNotExists`,\r\n        method: \"post\",\r\n        params,\r\n    });\r\n};\r\n\r\n/**\r\n * @desc 注册 字段验证\r\n * @params params\r\n * @returns\r\n */\r\n\r\nexport const checkFieldName = (params) => {\r\n    return request({\r\n        url: `${user}/user/account/isAccountExist`,\r\n        method: \"post\",\r\n        params,\r\n    });\r\n};\r\n\r\n/**\r\n * @desc 注册 验证短信\r\n * @params params\r\n * @returns\r\n */\r\n\r\nexport const checkCaptcha = (params) => {\r\n    return request({\r\n        url: `${user}/user/account/checkCaptcha`,\r\n        method: \"post\",\r\n        params,\r\n    });\r\n};\r\n\r\n/**\r\n * @desc 重置密码\r\n * @params params\r\n * @returns\r\n */\r\n\r\nexport const updatePassword = (data) => {\r\n    return request({\r\n        url: `${user}/user/account/password/update`,\r\n        method: \"post\",\r\n        data,\r\n    });\r\n};\r\n\r\nexport const childList = (data) => {\r\n    return request({\r\n        url: `${system}/dict-biz/child-list`,\r\n        method: \"get\",\r\n        data,\r\n    });\r\n};\r\n\r\n/**\r\n * @desc 修改密码\r\n * @params newPassword、newPassword1、oldPassword\r\n * @returns\r\n */\r\nexport const modifyPassword = (data) => {\r\n    return request({\r\n        url: `${user}/user/resetPassword`,\r\n        method: \"post\",\r\n        data,\r\n    });\r\n};\r\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"top-bar flex\"},[_vm._m(0),(_vm.isLogin)?_c('div',{staticClass:\"user flex\"},[_c('el-tooltip',{staticClass:\"item\",attrs:{\"effect\":\"light\",\"placement\":\"bottom-end\",\"popper-class\":\"top-bar-tooltip\"}},[_c('div',{staticClass:\"content\",attrs:{\"slot\":\"content\"},slot:\"content\"},[_c('p',{staticClass:\"phone\"},[_vm._v(_vm._s(_vm.userInfo.username))]),_c('div',{staticClass:\"user-info\"},[_c('div',{staticClass:\"item flex\",on:{\"click\":_vm.handleToAccountInfo}},[_c('img',{attrs:{\"src\":require(\"@/assets/images/index/user-mini-icon.png\"),\"alt\":\"\"}}),_c('span',[_vm._v(\"账号信息\")])]),_c('div',{staticClass:\"item flex\",on:{\"click\":_vm.handleUpdatePwd}},[_c('img',{attrs:{\"src\":require(\"@/assets/images/index/edit-mini-icon.png\"),\"alt\":\"\"}}),_c('span',[_vm._v(\"修改密码\")])])]),_c('p',{staticClass:\"out\",on:{\"click\":_vm.loginOut}},[_vm._v(\"退出登录\")])]),_c('img',{staticClass:\"user-pic\",attrs:{\"src\":require(\"@/assets/images/index/user-icon.png\"),\"alt\":\"\"}})])],1):_vm._e()])}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('p',{staticClass:\"flex\"},[_c('img',{attrs:{\"src\":require(\"@/assets/images/index/logo.png\"),\"alt\":\"\",\"ondragstart\":\"return false;\"}}),_c('span',[_vm._v(\"集成网关系统\")])])}]\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"top-bar flex\">\r\n    <p class=\"flex\">\r\n      <img src=\"~@/assets/images/index/logo.png\"\r\n           alt=\"\"\r\n           ondragstart=\"return false;\" />\r\n      <span>集成网关系统</span>\r\n    </p>\r\n    <div class=\"user flex\"\r\n         v-if=\"isLogin\">\r\n      <!-- <div class=\"user-message flex\">\r\n        <img src=\"~@/assets/images/index/message-icon.png\" alt=\"\" />\r\n      </div> -->\r\n      <el-tooltip class=\"item\"\r\n                  effect=\"light\"\r\n                  placement=\"bottom-end\"\r\n                  popper-class=\"top-bar-tooltip\">\r\n        <div slot=\"content\"\r\n             class=\"content\">\r\n          <p class=\"phone\">{{ userInfo.username }}</p>\r\n          <div class=\"user-info\">\r\n            <div class=\"item flex\"\r\n                 @click=\"handleToAccountInfo\">\r\n              <img src=\"~@/assets/images/index/user-mini-icon.png\"\r\n                   alt=\"\" />\r\n              <span>账号信息</span>\r\n            </div>\r\n            <div class=\"item flex\"\r\n                 @click=\"handleUpdatePwd\">\r\n              <img src=\"~@/assets/images/index/edit-mini-icon.png\"\r\n                   alt=\"\" />\r\n              <span>修改密码</span>\r\n            </div>\r\n          </div>\r\n          <p class=\"out\"\r\n             @click=\"loginOut\">退出登录</p>\r\n        </div>\r\n        <img class=\"user-pic\"\r\n             src=\"~@/assets/images/index/user-icon.png\"\r\n             alt=\"\" />\r\n      </el-tooltip>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from 'vuex'\r\nimport { getLoginOut } from '@/api/user'\r\nimport { userInfo } from 'os'\r\nexport default {\r\n  props: {\r\n    isLogin: {\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n  },\r\n  data() {\r\n    return {}\r\n  },\r\n  computed: {\r\n    ...mapGetters(['userInfo']),\r\n    // phone() {\r\n    //   let reg = /(\\d{3})\\d*(\\d{4})/\r\n    //   if (this.userInfo.phone) {\r\n    //     return this.userInfo.phone.replace(reg, '$1****$2') || '----'\r\n    //   } else {\r\n    //     return (\r\n    //       JSON.parse(localStorage.getItem('userInfo')).phone.replace(\r\n    //         reg,\r\n    //         '$1****$2'\r\n    //       ) || '----'\r\n    //     )\r\n    //   }\r\n    // },\r\n  },\r\n  mounted() {\r\n    // console.log(this.userInfo);\r\n  },\r\n  methods: {\r\n    loginOut() {\r\n      getLoginOut().then((res) => {\r\n        if (res.code == 200) {\r\n          this.$store.dispatch('loginOut')\r\n          this.$router.replace({ path: '/login' })\r\n        } else {\r\n          this.$message.warning('服务器异常，请联系管理员')\r\n        }\r\n      })\r\n    },\r\n    handleToAccountInfo() {\r\n      this.$router.push({\r\n        path: '/accountInfo',\r\n        query: {\r\n          type: '0',\r\n        },\r\n      })\r\n    },\r\n    handleUpdatePwd() {\r\n      this.$router.push({\r\n        path: '/accountInfo',\r\n        query: {\r\n          type: '1',\r\n        },\r\n      })\r\n    },\r\n    // 弹窗确定按钮\r\n    fn_sure() {},\r\n  },\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.top-bar {\r\n  width: 100%;\r\n  height: 52px;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 0 32px 0 20px;\r\n  background: #ffffff;\r\n  box-shadow: 0px 3px 8px #e6e6e6, inset 0px -1px 0px #eeeff1;\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  z-index: 99;\r\n  p {\r\n    align-items: center;\r\n    img {\r\n      width: 30px;\r\n    }\r\n    span {\r\n      padding-left: 10px;\r\n      font-family: YSBT;\r\n      font-size: 26px;\r\n      color: #333333;\r\n      font-weight: normal;\r\n    }\r\n  }\r\n  .user {\r\n    .user-message {\r\n      width: 30px;\r\n      height: 30px;\r\n      background: #ffffff;\r\n      // border-radius: 2px;\r\n      position: relative;\r\n      align-items: center;\r\n      justify-content: center;\r\n      img {\r\n        width: 12px;\r\n      }\r\n    }\r\n    .user-message::before {\r\n      content: '';\r\n      width: 5px;\r\n      height: 5px;\r\n      border-radius: 50%;\r\n      background: #f83e37;\r\n      position: absolute;\r\n      top: 0px;\r\n      right: 3px;\r\n    }\r\n    .user-pic {\r\n      width: 30px;\r\n      margin-left: 20px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n<style lang=\"scss\">\r\n.top-bar-tooltip {\r\n  border-radius: 3px !important;\r\n  // border: 1px solid #e4e7ec !important;\r\n  border: none !important;\r\n  background: #ffffff !important;\r\n  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.15) !important;\r\n  backdrop-filter: blur(4px);\r\n  padding: 0;\r\n  font-family: H_Medium;\r\n  top: 35px !important;\r\n  .popper__arrow {\r\n    border-width: 10px;\r\n    border-bottom-color: #e4e7ec !important;\r\n    top: -10px !important;\r\n  }\r\n  .popper__arrow::after {\r\n    border-width: 10px;\r\n    left: -5px;\r\n  }\r\n\r\n  .content {\r\n    width: 194px;\r\n    // height: 200px;\r\n    p {\r\n      display: flex;\r\n      align-items: center;\r\n      height: 53px;\r\n      padding: 0 30px;\r\n      border-bottom: 1px solid #eeeeee;\r\n    }\r\n    p:last-child {\r\n      border-bottom: none;\r\n    }\r\n    .phone {\r\n      color: #262626;\r\n      font-size: 16px;\r\n    }\r\n    .user-info {\r\n      border-bottom: 1px solid #eeeeee;\r\n      font-size: 14px;\r\n      color: #595959;\r\n      .item {\r\n        height: 38px;\r\n        line-height: 38px;\r\n        padding: 0 30px;\r\n        margin-top: 7px;\r\n        cursor: pointer;\r\n        &:nth-child(2) {\r\n          margin-bottom: 7px;\r\n          margin-top: 0;\r\n        }\r\n        &:hover {\r\n          background-color: #f2f9ff;\r\n        }\r\n        img {\r\n          width: 12px;\r\n          height: 13px;\r\n          margin-right: 10px;\r\n          margin-top: 11px;\r\n        }\r\n      }\r\n    }\r\n    .out {\r\n      color: rgba(0, 136, 254, 1);\r\n      font-size: 15px;\r\n      letter-spacing: 1px;\r\n      cursor: pointer;\r\n      font-weight: normal;\r\n      line-height: 21px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=2233f412&scoped=true&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=2233f412&lang=scss&scoped=true&\"\nimport style1 from \"./index.vue?vue&type=style&index=1&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2233f412\",\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}