(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7b090a63"],{"10a9":function(e,t,n){"use strict";n.r(t);var o=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"device"},[n("div",{staticClass:"device-top"},[n("div",{staticClass:"device-top-search"},[n("div",{staticClass:"top-left"},[n("iot-button",{attrs:{text:"添加连接器"},on:{search:e.fn_open}})],1),n("div",{staticClass:"top-right"},[n("el-input",{attrs:{clearable:"",placeholder:"输入连接器名称"},on:{clear:e.handleClear},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.fn_handle__query.apply(null,arguments)}},model:{value:e.connectorName,callback:function(t){e.connectorName=t},expression:"connectorName"}},[n("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"suffix"},on:{click:e.fn_handle__query},slot:"suffix"})])],1)])]),n("div",{staticClass:"device-table"},[n("iot-table",{attrs:{columns:e.columns,data:e.tableData,loading:e.loading},on:{"selection-change":e.fn_select_more_data,"selection-del":e.fn_del_more_data,"del-callbackSure":e.fn_del_sure},scopedSlots:e._u([{key:"commonConfig",fn:function(t){return[n("el-tooltip",{attrs:{placement:"top-start",effect:"light","popper-class":"event-tooltip"},scopedSlots:e._u([{key:"content",fn:function(){return[n("span",[e._v(" "+e._s(JSON.stringify(JSON.parse(t.row.commonConfig)))+" ")])]},proxy:!0}],null,!0)},[n("div",{staticClass:"alarmContent-tooltip"},[n("p",[e._v(" "+e._s(e.fn_sub10(t.row.commonConfig))+" ")])])])]}},{key:"customConfig",fn:function(t){return[n("el-tooltip",{attrs:{placement:"top-start",effect:"light","popper-class":"event-tooltip"},scopedSlots:e._u([{key:"content",fn:function(){return[n("span",[e._v(" "+e._s(t.row.customConfig)+" ")])]},proxy:!0}],null,!0)},[n("div",{staticClass:"alarmContent-tooltip"},[n("p",[e._v(" "+e._s(e.fn_sub10(t.row.customConfig))+" ")])])])]}},{key:"upLinkName",fn:function(t){return[n("el-tooltip",{attrs:{placement:"top-start",effect:"light","popper-class":"event-tooltip"},scopedSlots:e._u([{key:"content",fn:function(){return[n("span",[e._v(" "+e._s(t.row.upLinkName)+" ")])]},proxy:!0}],null,!0)},[n("div",{staticClass:"alarmContent-tooltip"},[n("p",[e._v(" "+e._s(e.fn_sub10(t.row.upLinkName))+" ")])])])]}},{key:"downLinkName",fn:function(t){return[n("el-tooltip",{attrs:{placement:"top-start",effect:"light","popper-class":"event-tooltip"},scopedSlots:e._u([{key:"content",fn:function(){return[n("span",[e._v(" "+e._s(t.row.downLinkName)+" ")])]},proxy:!0}],null,!0)},[n("div",{staticClass:"alarmContent-tooltip"},[n("p",[e._v(" "+e._s(e.fn_sub10(t.row.downLinkName))+" ")])])])]}},{key:"operation",fn:function(t){return[n("div",{staticClass:"flex table-edit"},[n("p",{staticClass:"color2",attrs:{slot:"operation"},on:{click:function(n){return e.relationDevice(t.row.id)}},slot:"operation"},[e._v(" 关联设备 ")]),n("p"),n("p",{staticClass:"color2",on:{click:function(n){return e.fn_check(t.row.id)}}},[e._v("详情")]),n("p"),n("p",{staticClass:"color2",on:{click:function(n){return e.fn_edit(t.row)}}},[e._v("修改")]),n("p"),n("p",{staticClass:"color2",on:{click:function(n){return e.fn_del(t.row.id)}}},[e._v("删除")]),n("p")])]}}])})],1),e.tableData.length?n("div",{staticClass:"device-bottom"},[n("iot-pagination",{attrs:{pagination:e.pagination},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1):e._e(),n("iot-dialog",{attrs:{visible:e.visible,title:e.title,width:e.dialogWidth},on:{"update:visible":function(t){e.visible=t},callbackSure:e.fn_sure,close:e.fn_close},scopedSlots:e._u([{key:"body",fn:function(){return[1==e.type?n("iot-form",[n("el-form",{ref:"connectorForm",staticClass:"connectorForm",attrs:{"label-position":"top",model:e.connectorForm,rules:e.rules,"label-width":"80px"},on:{validate:e.fn_validate}},[n("el-form-item",{attrs:{label:"连接器名称",prop:"connectorName"}},[n("el-input",{model:{value:e.connectorForm.connectorName,callback:function(t){e.$set(e.connectorForm,"connectorName",t)},expression:"connectorForm.connectorName"}})],1),e.connectorNameTrue?n("div",{staticClass:"el-form-tips"},[e._v(" 支持中文、英文字母、数字、和特殊字符“_-@()”，长度限制 4~30个字符，中文算 2 个字符 ")]):e._e(),n("el-form-item",{attrs:{label:"设备厂商",prop:"vendorName"}},[n("el-input",{model:{value:e.connectorForm.vendorName,callback:function(t){e.$set(e.connectorForm,"vendorName",t)},expression:"connectorForm.vendorName"}})],1),e.vendorNameTrue?n("div",{staticClass:"el-form-tips"},[e._v(" 支持中文、英文字母、数字、和特殊字符“_-@()”，长度限制 4~64个字符，中文算 2 个字符 ")]):e._e(),n("el-form-item",{attrs:{label:"协议方式",prop:"protocolType"}},[n("el-select",{attrs:{filterable:"",placeholder:"请选择协议方式"},model:{value:e.connectorForm.protocolType,callback:function(t){e.$set(e.connectorForm,"protocolType",t)},expression:"connectorForm.protocolType"}},[n("template",{slot:"empty"},[n("div",{staticClass:"empty-project"},[n("span",[e._v("协议方式列表里没有匹配的数据")])])]),e._l(e.protocolList,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}))],2)],1),n("el-form-item",{attrs:{label:"应用类型",prop:"applicationType"}},[n("el-input",{model:{value:e.connectorForm.applicationType,callback:function(t){e.$set(e.connectorForm,"applicationType",t)},expression:"connectorForm.applicationType"}})],1),e.applicationTypeTrue?n("div",{staticClass:"el-form-tips"},[e._v(" 最多不超过2000个字符 ")]):e._e(),n("el-form-item",{attrs:{label:"通用配置",prop:"commonConfig"}},[n("el-input",{attrs:{maxlength:2e3,type:"textarea"},model:{value:e.connectorForm.commonConfig,callback:function(t){e.$set(e.connectorForm,"commonConfig",t)},expression:"connectorForm.commonConfig"}})],1),e.commonConfigTrue?n("div",{staticClass:"el-form-tips"},[e._v(" 最多不超过2000个字符 ")]):e._e(),n("el-form-item",{attrs:{label:"自定义配置",prop:"customConfig"}},[n("el-input",{attrs:{maxlength:2e3,type:"textarea"},model:{value:e.connectorForm.customConfig,callback:function(t){e.$set(e.connectorForm,"customConfig",t)},expression:"connectorForm.customConfig"}})],1),e.customConfigTrue?n("div",{staticClass:"el-form-tips"},[e._v(" 最多不超过2000个字符 ")]):e._e(),n("el-form-item",{attrs:{label:"是否开启",prop:"enableStatus"}},[n("el-select",{attrs:{filterable:"",placeholder:"请选择是否开启"},model:{value:e.connectorForm.enableStatus,callback:function(t){e.$set(e.connectorForm,"enableStatus",t)},expression:"connectorForm.enableStatus"}},[n("template",{slot:"empty"},[n("div",{staticClass:"empty-project"},[n("span",[e._v("是否开启列表里没有匹配的数据")])])]),e._l(e.enableList,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}))],2)],1),n("el-form-item",{attrs:{label:"上行转换器",prop:"upLinkId"}},[n("el-select",{attrs:{filterable:"",placeholder:"请选择上行转换器"},model:{value:e.connectorForm.upLinkId,callback:function(t){e.$set(e.connectorForm,"upLinkId",t)},expression:"connectorForm.upLinkId"}},e._l(e.tempUpLinkList,(function(t){return n("el-option",{key:t.id,attrs:{label:t.converterName,disabled:t.disabled,value:t.id}},[n("span",{staticStyle:{float:"left"}},[e._v(e._s(t.converterName))]),n("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(e._s(t.bindStatusName))])])})),1)],1),n("el-form-item",{attrs:{label:"下行转换器",prop:"downLinkId"}},[n("el-select",{attrs:{filterable:"",placeholder:"请选择下行转换器"},model:{value:e.connectorForm.downLinkId,callback:function(t){e.$set(e.connectorForm,"downLinkId",t)},expression:"connectorForm.downLinkId"}},e._l(e.tempDownLinkList,(function(t){return n("el-option",{key:t.id,attrs:{label:t.converterName,disabled:t.disabled,value:t.id}},[n("span",{staticStyle:{float:"left"}},[e._v(e._s(t.converterName))]),n("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(e._s(t.bindStatusName))])])})),1)],1),n("el-form-item",{attrs:{label:"描述",prop:"connectorDesc"}},[n("el-input",{attrs:{maxlength:200,type:"textarea"},model:{value:e.connectorForm.connectorDesc,callback:function(t){e.$set(e.connectorForm,"connectorDesc",t)},expression:"connectorForm.connectorDesc"}})],1),e.connectorDescTrue?n("div",{staticClass:"el-form-tips"},[e._v(" 最多不超过200个字符 ")]):e._e()],1)],1):e._e(),2==e.type?n("div",[n("iot-form",{scopedSlots:e._u([{key:"default",fn:function(){return[n("el-form",[n("el-form-item",[n("div",{staticClass:"del-tips"},[e._v(" 删除改连接器后，该连接器的下的设备、转换器将跟连接器解绑，确认删除连接器? ")])])],1)]},proxy:!0}],null,!1,2616756830)})],1):e._e()]},proxy:!0}])}),n("relation",{ref:"relation",on:{close:e.handleReset}})],1)},i=[],a=n("5530"),r=(n("ac1f"),n("5319"),n("d3b7"),n("d81d"),n("a15b"),n("b329"),n("7413")),c=n("c2a2"),s=n("6e22"),l=n("511c"),u=n("673a"),d=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",[o("iot-dialog",{attrs:{title:"连接器关联设备",top:"10vh",maxHeight:"auto",visible:e.visible,width:e.width,appendBody:!0,footer:!1},on:{"update:visible":function(t){e.visible=t},close:e.handleClose},scopedSlots:e._u([{key:"body",fn:function(){return[o("div",{staticClass:"content"},[o("div",{staticClass:"device flex"},[o("div",{staticClass:"device-data not"},[o("h4",[e._v("待关联设备："+e._s(e.waitCount))]),o("div",{staticClass:"table"},[o("div",{staticClass:"form-item"},[o("el-input",{attrs:{placeholder:"请输入请输入设备名称",clearable:""},on:{clear:function(t){return e.fn_handle__query(!0)}},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.fn_handle__query(!0)}},model:{value:e.notSearchVal,callback:function(t){e.notSearchVal=t},expression:"notSearchVal"}},[o("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"suffix"},on:{click:function(t){return e.fn_handle__query(!0)}},slot:"suffix"})])],1),o("div",{staticClass:"device-table-content"},[o("iot-table",{attrs:{columns:e.notColumns,data:e.notSource,loading:e.notLoading},on:{"selection-change":function(t){return e.selectionChange(t,!0)}},scopedSlots:e._u([{key:"empty",fn:function(){return[e.isEmpty?o("div",{staticClass:"empty"},[e._v(" 该产品暂无设备，请先去"),o("span",{on:{click:e.routeDevice}},[e._v("添加设备")])]):e._e()]},proxy:!0},{key:"operation",fn:function(t){return[o("div",{staticClass:"flex table-edit"},[o("p",{staticClass:"color2",on:{click:function(n){return e.fn_edit(t.row)}}},[e._v("修改")])])]}}])}),o("div",{staticClass:"pagination flex"},[o("iot-pagination",{attrs:{pagination:e.notPagination,layout:"total, prev, pager, next,  jumper"},on:{"current-change":function(t){return e.handleCurrentChange(t,!0)}}})],1)],1)])]),o("div",{staticClass:"action flex"},[o("p",{staticClass:"bind",on:{click:function(t){return e.submitBind(!0)}}},[o("span",[e._v("绑定")]),o("img",{attrs:{src:n("93ed"),alt:""}})]),o("p",{staticClass:"unbound",on:{click:function(t){return e.submitBind(!1)}}},[o("img",{attrs:{src:n("93ed"),alt:""}}),o("span",[e._v("解绑")])])]),o("div",{staticClass:"device-data already"},[o("h4",[e._v("已关联设备："+e._s(e.doneCount))]),o("div",{staticClass:"table"},[o("div",{staticClass:"form-item"},[o("el-input",{attrs:{placeholder:"请输入设备名称",clearable:""},on:{clear:function(t){return e.fn_handle__query(!1)}},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.fn_handle__query(!1)}},model:{value:e.alreadySearchVal,callback:function(t){e.alreadySearchVal=t},expression:"alreadySearchVal"}},[o("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"suffix"},on:{click:function(t){return e.fn_handle__query(!1)}},slot:"suffix"})])],1),o("div",{staticClass:"device-table-content"},[o("iot-table",{attrs:{columns:e.notColumns,data:e.alreadySource,loading:e.alreadyLoading},on:{"selection-change":function(t){return e.selectionChange(t,!1)}},scopedSlots:e._u([{key:"operation",fn:function(t){return[o("div",{staticClass:"flex table-edit"},[o("p",{staticClass:"color2",on:{click:function(n){return e.fn_edit(t.row)}}},[e._v("修改")])])]}}])}),o("div",{staticClass:"pagination flex"},[o("iot-pagination",{attrs:{pagination:e.alreadyPagination,layout:"total, prev, pager, next,  jumper"},on:{"current-change":function(t){return e.handleCurrentChange(t,!1)}}})],1)],1)])])])])]},proxy:!0}])}),o("iot-dialog",{attrs:{visible:e.deviceVisible,title:"修改设备",width:"729px"},on:{"update:visible":function(t){e.deviceVisible=t},callbackSure:e.fn_sure,close:e.fn_close},scopedSlots:e._u([{key:"body",fn:function(){return[1==e.type?o("iot-form",[o("el-form",{ref:"deviceForm",staticClass:"deviceForm",attrs:{"label-position":"top",model:e.deviceForm,rules:e.rules,"label-width":"80px"},on:{validate:e.fn_validate}},[o("el-form-item",{attrs:{label:"自定义配置信息",prop:"configInfo"}},[o("el-input",{attrs:{maxlength:2e3,type:"textarea"},model:{value:e.deviceForm.configInfo,callback:function(t){e.$set(e.deviceForm,"configInfo",t)},expression:"deviceForm.configInfo"}})],1),e.configInfoTrue?o("div",{staticClass:"el-form-tips"},[e._v(" 最多不超过2000个字符 ")]):e._e(),o("el-form-item",{attrs:{label:"设备SN",prop:"deviceSn"}},[o("el-input",{model:{value:e.deviceForm.deviceSn,callback:function(t){e.$set(e.deviceForm,"deviceSn",t)},expression:"deviceForm.deviceSn"}})],1),e.deviceSnTrue?o("div",{staticClass:"el-form-tips"},[e._v(" 支持中文、英文字母、数字、和特殊字符“_-@()”，长度限制 4~64个字符，中文算 2 个字符 ")]):e._e(),o("el-form-item",{attrs:{label:"设备厂商",prop:"vendorName"}},[o("el-input",{model:{value:e.deviceForm.vendorName,callback:function(t){e.$set(e.deviceForm,"vendorName",t)},expression:"deviceForm.vendorName"}})],1),e.vendorNameTrue?o("div",{staticClass:"el-form-tips"},[e._v(" 支持中文、英文字母、数字、和特殊字符“_-@()”，长度限制 4~64个字符，中文算 2 个字符 ")]):e._e(),o("el-form-item",{attrs:{label:"设备描述",prop:"deviceDesc"}},[o("el-input",{attrs:{maxlength:200,type:"textarea"},model:{value:e.deviceForm.deviceDesc,callback:function(t){e.$set(e.deviceForm,"deviceDesc",t)},expression:"deviceForm.deviceDesc"}})],1),e.deviceDescTrue?o("div",{staticClass:"el-form-tips"},[e._v(" 最多不超过200个字符 ")]):e._e()],1)],1):e._e()]},proxy:!0}])})],1)},f=[],p=(n("4de4"),n("0e0b")),m=n("aa98"),h={data:function(){return{type:1,visible:!1,deviceVisible:!1,product:"",c:{},deviceForm:{configInfo:"",deviceSn:"",vendorName:"",deviceDesc:""},options:[{value:"1",label:"测试"}],notSearchVal:"",notColumns:[{type:"selection"},{prop:"productKey",label:"ProductKey",width:180},{prop:"deviceName",label:"设备名称"},{prop:"deviceSn",label:"设备SN"},{prop:"deviceStatusName",label:"在线状态"},{prop:"operation",label:"操作",slotName:"operation"}],rules:{configInfo:[{required:!0,trigger:"blur",validator:this.checkConfigInfoLength}],deviceSn:[{required:!0,trigger:"blur",validator:this.checkDeviceSn}],vendorName:[{required:!0,trigger:"blur",validator:this.checkVendorName}],deviceDesc:[{required:!1,trigger:"blur",validator:this.checkLength}]},notSource:[],notLoading:!1,notPagination:{current:1,size:7,total:0},notSelectList:[],alreadySearchVal:"",alreadySource:[],alreadyLoading:!1,alreadyPagination:{current:1,size:7,total:0},alreadySelectList:[],connectorId:"",isEmpty:!1,waitCount:0,doneCount:0,width:"".concat(1330/1920*100,"vw"),configInfoTrue:!0,deviceSnTrue:!0,vendorNameTrue:!0,deviceDescTrue:!0}},components:{iotDialog:l["a"],iotTable:u["a"],iotPagination:s["a"],IotForm:r["a"]},props:{hostProductKey:{type:String},hostDeviceName:{type:String}},methods:{fn_edit:function(e){console.log("row",e),this.deviceForm=JSON.parse(JSON.stringify(e)),this.deviceForm.configInfo=JSON.stringify(JSON.parse(this.deviceForm.configInfo),null,2),this.deviceVisible=!0},checkDeviceSn:function(e,t,n){return this.fn_notNull(t)?n(new Error("请输入设备SN")):Object(p["i"])(t)?void n():n(new Error("支持中文、英文字母、数字、和特殊字符_-@()，长度限制 4~64个字符，中文算 2 个字符"))},checkVendorName:function(e,t,n){if(!Object(p["i"])(t,201))return n(new Error("支持中文、英文字母、数字、和特殊字符_-@()，长度限制 4~64个字符，中文算 2 个字符"));n()},checkConfigInfoLength:function(e,t,n){return t=JSON.stringify(t),this.fn_notNull(t)?n(new Error("请输入自定义配置信息")):Object(p["c"])(t,2001)?Object(p["b"])(t)?void n():n(new Error("请输入正确的JSON格式")):n(new Error("最多不超过2000个字符"))},checkDeviceSnLength:function(e,t,n){return this.fn_notNull(t)?n(new Error("请输入设备SN")):Object(p["c"])(t,201)?void n():n(new Error("最多不超过200个字符"))},fn_notNull:function(e){return 0!==e&&!e},checkLength:function(e,t,n){if(!Object(p["c"])(t,201))return n(new Error("最多不超过200个字符"));n()},fn_sure:function(){var e=this;this.$refs["deviceForm"].validate((function(t){t&&Object(m["F"])(e.deviceForm).then((function(t){200==t.code?(e.$newNotify.success({message:t.message}),e.getProductKey(0,!0,!0),e.getProductKey(1,!0,!0),e.deviceVisible=!1):e.$newNotify.error({message:t.message})}))}))},fn_close:function(){this.configInfoTrue=!0,this.deviceSnTrue=!0,this.vendorNameTrue=!0,this.deviceDescTrue=!0,this.$refs.deviceForm.clearValidate()},fn_validate:function(e,t){"configInfo"===e&&(this.configInfoTrue=t),"deviceSn"===e&&(this.deviceSnTrue=t),"vendorName"===e&&(this.vendorNameTrue=t),"deviceDesc"===e&&(this.deviceDescTrue=t)},open:function(e){this.connectorId=e,this.visible=!0,this.getProductKey(0,!0,!0),this.getProductKey(1,!0,!0)},selectChange:function(e){this.notPagination.current=1,this.alreadyPagination.current=1;var t=this.options.filter((function(t){return t.id==e}))[0];this.productInfo=t,this.getProductKey(0,!0,!0),this.getProductKey(1,!0,!0)},fn_handle__query:function(e){e?(this.notPagination.current=1,this.getProductKey(0)):(this.alreadyPagination.current=1,this.getProductKey(1))},getProductKey:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i={};e?(this.alreadySearchVal=o?"":this.alreadySearchVal,i={connectorId:this.connectorId,current:this.alreadyPagination.current,size:this.alreadyPagination.size,deviceName:o?"":this.alreadySearchVal},Object(m["c"])(i).then((function(e){if(200==e.code){var i=e.data;t.alreadySource=i.records||[],t.doneCount=o?i.total:t.doneCount,t.alreadyPagination.total=i.total||0}else t.alreadySource=[],t.doneCount=0,t.alreadyPagination.total=0,n&&t.$newNotify.warning({message:e.message})}))):(this.notSearchVal=o?"":this.notSearchVal,i={current:this.notPagination.current,size:this.notPagination.size,deviceName:o?"":this.notSearchVal},Object(m["e"])(i).then((function(e){if(200==e.code){var i=e.data;4603==e.code?t.isEmpty=!0:t.isEmpty=!1,t.notSource=i.records||[],t.waitCount=o?i.total:t.waitCount,t.notPagination.total=i.total||0}else t.notSource=[],t.waitCount=0,t.notPagination.total=0,n&&t.$newNotify.warning({message:e.message})})))},handleClear:function(){},selectionChange:function(e,t){t?this.notSelectList=e.map((function(e){return e.id})):this.alreadySelectList=e.map((function(e){return e.id}))},handleCurrentChange:function(e,t){t?(this.notPagination.current=e,this.getProductKey(0)):(this.alreadyPagination.current=e,this.getProductKey(1))},submitBind:function(e){var t=this;if(e){if(0==this.notSelectList.length)return void this.$newNotify.warning({message:"请选择未关联设备"});Object(m["A"])({connectorId:this.connectorId,deviceIdList:this.notSelectList}).then((function(e){200==e.code?(t.$newNotify.success({message:e.message}),t.notPagination.current=1,t.alreadyPagination.current=1,t.getProductKey(0,!1,!0),t.getProductKey(1,!1,!0)):t.$newNotify.error({message:e.message})}))}else{if(0==this.alreadySelectList.length)return void this.$newNotify.warning({message:"请选择已关联设备"});Object(m["E"])({connectorId:this.connectorId,deviceIdList:this.alreadySelectList}).then((function(e){200==e.code?(t.$newNotify.success({message:e.message}),t.notPagination.current=1,t.alreadyPagination.current=1,t.getProductKey(0,!1,!0),t.getProductKey(1,!1,!0)):t.$newNotify.error({message:e.message})}))}},routeDevice:function(){this.$router.replace({path:"/device"})},handleClose:function(){this.product="",this.productInfo={},this.notSearchVal="",this.alreadySearchVal="",this.notSource=[],this.alreadySource=[],this.notPagination.current=1,this.alreadyPagination.current=1,this.notPagination.total=0,this.alreadyPagination.total=0,this.$emit("close")}}},v=h,g=(n("56ff"),n("2877")),_=Object(g["a"])(v,d,f,!1,null,"f65b9e4e",null),b=_.exports,y=(n("1503"),{name:"Device",components:{IotForm:r["a"],IotButton:c["a"],IotPagination:s["a"],IotDialog:l["a"],IotTable:u["a"],relation:b},data:function(){return{connectorName:"",columns:[{label:"连接器名称",prop:"connectorName"},{label:"设备厂商",prop:"vendorName"},{label:"协议方式",prop:"protocolType"},{label:"通用配置",prop:"commonConfig",slotName:"commonConfig"},{label:"自定义配置",prop:"customConfig",slotName:"customConfig"},{label:"是否启用",prop:"enableStatusName"},{label:"上行转换器",prop:"upLinkName",slotName:"upLinkName"},{label:"下行转换器",prop:"downLinkName",slotName:"downLinkName"},{label:"描述",prop:"connectorDesc"},{label:"创建时间",prop:"createTime"},{label:"修改时间",prop:"updateTime"},{label:"操作",prop:"operation",slotName:"operation",width:240}],enableList:[{value:0,label:"未启用"},{value:1,label:"已启用"}],upLinkList:[],downLinkList:[],tempUpLinkList:[],tempDownLinkList:[],protocolList:[{value:"HTTP",label:"HTTP"},{value:"ISAPI_PARKING",label:"ISAPI_PARKING"},{value:"ISAPI_THERMOMETRY",label:"ISAPI_THERMOMETRY"},{value:"ISAPI_OLD_PARKING",label:"ISAPI_OLD_PARKING"},{value:"ISAPI_ENTRANCEGUARD",label:"ISAPI_ENTRANCEGUARD"},{value:"ISAPI_TONGYE_PARKING",label:"ISAPI_TONGYE_PARKING"},{value:"JIESHUN_PARKING",label:"JIESHUN_PARKING"},{value:"MQTT_SERVER",label:"MQTT_SERVER"},{value:"UDP",label:"UDP"},{value:"TCP_SERVER",label:"TCP_SERVER"},{value:"TCP_HIK_LED",label:"TCP_HIK_LED"},{value:"HIK_CHARGING",label:"HIK_CHARGING"},{value:"COAP",label:"COAP"},{value:"HXZX_WEBSOCKET_PARKING",label:"HXZX_WEBSOCKET_PARKING"},{value:"HIK_SDK_PARKING",label:"HIK_SDK_PARKING"},{value:"HXZX_SDK_PARKING",label:"HXZX_SDK_PARKING"}],tableData:[],pagination:{current:1,total:0,pages:0,sizes:[10,20,50,100],size:10},loading:!1,dialogWidth:"792px",type:1,visible:!1,inputHolder:"请输入搜索关键词",selectHolder:"请选择设备名称",searchValue:{productId:""},productOptions:[],productOptionsCopy:[],deviceOptions:[{id:"2",name:"设备厂商"}],statusCount:{totalNum:0,activeNum:0,onlineNum:0},connectorForm:{connectorName:"",applicationType:"",protocolType:"",enableStatus:0,commonConfig:"{}",customConfig:"{}",upLinkId:"",downLinkId:"",vendorName:"",connectorDesc:""},connectorNameTrue:!0,vendorNameTrue:!0,applicationTypeTrue:!0,commonConfigTrue:!0,customConfigTrue:!0,connectorDescTrue:!0,rules:{connectorName:[{required:!0,trigger:"blur",validator:this.checkConnectorName}],vendorName:[{required:!0,trigger:"blur",validator:this.checkVendorName}],protocolType:[{required:!0,trigger:"change",message:"须选择协议方式"}],applicationType:[{required:!1,trigger:"blur",validator:this.checkLength}],commonConfig:[{required:!1,trigger:"blur",validator:this.checkConfigLength}],customConfig:[{required:!1,trigger:"blur",validator:this.checkConfigLength}],upLinkId:[{required:!0,message:"须选择上行转换器",trigger:"change"}],downLinkId:[{required:!0,message:"须选择下行转换器",trigger:"change"}],connectorDesc:[{required:!1,trigger:"blur",validator:this.checkLength}]},title:"",delId:"",delIds:[]}},created:function(){},watch:{visible:function(e){e||1!=this.type||(this.connectorForm={},this.$refs["connectorForm"]&&this.$refs["connectorForm"].resetFields())}},mounted:function(){this.$route.query.id&&(this.searchValue.productId=this.$route.query.id);var e=Object(a["a"])(Object(a["a"])({},this.searchValue),{},{current:this.pagination.current,size:this.pagination.size});this.fn_get_table_data(e),this.fn_get_upLink_select(),this.fn_get_downLink_select()},methods:{handleReset:function(){this.pagination.current=1,this.fn_get_table_data()},relationDevice:function(e){this.$refs.relation.open(e)},fn_sub10:function(e){if(e)return e.length>20?"".concat(e.substr(0,20),"..."):e},fn_notNull:function(e){return 0!==e&&!e},fn_handle__query:function(){var e={connectorName:this.connectorName,current:1,size:this.pagination.size};this.fn_get_table_data(e)},handleClear:function(){this.fn_get_table_data()},calcul_long_text:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e.replace(/[\u0391-\uFFE5\u0800-\u4e00 ]/g,"aa").length},fn_select:function(){var e=Object(a["a"])({},this.searchValue);this.fn_get_table_data(e)},checkConnectorName:function(e,t,n){var o=this,i=!1;Object(m["B"])({connectorName:t,id:this.connectorForm.id}).then((function(e){return 200==e.code&&(i=e.data),o.fn_notNull(t)?n(new Error("请输入连接器名称")):Object(p["g"])(t)?i?n(new Error("接入连接器名称不充许重复")):void n():n(new Error("支持中文、英文字母、数字、和特殊字符_-@()，长度限制 4~30个字符，中文算 2 个字符"))}))},checkVendorName:function(e,t,n){return this.fn_notNull(t)?n(new Error("请输入设备厂商")):Object(p["i"])(t)?void n():n(new Error("支持中文、英文字母、数字、和特殊字符_-@()，长度限制 4~64个字符，中文算 2 个字符"))},checkApplicationType:function(e,t,n){return this.fn_notNull(t)?n(new Error("请输入应用类型")):Object(p["j"])(t,32)?void n():n(new Error("支持英文字母，数字组合，长度限制2-32个字符"))},checkDevice:function(e,t,n){if(""!=t){if(!Object(p["e"])(t))return n(new Error("支持英文字母、数字、下划线（_），中划线（-）、点号（.）、半冒号（:）和特殊字符@、只能以英文开头、长度限制为6-16个字符；"));n()}else n()},checkConfigLength:function(e,t,n){return t=JSON.stringify(t),Object(p["b"])(t)?Object(p["c"])(t,2001)?void n():n(new Error("最多不超过2000个字符")):n(new Error("请输入正确的JSON格式"))},checkLength:function(e,t,n){if(!Object(p["c"])(t,201))return n(new Error("最多不超过200个字符"));n()},fn_get_device_status_count:function(){var e=this;Object(m["l"])().then((function(t){e.statusCount=t.data}))},fn_get_upLink_select:function(){var e=this;Object(m["t"])().then((function(t){200==t.code&&(e.upLinkList=t.data,e.tempUpLinkList=JSON.parse(JSON.stringify(e.upLinkList)))}))},fn_get_downLink_select:function(){var e=this;Object(m["m"])().then((function(t){200==t.code&&(e.downLinkList=t.data,e.tempDownLinkList=JSON.parse(JSON.stringify(e.downLinkList)))}))},fn_get_table_data:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=Object(a["a"])({},t);t.size||(n.size=10,n.current=1),Object(m["d"])(n).then((function(t){200==t.code?(setTimeout((function(){e.loading=!1}),300),e.tableData=t.data.records,e.pagination.total=t.data.total,e.pagination.current=t.data.current,e.pagination.pages=t.data.pages,e.pagination.size=t.data.size):e.$newNotify.error({message:t.message})})).finally((function(){setTimeout((function(){e.loading=!1}),300)}))},fn_validate:function(e,t){"connectorName"===e&&(this.connectorNameTrue=t),"vendorName"===e&&(this.vendorNameTrue=t),"applicationType"===e&&(this.applicationTypeTrue=t),"commonConfig"===e&&(this.commonConfigTrue=t),"customConfig"===e&&(this.customConfigTrue=t),"connectorDesc"===e&&(this.connectorDescTrue=t)},fn_select_more_data:function(e){this.delIds=e.map((function(e){return e.id}))},fn_del_more_data:function(){0!==this.delIds.length&&(this.columns[0].visible=!0)},fn_del_sure:function(){var e={ids:this.delIds.join(",")};this.fn_del_table_data(e)},fn_open:function(){this.title="添加连接器",this.type=1,this.dialogWidth="792px",this.visible=!0,this.tempUpLinkList=this.fn_format_select(this.upLinkList),this.tempDownLinkList=this.fn_format_select(this.downLinkList)},fn_edit:function(e){this.connectorForm=JSON.parse(JSON.stringify(e)),this.connectorForm.commonConfig=JSON.stringify(JSON.parse(this.connectorForm.commonConfig),null,2),this.connectorForm.customConfig=JSON.stringify(JSON.parse(this.connectorForm.customConfig),null,2),this.tempUpLinkList=this.fn_format_select(this.upLinkList,e.upLinkId),this.tempDownLinkList=this.fn_format_select(this.downLinkList,e.downLinkId),this.title="编辑连接器",this.type=1,this.dialogWidth="792px",this.visible=!0},fn_format_select:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return e.map((function(e){return{id:e.id,converterName:e.converterName,bindStatusName:e.bindStatusName,disabled:"已绑定"==e.bindStatusName&&e.id!=t}}))},fn_search_table_data:function(e){console.log(e),"1"===e.id?this.searchValue.aliasName=e.value:this.searchValue.deviceName=e.value;var t=Object(a["a"])({},this.searchValue);t.size=this.pagination.size,this.fn_get_table_data(t)},handleSizeChange:function(e){this.pagination.size=e;var t={size:this.pagination.size,current:1};this.fn_get_table_data(t)},handleCurrentChange:function(e){this.pagination.current=e;var t=Object(a["a"])(Object(a["a"])({},this.searchValue),{},{current:this.pagination.current,size:this.pagination.size});this.fn_get_table_data(t)},fn_clear_search_info:function(){this.searchValue.aliasName="",this.searchValue.deviceName="";var e=Object(a["a"])({},this.searchValue);this.fn_get_table_data(e)},fn_check:function(e,t){this.$router.push({path:"/connectorDetail",query:{id:e,num:t}})},fn_sure:function(){var e=this;if(2===this.type){var t={id:this.delId};Object(m["C"])(t).then((function(t){200==t.code?(e.$newNotify.success({message:t.message}),e.pagination.current=1,e.searchValue.productId="",e.fn_get_table_data({size:e.pagination.size,current:1}),e.visible=!1):e.$newNotify.error({message:t.message})}))}else 1===this.type&&this.$refs["connectorForm"].validate((function(t){if(t){var n=e.connectorForm.id?m["D"]:m["z"];e.connectorForm.commonConfig=JSON.parse(e.connectorForm.commonConfig),e.connectorForm.customConfig=JSON.parse(e.connectorForm.customConfig),n(e.connectorForm).then((function(t){200==t.code?(e.$newNotify.success({message:t.message}),e.pagination.current=1,e.searchValue.productId="",e.fn_get_table_data({size:e.pagination.size,current:1}),e.visible=!1,e.fn_get_upLink_select(),e.fn_get_downLink_select()):e.$newNotify.error({message:t.message})}))}}))},fn_close:function(){this.connectorNameTrue=!0,this.vendorNameTrue=!0,this.applicationTypeTrue=!0,this.commonConfigTrue=!0,this.customConfigTrue=!0,this.connectorDescTrue=!0},fn_del:function(e){console.log(e),this.delId=e,this.title="确定删除该接入连接器？",this.type=2,this.dialogWidth="550px",this.visible=!0}}}),N=y,C=(n("c0f4e"),Object(g["a"])(N,o,i,!1,null,"0dd9283a",null));t["default"]=C.exports},"56ff":function(e,t,n){"use strict";n("8c00")},"8c00":function(e,t,n){},c0f4e:function(e,t,n){"use strict";n("cf47")},cf47:function(e,t,n){}}]);
//# sourceMappingURL=chunk-7b090a63.d5362b3a.js.map