(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2de70c0e"],{"0e0b":function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"g",(function(){return i})),n.d(t,"c",(function(){return o})),n.d(t,"e",(function(){return l})),n.d(t,"f",(function(){return c})),n.d(t,"d",(function(){return s})),n.d(t,"h",(function(){return u})),n.d(t,"j",(function(){return d})),n.d(t,"i",(function(){return f})),n.d(t,"b",(function(){return v}));var a=n("53ca"),r=(n("a9e3"),n("ac1f"),n("1276"),n("caad"),n("5319"),function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Date,t=new Date(e);if("Invalid Date"===t&&(t=new Date),"Invalid Date"!==t){var n=t.getFullYear(),a=t.getMonth()+1,r=t.getDate(),i=t.getHours(),o=t.getMinutes(),l=t.getSeconds(),c=t.getTime(),s=Number((c/1e3+"").split(".")[0]),u=t.getDay();a=a>9?a:"0"+a,r=r>9?r:"0"+r,i=i>9?i:"0"+i,o=o>9?o:"0"+o,l=l>9?l:"0"+l,u=0===+u?7:u;var d=["一","二","三","四","五","六","日"];return{yy:n,MM:a,dd:r,hh:i,mm:o,ss:l,timestamp:c,linuxtime:s,day:u,dayToUpperCase:d[u-1]}}}),i=function(e){return/^[a-zA-Z0-9_\u4e00-\u9fa5\u0800-\u4e00 -@()/]{0,30}$/.test(e)&&e.replace(/[\u0391-\uFFE5\u0800-\u4e00 ]/g,"aa").length<31&&e.replace(/[\u0391-\uFFE5\u0800-\u4e00 ]/g,"aa").length>3},o=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:33;return e.replace(/[\u0391-\uFFE5]/g,"aa").length<t},l=function(e){return/^[a-zA-Z][a-z_A-Z0-9- \\.@:]{5,16}$/.test(e)&&e.replace(/[\u0391-\uFFE5]/g,"aa").length<17&&e.replace(/[\u0391-\uFFE5]/g,"aa").length>5},c=function(e){return/^[a-z_A-Z0-9- \\.@:]{5,16}$/.test(e)&&e.replace(/[\u0391-\uFFE5]/g,"aa").length<17&&e.replace(/[\u0391-\uFFE5]/g,"aa").length>5},s=function(e){return/^[a-zA-Z0-9\u0391-\uFFE5][a-z_A-Z0-9\u0391-\uFFE5-_()]{0,32}$/.test(e)&&e.replace(/[\u0391-\uFFE5]/g,"aa").length<32&&e.replace(/[\u0391-\uFFE5]/g,"aa").length>0},u=function(e){return/^[a-zA-Z0-9_\u4e00-\u9fa5\u0800-\u4e00 -@()/]{0,120}$/.test(e)&&e.replace(/[\u0391-\uFFE5\u0800-\u4e00 ]/g,"aa").length<121&&e.replace(/[\u0391-\uFFE5\u0800-\u4e00 ]/g,"aa").length>3},d=function(e){return/^[0-9a-z_A-Z]{2,32}$/.test(e)},f=function(e){return/^[a-zA-Z0-9_\u4e00-\u9fa5\u0800-\u4e00 -@()/]{0,64}$/.test(e)&&e.replace(/[\u0391-\uFFE5\u0800-\u4e00 ]/g,"aa").length<65&&e.replace(/[\u0391-\uFFE5\u0800-\u4e00 ]/g,"aa").length>3},v=function(e){if("string"==typeof e)try{var t=JSON.parse(JSON.parse(e));return!("object"!=Object(a["a"])(t)||!t)}catch(n){return!1}}},1276:function(e,t,n){"use strict";var a=n("2ba4"),r=n("c65b"),i=n("e330"),o=n("d784"),l=n("44e7"),c=n("825a"),s=n("1d80"),u=n("4840"),d=n("8aa5"),f=n("50c4"),v=n("577e"),p=n("dc4a"),h=n("f36a"),g=n("14c3"),_=n("9263"),m=n("9f7f"),b=n("d039"),y=m.UNSUPPORTED_Y,F=4294967295,N=Math.min,C=[].push,k=i(/./.exec),x=i(C),w=i("".slice),E=!b((function(){var e=/(?:)/,t=e.exec;e.exec=function(){return t.apply(this,arguments)};var n="ab".split(e);return 2!==n.length||"a"!==n[0]||"b"!==n[1]}));o("split",(function(e,t,n){var i;return i="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(e,n){var i=v(s(this)),o=void 0===n?F:n>>>0;if(0===o)return[];if(void 0===e)return[i];if(!l(e))return r(t,i,e,o);var c,u,d,f=[],p=(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")+(e.sticky?"y":""),g=0,m=new RegExp(e.source,p+"g");while(c=r(_,m,i)){if(u=m.lastIndex,u>g&&(x(f,w(i,g,c.index)),c.length>1&&c.index<i.length&&a(C,f,h(c,1)),d=c[0].length,g=u,f.length>=o))break;m.lastIndex===c.index&&m.lastIndex++}return g===i.length?!d&&k(m,"")||x(f,""):x(f,w(i,g)),f.length>o?h(f,0,o):f}:"0".split(void 0,0).length?function(e,n){return void 0===e&&0===n?[]:r(t,this,e,n)}:t,[function(t,n){var a=s(this),o=void 0==t?void 0:p(t,e);return o?r(o,t,a,n):r(i,v(a),t,n)},function(e,a){var r=c(this),o=v(e),l=n(i,r,o,a,i!==t);if(l.done)return l.value;var s=u(r,RegExp),p=r.unicode,h=(r.ignoreCase?"i":"")+(r.multiline?"m":"")+(r.unicode?"u":"")+(y?"g":"y"),_=new s(y?"^(?:"+r.source+")":r,h),m=void 0===a?F:a>>>0;if(0===m)return[];if(0===o.length)return null===g(_,o)?[o]:[];var b=0,C=0,k=[];while(C<o.length){_.lastIndex=y?0:C;var E,z=g(_,y?w(o,C):o);if(null===z||(E=N(f(_.lastIndex+(y?C:0)),o.length))===b)C=d(o,C,p);else{if(x(k,w(o,b,C)),k.length===m)return k;for(var O=1;O<=z.length-1;O++)if(x(k,z[O]),k.length===m)return k;C=b=E}}return x(k,w(o,b)),k}]}),!E,y)},"44e7":function(e,t,n){var a=n("861d"),r=n("c6b6"),i=n("b622"),o=i("match");e.exports=function(e){var t;return a(e)&&(void 0!==(t=e[o])?!!t:"RegExp"==r(e))}},"47c3":function(e,t,n){},7413:function(e,t,n){"use strict";var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"iot-form"},[e._t("default")],2)},r=[],i={name:"IotForm",props:{}},o=i,l=(n("7c9c"),n("2877")),c=Object(l["a"])(o,a,r,!1,null,"74134f94",null);t["a"]=c.exports},"7c9c":function(e,t,n){"use strict";n("47c3")},8150:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"device"},[n("div",{staticClass:"device-top"},[n("div",{staticClass:"device-top-search"},[n("div",{staticClass:"top-left"},[n("iot-button",{attrs:{text:"添加转换器"},on:{search:e.fn_open}})],1),n("div",{staticClass:"top-right"},[n("el-input",{attrs:{clearable:"",placeholder:"输入转换器名称"},on:{clear:e.handleClear},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.fn_handle__query.apply(null,arguments)}},model:{value:e.converterName,callback:function(t){e.converterName=t},expression:"converterName"}},[n("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"suffix"},on:{click:e.fn_handle__query},slot:"suffix"})])],1)])]),n("div",{staticClass:"device-table"},[n("iot-table",{attrs:{columns:e.columns,data:e.tableData,loading:e.loading},on:{"selection-change":e.fn_select_more_data,"selection-del":e.fn_del_more_data,"del-callbackSure":e.fn_del_sure},scopedSlots:e._u([{key:"converterName",fn:function(t){return[n("el-tooltip",{attrs:{placement:"top-start",effect:"light","popper-class":"event-tooltip"},scopedSlots:e._u([{key:"content",fn:function(){return[n("span",[e._v(" "+e._s(t.row.converterName)+" ")])]},proxy:!0}],null,!0)},[n("div",{staticClass:"alarmContent-tooltip"},[n("p",[e._v(" "+e._s(e.fn_sub10(t.row.converterName))+" ")])])])]}},{key:"analyticCode",fn:function(t){return[n("el-tooltip",{attrs:{placement:"top-start",effect:"light","popper-class":"event-tooltip"},scopedSlots:e._u([{key:"content",fn:function(){return[n("span",[e._v(" "+e._s(t.row.analyticCode)+" ")])]},proxy:!0}],null,!0)},[n("div",{staticClass:"alarmContent-tooltip"},[n("p",[e._v(" "+e._s(e.fn_sub10(t.row.analyticCode))+" ")])])])]}},{key:"operation",fn:function(t){return[n("div",{staticClass:"flex table-edit"},[n("p",{staticClass:"color2",on:{click:function(n){return e.fn_edit(t.row)}}},[e._v("修改")]),n("p"),n("p",{staticClass:"color2",on:{click:function(n){return e.fn_del(t.row.id)}}},[e._v("删除")])])]}}])})],1),e.tableData.length?n("div",{staticClass:"device-bottom"},[n("iot-pagination",{attrs:{pagination:e.pagination},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1):e._e(),n("iot-dialog",{attrs:{visible:e.visible,title:e.title,width:e.dialogWidth},on:{"update:visible":function(t){e.visible=t},callbackSure:e.fn_sure,close:e.fn_close},scopedSlots:e._u([{key:"body",fn:function(){return[1==e.type?n("iot-form",[n("el-form",{ref:"converterForm",staticClass:"converterForm",attrs:{"label-position":"top",model:e.converterForm,rules:e.rules,"label-width":"80px"},on:{validate:e.fn_validate}},[n("el-form-item",{attrs:{label:"转换器名称",prop:"converterName"}},[n("el-input",{model:{value:e.converterForm.converterName,callback:function(t){e.$set(e.converterForm,"converterName",t)},expression:"converterForm.converterName"}})],1),e.converterNameTrue?n("div",{staticClass:"el-form-tips"},[e._v(" 支持中文、英文字母、数字、和特殊字符“_-@()”，长度限制 4~30个字符，中文算 2 个字符 ")]):e._e(),n("el-form-item",{attrs:{label:"解析方式",prop:"analyticMode"}},[n("el-select",{attrs:{filterable:"",placeholder:"请选择解析方式"},model:{value:e.converterForm.analyticMode,callback:function(t){e.$set(e.converterForm,"analyticMode",t)},expression:"converterForm.analyticMode"}},e._l(e.analyticModeList,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),n("el-form-item",{attrs:{label:"类型",prop:"converterType"}},[n("el-select",{attrs:{filterable:"",placeholder:"请选择类型"},model:{value:e.converterForm.converterType,callback:function(t){e.$set(e.converterForm,"converterType",t)},expression:"converterForm.converterType"}},e._l(e.converterTypeList,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),n("el-form-item",{attrs:{label:"代码",prop:"analyticCode"}},[n("el-input",{model:{value:e.converterForm.analyticCode,callback:function(t){e.$set(e.converterForm,"analyticCode",t)},expression:"converterForm.analyticCode"}})],1),e.analyticCodeTrue?n("div",{staticClass:"el-form-tips"},[e._v(" 支持中文、英文字母、数字、和特殊字符“_-@()”，长度限制 4~32个字符，中文算 2 个字符 ")]):e._e(),n("el-form-item",{attrs:{label:"描述",prop:"converterDesc"}},[n("el-input",{attrs:{maxlength:200,type:"textarea"},model:{value:e.converterForm.converterDesc,callback:function(t){e.$set(e.converterForm,"converterDesc",t)},expression:"converterForm.converterDesc"}})],1),e.converterDescTrue?n("div",{staticClass:"el-form-tips"},[e._v(" 最多不超过200个字符 ")]):e._e()],1)],1):e._e(),2==e.type?n("div",[n("iot-form",{scopedSlots:e._u([{key:"default",fn:function(){return[n("el-form",[n("el-form-item",[n("div",{staticClass:"del-tips"},[e._v(" 删除后，绑定的连接器不能正常工作，请确认是否删除该转换器？ ")])])],1)]},proxy:!0}],null,!1,2225011599)})],1):e._e()]},proxy:!0}])})],1)},r=[],i=n("5530"),o=(n("ac1f"),n("5319"),n("d3b7"),n("d81d"),n("a15b"),n("b329"),n("7413")),l=n("c2a2"),c=n("6e22"),s=n("511c"),u=n("673a"),d=n("aa98"),f=n("0e0b"),v={name:"Device",components:{IotForm:o["a"],IotButton:l["a"],IotPagination:c["a"],IotDialog:s["a"],IotTable:u["a"]},data:function(){return{converterName:"",columns:[{label:"转换器名称",prop:"converterName",slotName:"converterName"},{label:"所属连接器",prop:"connectorName"},{label:"类型",prop:"converterTypeName"},{label:"解析方式",prop:"analyticModeName"},{label:"代码",prop:"analyticCode",slotName:"analyticCode"},{label:"描述",prop:"converterDesc"},{label:"创建时间",prop:"createTime"},{label:"修改时间",prop:"updateTime"},{label:"操作",prop:"operation",slotName:"operation",width:120}],enableList:[{value:0,label:"未启用"},{value:1,label:"已启用"}],upLinkList:[],downLinkList:[],analyticModeList:[{value:"JS",label:"JS脚本解析"},{value:"JAR",label:"动态jar包解析"},{value:"INTERNAL",label:"内置解析"}],converterTypeList:[{value:"UP_LINK",label:"上行转换器"},{value:"DOWN_LINK",label:"下行转换器"}],tableData:[],pagination:{current:1,total:0,pages:0,sizes:[10,20,50,100],size:10},loading:!1,dialogWidth:"792px",type:1,visible:!1,inputHolder:"请输入搜索关键词",selectHolder:"请选择设备名称",searchValue:{productId:""},productOptions:[],productOptionsCopy:[],deviceOptions:[{id:"2",name:"设备厂商"}],statusCount:{totalNum:0,activeNum:0,onlineNum:0},converterForm:{id:"",converterName:"",converterType:"",analyticMode:"",analyticCode:"",converterDesc:""},converterNameTrue:!0,analyticCodeTrue:!0,converterDescTrue:!0,rules:{converterName:[{required:!0,trigger:"blur",validator:this.checkConverterName}],analyticMode:[{required:!0,message:"请选择解析方式",trigger:"blur"}],converterType:[{required:!0,message:"请选择类型",trigger:"blur"}],analyticCode:[{required:!0,trigger:"blur",validator:this.checkAnalyticCode}],connectorDesc:[{required:!1,trigger:"blur",validator:this.checkLength}]},title:"",delId:"",delIds:[]}},created:function(){},watch:{visible:function(e){e||1!=this.type||(this.converterForm={},this.$refs["converterForm"]&&this.$refs["converterForm"].resetFields())}},mounted:function(){this.$route.query.id&&(this.searchValue.productId=this.$route.query.id);var e=Object(i["a"])(Object(i["a"])({},this.searchValue),{},{current:this.pagination.current,size:this.pagination.size});this.fn_get_table_data(e)},methods:{handleReset:function(){this.pagination.current=1,this.fn_get_table_data()},relationDevice:function(e){this.$refs.relation.open(e)},fn_sub10:function(e){if(e)return e.length>20?"".concat(e.substr(0,20),"..."):e},fn_notNull:function(e){return 0!==e&&!e},fn_handle__query:function(){var e={converterName:this.converterName,current:1,size:this.pagination.size};this.fn_get_table_data(e)},handleClear:function(){this.fn_get_table_data()},calcul_long_text:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e.replace(/[\u0391-\uFFE5\u0800-\u4e00 ]/g,"aa").length},fn_select:function(){var e=Object(i["a"])({},this.searchValue);this.fn_get_table_data(e)},checkConverterName:function(e,t,n){var a=this,r=!1;Object(d["H"])({converterName:t,id:this.converterForm.id}).then((function(e){return 200==e.code&&(r=e.data),a.fn_notNull(t)?n(new Error("请输入转换器名称")):Object(f["g"])(t)?r?n(new Error("接入转换名称不充许重复")):void n():n(new Error("支持中文、英文字母、数字、和特殊字符_-@()，长度限制 4~30个字符，中文算 2 个字符"))}))},checkAnalyticCode:function(e,t,n){return this.fn_notNull(t)?n(new Error("请输入解析代码")):Object(f["g"])(t)?void n():n(new Error("支持中文、英文字母、数字、和特殊字符_-@()，长度限制 4~30个字符，中文算 2 个字符"))},checkVendorName:function(e,t,n){return this.fn_notNull(t)?n(new Error("请输入产品名称")):Object(f["h"])(t)?void n():n(new Error("支持中文、英文字母、数字、和特殊字符_-@()，长度限制 4~120个字符，中文算 2 个字符"))},checkApplicationType:function(e,t,n){return this.fn_notNull(t)?n(new Error("请输入应用类型")):Object(f["j"])(t,32)?void n():n(new Error("支持英文字母，数字组合，长度限制2-32个字符"))},checkDevice:function(e,t,n){if(""!=t){if(!Object(f["e"])(t))return n(new Error("支持英文字母、数字、下划线（_），中划线（-）、点号（.）、半冒号（:）和特殊字符@、只能以英文开头、长度限制为6-16个字符；"));n()}else n()},checkLength:function(e,t,n){if(!Object(f["c"])(t,201))return n(new Error("最多不超过200个字符"));n()},fn_get_device_status_count:function(){var e=this;Object(d["l"])().then((function(t){e.statusCount=t.data}))},fn_get_table_data:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=Object(i["a"])({},t);t.size||(n.size=10,n.current=1),Object(d["f"])(n).then((function(t){200==t.code?(setTimeout((function(){e.loading=!1}),300),e.tableData=t.data.records,e.pagination.total=t.data.total,e.pagination.current=t.data.current,e.pagination.pages=t.data.pages,e.pagination.size=t.data.size):e.$newNotify.error({message:t.message})})).finally((function(){setTimeout((function(){e.loading=!1}),300)}))},fn_validate:function(e,t){"converterName"===e&&(this.converterNameTrue=t),"converterDesc"===e&&(this.converterDescTrue=t),"analyticCode"===e&&(this.analyticCodeTrue=t)},fn_select_more_data:function(e){this.delIds=e.map((function(e){return e.id}))},fn_del_more_data:function(){0!==this.delIds.length&&(this.columns[0].visible=!0)},fn_del_sure:function(){var e={ids:this.delIds.join(",")};this.fn_del_table_data(e)},fn_open:function(){this.title="添加转换器",this.type=1,this.dialogWidth="792px",this.visible=!0},fn_edit:function(e){this.converterForm=JSON.parse(JSON.stringify(e)),this.title="编辑转换器",this.type=1,this.dialogWidth="792px",this.visible=!0},fn_search_table_data:function(e){console.log(e),"1"===e.id?this.searchValue.aliasName=e.value:this.searchValue.deviceName=e.value;var t=Object(i["a"])({},this.searchValue);t.size=this.pagination.size,this.fn_get_table_data(t)},handleSizeChange:function(e){this.pagination.size=e;var t={size:this.pagination.size,current:1};this.fn_get_table_data(t)},handleCurrentChange:function(e){this.pagination.current=e;var t=Object(i["a"])(Object(i["a"])({},this.searchValue),{},{current:this.pagination.current,size:this.pagination.size});this.fn_get_table_data(t)},fn_clear_search_info:function(){this.searchValue.aliasName="",this.searchValue.deviceName="";var e=Object(i["a"])({},this.searchValue);this.fn_get_table_data(e)},fn_check:function(e,t){this.$router.push({path:"/device/connectorDetail",query:{id:e,num:t}})},fn_sure:function(){var e=this;if(2===this.type){var t={id:this.delId};Object(d["I"])(t).then((function(t){200==t.code?(e.$newNotify.success({message:t.message}),e.pagination.current=1,e.searchValue.productId="",e.fn_get_table_data({size:e.pagination.size,current:1}),e.visible=!1):e.$newNotify.error({message:t.message})}))}else 1===this.type&&this.$refs["converterForm"].validate((function(t){if(t){var n=e.converterForm.id?d["J"]:d["G"];n(e.converterForm).then((function(t){200==t.code?(e.$newNotify.success({message:t.message}),e.pagination.current=1,e.searchValue.productId="",e.fn_get_table_data({size:e.pagination.size,current:1}),e.visible=!1):e.$newNotify.error({message:t.message})}))}}))},fn_close:function(){this.converterNameTrue=!0,this.converterDescTrue=!0,this.analyticCodeTrue=!0},fn_del:function(e){console.log(e),this.delId=e,this.title="确定删除该转换器？",this.type=2,this.dialogWidth="550px",this.visible=!0}}},p=v,h=(n("899e"),n("2877")),g=Object(h["a"])(p,a,r,!1,null,"60b14890",null);t["default"]=g.exports},"899e":function(e,t,n){"use strict";n("f03d")},a15b:function(e,t,n){"use strict";var a=n("23e7"),r=n("e330"),i=n("44ad"),o=n("fc6a"),l=n("a640"),c=r([].join),s=i!=Object,u=l("join",",");a({target:"Array",proto:!0,forced:s||!u},{join:function(e){return c(o(this),void 0===e?",":e)}})},caad:function(e,t,n){"use strict";var a=n("23e7"),r=n("4d64").includes,i=n("44d2");a({target:"Array",proto:!0},{includes:function(e){return r(this,e,arguments.length>1?arguments[1]:void 0)}}),i("includes")},f03d:function(e,t,n){}}]);
//# sourceMappingURL=chunk-2de70c0e.fa7cbddb.js.map