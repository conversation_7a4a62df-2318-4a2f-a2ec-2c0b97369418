{"version": 3, "sources": ["webpack:///./src/views/groupDevice/list/components/relation/index.vue?c471", "webpack:///./src/views/groupDevice/list/index.vue?a7f5", "webpack:///./src/views/groupDevice/list/index.vue?949d", "webpack:///./src/views/groupDevice/list/components/relation/index.vue?9082", "webpack:///src/views/groupDevice/list/components/relation/index.vue", "webpack:///./src/views/groupDevice/list/components/relation/index.vue?10d9", "webpack:///./src/views/groupDevice/list/components/relation/index.vue", "webpack:///src/views/groupDevice/list/index.vue", "webpack:///./src/views/groupDevice/list/index.vue?a09d", "webpack:///./src/views/groupDevice/list/index.vue"], "names": ["render", "_vm", "this", "_h", "$createElement", "_c", "_self", "staticClass", "attrs", "on", "fn_open", "handleClear", "nativeOn", "$event", "type", "indexOf", "_k", "keyCode", "key", "fn_handle__query", "apply", "arguments", "model", "value", "callback", "$$v", "deviceSn", "expression", "slot", "columns", "tableData", "loading", "fn_select_more_data", "fn_del_more_data", "fn_del_sure", "scopedSlots", "_u", "fn", "scope", "fn_edit", "row", "_v", "fn_del", "id", "fn_check", "pagination", "handleSizeChange", "handleCurrentChange", "_e", "visible", "title", "dialogWidth", "fn_sure", "fn_close", "ref", "deviceForm", "rules", "fn_validate", "$set", "_l", "item", "name", "proxy", "handleReset", "staticRenderFns", "width", "handleClose", "_s", "waitCount", "notSearchVal", "notColumns", "notSource", "notLoading", "data", "selectionChange", "routeDevice", "notPagination", "submitBind", "doneCount", "alreadySearchVal", "alreadySource", "alreadyLoading", "alreadyPagination", "deviceVisible", "product", "c", "configInfo", "vendorName", "deviceDesc", "options", "current", "size", "total", "notSelectList", "alreadySelectList", "connectorId", "isEmpty", "configInfoTrue", "deviceSnTrue", "vendorNameTrue", "deviceDescTrue", "components", "props", "hostProductKey", "String", "hostDeviceName", "methods", "console", "log", "JSON", "parse", "stringify", "checkDeviceSn", "fn_notNull", "Error", "checkVendorName", "checkConfigInfoLength", "checkDeviceSnLength", "val", "checkLength", "$refs", "validate", "valid", "res", "code", "message", "clearValidate", "open", "getProductKey", "selectChange", "productInfo", "object", "flag", "isTotal", "params", "deviceName", "isTips", "map", "length", "$newNotify", "warning", "deviceIdList", "$router", "replace", "path", "$emit", "component", "IotPagination", "IotTable", "relation", "IotButton", "IotDialog", "IotForm", "enableList", "upLinkList", "downLinkList", "deviceTypeList", "tempDownLinkList", "pages", "sizes", "inputHolder", "selectHolder", "searchValue", "productId", "productOptions", "productOptionsCopy", "deviceOptions", "statusCount", "totalNum", "activeNum", "onlineNum", "deviceType", "delId", "delIds", "created", "watch", "resetFields", "mounted", "fn_get_table_data", "relationDevice", "fn_sub10", "str", "calcul_long_text", "checkDeviceName", "checkApplicationType", "checkDeviceSN", "checkConfig<PERSON>ength", "fn_get_device_status_count", "others", "ids", "join", "fn_del_table_data", "fn_format_select", "list", "converterName", "bindStatusName", "disabled", "fn_search_table_data", "<PERSON><PERSON><PERSON>", "fn_clear_search_info", "push", "query", "num", "postUrl"], "mappings": "kHAAA,W,6DCAA,W,kECAA,IAAIA,EAAS,WAAa,IAAIC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACE,YAAY,UAAU,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,MAAM,CAACE,YAAY,YAAY,CAACF,EAAG,aAAa,CAACG,MAAM,CAAC,KAAO,QAAQC,GAAG,CAAC,OAASR,EAAIS,YAAY,GAAGL,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,WAAW,CAACG,MAAM,CAAC,UAAY,GAAG,YAAc,aAAaC,GAAG,CAAC,MAAQR,EAAIU,aAAaC,SAAS,CAAC,MAAQ,SAASC,GAAQ,OAAIA,EAAOC,KAAKC,QAAQ,QAAQd,EAAIe,GAAGH,EAAOI,QAAQ,QAAQ,GAAGJ,EAAOK,IAAI,SAAkB,KAAcjB,EAAIkB,iBAAiBC,MAAM,KAAMC,aAAaC,MAAM,CAACC,MAAOtB,EAAY,SAAEuB,SAAS,SAAUC,GAAMxB,EAAIyB,SAASD,GAAKE,WAAW,aAAa,CAACtB,EAAG,IAAI,CAACE,YAAY,gCAAgCC,MAAM,CAAC,KAAO,UAAUC,GAAG,CAAC,MAAQR,EAAIkB,kBAAkBS,KAAK,cAAc,OAAOvB,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,YAAY,CAACG,MAAM,CAAC,QAAUP,EAAI4B,QAAQ,KAAO5B,EAAI6B,UAAU,QAAU7B,EAAI8B,SAAStB,GAAG,CAAC,mBAAmBR,EAAI+B,oBAAoB,gBAAgB/B,EAAIgC,iBAAiB,mBAAmBhC,EAAIiC,aAAaC,YAAYlC,EAAImC,GAAG,CAAC,CAAClB,IAAI,YAAYmB,GAAG,SAASC,GAAO,MAAO,CAACjC,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,IAAI,CAACE,YAAY,SAASE,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOZ,EAAIsC,QAAQD,EAAME,QAAQ,CAACvC,EAAIwC,GAAG,QAAQpC,EAAG,KAAKA,EAAG,IAAI,CAACE,YAAY,SAASE,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOZ,EAAIyC,OAAOJ,EAAME,IAAIG,OAAO,CAAC1C,EAAIwC,GAAG,QAAQpC,EAAG,KAAKA,EAAG,IAAI,CAACE,YAAY,SAASE,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOZ,EAAI2C,SAASN,EAAME,IAAIG,OAAO,CAAC1C,EAAIwC,GAAG,kBAAkB,GAAIxC,EAAI6B,UAAgB,OAAEzB,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,iBAAiB,CAACG,MAAM,CAAC,WAAaP,EAAI4C,YAAYpC,GAAG,CAAC,cAAcR,EAAI6C,iBAAiB,iBAAiB7C,EAAI8C,wBAAwB,GAAG9C,EAAI+C,KAAK3C,EAAG,aAAa,CAACG,MAAM,CAAC,QAAUP,EAAIgD,QAAQ,MAAQhD,EAAIiD,MAAM,MAAQjD,EAAIkD,aAAa1C,GAAG,CAAC,iBAAiB,SAASI,GAAQZ,EAAIgD,QAAQpC,GAAQ,aAAeZ,EAAImD,QAAQ,MAAQnD,EAAIoD,UAAUlB,YAAYlC,EAAImC,GAAG,CAAC,CAAClB,IAAI,OAAOmB,GAAG,WAAW,MAAO,CAAc,GAAZpC,EAAIa,KAAWT,EAAG,WAAW,CAACA,EAAG,UAAU,CAACiD,IAAI,aAAa/C,YAAY,aAAaC,MAAM,CAAC,iBAAiB,MAAM,MAAQP,EAAIsD,WAAW,MAAQtD,EAAIuD,MAAM,cAAc,QAAQ/C,GAAG,CAAC,SAAWR,EAAIwD,cAAc,CAACpD,EAAG,eAAe,CAACG,MAAM,CAAC,MAAQ,OAAO,KAAO,aAAa,CAACH,EAAG,WAAW,CAACiB,MAAM,CAACC,MAAOtB,EAAIsD,WAAmB,SAAE/B,SAAS,SAAUC,GAAMxB,EAAIyD,KAAKzD,EAAIsD,WAAY,WAAY9B,IAAME,WAAW,0BAA0B,GAAI1B,EAAgB,aAAEI,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACN,EAAIwC,GAAG,qBAAqBxC,EAAI+C,KAAK3C,EAAG,eAAe,CAACG,MAAM,CAAC,MAAQ,OAAO,KAAO,eAAe,CAACH,EAAG,YAAY,CAACG,MAAM,CAAC,WAAa,GAAG,YAAc,OAAOc,MAAM,CAACC,MAAOtB,EAAIsD,WAAqB,WAAE/B,SAAS,SAAUC,GAAMxB,EAAIyD,KAAKzD,EAAIsD,WAAY,aAAc9B,IAAME,WAAW,0BAA0B1B,EAAI0D,GAAI1D,EAAkB,gBAAE,SAAS2D,GAAM,OAAOvD,EAAG,YAAY,CAACa,IAAI0C,EAAKjB,GAAGnC,MAAM,CAAC,MAAQoD,EAAKC,KAAK,MAAQD,EAAKjB,SAAQ,IAAI,IAAI,IAAI,GAAG1C,EAAI+C,KAAkB,GAAZ/C,EAAIa,KAAWT,EAAG,MAAM,CAACA,EAAG,WAAW,CAAC8B,YAAYlC,EAAImC,GAAG,CAAC,CAAClB,IAAI,UAAUmB,GAAG,WAAW,MAAO,CAAChC,EAAG,UAAU,CAACA,EAAG,eAAe,CAACA,EAAG,MAAM,CAACE,YAAY,YAAY,CAACN,EAAIwC,GAAG,kCAAkC,KAAKqB,OAAM,IAAO,MAAK,EAAM,eAAe,GAAG7D,EAAI+C,OAAOc,OAAM,OAAUzD,EAAG,WAAW,CAACiD,IAAI,WAAW7C,GAAG,CAAC,MAAQR,EAAI8D,gBAAgB,IACtwGC,EAAkB,G,sICDlB,EAAS,WAAa,IAAI/D,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACA,EAAG,aAAa,CAACG,MAAM,CAAC,MAAQ,UAAU,IAAM,OAAO,UAAY,OAAO,QAAUP,EAAIgD,QAAQ,MAAQhD,EAAIgE,MAAM,YAAa,EAAK,QAAS,GAAOxD,GAAG,CAAC,iBAAiB,SAASI,GAAQZ,EAAIgD,QAAQpC,GAAQ,MAAQZ,EAAIiE,aAAa/B,YAAYlC,EAAImC,GAAG,CAAC,CAAClB,IAAI,OAAOmB,GAAG,WAAW,MAAO,CAAChC,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,KAAK,CAACJ,EAAIwC,GAAG,SAASxC,EAAIkE,GAAGlE,EAAImE,cAAc/D,EAAG,MAAM,CAACE,YAAY,SAAS,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,WAAW,CAACG,MAAM,CAAC,YAAc,aAAa,UAAY,IAAIC,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOZ,EAAIkB,kBAAiB,KAAQP,SAAS,CAAC,MAAQ,SAASC,GAAQ,OAAIA,EAAOC,KAAKC,QAAQ,QAAQd,EAAIe,GAAGH,EAAOI,QAAQ,QAAQ,GAAGJ,EAAOK,IAAI,SAAkB,KAAcjB,EAAIkB,kBAAiB,KAAQG,MAAM,CAACC,MAAOtB,EAAgB,aAAEuB,SAAS,SAAUC,GAAMxB,EAAIoE,aAAa5C,GAAKE,WAAW,iBAAiB,CAACtB,EAAG,IAAI,CAACE,YAAY,gCAAgCC,MAAM,CAAC,KAAO,UAAUC,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOZ,EAAIkB,kBAAiB,KAAQS,KAAK,cAAc,GAAGvB,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,YAAY,CAACG,MAAM,CAAC,QAAUP,EAAIqE,WAAW,KAAOrE,EAAIsE,UAAU,QAAUtE,EAAIuE,YAAY/D,GAAG,CAAC,mBAAmB,SAAUgE,GAAQ,OAAOxE,EAAIyE,gBAAgBD,GAAM,KAAUtC,YAAYlC,EAAImC,GAAG,CAAC,CAAClB,IAAI,QAAQmB,GAAG,WAAW,MAAO,CAAEpC,EAAW,QAAEI,EAAG,MAAM,CAACE,YAAY,SAAS,CAACN,EAAIwC,GAAG,gBAAgBpC,EAAG,OAAO,CAACI,GAAG,CAAC,MAAQR,EAAI0E,cAAc,CAAC1E,EAAIwC,GAAG,YAAYxC,EAAI+C,OAAOc,OAAM,GAAM,CAAC5C,IAAI,YAAYmB,GAAG,SAASC,GAAO,MAAO,CAACjC,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,IAAI,CAACE,YAAY,SAASE,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOZ,EAAIsC,QAAQD,EAAME,QAAQ,CAACvC,EAAIwC,GAAG,iBAAiBpC,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,iBAAiB,CAACG,MAAM,CAAC,WAAaP,EAAI2E,cAAc,OAAS,qCAAqCnE,GAAG,CAAC,iBAAiB,SAAUgE,GAAQ,OAAOxE,EAAI8C,oBAAoB0B,GAAM,QAAa,IAAI,OAAOpE,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,IAAI,CAACE,YAAY,OAAOE,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOZ,EAAI4E,YAAW,MAAS,CAACxE,EAAG,OAAO,CAACJ,EAAIwC,GAAG,QAAQpC,EAAG,MAAM,CAACG,MAAM,CAAC,IAAM,EAAQ,QAAwC,IAAM,QAAQH,EAAG,IAAI,CAACE,YAAY,UAAUE,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOZ,EAAI4E,YAAW,MAAU,CAACxE,EAAG,MAAM,CAACG,MAAM,CAAC,IAAM,EAAQ,QAAwC,IAAM,MAAMH,EAAG,OAAO,CAACJ,EAAIwC,GAAG,YAAYpC,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,KAAK,CAACJ,EAAIwC,GAAG,SAASxC,EAAIkE,GAAGlE,EAAI6E,cAAczE,EAAG,MAAM,CAACE,YAAY,SAAS,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,WAAW,CAACG,MAAM,CAAC,YAAc,UAAU,UAAY,IAAIC,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOZ,EAAIkB,kBAAiB,KAASP,SAAS,CAAC,MAAQ,SAASC,GAAQ,OAAIA,EAAOC,KAAKC,QAAQ,QAAQd,EAAIe,GAAGH,EAAOI,QAAQ,QAAQ,GAAGJ,EAAOK,IAAI,SAAkB,KAAcjB,EAAIkB,kBAAiB,KAASG,MAAM,CAACC,MAAOtB,EAAoB,iBAAEuB,SAAS,SAAUC,GAAMxB,EAAI8E,iBAAiBtD,GAAKE,WAAW,qBAAqB,CAACtB,EAAG,IAAI,CAACE,YAAY,gCAAgCC,MAAM,CAAC,KAAO,UAAUC,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOZ,EAAIkB,kBAAiB,KAASS,KAAK,cAAc,GAAGvB,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,YAAY,CAACG,MAAM,CAAC,QAAUP,EAAIqE,WAAW,KAAOrE,EAAI+E,cAAc,QAAU/E,EAAIgF,gBAAgBxE,GAAG,CAAC,mBAAmB,SAAUgE,GAAQ,OAAOxE,EAAIyE,gBAAgBD,GAAM,KAAWtC,YAAYlC,EAAImC,GAAG,CAAC,CAAClB,IAAI,YAAYmB,GAAG,SAASC,GAAO,MAAO,CAACjC,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,IAAI,CAACE,YAAY,SAASE,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOZ,EAAIsC,QAAQD,EAAME,QAAQ,CAACvC,EAAIwC,GAAG,iBAAiBpC,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,iBAAiB,CAACG,MAAM,CAAC,WAAaP,EAAIiF,kBAAkB,OAAS,qCAAqCzE,GAAG,CAAC,iBAAiB,SAAUgE,GAAQ,OAAOxE,EAAI8C,oBAAoB0B,GAAM,QAAc,IAAI,aAAaX,OAAM,OAAUzD,EAAG,aAAa,CAACG,MAAM,CAAC,QAAUP,EAAIkF,cAAc,MAAQ,OAAO,MAAQ,SAAS1E,GAAG,CAAC,iBAAiB,SAASI,GAAQZ,EAAIkF,cAActE,GAAQ,aAAeZ,EAAImD,QAAQ,MAAQnD,EAAIoD,UAAUlB,YAAYlC,EAAImC,GAAG,CAAC,CAAClB,IAAI,OAAOmB,GAAG,WAAW,MAAO,CAAc,GAAZpC,EAAIa,KAAWT,EAAG,WAAW,CAACA,EAAG,UAAU,CAACiD,IAAI,aAAa/C,YAAY,aAAaC,MAAM,CAAC,iBAAiB,MAAM,MAAQP,EAAIsD,WAAW,MAAQtD,EAAIuD,MAAM,cAAc,QAAQ/C,GAAG,CAAC,SAAWR,EAAIwD,cAAc,CAACpD,EAAG,eAAe,CAACG,MAAM,CAAC,MAAQ,UAAU,KAAO,eAAe,CAACH,EAAG,WAAW,CAACG,MAAM,CAAC,UAAY,IAAK,KAAO,YAAYc,MAAM,CAACC,MAAOtB,EAAIsD,WAAqB,WAAE/B,SAAS,SAAUC,GAAMxB,EAAIyD,KAAKzD,EAAIsD,WAAY,aAAc9B,IAAME,WAAW,4BAA4B,GAAI1B,EAAkB,eAAEI,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACN,EAAIwC,GAAG,oBAAoBxC,EAAI+C,KAAK3C,EAAG,eAAe,CAACG,MAAM,CAAC,MAAQ,OAAO,KAAO,aAAa,CAACH,EAAG,WAAW,CAACiB,MAAM,CAACC,MAAOtB,EAAIsD,WAAmB,SAAE/B,SAAS,SAAUC,GAAMxB,EAAIyD,KAAKzD,EAAIsD,WAAY,WAAY9B,IAAME,WAAW,0BAA0B,GAAI1B,EAAgB,aAAEI,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACN,EAAIwC,GAAG,wDAAwDxC,EAAI+C,KAAK3C,EAAG,eAAe,CAACG,MAAM,CAAC,MAAQ,OAAO,KAAO,eAAe,CAACH,EAAG,WAAW,CAACiB,MAAM,CAACC,MAAOtB,EAAIsD,WAAqB,WAAE/B,SAAS,SAAUC,GAAMxB,EAAIyD,KAAKzD,EAAIsD,WAAY,aAAc9B,IAAME,WAAW,4BAA4B,GAAI1B,EAAkB,eAAEI,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACN,EAAIwC,GAAG,wDAAwDxC,EAAI+C,KAAK3C,EAAG,eAAe,CAACG,MAAM,CAAC,MAAQ,OAAO,KAAO,eAAe,CAACH,EAAG,WAAW,CAACG,MAAM,CAAC,UAAY,IAAI,KAAO,YAAYc,MAAM,CAACC,MAAOtB,EAAIsD,WAAqB,WAAE/B,SAAS,SAAUC,GAAMxB,EAAIyD,KAAKzD,EAAIsD,WAAY,aAAc9B,IAAME,WAAW,4BAA4B,GAAI1B,EAAkB,eAAEI,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACN,EAAIwC,GAAG,mBAAmBxC,EAAI+C,MAAM,IAAI,GAAG/C,EAAI+C,OAAOc,OAAM,QAAW,IACv0L,EAAkB,G,oCCyLtB,GACEW,KADF,WAEI,MAAO,CACL3D,KAAM,EACNmC,SAAS,EACTkC,eAAe,EACfC,QAAS,GACTC,EAAG,GACH9B,WAAY,CACV+B,WAAY,GACZ5D,SAAU,GACV6D,WAAY,GACZC,WAAY,IAEdC,QAAS,CACf,CACQ,MAAR,IACQ,MAAR,OAGMpB,aAAc,GACdC,WAAY,CAClB,CACQ,KAAR,aAEA,CACQ,KAAR,aACQ,MAAR,aACQ,MAAR,KAEA,CACQ,KAAR,aACQ,MAAR,QAEA,CACQ,KAAR,WACQ,MAAR,QAEA,CACQ,KAAR,mBACQ,MAAR,QAEA,CACQ,KAAR,YACQ,MAAR,KACQ,SAAR,cAGMd,MAAO,CACL8B,WAAY,CACpB,CACU,UAAV,EACU,QAAV,OACU,UAAV,6BAGQ5D,SAAU,CAClB,CACU,UAAV,EACU,QAAV,OACU,UAAV,qBAGQ6D,WAAY,CACpB,CACU,UAAV,EACU,QAAV,OACU,UAAV,uBAIQC,WAAY,CACpB,CACU,UAAV,EAEU,QAAV,OACU,UAAV,oBAIMjB,UAAW,GACXC,YAAY,EACZI,cAAe,CACbc,QAAS,EACTC,KAAM,EACNC,MAAO,GAETC,cAAe,GACfd,iBAAkB,GAClBC,cAAe,GACfC,gBAAgB,EAChBC,kBAAmB,CACjBQ,QAAS,EACTC,KAAM,EACNC,MAAO,GAETE,kBAAmB,GAEnBC,YAAa,GACbC,SAAS,EACT5B,UAAW,EACXU,UAAW,EACXb,MAAO,GAAb,2BAEMgC,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,EAChBC,gBAAgB,IAGpBC,WAAY,CAAd,sEACEC,MAAO,CACLC,eAAgB,CACdzF,KAAM0F,QAERC,eAAgB,CACd3F,KAAM0F,SAGVE,QAAS,CACPnE,QADJ,SACA,GACMoE,QAAQC,IAAI,MAAOpE,GACnBtC,KAAKqD,WAAasD,KAAKC,MAAMD,KAAKE,UAAUvE,IAC5CtC,KAAKqD,WAAW+B,WAAauB,KAAKE,UACxC,uCACA,KACA,GAEM7G,KAAKiF,eAAgB,GAEvB6B,cAXJ,SAWA,OACM,OAAI9G,KAAK+G,WAAW1F,GACXC,EAAS,IAAI0F,MAAM,YAClC,uBAOQ1F,IANOA,EACf,UACA,oDAOI2F,gBAxBJ,SAwBA,OACM,IAAK,OAAX,OAAW,CAAX,OACQ,OAAO3F,EACf,UACA,mDAIQA,KAGJ4F,sBAnCJ,SAmCA,OAGM,OAFA7F,EAAQsF,KAAKE,UAAUxF,GAEnBrB,KAAK+G,WAAW1F,GACXC,EAAS,IAAI0F,MAAM,eAClC,uBAGW,OAAX,OAAW,CAAX,QAGQ1F,IAFOA,EAAS,IAAI0F,MAAM,iBAHnB1F,EAAS,IAAI0F,MAAM,kBAS9BG,oBAlDJ,SAkDA,OACM,OAAInH,KAAK+G,WAAW1F,GACXC,EAAS,IAAI0F,MAAM,YAClC,2BAGQ1F,IAFOA,EAAS,IAAI0F,MAAM,iBAK9BD,WA3DJ,SA2DA,GACM,OAAe,IAARK,IAAcA,GAEvBC,YA9DJ,SA8DA,OACM,IAAK,OAAX,OAAW,CAAX,OACQ,OAAO/F,EAAS,IAAI0F,MAAM,gBAE1B1F,KAGJ4B,QArEJ,WAqEA,WACMlD,KAAKsH,MAAM,cAAcC,UAAS,SAAxC,GACYC,GACF,OAAV,OAAU,CAAV,gCAC4B,KAAZC,EAAIC,MACN,EAAd,oBACgBC,QAASF,EAAIE,UAIf,EAAd,uBAEc,EAAd,uBAEc,EAAd,kBAEc,EAAd,kBACgBA,QAASF,EAAIE,iBAOzBxE,SA7FJ,WA8FMnD,KAAK+F,gBAAiB,EACtB/F,KAAKgG,cAAe,EACpBhG,KAAKiG,gBAAiB,EACtBjG,KAAKkG,gBAAiB,EACtBlG,KAAKsH,MAAMjE,WAAWuE,iBAGxBrE,YArGJ,SAqGA,KACmB,eAATI,IACF3D,KAAK+F,eAAiB1E,GAEX,aAATsC,IACF3D,KAAKgG,aAAe3E,GAET,eAATsC,IACF3D,KAAKiG,eAAiB5E,GAEX,eAATsC,IACF3D,KAAKkG,eAAiB7E,IAI1BwG,KApHJ,SAoHA,GACM7H,KAAK6F,YAAcA,EACnB7F,KAAK+C,SAAU,EAGf/C,KAAK8H,cAAc,GAAG,GAAM,GAE5B9H,KAAK8H,cAAc,GAAG,GAAM,IAE9BC,aA7HJ,SA6HA,GACM/H,KAAK0E,cAAcc,QAAU,EAC7BxF,KAAKgF,kBAAkBQ,QAAU,EACjC,IAAN,wDAEMxF,KAAKgI,YAAcC,EACnBjI,KAAK8H,cAAc,GAAG,GAAM,GAE5B9H,KAAK8H,cAAc,GAAG,GAAM,IAE9B7G,iBAvIJ,SAuIA,GACUiH,GAEFlI,KAAK0E,cAAcc,QAAU,EAC7BxF,KAAK8H,cAAc,KAGnB9H,KAAKgF,kBAAkBQ,QAAU,EACjCxF,KAAK8H,cAAc,KAGvBA,cAlJJ,SAkJA,kIACA,KACUI,GAEFlI,KAAK6E,iBAAmBsD,EAAU,GAAKnI,KAAK6E,iBAC5CuD,EAAS,CACPvC,YAAa7F,KAAK6F,YAClBL,QAASxF,KAAKgF,kBAAkBQ,QAChCC,KAAMzF,KAAKgF,kBAAkBS,KAC7B4C,WAAYF,EAAU,GAAKnI,KAAK6E,kBAElC,OAAR,OAAQ,CAAR,qBACU,GAAgB,KAAZ4C,EAAIC,KAAa,CACnB,IAAZ,SAEY,EAAZ,4BACY,EAAZ,gCACY,EAAZ,wCAGY,EAAZ,iBACY,EAAZ,YACY,EAAZ,0BACgBY,GACF,EAAd,oBACgBX,QAASF,EAAIE,eAOrB3H,KAAKmE,aAAegE,EAAU,GAAKnI,KAAKmE,aACxCiE,EAAS,CACP5C,QAASxF,KAAK0E,cAAcc,QAC5BC,KAAMzF,KAAK0E,cAAce,KACzB4C,WAAYF,EAAU,GAAKnI,KAAKmE,cAElC,OAAR,OAAQ,CAAR,qBACU,GAAgB,KAAZsD,EAAIC,KAAa,CACnB,IAAZ,SAE4B,MAAZD,EAAIC,KACN,EAAd,WAEc,EAAd,WAEY,EAAZ,wBACY,EAAZ,gCACY,EAAZ,oCAEY,EAAZ,aACY,EAAZ,YACY,EAAZ,sBACgBY,GACF,EAAd,oBACgBX,QAASF,EAAIE,eAOzBlH,YAjNJ,aAkNI+D,gBAlNJ,SAkNA,KACU0D,EAEFlI,KAAK2F,cAAgBpB,EAAKgE,KAAI,SAAtC,kBAGQvI,KAAK4F,kBAAoBrB,EAAKgE,KAAI,SAA1C,mBAGI1F,oBA3NJ,SA2NA,KACUqF,GAEFlI,KAAK0E,cAAcc,QAAUjB,EAC7BvE,KAAK8H,cAAc,KAGnB9H,KAAKgF,kBAAkBQ,QAAUjB,EACjCvE,KAAK8H,cAAc,KAGvBnD,WAtOJ,SAsOA,cACM,GAAIuD,EAAM,CAER,GAAiC,GAA7BlI,KAAK2F,cAAc6C,OAIrB,YAHAxI,KAAKyI,WAAWC,QAAQ,CACtBf,QAAS,aAIb,OAAR,OAAQ,CAAR,CACU9B,YAAa7F,KAAK6F,YAClB8C,aAAc3I,KAAK2F,gBAC7B,kBAC0B,KAAZ8B,EAAIC,MACN,EAAZ,oBACcC,QAASF,EAAIE,UAEf,EAAZ,wBACY,EAAZ,4BAEY,EAAZ,uBAEY,EAAZ,wBAEY,EAAZ,kBACcA,QAASF,EAAIE,iBAI3B,CAEQ,GAAqC,GAAjC3H,KAAK4F,kBAAkB4C,OAIzB,YAHAxI,KAAKyI,WAAWC,QAAQ,CACtBf,QAAS,aAIb,OAAR,OAAQ,CAAR,CACU9B,YAAa7F,KAAK6F,YAClB8C,aAAc3I,KAAK4F,oBAC7B,kBAC0B,KAAZ6B,EAAIC,MACN,EAAZ,oBACcC,QAASF,EAAIE,UAEf,EAAZ,wBACY,EAAZ,4BAEY,EAAZ,uBAEY,EAAZ,wBAEY,EAAZ,kBACcA,QAASF,EAAIE,eAMvBlD,YAjSJ,WAkSMzE,KAAK4I,QAAQC,QAAQ,CACnBC,KAAM,aAGV9E,YAtSJ,WAuSMhE,KAAKkF,QAAU,GACflF,KAAKgI,YAAc,GACnBhI,KAAKmE,aAAe,GACpBnE,KAAK6E,iBAAmB,GACxB7E,KAAKqE,UAAY,GACjBrE,KAAK8E,cAAgB,GACrB9E,KAAK0E,cAAcc,QAAU,EAC7BxF,KAAKgF,kBAAkBQ,QAAU,EACjCxF,KAAK0E,cAAcgB,MAAQ,EAC3B1F,KAAKgF,kBAAkBU,MAAQ,EAC/B1F,KAAK+I,MAAM,YClmB0X,I,wBCQvYC,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIa,EAAAA,E,QCsIf,G,UAAA,CACErF,KAAM,SACNwC,WAAY,CACV8C,cAAJ,OACIC,SAAJ,OACIC,SAAJ,EACIC,UAAJ,OACIC,UAAJ,OACIC,QAAJ,QAEE/E,KAVF,WAWI,MAAO,CACL/C,SAAU,GACVG,QAAS,CACf,CACQ,MAAR,MACQ,KAAR,aAEA,CAAQ,MAAR,QAAQ,KAAR,cACA,CAAQ,MAAR,QAAQ,KAAR,YACA,CACQ,MAAR,OACQ,KAAR,kBAEA,CACQ,MAAR,OACQ,KAAR,gBAEA,CAAQ,MAAR,OAAQ,KAAR,kBAEA,CACQ,MAAR,OACQ,KAAR,cAEA,CACQ,MAAR,OACQ,KAAR,cAEA,CACQ,MAAR,KACQ,KAAR,YACQ,SAAR,YACQ,MAAR,MAGM4H,WAAY,CAClB,CACQ,MAAR,EACQ,MAAR,OAEA,CACQ,MAAR,EACQ,MAAR,QAGMC,WAAY,GACZC,aAAc,GAEdC,eAAgB,CACtB,CACQ,GAAR,EACQ,KAAR,OAEA,CACQ,GAAR,EACQ,KAAR,OAEA,CACQ,GAAR,EACQ,KAAR,QAGMC,iBAAkB,GAClB/H,UAAW,GACXe,WAAY,CACV6C,QAAS,EACTE,MAAO,EACPkE,MAAO,EACPC,MAAO,CAAC,GAAI,GAAI,GAAI,KACpBpE,KAAM,IAGR5D,SAAS,EACToB,YAAa,QACbrC,KAAM,EACNmC,SAAS,EACT+G,YAAa,WACbC,aAAc,UACdC,YAAa,CACXC,UAAW,IAGbC,eAAgB,GAChBC,mBAAoB,GAEpBC,cAAe,CACrB,CACQ,GAAR,IACQ,KAAR,SAGMC,YAAa,CACXC,SAAU,EACVC,UAAW,EACXC,UAAW,GAGbnH,WAAY,CACV7B,SAAU,GACViJ,WAAY,IAGdzE,cAAc,EAEd1C,MAAO,CACL9B,SAAU,CAClB,CACU,UAAV,EAEU,QAAV,OACU,UAAV,qBAGQiJ,WAAY,CACpB,CACU,UAAV,EACU,QAAV,SACU,QAAV,aAIMzH,MAAO,GACP0H,MAAO,GAEPC,OAAQ,KAGZC,QAzIF,aA0IEC,MAAO,CACL9H,QADJ,SACA,GACWqE,GAAoB,GAAbpH,KAAKY,OACfZ,KAAKqD,WAAa,GAClBrD,KAAKsH,MAAM,eAAiBtH,KAAKsH,MAAM,cAAcwD,iBAK3DC,QAnJF,WAuJI,IAAJ,GACMvF,QAASxF,KAAK2C,WAAW6C,QACzBC,KAAMzF,KAAK2C,WAAW8C,MAGxBzF,KAAKgL,kBAAkBzG,IAEzBiC,QAAS,CACP3C,YADJ,WAEM7D,KAAK2C,WAAW6C,QAAU,EAC1BxF,KAAKgL,qBAEPC,eALJ,SAKA,GACMjL,KAAKsH,MAAM6B,SAAStB,KAAKpF,IAE3ByI,SARJ,SAQA,GACM,GAAIC,EAAK,OAAOA,EAAI3C,OAAS,GAAK,GAAxC,gCAEIzB,WAXJ,SAWA,GACM,OAAe,IAARK,IAAcA,GAGvBnG,iBAfJ,WAgBM,IAAN,GACQO,SAAUxB,KAAKwB,SACfgE,QAAS,EACTC,KAAMzF,KAAK2C,WAAW8C,MAExBzF,KAAKgL,kBAAkB5C,IAEzB3H,YAvBJ,WAwBMT,KAAKgL,qBAEPI,iBA1BJ,WA0BA,gEACM,OAAOhE,EAAIyB,QAAQ,iCAAkC,MAAML,QAG7D6C,gBA9BJ,SA8BA,kBACA,KACM,OAAN,OAAM,CAAN,CACQhD,WAAYhH,EACZoB,GAAIzC,KAAKqD,WAAWZ,KAC5B,kBAKQ,OAJgB,KAAZgF,EAAIC,OACNQ,EAAOT,EAAIlD,MAGT,EAAZ,cACiBjD,EAAS,IAAI0F,MAAM,aACpC,kBAMA,EACiB1F,EAAS,IAAI0F,MAAM,sBAE1B1F,IAROA,EACjB,UACA,kEAWI2F,gBAxDJ,SAwDA,OACM,OAAIjH,KAAK+G,WAAW1F,GACXC,EAAS,IAAI0F,MAAM,YAClC,uBAOQ1F,IANOA,EACf,UACA,oDAOIgK,qBArEJ,SAqEA,OACM,OAAItL,KAAK+G,WAAW1F,GACXC,EAAS,IAAI0F,MAAM,YAClC,0BAGQ1F,IAFOA,EAAS,IAAI0F,MAAM,6BAK9BuE,cA9EJ,SA8EA,kBACA,KACM,OAAN,OAAM,CAAN,CACQ/J,SAAUxB,KAAKqD,WAAW7B,SAC1BiB,GAAIzC,KAAKqD,WAAWZ,KAC5B,kBAIQ,OAHgB,KAAZgF,EAAIC,OACNQ,EAAOT,EAAIlD,MAET,EAAZ,cACiBjD,EAAS,IAAI0F,MAAM,YACpC,wBACiB1F,EAAS,IAAI0F,MAAM,kBACpC,EACiB1F,EAAS,IAAI0F,MAAM,mBAE1B1F,QAKNkK,kBAnGJ,SAmGA,OAGM,OAFAnK,EAAQsF,KAAKE,UAAUxF,GAEnB,OAAV,OAAU,CAAV,GACa,OAAb,OAAa,CAAb,aAGUC,IAFOA,EAAS,IAAI0F,MAAM,iBAKrB1F,EAAS,IAAI0F,MAAM,kBAG9BK,YAhHJ,SAgHA,OACM,IAAK,OAAX,OAAW,CAAX,OACQ,OAAO/F,EAAS,IAAI0F,MAAM,gBAE1B1F,KAGJmK,2BAvHJ,WAuHA,WACM,OAAN,OAAM,GAAN,kBACQ,EAAR,uBAIIT,kBA7HJ,WA6HA,uEACA,uBACW5C,EAAO3C,OACViG,EAAOjG,KAAO,GACdiG,EAAOlG,QAAU,GAEnB,OAAN,OAAM,CAAN,GACA,kBACA,aACU,YAAV,WACY,EAAZ,aACA,KACU,EAAV,yBACU,EAAV,8BACU,EAAV,kCACU,EAAV,8BACU,EAAV,6BAEU,EAAV,kBACY,QAAZ,eAIA,oBACQ,YAAR,WACU,EAAV,aACA,SAIIjC,YA3JJ,SA2JA,KACmB,aAATI,IACF3D,KAAKgG,aAAe3E,IAIxBS,oBAjKJ,SAiKA,GAEM9B,KAAK2K,OAASpG,EAAKgE,KAAI,SAA7B,GACQ,OAAO7E,EAAKjB,OAIhBV,iBAxKJ,WAyKiC,IAAvB/B,KAAK2K,OAAOnC,SACdxI,KAAK2B,QAAQ,GAAGoB,SAAU,IAM9Bf,YAhLJ,WAiLM,IAAN,GACQ2J,IAAK3L,KAAK2K,OAAOiB,KAAK,MAExB5L,KAAK6L,kBAAkBtH,IAGzB/D,QAvLJ,WAwLMR,KAAKgD,MAAQ,OACbhD,KAAKY,KAAO,EACZZ,KAAKiD,YAAc,QACnBjD,KAAK+C,SAAU,GAEjBV,QA7LJ,SA6LA,GACMrC,KAAKqD,WAAasD,KAAKC,MAAMD,KAAKE,UAAUvE,IAC5CtC,KAAKgD,MAAQ,OACbhD,KAAKY,KAAO,EACZZ,KAAKiD,YAAc,QACnBjD,KAAK+C,SAAU,GAGjB+I,iBArMJ,SAqMA,mEACM,OAAOC,EAAKxD,KAAI,SAAtB,GACQ,MAAO,CACL9F,GAAIiB,EAAKjB,GACTuJ,cAAetI,EAAKsI,cACpBC,eAAgBvI,EAAKuI,eACrBC,SAAiC,OAAvBxI,EAAKuI,gBAA2BvI,EAAKjB,IAAMA,OAK3D0J,qBAhNJ,SAgNA,GACM1F,QAAQC,IAAI0B,GACM,MAAdA,EAAO3F,GACTzC,KAAKgK,YAAYoC,UAAYhE,EAAO/G,MAEpCrB,KAAKgK,YAAY3B,WAAaD,EAAO/G,MAEvC,IAAN,sCACMkD,EAAKkB,KAAOzF,KAAK2C,WAAW8C,KAC5BzF,KAAKgL,kBAAkBzG,IAGzB3B,iBA5NJ,SA4NA,GAEM5C,KAAK2C,WAAW8C,KAAO2B,EACvB,IAAN,GACQ3B,KAAMzF,KAAK2C,WAAW8C,KACtBD,QAAS,GAEXxF,KAAKgL,kBAAkB5C,IAGzBvF,oBAtOJ,SAsOA,GAEM7C,KAAK2C,WAAW6C,QAAU4B,EAC1B,IAAN,mCACA,kBADA,IAEQ5B,QAASxF,KAAK2C,WAAW6C,QACzBC,KAAMzF,KAAK2C,WAAW8C,OAExBzF,KAAKgL,kBAAkB5C,IAGzBiE,qBAjPJ,WAkPMrM,KAAKgK,YAAYoC,UAAY,GAC7BpM,KAAKgK,YAAY3B,WAAa,GAC9B,IAAN,sCACMrI,KAAKgL,kBAAkBzG,IAGzB7B,SAxPJ,SAwPA,KACM1C,KAAK4I,QAAQ0D,KAAK,CAChBxD,KAAM,qBACNyD,MAAO,CACL9J,GAAI8B,EACJiI,IAAKA,MAKXtJ,QAlQJ,WAkQA,WAEM,GAAkB,IAAdlD,KAAKY,KAAY,CACnB,IAAR,GACU6B,GAAIzC,KAAK0K,OAEX,OAAR,OAAQ,CAAR,qBAC0B,KAAZjD,EAAIC,MACN,EAAZ,oBACcC,QAASF,EAAIE,UAEf,EAAZ,qBAGY,EAAZ,mBACclC,KAAM,EAApB,gBACcD,QAAS,IAEX,EAAZ,YAEY,EAAZ,kBACcmC,QAASF,EAAIE,kBAM3B,eACQ3H,KAAKsH,MAAM,cAAcC,UAAS,SAA1C,GACU,GAAIC,EAAO,CACT,IAAZ,kBACA,OACA,OAEYiF,EAAQ,EAApB,8BAC8B,KAAZhF,EAAIC,MACN,EAAhB,oBACkBC,QAASF,EAAIE,UAEf,EAAhB,qBAGgB,EAAhB,mBACkBlC,KAAM,EAAxB,gBACkBD,QAAS,IAEX,EAAhB,WACgB,EAAhB,uBACgB,EAAhB,0BAEgB,EAAhB,kBACkBmC,QAASF,EAAIE,kBAQ3BxE,SA7TJ,WA8TMnD,KAAKgG,cAAe,GAGtBxD,OAjUJ,SAiUA,GACMxC,KAAK0K,MAAQjI,EACbzC,KAAKgD,MAAQ,YACbhD,KAAKY,KAAO,EACZZ,KAAKiD,YAAc,QACnBjD,KAAK+C,SAAU,MC7nBwV,ICQzW,G,UAAY,eACd,EACAjD,EACAgE,GACA,EACA,KACA,WACA,OAIa,e", "file": "js/chunk-6b04cada.3f5ed77e.js", "sourcesContent": ["export * from \"-!../../../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=068013f1&lang=scss&scoped=true&\"", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=1198ccd4&lang=scss&scoped=true&\"", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"device\"},[_c('div',{staticClass:\"device-top\"},[_c('div',{staticClass:\"device-top-search\"},[_c('div',{staticClass:\"top-left\"},[_c('iot-button',{attrs:{\"text\":\"添加设备\"},on:{\"search\":_vm.fn_open}})],1),_c('div',{staticClass:\"top-right\"},[_c('el-input',{attrs:{\"clearable\":\"\",\"placeholder\":\"输入设备SN号搜索\"},on:{\"clear\":_vm.handleClear},nativeOn:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\")){ return null; }return _vm.fn_handle__query.apply(null, arguments)}},model:{value:(_vm.deviceSn),callback:function ($$v) {_vm.deviceSn=$$v},expression:\"deviceSn\"}},[_c('i',{staticClass:\"el-input__icon el-icon-search\",attrs:{\"slot\":\"suffix\"},on:{\"click\":_vm.fn_handle__query},slot:\"suffix\"})])],1)])]),_c('div',{staticClass:\"device-table\"},[_c('iot-table',{attrs:{\"columns\":_vm.columns,\"data\":_vm.tableData,\"loading\":_vm.loading},on:{\"selection-change\":_vm.fn_select_more_data,\"selection-del\":_vm.fn_del_more_data,\"del-callbackSure\":_vm.fn_del_sure},scopedSlots:_vm._u([{key:\"operation\",fn:function(scope){return [_c('div',{staticClass:\"flex table-edit\"},[_c('p',{staticClass:\"color2\",on:{\"click\":function($event){return _vm.fn_edit(scope.row)}}},[_vm._v(\"修改\")]),_c('p'),_c('p',{staticClass:\"color2\",on:{\"click\":function($event){return _vm.fn_del(scope.row.id)}}},[_vm._v(\"删除\")]),_c('p'),_c('p',{staticClass:\"color2\",on:{\"click\":function($event){return _vm.fn_check(scope.row.id)}}},[_vm._v(\"详情\")])])]}}])})],1),(_vm.tableData.length)?_c('div',{staticClass:\"device-bottom\"},[_c('iot-pagination',{attrs:{\"pagination\":_vm.pagination},on:{\"size-change\":_vm.handleSizeChange,\"current-change\":_vm.handleCurrentChange}})],1):_vm._e(),_c('iot-dialog',{attrs:{\"visible\":_vm.visible,\"title\":_vm.title,\"width\":_vm.dialogWidth},on:{\"update:visible\":function($event){_vm.visible=$event},\"callbackSure\":_vm.fn_sure,\"close\":_vm.fn_close},scopedSlots:_vm._u([{key:\"body\",fn:function(){return [(_vm.type == 1)?_c('iot-form',[_c('el-form',{ref:\"deviceForm\",staticClass:\"deviceForm\",attrs:{\"label-position\":'top',\"model\":_vm.deviceForm,\"rules\":_vm.rules,\"label-width\":\"80px\"},on:{\"validate\":_vm.fn_validate}},[_c('el-form-item',{attrs:{\"label\":\"设备SN\",\"prop\":\"deviceSn\"}},[_c('el-input',{model:{value:(_vm.deviceForm.deviceSn),callback:function ($$v) {_vm.$set(_vm.deviceForm, \"deviceSn\", $$v)},expression:\"deviceForm.deviceSn\"}})],1),(_vm.deviceSnTrue)?_c('div',{staticClass:\"el-form-tips\"},[_vm._v(\" 长度限制为4-30个字符; \")]):_vm._e(),_c('el-form-item',{attrs:{\"label\":\"设备类型\",\"prop\":\"deviceType\"}},[_c('el-select',{attrs:{\"filterable\":\"\",\"placeholder\":\"请选择\"},model:{value:(_vm.deviceForm.deviceType),callback:function ($$v) {_vm.$set(_vm.deviceForm, \"deviceType\", $$v)},expression:\"deviceForm.deviceType\"}},_vm._l((_vm.deviceTypeList),function(item){return _c('el-option',{key:item.id,attrs:{\"label\":item.name,\"value\":item.id}})}),1)],1)],1)],1):_vm._e(),(_vm.type == 2)?_c('div',[_c('iot-form',{scopedSlots:_vm._u([{key:\"default\",fn:function(){return [_c('el-form',[_c('el-form-item',[_c('div',{staticClass:\"del-tips\"},[_vm._v(\" 删除后关联组的屏蔽功能会失效，请确认是否删除？ \")])])],1)]},proxy:true}],null,false,3774706394)})],1):_vm._e()]},proxy:true}])}),_c('relation',{ref:\"relation\",on:{\"close\":_vm.handleReset}})],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('iot-dialog',{attrs:{\"title\":\"连接器关联设备\",\"top\":\"10vh\",\"maxHeight\":\"auto\",\"visible\":_vm.visible,\"width\":_vm.width,\"appendBody\":true,\"footer\":false},on:{\"update:visible\":function($event){_vm.visible=$event},\"close\":_vm.handleClose},scopedSlots:_vm._u([{key:\"body\",fn:function(){return [_c('div',{staticClass:\"content\"},[_c('div',{staticClass:\"device flex\"},[_c('div',{staticClass:\"device-data not\"},[_c('h4',[_vm._v(\"待关联设备：\"+_vm._s(_vm.waitCount))]),_c('div',{staticClass:\"table\"},[_c('div',{staticClass:\"form-item\"},[_c('el-input',{attrs:{\"placeholder\":\"请输入请输入设备名称\",\"clearable\":\"\"},on:{\"clear\":function($event){return _vm.fn_handle__query(true)}},nativeOn:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\")){ return null; }return _vm.fn_handle__query(true)}},model:{value:(_vm.notSearchVal),callback:function ($$v) {_vm.notSearchVal=$$v},expression:\"notSearchVal\"}},[_c('i',{staticClass:\"el-input__icon el-icon-search\",attrs:{\"slot\":\"suffix\"},on:{\"click\":function($event){return _vm.fn_handle__query(true)}},slot:\"suffix\"})])],1),_c('div',{staticClass:\"device-table-content\"},[_c('iot-table',{attrs:{\"columns\":_vm.notColumns,\"data\":_vm.notSource,\"loading\":_vm.notLoading},on:{\"selection-change\":function (data) { return _vm.selectionChange(data, true); }},scopedSlots:_vm._u([{key:\"empty\",fn:function(){return [(_vm.isEmpty)?_c('div',{staticClass:\"empty\"},[_vm._v(\" 该产品暂无设备，请先去\"),_c('span',{on:{\"click\":_vm.routeDevice}},[_vm._v(\"添加设备\")])]):_vm._e()]},proxy:true},{key:\"operation\",fn:function(scope){return [_c('div',{staticClass:\"flex table-edit\"},[_c('p',{staticClass:\"color2\",on:{\"click\":function($event){return _vm.fn_edit(scope.row)}}},[_vm._v(\"修改\")])])]}}])}),_c('div',{staticClass:\"pagination flex\"},[_c('iot-pagination',{attrs:{\"pagination\":_vm.notPagination,\"layout\":\"total, prev, pager, next,  jumper\"},on:{\"current-change\":function (data) { return _vm.handleCurrentChange(data, true); }}})],1)],1)])]),_c('div',{staticClass:\"action flex\"},[_c('p',{staticClass:\"bind\",on:{\"click\":function($event){return _vm.submitBind(true)}}},[_c('span',[_vm._v(\"绑定\")]),_c('img',{attrs:{\"src\":require(\"@/assets/images/index/arrow-icon.png\"),\"alt\":\"\"}})]),_c('p',{staticClass:\"unbound\",on:{\"click\":function($event){return _vm.submitBind(false)}}},[_c('img',{attrs:{\"src\":require(\"@/assets/images/index/arrow-icon.png\"),\"alt\":\"\"}}),_c('span',[_vm._v(\"解绑\")])])]),_c('div',{staticClass:\"device-data already\"},[_c('h4',[_vm._v(\"已关联设备：\"+_vm._s(_vm.doneCount))]),_c('div',{staticClass:\"table\"},[_c('div',{staticClass:\"form-item\"},[_c('el-input',{attrs:{\"placeholder\":\"请输入设备名称\",\"clearable\":\"\"},on:{\"clear\":function($event){return _vm.fn_handle__query(false)}},nativeOn:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\")){ return null; }return _vm.fn_handle__query(false)}},model:{value:(_vm.alreadySearchVal),callback:function ($$v) {_vm.alreadySearchVal=$$v},expression:\"alreadySearchVal\"}},[_c('i',{staticClass:\"el-input__icon el-icon-search\",attrs:{\"slot\":\"suffix\"},on:{\"click\":function($event){return _vm.fn_handle__query(false)}},slot:\"suffix\"})])],1),_c('div',{staticClass:\"device-table-content\"},[_c('iot-table',{attrs:{\"columns\":_vm.notColumns,\"data\":_vm.alreadySource,\"loading\":_vm.alreadyLoading},on:{\"selection-change\":function (data) { return _vm.selectionChange(data, false); }},scopedSlots:_vm._u([{key:\"operation\",fn:function(scope){return [_c('div',{staticClass:\"flex table-edit\"},[_c('p',{staticClass:\"color2\",on:{\"click\":function($event){return _vm.fn_edit(scope.row)}}},[_vm._v(\"修改\")])])]}}])}),_c('div',{staticClass:\"pagination flex\"},[_c('iot-pagination',{attrs:{\"pagination\":_vm.alreadyPagination,\"layout\":\"total, prev, pager, next,  jumper\"},on:{\"current-change\":function (data) { return _vm.handleCurrentChange(data, false); }}})],1)],1)])])])])]},proxy:true}])}),_c('iot-dialog',{attrs:{\"visible\":_vm.deviceVisible,\"title\":\"修改设备\",\"width\":\"729px\"},on:{\"update:visible\":function($event){_vm.deviceVisible=$event},\"callbackSure\":_vm.fn_sure,\"close\":_vm.fn_close},scopedSlots:_vm._u([{key:\"body\",fn:function(){return [(_vm.type == 1)?_c('iot-form',[_c('el-form',{ref:\"deviceForm\",staticClass:\"deviceForm\",attrs:{\"label-position\":'top',\"model\":_vm.deviceForm,\"rules\":_vm.rules,\"label-width\":\"80px\"},on:{\"validate\":_vm.fn_validate}},[_c('el-form-item',{attrs:{\"label\":\"自定义配置信息\",\"prop\":\"configInfo\"}},[_c('el-input',{attrs:{\"maxlength\":2000,\"type\":\"textarea\"},model:{value:(_vm.deviceForm.configInfo),callback:function ($$v) {_vm.$set(_vm.deviceForm, \"configInfo\", $$v)},expression:\"deviceForm.configInfo\"}})],1),(_vm.configInfoTrue)?_c('div',{staticClass:\"el-form-tips\"},[_vm._v(\" 最多不超过2000个字符 \")]):_vm._e(),_c('el-form-item',{attrs:{\"label\":\"设备SN\",\"prop\":\"deviceSn\"}},[_c('el-input',{model:{value:(_vm.deviceForm.deviceSn),callback:function ($$v) {_vm.$set(_vm.deviceForm, \"deviceSn\", $$v)},expression:\"deviceForm.deviceSn\"}})],1),(_vm.deviceSnTrue)?_c('div',{staticClass:\"el-form-tips\"},[_vm._v(\" 支持中文、英文字母、数字、和特殊字符“_-@()”，长度限制 4~64个字符，中文算 2 个字符 \")]):_vm._e(),_c('el-form-item',{attrs:{\"label\":\"设备厂商\",\"prop\":\"vendorName\"}},[_c('el-input',{model:{value:(_vm.deviceForm.vendorName),callback:function ($$v) {_vm.$set(_vm.deviceForm, \"vendorName\", $$v)},expression:\"deviceForm.vendorName\"}})],1),(_vm.vendorNameTrue)?_c('div',{staticClass:\"el-form-tips\"},[_vm._v(\" 支持中文、英文字母、数字、和特殊字符“_-@()”，长度限制 4~64个字符，中文算 2 个字符 \")]):_vm._e(),_c('el-form-item',{attrs:{\"label\":\"设备描述\",\"prop\":\"deviceDesc\"}},[_c('el-input',{attrs:{\"maxlength\":200,\"type\":\"textarea\"},model:{value:(_vm.deviceForm.deviceDesc),callback:function ($$v) {_vm.$set(_vm.deviceForm, \"deviceDesc\", $$v)},expression:\"deviceForm.deviceDesc\"}})],1),(_vm.deviceDescTrue)?_c('div',{staticClass:\"el-form-tips\"},[_vm._v(\" 最多不超过200个字符 \")]):_vm._e()],1)],1):_vm._e()]},proxy:true}])})],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div>\r\n    <iot-dialog title=\"连接器关联设备\"\r\n                top=\"10vh\"\r\n                maxHeight=\"auto\"\r\n                :visible.sync=\"visible\"\r\n                :width=\"width\"\r\n                :appendBody=\"true\"\r\n                :footer=\"false\"\r\n                @close=\"handleClose\">\r\n      <template #body>\r\n        <div class=\"content\">\r\n          <div class=\"device flex\">\r\n            <div class=\"device-data not\">\r\n              <h4>待关联设备：{{ waitCount }}</h4>\r\n              <div class=\"table\">\r\n                <div class=\"form-item\">\r\n                  <el-input v-model=\"notSearchVal\"\r\n                            placeholder=\"请输入请输入设备名称\"\r\n                            clearable\r\n                            @keyup.enter.native=\"fn_handle__query(true)\"\r\n                            @clear=\"fn_handle__query(true)\">\r\n                    <i slot=\"suffix\"\r\n                       class=\"el-input__icon el-icon-search\"\r\n                       @click=\"fn_handle__query(true)\"></i>\r\n                  </el-input>\r\n                </div>\r\n                <div class=\"device-table-content\">\r\n                  <iot-table :columns=\"notColumns\"\r\n                             :data=\"notSource\"\r\n                             :loading=\"notLoading\"\r\n                             @selection-change=\"(data) => selectionChange(data, true)\">\r\n                    <template #empty>\r\n                      <div class=\"empty\"\r\n                           v-if=\"isEmpty\">\r\n                        该产品暂无设备，请先去<span @click=\"routeDevice\">添加设备</span>\r\n                      </div>\r\n                    </template>\r\n\r\n                    <template slot=\"operation\"\r\n                              slot-scope=\"scope\">\r\n                      <div class=\"flex table-edit\">\r\n                        <p @click=\"fn_edit(scope.row)\"\r\n                           class=\"color2\">修改</p>\r\n                      </div>\r\n                    </template>\r\n                  </iot-table>\r\n                  <div class=\"pagination flex\">\r\n                    <iot-pagination :pagination=\"notPagination\"\r\n                                    layout=\"total, prev, pager, next,  jumper\"\r\n                                    @current-change=\"(data) => handleCurrentChange(data, true)\" />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"action flex\">\r\n              <p class=\"bind\"\r\n                 @click=\"submitBind(true)\">\r\n                <span>绑定</span>\r\n                <img src=\"~@/assets/images/index/arrow-icon.png\"\r\n                     alt=\"\" />\r\n              </p>\r\n              <p class=\"unbound\"\r\n                 @click=\"submitBind(false)\">\r\n                <img src=\"~@/assets/images/index/arrow-icon.png\"\r\n                     alt=\"\" />\r\n                <span>解绑</span>\r\n              </p>\r\n            </div>\r\n            <div class=\"device-data already\">\r\n              <h4>已关联设备：{{ doneCount }}</h4>\r\n              <div class=\"table\">\r\n                <div class=\"form-item\">\r\n                  <el-input v-model=\"alreadySearchVal\"\r\n                            placeholder=\"请输入设备名称\"\r\n                            clearable\r\n                            @keyup.enter.native=\"fn_handle__query(false)\"\r\n                            @clear=\"fn_handle__query(false)\">\r\n                    <i slot=\"suffix\"\r\n                       class=\"el-input__icon el-icon-search\"\r\n                       @click=\"fn_handle__query(false)\"></i>\r\n                  </el-input>\r\n                </div>\r\n                <div class=\"device-table-content\">\r\n                  <iot-table :columns=\"notColumns\"\r\n                             :data=\"alreadySource\"\r\n                             :loading=\"alreadyLoading\"\r\n                             @selection-change=\"(data) => selectionChange(data, false)\">\r\n                    <template slot=\"operation\"\r\n                              slot-scope=\"scope\">\r\n                      <div class=\"flex table-edit\">\r\n                        <p @click=\"fn_edit(scope.row)\"\r\n                           class=\"color2\">修改</p>\r\n                      </div>\r\n                    </template></iot-table>\r\n                  <div class=\"pagination flex\">\r\n                    <iot-pagination :pagination=\"alreadyPagination\"\r\n                                    layout=\"total, prev, pager, next,  jumper\"\r\n                                    @current-change=\"(data) => handleCurrentChange(data, false)\" />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <!-- <div class=\"mask flex\">\r\n          <span>请先选择产品</span>\r\n        </div> -->\r\n\r\n        </div>\r\n      </template>\r\n    </iot-dialog>\r\n    <iot-dialog :visible.sync=\"deviceVisible\"\r\n                title=\"修改设备\"\r\n                width=\"729px\"\r\n                @callbackSure=\"fn_sure\"\r\n                @close=\"fn_close\">\r\n      <template #body>\r\n        <!-- 新增和修改 -->\r\n        <iot-form v-if=\"type == 1\">\r\n          <el-form class=\"deviceForm\"\r\n                   ref=\"deviceForm\"\r\n                   :label-position=\"'top'\"\r\n                   :model=\"deviceForm\"\r\n                   :rules=\"rules\"\r\n                   @validate=\"fn_validate\"\r\n                   label-width=\"80px\">\r\n\r\n            <el-form-item label=\"自定义配置信息\"\r\n                          prop=\"configInfo\">\r\n              <el-input v-model=\"deviceForm.configInfo\"\r\n                        :maxlength=\"2000\"\r\n                        type=\"textarea\"></el-input>\r\n            </el-form-item>\r\n            <div class=\"el-form-tips\"\r\n                 v-if=\"configInfoTrue\">\r\n              最多不超过2000个字符\r\n            </div>\r\n\r\n            <el-form-item label=\"设备SN\"\r\n                          prop=\"deviceSn\">\r\n              <el-input v-model=\"deviceForm.deviceSn\"></el-input>\r\n            </el-form-item>\r\n            <div class=\"el-form-tips\"\r\n                 v-if=\"deviceSnTrue\">\r\n              支持中文、英文字母、数字、和特殊字符“_-@()”，长度限制 4~64个字符，中文算 2 个字符\r\n            </div>\r\n\r\n            <el-form-item label=\"设备厂商\"\r\n                          prop=\"vendorName\">\r\n              <el-input v-model=\"deviceForm.vendorName\"></el-input>\r\n            </el-form-item>\r\n            <div class=\"el-form-tips\"\r\n                 v-if=\"vendorNameTrue\">\r\n              支持中文、英文字母、数字、和特殊字符“_-@()”，长度限制 4~64个字符，中文算 2 个字符\r\n            </div>\r\n\r\n            <el-form-item label=\"设备描述\"\r\n                          prop=\"deviceDesc\">\r\n              <el-input v-model=\"deviceForm.deviceDesc\"\r\n                        :maxlength=\"200\"\r\n                        type=\"textarea\"></el-input>\r\n            </el-form-item>\r\n            <div class=\"el-form-tips\"\r\n                 v-if=\"deviceDescTrue\">\r\n              最多不超过200个字符\r\n            </div>\r\n          </el-form>\r\n        </iot-form>\r\n      </template>\r\n    </iot-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport iotDialog from '@/components/iot-dialog'\r\nimport iotTable from '@/components/iot-table'\r\nimport IotForm from '@/components/iot-form'\r\nimport iotPagination from '@/components/iot-pagination'\r\nimport { reg_seven, twenty_three, isJSON } from '@/util/util.js'\r\nimport {\r\n  postConnectorBindDevice,\r\n  postConnectorUnBindDevice,\r\n  getConnectorNotLinkDeviceList,\r\n  getConnectorLinkDeviceList,\r\n  postConnectorUpdateDevice,\r\n} from '@/api/device.js'\r\nexport default {\r\n  data() {\r\n    return {\r\n      type: 1,\r\n      visible: false,\r\n      deviceVisible: false,\r\n      product: '',\r\n      c: {},\r\n      deviceForm: {\r\n        configInfo: '',\r\n        deviceSn: '',\r\n        vendorName: '',\r\n        deviceDesc: '',\r\n      },\r\n      options: [\r\n        {\r\n          value: '1',\r\n          label: '测试',\r\n        },\r\n      ],\r\n      notSearchVal: '',\r\n      notColumns: [\r\n        {\r\n          type: 'selection',\r\n        },\r\n        {\r\n          prop: 'productKey',\r\n          label: 'ProductKey',\r\n          width: 180,\r\n        },\r\n        {\r\n          prop: 'deviceName',\r\n          label: '设备名称',\r\n        },\r\n        {\r\n          prop: 'deviceSn',\r\n          label: '设备SN',\r\n        },\r\n        {\r\n          prop: 'deviceStatusName',\r\n          label: '在线状态',\r\n        },\r\n        {\r\n          prop: 'operation',\r\n          label: '操作',\r\n          slotName: 'operation',\r\n        },\r\n      ],\r\n      rules: {\r\n        configInfo: [\r\n          {\r\n            required: true,\r\n            trigger: 'blur',\r\n            validator: this.checkConfigInfoLength,\r\n          },\r\n        ],\r\n        deviceSn: [\r\n          {\r\n            required: true,\r\n            trigger: 'blur',\r\n            validator: this.checkDeviceSn,\r\n          },\r\n        ],\r\n        vendorName: [\r\n          {\r\n            required: true,\r\n            trigger: 'blur',\r\n            validator: this.checkVendorName,\r\n          },\r\n        ],\r\n\r\n        deviceDesc: [\r\n          {\r\n            required: false,\r\n            // message: '最多不超过200个字符',\r\n            trigger: 'blur',\r\n            validator: this.checkLength,\r\n          },\r\n        ],\r\n      },\r\n      notSource: [],\r\n      notLoading: false,\r\n      notPagination: {\r\n        current: 1,\r\n        size: 7,\r\n        total: 0,\r\n      },\r\n      notSelectList: [],\r\n      alreadySearchVal: '',\r\n      alreadySource: [],\r\n      alreadyLoading: false,\r\n      alreadyPagination: {\r\n        current: 1,\r\n        size: 7,\r\n        total: 0,\r\n      },\r\n      alreadySelectList: [],\r\n\r\n      connectorId: '', //连接器id\r\n      isEmpty: false, //左侧table empty 特殊处理\r\n      waitCount: 0,\r\n      doneCount: 0,\r\n      width: `${(1330 / 1920) * 100}vw`, // postcss 计算方法\r\n\r\n      configInfoTrue: true,\r\n      deviceSnTrue: true,\r\n      vendorNameTrue: true,\r\n      deviceDescTrue: true,\r\n    }\r\n  },\r\n  components: { iotDialog, iotTable, iotPagination, IotForm },\r\n  props: {\r\n    hostProductKey: {\r\n      type: String,\r\n    },\r\n    hostDeviceName: {\r\n      type: String,\r\n    },\r\n  },\r\n  methods: {\r\n    fn_edit(row) {\r\n      console.log('row', row)\r\n      this.deviceForm = JSON.parse(JSON.stringify(row))\r\n      this.deviceForm.configInfo = JSON.stringify(\r\n        JSON.parse(this.deviceForm.configInfo),\r\n        null,\r\n        2\r\n      )\r\n      this.deviceVisible = true\r\n    },\r\n    checkDeviceSn(rule, value, callback) {\r\n      if (this.fn_notNull(value)) {\r\n        return callback(new Error('请输入设备SN'))\r\n      } else if (!twenty_three(value)) {\r\n        return callback(\r\n          new Error(\r\n            '支持中文、英文字母、数字、和特殊字符_-@()，长度限制 4~64个字符，中文算 2 个字符'\r\n          )\r\n        )\r\n      } else {\r\n        callback()\r\n      }\r\n    },\r\n    checkVendorName(rule, value, callback) {\r\n      if (!twenty_three(value, 201)) {\r\n        return callback(\r\n          new Error(\r\n            '支持中文、英文字母、数字、和特殊字符_-@()，长度限制 4~64个字符，中文算 2 个字符'\r\n          )\r\n        )\r\n      } else {\r\n        callback()\r\n      }\r\n    },\r\n    checkConfigInfoLength(rule, value, callback) {\r\n      value = JSON.stringify(value)\r\n\r\n      if (this.fn_notNull(value)) {\r\n        return callback(new Error('请输入自定义配置信息'))\r\n      } else if (!reg_seven(value, 2001)) {\r\n        return callback(new Error('最多不超过2000个字符'))\r\n      }\r\n      if (!isJSON(value)) {\r\n        return callback(new Error('请输入正确的JSON格式'))\r\n      } else {\r\n        callback()\r\n      }\r\n    },\r\n\r\n    checkDeviceSnLength(rule, value, callback) {\r\n      if (this.fn_notNull(value)) {\r\n        return callback(new Error('请输入设备SN'))\r\n      } else if (!reg_seven(value, 201)) {\r\n        return callback(new Error('最多不超过200个字符'))\r\n      } else {\r\n        callback()\r\n      }\r\n    },\r\n    fn_notNull(val) {\r\n      return val !== 0 && !val\r\n    },\r\n    checkLength(rule, value, callback) {\r\n      if (!reg_seven(value, 201)) {\r\n        return callback(new Error('最多不超过200个字符'))\r\n      } else {\r\n        callback()\r\n      }\r\n    },\r\n    fn_sure() {\r\n      this.$refs['deviceForm'].validate((valid) => {\r\n        if (valid) {\r\n          postConnectorUpdateDevice(this.deviceForm).then((res) => {\r\n            if (res.code == 200) {\r\n              this.$newNotify.success({\r\n                message: res.message,\r\n              })\r\n\r\n              // 未绑定的\r\n              this.getProductKey(0, true, true)\r\n              // 已绑定的\r\n              this.getProductKey(1, true, true)\r\n\r\n              this.deviceVisible = false\r\n            } else {\r\n              this.$newNotify.error({\r\n                message: res.message,\r\n              })\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    fn_close() {\r\n      this.configInfoTrue = true\r\n      this.deviceSnTrue = true\r\n      this.vendorNameTrue = true\r\n      this.deviceDescTrue = true\r\n      this.$refs.deviceForm.clearValidate()\r\n    },\r\n    // 表单验证触发\r\n    fn_validate(name, value) {\r\n      if (name === 'configInfo') {\r\n        this.configInfoTrue = value\r\n      }\r\n      if (name === 'deviceSn') {\r\n        this.deviceSnTrue = value\r\n      }\r\n      if (name === 'vendorName') {\r\n        this.vendorNameTrue = value\r\n      }\r\n      if (name === 'deviceDesc') {\r\n        this.deviceDescTrue = value\r\n      }\r\n    },\r\n\r\n    open(connectorId) {\r\n      this.connectorId = connectorId\r\n      this.visible = true\r\n\r\n      // 未绑定的\r\n      this.getProductKey(0, true, true)\r\n      // 已绑定的\r\n      this.getProductKey(1, true, true)\r\n    },\r\n    selectChange(data) {\r\n      this.notPagination.current = 1\r\n      this.alreadyPagination.current = 1\r\n      let object = this.options.filter((item) => item.id == data)[0]\r\n      // 未绑定的\r\n      this.productInfo = object\r\n      this.getProductKey(0, true, true)\r\n      // 已绑定的\r\n      this.getProductKey(1, true, true)\r\n    },\r\n    fn_handle__query(flag) {\r\n      if (flag) {\r\n        // 搜索待关联\r\n        this.notPagination.current = 1\r\n        this.getProductKey(0)\r\n      } else {\r\n        // 搜索已关联\r\n        this.alreadyPagination.current = 1\r\n        this.getProductKey(1)\r\n      }\r\n    },\r\n    getProductKey(flag, isTips = false, isTotal = false) {\r\n      let params = {}\r\n      if (flag) {\r\n        // 已绑定\r\n        this.alreadySearchVal = isTotal ? '' : this.alreadySearchVal\r\n        params = {\r\n          connectorId: this.connectorId,\r\n          current: this.alreadyPagination.current,\r\n          size: this.alreadyPagination.size,\r\n          deviceName: isTotal ? '' : this.alreadySearchVal,\r\n        }\r\n        getConnectorLinkDeviceList(params).then((res) => {\r\n          if (res.code == 200) {\r\n            let data = res.data\r\n            // 已绑定\r\n            this.alreadySource = data.records || []\r\n            this.doneCount = isTotal ? data.total : this.doneCount\r\n            this.alreadyPagination.total = data.total || 0\r\n          } else {\r\n            // 已绑定\r\n            this.alreadySource = []\r\n            this.doneCount = 0\r\n            this.alreadyPagination.total = 0\r\n            if (isTips) {\r\n              this.$newNotify.warning({\r\n                message: res.message,\r\n              })\r\n            }\r\n          }\r\n        })\r\n      } else {\r\n        // 未绑定\r\n        this.notSearchVal = isTotal ? '' : this.notSearchVal\r\n        params = {\r\n          current: this.notPagination.current,\r\n          size: this.notPagination.size,\r\n          deviceName: isTotal ? '' : this.notSearchVal,\r\n        }\r\n        getConnectorNotLinkDeviceList(params).then((res) => {\r\n          if (res.code == 200) {\r\n            let data = res.data\r\n            // 未绑定\r\n            if (res.code == 4603) {\r\n              this.isEmpty = true\r\n            } else {\r\n              this.isEmpty = false\r\n            }\r\n            this.notSource = data.records || []\r\n            this.waitCount = isTotal ? data.total : this.waitCount\r\n            this.notPagination.total = data.total || 0\r\n          } else {\r\n            this.notSource = []\r\n            this.waitCount = 0\r\n            this.notPagination.total = 0\r\n            if (isTips) {\r\n              this.$newNotify.warning({\r\n                message: res.message,\r\n              })\r\n            }\r\n          }\r\n        })\r\n      }\r\n    },\r\n    handleClear() {},\r\n    selectionChange(data, flag) {\r\n      if (flag) {\r\n        // 未关联数组\r\n        this.notSelectList = data.map((item) => item.id)\r\n      } else {\r\n        // 已关联数组\r\n        this.alreadySelectList = data.map((item) => item.id)\r\n      }\r\n    },\r\n    handleCurrentChange(data, flag) {\r\n      if (flag) {\r\n        // 未绑定\r\n        this.notPagination.current = data\r\n        this.getProductKey(0)\r\n      } else {\r\n        // 已绑定\r\n        this.alreadyPagination.current = data\r\n        this.getProductKey(1)\r\n      }\r\n    },\r\n    submitBind(flag) {\r\n      if (flag) {\r\n        //绑定\r\n        if (this.notSelectList.length == 0) {\r\n          this.$newNotify.warning({\r\n            message: '请选择未关联设备',\r\n          })\r\n          return\r\n        }\r\n        postConnectorBindDevice({\r\n          connectorId: this.connectorId,\r\n          deviceIdList: this.notSelectList,\r\n        }).then((res) => {\r\n          if (res.code == 200) {\r\n            this.$newNotify.success({\r\n              message: res.message,\r\n            })\r\n            this.notPagination.current = 1\r\n            this.alreadyPagination.current = 1\r\n            // 未绑定的\r\n            this.getProductKey(0, false, true)\r\n            // 已绑定的\r\n            this.getProductKey(1, false, true)\r\n          } else {\r\n            this.$newNotify.error({\r\n              message: res.message,\r\n            })\r\n          }\r\n        })\r\n      } else {\r\n        // 解绑\r\n        if (this.alreadySelectList.length == 0) {\r\n          this.$newNotify.warning({\r\n            message: '请选择已关联设备',\r\n          })\r\n          return\r\n        }\r\n        postConnectorUnBindDevice({\r\n          connectorId: this.connectorId,\r\n          deviceIdList: this.alreadySelectList,\r\n        }).then((res) => {\r\n          if (res.code == 200) {\r\n            this.$newNotify.success({\r\n              message: res.message,\r\n            })\r\n            this.notPagination.current = 1\r\n            this.alreadyPagination.current = 1\r\n            // 未绑定的\r\n            this.getProductKey(0, false, true)\r\n            // 已绑定的\r\n            this.getProductKey(1, false, true)\r\n          } else {\r\n            this.$newNotify.error({\r\n              message: res.message,\r\n            })\r\n          }\r\n        })\r\n      }\r\n    },\r\n    routeDevice() {\r\n      this.$router.replace({\r\n        path: '/device',\r\n      })\r\n    },\r\n    handleClose() {\r\n      this.product = ''\r\n      this.productInfo = {}\r\n      this.notSearchVal = ''\r\n      this.alreadySearchVal = ''\r\n      this.notSource = []\r\n      this.alreadySource = []\r\n      this.notPagination.current = 1\r\n      this.alreadyPagination.current = 1\r\n      this.notPagination.total = 0\r\n      this.alreadyPagination.total = 0\r\n      this.$emit('close')\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  padding-bottom: 12px;\r\n  position: relative;\r\n  h5 {\r\n    color: #666666;\r\n    font-size: 14px;\r\n    line-height: 16px;\r\n    font-weight: normal;\r\n    padding: 8px 0;\r\n    span {\r\n      color: #ff0000;\r\n    }\r\n  }\r\n  .item {\r\n    padding-bottom: 26px;\r\n  }\r\n\r\n  .device {\r\n    align-items: center;\r\n\r\n    .device-data {\r\n      width: 586px;\r\n      h4 {\r\n        padding-left: 14px;\r\n        position: relative;\r\n        color: #262626;\r\n        font-size: 18px;\r\n        font-weight: 500;\r\n      }\r\n      h4::before {\r\n        content: '';\r\n        width: 4px;\r\n        height: 14px;\r\n        background: #1890ff;\r\n        position: absolute;\r\n        left: 0;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n      }\r\n      .table {\r\n        margin-top: 18px;\r\n        height: 520px;\r\n        background: #ffffff;\r\n        border: 1px solid #ececec;\r\n        padding: 18px;\r\n        .form-item {\r\n          padding-bottom: 18px;\r\n        }\r\n      }\r\n      .pagination {\r\n        padding-top: 14px;\r\n        justify-content: flex-end;\r\n      }\r\n    }\r\n    .action {\r\n      flex-direction: column;\r\n      align-items: center;\r\n      justify-content: center;\r\n      padding: 0 12px;\r\n      p {\r\n        width: 72px;\r\n        height: 32px;\r\n        text-align: center;\r\n        border-radius: 3px;\r\n        cursor: pointer;\r\n        transition: all 0.3s;\r\n        span {\r\n          color: #515151;\r\n          font-size: 14px;\r\n          line-height: 32px;\r\n        }\r\n        img {\r\n          width: 12px;\r\n          height: 9px;\r\n        }\r\n      }\r\n      .bind {\r\n        background: linear-gradient(\r\n          270deg,\r\n          #eeeeee 0%,\r\n          #dadddf 75%,\r\n          #c9c9c9 100%\r\n        );\r\n        background-size: 200%;\r\n        background-position: 100% 0;\r\n        margin-bottom: 14px;\r\n        span {\r\n          padding-right: 8px;\r\n        }\r\n      }\r\n      .bind:hover {\r\n        // background: linear-gradient(270deg, #e7e7e7 0%, #c9c9c9 100%);\r\n        background-position: 0;\r\n      }\r\n      .unbound {\r\n        background: linear-gradient(\r\n          90deg,\r\n          #eeeeee 0%,\r\n          #dadddf 75%,\r\n          #c9c9c9 100%\r\n        );\r\n        background-size: 200%;\r\n\r\n        span {\r\n          padding-left: 8px;\r\n        }\r\n        img {\r\n          transform: rotate(180deg);\r\n        }\r\n      }\r\n      .unbound:hover {\r\n        // background: linear-gradient(90deg, #e7e7e7 0%, #c9c9c9 100%);\r\n        background-position: 100% 0;\r\n      }\r\n    }\r\n  }\r\n  .mask {\r\n    position: absolute;\r\n    width: 100%;\r\n    height: calc(100% - 92px);\r\n    position: absolute;\r\n    top: 92px;\r\n    left: 0;\r\n    background: rgba(0, 0, 0, 0.7);\r\n    align-items: center;\r\n    justify-content: center;\r\n    z-index: 99;\r\n    span {\r\n      color: #ffffff;\r\n      text-align: center;\r\n      font-family: H_Medium;\r\n      font-size: 18px;\r\n    }\r\n  }\r\n  .item {\r\n    /deep/ {\r\n      .el-input__inner {\r\n        width: 586px;\r\n        border-radius: 0;\r\n      }\r\n    }\r\n  }\r\n\r\n  .device {\r\n    /deep/ .el-select {\r\n      .el-input__inner::placeholder {\r\n        color: #515151;\r\n      }\r\n    }\r\n    /deep/ {\r\n      .el-table__header {\r\n        tr {\r\n          height: 42px !important;\r\n        }\r\n        .el-table__cell {\r\n          padding: 0;\r\n        }\r\n      }\r\n      .el-table__row {\r\n        height: 42px !important;\r\n        .el-table__cell {\r\n          padding: 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n.deviceForm {\r\n  /deep/ .el-form-item {\r\n    margin-bottom: 24px;\r\n  }\r\n  .el-form-tips {\r\n    // margin-top: -17px;\r\n    margin-top: -22px;\r\n    margin-bottom: 6px;\r\n  }\r\n}\r\n/deep/ .empty {\r\n  padding-top: 68px;\r\n  color: #888888;\r\n  font-size: 14px;\r\n  span {\r\n    color: #018aff;\r\n    cursor: pointer;\r\n  }\r\n}\r\n.device-table-content {\r\n  .iot-table {\r\n    height: 378px;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../../node_modules/babel-loader/lib/index.js!../../../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../../node_modules/babel-loader/lib/index.js!../../../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=068013f1&scoped=true&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=068013f1&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"068013f1\",\n  null\n  \n)\n\nexport default component.exports", "<!--\r\n * @Description: 1.0\r\n * @Version: 1.0\r\n * @Autor: hs\r\n * @Date: 2021-11-08 09:51:45\r\n * @LastEditors: lb <EMAIL>\r\n * @LastEditTime: 2023-05-20 17:16:57\r\n-->\r\n<template>\r\n  <div class=\"device\">\r\n    <div class=\"device-top\">\r\n      <div class=\"device-top-search\">\r\n        <div class=\"top-left\">\r\n          <iot-button text=\"添加设备\"\r\n                      @search=\"fn_open\"></iot-button>\r\n        </div>\r\n        <div class=\"top-right\">\r\n          <!-- 搜索栏 -->\r\n          <el-input v-model=\"deviceSn\"\r\n                    @keyup.enter.native=\"fn_handle__query\"\r\n                    clearable\r\n                    placeholder=\"输入设备SN号搜索\"\r\n                    @clear=\"handleClear\">\r\n            <i slot=\"suffix\"\r\n               class=\"el-input__icon el-icon-search\"\r\n               @click=\"fn_handle__query\"></i>\r\n          </el-input>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"device-table\">\r\n      <!-- 表格 -->\r\n      <iot-table :columns=\"columns\"\r\n                 :data=\"tableData\"\r\n                 :loading=\"loading\"\r\n                 @selection-change=\"fn_select_more_data\"\r\n                 @selection-del=\"fn_del_more_data\"\r\n                 @del-callbackSure=\"fn_del_sure\">\r\n        <template slot=\"operation\"\r\n                  slot-scope=\"scope\">\r\n          <div class=\"flex table-edit\">\r\n            <p @click=\"fn_edit(scope.row)\"\r\n               class=\"color2\">修改</p>\r\n            <p></p>\r\n\r\n            <p @click=\"fn_del(scope.row.id)\"\r\n               class=\"color2\">删除</p>\r\n            <p></p>\r\n\r\n            <p @click=\"fn_check(scope.row.id)\"\r\n               class=\"color2\">详情</p>\r\n          </div>\r\n        </template>\r\n      </iot-table>\r\n    </div>\r\n\r\n    <div class=\"device-bottom\"\r\n         v-if=\"tableData.length\">\r\n      <!-- 分页 -->\r\n      <iot-pagination :pagination=\"pagination\"\r\n                      @size-change=\"handleSizeChange\"\r\n                      @current-change=\"handleCurrentChange\" />\r\n    </div>\r\n    <iot-dialog :visible.sync=\"visible\"\r\n                :title=\"title\"\r\n                :width=\"dialogWidth\"\r\n                @callbackSure=\"fn_sure\"\r\n                @close=\"fn_close\">\r\n      <template #body>\r\n        <!-- 新增和修改 -->\r\n        <iot-form v-if=\"type == 1\">\r\n          <el-form class=\"deviceForm\"\r\n                   ref=\"deviceForm\"\r\n                   :label-position=\"'top'\"\r\n                   :model=\"deviceForm\"\r\n                   :rules=\"rules\"\r\n                   @validate=\"fn_validate\"\r\n                   label-width=\"80px\">\r\n\r\n            <el-form-item label=\"设备SN\"\r\n                          prop=\"deviceSn\">\r\n              <el-input v-model=\"deviceForm.deviceSn\"></el-input>\r\n            </el-form-item>\r\n            <div class=\"el-form-tips\"\r\n                 v-if=\"deviceSnTrue\">\r\n              长度限制为4-30个字符;\r\n            </div>\r\n            <el-form-item label=\"设备类型\"\r\n                          prop=\"deviceType\">\r\n              <el-select v-model=\"deviceForm.deviceType\"\r\n                         filterable\r\n                         placeholder=\"请选择\">\r\n                <el-option v-for=\"item in deviceTypeList\"\r\n                           :key=\"item.id\"\r\n                           :label=\"item.name\"\r\n                           :value=\"item.id\">\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-form>\r\n\r\n        </iot-form>\r\n        <div v-if=\"type == 2\">\r\n          <iot-form>\r\n            <template #default>\r\n              <el-form>\r\n                <el-form-item>\r\n                  <div class=\"del-tips\">\r\n                    删除后关联组的屏蔽功能会失效，请确认是否删除？\r\n                  </div>\r\n                </el-form-item>\r\n              </el-form>\r\n            </template>\r\n          </iot-form>\r\n        </div>\r\n      </template>\r\n    </iot-dialog>\r\n    <relation ref=\"relation\"\r\n              @close=\"handleReset\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport FormSearch from '@/components/form-search'\r\nimport IotForm from '@/components/iot-form'\r\nimport IotButton from '@/components/iot-button'\r\nimport IotPagination from '@/components/iot-pagination'\r\nimport IotTable from '@/components/iot-table'\r\nimport IotDialog from '@/components/iot-dialog'\r\nimport relation from './components/relation'\r\nimport {\r\n  getDeviceStatusNum,\r\n  postDeviceShieldDelete,\r\n  postDeviceCheckName,\r\n  getDeviceShieldListPage,\r\n  postDeviceShieldAdd,\r\n  postDeviceShieldUpdate,\r\n  postDeviceShieldCheck,\r\n} from '@/api/device'\r\nimport {\r\n  reg_thirteen,\r\n  reg_thirteen_one,\r\n  twenty_four,\r\n  reg_two,\r\n  reg_seven,\r\n  twenty_one,\r\n  twenty_two,\r\n  twenty_three,\r\n  isJSON,\r\n} from '@/util/util.js'\r\nimport { json } from 'body-parser'\r\n\r\nexport default {\r\n  name: 'Device',\r\n  components: {\r\n    IotPagination,\r\n    IotTable,\r\n    relation,\r\n    IotButton,\r\n    IotDialog,\r\n    IotForm,\r\n  },\r\n  data() {\r\n    return {\r\n      deviceSn: '',\r\n      columns: [\r\n        {\r\n          label: '组名称',\r\n          prop: 'groupName',\r\n        },\r\n        { label: '产品Key', prop: 'productKey' },\r\n        { label: '设备SN号', prop: 'deviceSn' },\r\n        {\r\n          label: '设备类型',\r\n          prop: 'deviceTypeName',\r\n        },\r\n        {\r\n          label: '操作时间',\r\n          prop: 'operatorTime',\r\n        },\r\n        { label: '操作对象', prop: 'operatorObject' },\r\n\r\n        {\r\n          label: '创建时间',\r\n          prop: 'createTime',\r\n        },\r\n        {\r\n          label: '更新时间',\r\n          prop: 'updateTime',\r\n        },\r\n        {\r\n          label: '操作',\r\n          prop: 'operation',\r\n          slotName: 'operation',\r\n          width: 180,\r\n        },\r\n      ],\r\n      enableList: [\r\n        {\r\n          value: 0,\r\n          label: '未启用',\r\n        },\r\n        {\r\n          value: 1,\r\n          label: '已启用',\r\n        },\r\n      ],\r\n      upLinkList: [],\r\n      downLinkList: [],\r\n\r\n      deviceTypeList: [\r\n        {\r\n          id: 1,\r\n          name: '主设备',\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '副设备',\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '不区分',\r\n        },\r\n      ],\r\n      tempDownLinkList: [],\r\n      tableData: [],\r\n      pagination: {\r\n        current: 1, // 当前页\r\n        total: 0, // 记录条数\r\n        pages: 0, // 总页数\r\n        sizes: [10, 20, 50, 100], // 每一页的数量  [10, 20, 50, 100]\r\n        size: 10,\r\n      },\r\n      // 加载效果开关\r\n      loading: false,\r\n      dialogWidth: '792px',\r\n      type: 1, //1 查看 2 删除\r\n      visible: false,\r\n      inputHolder: '请输入搜索关键词',\r\n      selectHolder: '请选择设备名称',\r\n      searchValue: {\r\n        productId: '',\r\n      },\r\n      // 产品列表\r\n      productOptions: [],\r\n      productOptionsCopy: [],\r\n      // 设备列表\r\n      deviceOptions: [\r\n        {\r\n          id: '2',\r\n          name: '设备厂商',\r\n        },\r\n      ],\r\n      statusCount: {\r\n        totalNum: 0,\r\n        activeNum: 0,\r\n        onlineNum: 0,\r\n      },\r\n      // 表单数据\r\n      deviceForm: {\r\n        deviceSn: '', // 设备sn\r\n        deviceType: '', //设备类型\r\n      },\r\n\r\n      deviceSnTrue: true,\r\n\r\n      rules: {\r\n        deviceSn: [\r\n          {\r\n            required: true,\r\n            // message: '支持中文、英文、数字、下划线的组合，最多不超过32个字符',\r\n            trigger: 'blur',\r\n            validator: this.checkDeviceSN,\r\n          },\r\n        ],\r\n        deviceType: [\r\n          {\r\n            required: true,\r\n            trigger: 'change',\r\n            message: '须选择设备类型',\r\n          },\r\n        ],\r\n      },\r\n      title: '',\r\n      delId: '',\r\n      // 多选删除\r\n      delIds: [],\r\n    }\r\n  },\r\n  created() {},\r\n  watch: {\r\n    visible(val) {\r\n      if (!val && this.type == 1) {\r\n        this.deviceForm = {}\r\n        this.$refs['deviceForm'] && this.$refs['deviceForm'].resetFields()\r\n      }\r\n    },\r\n  },\r\n  // keepalive 生命周期      //组件激活时触发\r\n  mounted() {\r\n    // if (this.$route.query.id) {\r\n    //   this.searchValue.productId = this.$route.query.id\r\n    // }\r\n    let data = {\r\n      current: this.pagination.current,\r\n      size: this.pagination.size,\r\n    }\r\n\r\n    this.fn_get_table_data(data)\r\n  },\r\n  methods: {\r\n    handleReset() {\r\n      this.pagination.current = 1\r\n      this.fn_get_table_data()\r\n    },\r\n    relationDevice(id) {\r\n      this.$refs.relation.open(id)\r\n    },\r\n    fn_sub10(str) {\r\n      if (str) return str.length > 20 ? `${str.substr(0, 20)}...` : str\r\n    },\r\n    fn_notNull(val) {\r\n      return val !== 0 && !val\r\n    },\r\n    // 输入框icon查询\r\n    fn_handle__query() {\r\n      let params = {\r\n        deviceSn: this.deviceSn,\r\n        current: 1,\r\n        size: this.pagination.size,\r\n      }\r\n      this.fn_get_table_data(params)\r\n    },\r\n    handleClear() {\r\n      this.fn_get_table_data()\r\n    },\r\n    calcul_long_text(val = '') {\r\n      return val.replace(/[\\u0391-\\uFFE5\\u0800-\\u4e00 ]/g, 'aa').length\r\n    },\r\n    // 名称校验\r\n    checkDeviceName(rule, value, callback) {\r\n      let flag = false\r\n      postDeviceCheckName({\r\n        deviceName: value,\r\n        id: this.deviceForm.id,\r\n      }).then((res) => {\r\n        if (res.code == 200) {\r\n          flag = res.data\r\n        }\r\n\r\n        if (this.fn_notNull(value)) {\r\n          return callback(new Error('请输入连接器名称'))\r\n        } else if (!reg_thirteen_one(value)) {\r\n          return callback(\r\n            new Error(\r\n              '支持英文字母、数字、下划线(_)、中划线(-)、点号(.)、半冒号(:)和特殊字符(@)、长度限制为6-16个字符'\r\n            )\r\n          )\r\n        } else if (flag) {\r\n          return callback(new Error('接入连接器名称不充许重复'))\r\n        } else {\r\n          callback()\r\n        }\r\n      })\r\n    },\r\n    // 名称校验\r\n    checkVendorName(rule, value, callback) {\r\n      if (this.fn_notNull(value)) {\r\n        return callback(new Error('请输入设备厂商'))\r\n      } else if (!twenty_three(value)) {\r\n        return callback(\r\n          new Error(\r\n            '支持中文、英文字母、数字、和特殊字符_-@()，长度限制 4~64个字符，中文算 2 个字符'\r\n          )\r\n        )\r\n      } else {\r\n        callback()\r\n      }\r\n    },\r\n    checkApplicationType(rule, value, callback) {\r\n      if (this.fn_notNull(value)) {\r\n        return callback(new Error('请输入应用类型'))\r\n      } else if (!twenty_two(value, 32)) {\r\n        return callback(new Error('支持英文字母，数字组合，长度限制2-32个字符'))\r\n      } else {\r\n        callback()\r\n      }\r\n    },\r\n    checkDeviceSN(rule, value, callback) {\r\n      let flag = false\r\n      postDeviceShieldCheck({\r\n        deviceSn: this.deviceForm.deviceSn,\r\n        id: this.deviceForm.id,\r\n      }).then((res) => {\r\n        if (res.code == 200) {\r\n          flag = res.data\r\n        }\r\n        if (this.fn_notNull(value)) {\r\n          return callback(new Error('请输入设备SN'))\r\n        } else if (value.length > 30 || value.length < 4) {\r\n          return callback(new Error('长度限制为4-30个字符；'))\r\n        } else if (flag) {\r\n          return callback(new Error('设备SN不充许重复'))\r\n        } else {\r\n          callback()\r\n        }\r\n      })\r\n    },\r\n\r\n    checkConfigLength(rule, value, callback) {\r\n      value = JSON.stringify(value)\r\n\r\n      if (isJSON(value)) {\r\n        if (!reg_seven(value, 2001)) {\r\n          return callback(new Error('最多不超过2000个字符'))\r\n        } else {\r\n          callback()\r\n        }\r\n      } else {\r\n        return callback(new Error('请输入正确的JSON格式'))\r\n      }\r\n    },\r\n    checkLength(rule, value, callback) {\r\n      if (!reg_seven(value, 201)) {\r\n        return callback(new Error('最多不超过200个字符'))\r\n      } else {\r\n        callback()\r\n      }\r\n    },\r\n    fn_get_device_status_count() {\r\n      getDeviceStatusNum().then((res) => {\r\n        this.statusCount = res.data\r\n      })\r\n    },\r\n    // 设备列表-表格数据接口\r\n    fn_get_table_data(params = {}) {\r\n      let others = { ...params }\r\n      if (!params.size) {\r\n        others.size = 10\r\n        others.current = 1\r\n      }\r\n      getDeviceShieldListPage(others)\r\n        .then((res) => {\r\n          if (res.code == 200) {\r\n            setTimeout(() => {\r\n              this.loading = false\r\n            }, 300)\r\n            this.tableData = res.data.records\r\n            this.pagination.total = res.data.total\r\n            this.pagination.current = res.data.current\r\n            this.pagination.pages = res.data.pages\r\n            this.pagination.size = res.data.size\r\n          } else {\r\n            this.$newNotify.error({\r\n              message: res.message,\r\n            })\r\n          }\r\n        })\r\n        .finally(() => {\r\n          setTimeout(() => {\r\n            this.loading = false\r\n          }, 300)\r\n        })\r\n    },\r\n    // 表单验证触发\r\n    fn_validate(name, value) {\r\n      if (name === 'deviceSn') {\r\n        this.deviceSnTrue = value\r\n      }\r\n    },\r\n    // 多选数据\r\n    fn_select_more_data(data) {\r\n      // console.log(data)\r\n      this.delIds = data.map((item) => {\r\n        return item.id\r\n      })\r\n    },\r\n    // 多选删除按钮\r\n    fn_del_more_data() {\r\n      if (this.delIds.length !== 0) {\r\n        this.columns[0].visible = true\r\n      } else {\r\n        return\r\n      }\r\n    },\r\n    // 多选删除确定按钮\r\n    fn_del_sure() {\r\n      let data = {\r\n        ids: this.delIds.join(','),\r\n      }\r\n      this.fn_del_table_data(data)\r\n    },\r\n    // 打开dialog\r\n    fn_open() {\r\n      this.title = '添加设备'\r\n      this.type = 1\r\n      this.dialogWidth = '792px'\r\n      this.visible = true\r\n    },\r\n    fn_edit(row) {\r\n      this.deviceForm = JSON.parse(JSON.stringify(row))\r\n      this.title = '编辑设备'\r\n      this.type = 1\r\n      this.dialogWidth = '792px'\r\n      this.visible = true\r\n    },\r\n\r\n    fn_format_select(list, id = '') {\r\n      return list.map((item) => {\r\n        return {\r\n          id: item.id,\r\n          converterName: item.converterName,\r\n          bindStatusName: item.bindStatusName,\r\n          disabled: item.bindStatusName == '已绑定' && item.id != id,\r\n        }\r\n      })\r\n    },\r\n    // 搜索\r\n    fn_search_table_data(params) {\r\n      console.log(params)\r\n      if (params.id === '1') {\r\n        this.searchValue.aliasName = params.value\r\n      } else {\r\n        this.searchValue.deviceName = params.value\r\n      }\r\n      let data = { ...this.searchValue }\r\n      data.size = this.pagination.size\r\n      this.fn_get_table_data(data)\r\n    },\r\n    // 当前页总条数\r\n    handleSizeChange(val) {\r\n      // console.log(`每页 ${val} 条`)\r\n      this.pagination.size = val\r\n      let params = {\r\n        size: this.pagination.size,\r\n        current: 1,\r\n      }\r\n      this.fn_get_table_data(params)\r\n    },\r\n    // 当前页\r\n    handleCurrentChange(val) {\r\n      // console.log(`当前页: ${val}`)\r\n      this.pagination.current = val\r\n      let params = {\r\n        ...this.searchValue,\r\n        current: this.pagination.current,\r\n        size: this.pagination.size,\r\n      }\r\n      this.fn_get_table_data(params)\r\n    },\r\n    // 清除输入搜索\r\n    fn_clear_search_info() {\r\n      this.searchValue.aliasName = ''\r\n      this.searchValue.deviceName = ''\r\n      let data = { ...this.searchValue }\r\n      this.fn_get_table_data(data)\r\n    },\r\n    // 查看详情的跳转\r\n    fn_check(data, num) {\r\n      this.$router.push({\r\n        path: '/groupDeviceDetail',\r\n        query: {\r\n          id: data,\r\n          num: num,\r\n        },\r\n      })\r\n    },\r\n    // 弹窗确认按钮\r\n    fn_sure() {\r\n      // 删除确认\r\n      if (this.type === 2) {\r\n        let data = {\r\n          id: this.delId,\r\n        }\r\n        postDeviceShieldDelete(data).then((res) => {\r\n          if (res.code == 200) {\r\n            this.$newNotify.success({\r\n              message: res.message,\r\n            })\r\n            this.pagination.current = 1\r\n            // this.searchValue.productId = ''\r\n            // this.fn_get_device_status_count()\r\n            this.fn_get_table_data({\r\n              size: this.pagination.size,\r\n              current: 1,\r\n            })\r\n            this.visible = false\r\n          } else {\r\n            this.$newNotify.error({\r\n              message: res.message,\r\n            })\r\n          }\r\n        })\r\n      }\r\n      // 新增确认\r\n      else if (this.type === 1) {\r\n        this.$refs['deviceForm'].validate((valid) => {\r\n          if (valid) {\r\n            let postUrl = this.deviceForm.id\r\n              ? postDeviceShieldUpdate\r\n              : postDeviceShieldAdd\r\n\r\n            postUrl(this.deviceForm).then((res) => {\r\n              if (res.code == 200) {\r\n                this.$newNotify.success({\r\n                  message: res.message,\r\n                })\r\n                this.pagination.current = 1\r\n                // this.searchValue.productId = ''\r\n                // this.fn_get_device_status_count()\r\n                this.fn_get_table_data({\r\n                  size: this.pagination.size,\r\n                  current: 1,\r\n                })\r\n                this.visible = false\r\n                this.fn_get_upLink_select()\r\n                this.fn_get_downLink_select()\r\n              } else {\r\n                this.$newNotify.error({\r\n                  message: res.message,\r\n                })\r\n              }\r\n            })\r\n          }\r\n        })\r\n      }\r\n    },\r\n    fn_close() {\r\n      this.deviceSnTrue = true\r\n    },\r\n    // 行删除\r\n    fn_del(id) {\r\n      this.delId = id\r\n      this.title = '确定删除该组设备？'\r\n      this.type = 2\r\n      this.dialogWidth = '550px'\r\n      this.visible = true\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.device {\r\n  padding-bottom: 20px;\r\n  .device-top {\r\n    font-family: HarmonyOS Sans SC;\r\n    .device-top-count {\r\n      margin-top: 18px;\r\n      .el-select {\r\n        margin-right: 48px;\r\n      }\r\n      /deep/ .el-input__inner {\r\n        border-radius: 0;\r\n      }\r\n      .point {\r\n        margin: 0 10px;\r\n      }\r\n      p {\r\n        display: inline-block;\r\n        font-size: 14px;\r\n        line-height: 16px;\r\n        letter-spacing: 1px;\r\n        font-weight: normal;\r\n      }\r\n      span {\r\n        font-size: 18px;\r\n        line-height: 20px;\r\n        margin: 0 6px;\r\n        font-weight: 500;\r\n      }\r\n    }\r\n    .device-top-search {\r\n      margin: 18px 0 18px 0;\r\n      width: 100%;\r\n      display: flex;\r\n      justify-content: space-between;\r\n    }\r\n  }\r\n  .device-table {\r\n    .table-edit {\r\n      display: flex;\r\n      align-items: center;\r\n      p {\r\n        flex-shrink: 0;\r\n        cursor: pointer;\r\n      }\r\n      p:nth-child(2) {\r\n        margin: 0px 12px;\r\n        width: 1px;\r\n        height: 13px;\r\n        border: 1px solid #ededed;\r\n      }\r\n      p:nth-child(4) {\r\n        margin: 0px 12px;\r\n        width: 1px;\r\n        height: 13px;\r\n        border: 1px solid #ededed;\r\n      }\r\n      p:nth-child(6) {\r\n        margin: 0px 12px;\r\n        width: 1px;\r\n        height: 13px;\r\n        border: 1px solid #ededed;\r\n      }\r\n    }\r\n    .table-status {\r\n      .status {\r\n        .red {\r\n          background: #ff4d4f;\r\n          width: 8px;\r\n          height: 8px;\r\n          border-radius: 4px;\r\n          margin-top: 8px;\r\n          margin-right: 6px;\r\n        }\r\n        .green {\r\n          background: #00c250;\r\n          width: 8px;\r\n          height: 8px;\r\n          border-radius: 4px;\r\n          margin-top: 8px;\r\n          margin-right: 6px;\r\n        }\r\n        .yellow {\r\n          background: #e6a23c;\r\n          width: 8px;\r\n          height: 8px;\r\n          border-radius: 4px;\r\n          margin-top: 8px;\r\n          margin-right: 6px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .deviceForm {\r\n    /deep/ .el-form-item {\r\n      margin-bottom: 24px;\r\n    }\r\n    .el-form-tips {\r\n      // margin-top: -17px;\r\n      margin-top: -22px;\r\n      margin-bottom: 6px;\r\n    }\r\n  }\r\n  .device-bottom {\r\n    text-align: right;\r\n    margin-top: 14px;\r\n  }\r\n  .del-tips {\r\n    width: 420px;\r\n  }\r\n\r\n  .specialDesc {\r\n    padding: 11px 0 11px 14px;\r\n    background-color: rgba(1, 138, 255, 0.08);\r\n    margin-bottom: 18px;\r\n    span {\r\n      font-size: 12px;\r\n      line-height: 14px;\r\n    }\r\n    img {\r\n      display: inline-block;\r\n      width: 14px;\r\n      height: 14px;\r\n      line-height: 14px;\r\n      margin-right: 10px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=1198ccd4&scoped=true&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=1198ccd4&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1198ccd4\",\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}