{"version": 3, "sources": ["webpack:///./src/util/toVW.js", "webpack:///./src/components/iot-dialog/index.vue?07b8", "webpack:///src/components/iot-dialog/index.vue", "webpack:///./src/components/iot-dialog/index.vue?07f9", "webpack:///./src/components/iot-dialog/index.vue", "webpack:///./src/components/iot-pagination/index.vue?161d", "webpack:///./src/components/iot-dialog/index.vue?2ab7", "webpack:///./src/assets/images/empty/empty.png", "webpack:///./src/components/iot-table/index.vue?bb4b", "webpack:///src/components/iot-table/index.vue", "webpack:///./src/components/iot-table/index.vue?be0a", "webpack:///./src/components/iot-table/index.vue", "webpack:///./src/components/iot-pagination/index.vue?3593", "webpack:///src/components/iot-pagination/index.vue", "webpack:///./src/components/iot-pagination/index.vue?8244", "webpack:///./src/components/iot-pagination/index.vue", "webpack:///./src/components/iot-button/index.vue?571c", "webpack:///./src/components/iot-button/index.vue?9810", "webpack:///src/components/iot-button/index.vue", "webpack:///./src/components/iot-button/index.vue?972b", "webpack:///./src/components/iot-button/index.vue", "webpack:///./src/views/deviceGroup/list/connectorDetail/index.vue?6b9a", "webpack:///./node_modules/core-js/modules/es.array.map.js", "webpack:///./src/views/deviceGroup/list/connectorDetail/index.vue?8c05", "webpack:///src/views/deviceGroup/list/connectorDetail/index.vue", "webpack:///./src/views/deviceGroup/list/connectorDetail/index.vue?fb52", "webpack:///./src/views/deviceGroup/list/connectorDetail/index.vue", "webpack:///./src/components/iot-table/index.vue?3309"], "names": ["num", "render", "_vm", "this", "_h", "$createElement", "_c", "_self", "attrs", "top", "title", "dialogVisible", "width", "fn_close", "appendBody", "maskModel", "on", "$event", "staticClass", "style", "maxHeight", "_t", "directives", "name", "rawName", "value", "expression", "btnClass", "comfirmText", "fn_sure", "_e", "staticRenderFns", "components", "IotButton", "props", "type", "String", "default", "visible", "Boolean", "footer", "callbackSure", "Function", "showLoading", "computed", "data", "methods", "$emit", "component", "module", "exports", "columns", "_v", "_s", "selecionData", "length", "fn_del_selection_data", "fn_ignore_selection_data", "fn_handle_selection_data", "_g", "_b", "ref", "staticStyle", "background", "height", "toVW", "handleSelectionChange", "$attrs", "$listeners", "_l", "item", "key", "selectable", "prop", "label", "fixed", "scopedSlots", "_u", "fn", "row", "slotName", "slot", "loading", "dialogWidth", "$set", "text", "proxy", "IotDialog", "Array", "selectionText", "isShowdelete", "isMonitoring", "console", "log", "alarmStatus", "map", "id", "$newNotify", "warning", "message", "toggleSelect", "$refs", "table", "toggleRowSelection", "select", "doLayout", "$nextTick", "pagination", "current", "sizes", "size", "pagerCount", "layout", "total", "handleSizeChange", "handleCurrentChange", "Object", "val", "class", "fn_search", "bgcolor", "$", "$map", "arrayMethodHasSpeciesSupport", "HAS_SPECIES_SUPPORT", "target", "proto", "forced", "callbackfn", "arguments", "undefined", "_m", "connectorDetail", "groupName", "createTime", "updateTime", "tableData", "scope", "configInfo", "fn_sub10", "IotTable", "IotPagination", "pages", "infoForm", "description", "rules", "nameTrue", "<PERSON>c<PERSON><PERSON>", "firmwareJobDetailForm", "firmwareJobStatic", "firmwareJobList", "groupId", "inputHolder", "jobId", "created", "$route", "query", "mounted", "fn_getConnectorDetail", "fn_get_table_data", "str", "res", "code", "deviceName", "params", "others", "fn_notNull", "handleSearch", "from", "fn_clear_search_info", "checkName", "callback", "Error", "fn_validate", "checkLength"], "mappings": "kHAMe,gBAAUA,GACvB,MAAmB,kBAARA,EACT,UAAWA,EAAM,KAAQ,IAAzB,MACYA,I,oCCThB,IAAIC,EAAS,WAAa,IAAIC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,YAAY,CAACE,MAAM,CAAC,wBAAuB,EAAM,eAAe,aAAa,IAAMN,EAAIO,IAAI,MAAQP,EAAIQ,MAAM,QAAUR,EAAIS,cAAc,MAAQT,EAAIU,MAAM,eAAeV,EAAIW,SAAS,iBAAiBX,EAAIY,WAAW,MAAQZ,EAAIa,WAAWC,GAAG,CAAC,iBAAiB,SAASC,GAAQf,EAAIS,cAAcM,KAAU,CAACX,EAAG,MAAM,CAACY,YAAY,qBAAqBC,MAAM,CAAGC,UAAWlB,EAAIkB,YAAc,CAAClB,EAAImB,GAAG,SAAS,GAAInB,EAAU,OAAEI,EAAG,MAAM,CAACY,YAAY,UAAU,CAACZ,EAAG,aAAa,CAACgB,WAAW,CAAC,CAACC,KAAK,WAAWC,QAAQ,aAAaC,MAAM,IAAMC,WAAW,QAAQlB,MAAM,CAAC,KAAO,KAAK,KAAO,SAASQ,GAAG,CAAC,OAASd,EAAIW,YAAYP,EAAG,aAAa,CAACgB,WAAW,CAAC,CAACC,KAAK,WAAWC,QAAQ,aAAaC,MAAM,IAAMC,WAAW,QAAQlB,MAAM,CAAC,KAAON,EAAIyB,SAAS,KAAOzB,EAAI0B,aAAaZ,GAAG,CAAC,OAASd,EAAI2B,YAAY,GAAG3B,EAAI4B,QAC93BC,EAAkB,G,YCyCtB,GACER,KAAM,YACNS,WAAY,CACVC,UAAJ,QAEEC,MAAO,CACLzB,IAAK,CACH0B,KAAMC,OACNC,QAAS,QAEXjB,UAAW,CACTe,KAAMC,OACNC,QAAS,QAEX3B,MAAO,CACLyB,KAAMC,OACNC,QAAS,MAEXC,QAAS,CACPH,KAAMI,QACNF,SAAS,GAEXzB,MAAO,CACLuB,KAAMC,OACNC,QAAS,OAEXG,OAAQ,CACNL,KAAMI,QACNF,SAAS,GAEXvB,WAAY,CACVqB,KAAMI,QACNF,SAAS,GAEXI,aAAcC,SACdd,YAAa,CACXO,KAAMC,OACNC,QAAS,OAEXtB,UAAW,CACToB,KAAMI,QACNF,SAAS,GAEXM,YAAa,CACXR,KAAMI,QACNF,SAAS,GAEXV,SAAU,CACRQ,KAAMC,OACNC,QAAS,YAGbO,SAAU,CACRjC,cADJ,WAEM,OAAOR,KAAKmC,UAGhBO,KAzDF,WA0DI,MAAO,IAETC,QAAS,CAEPjC,SAFJ,WAGMV,KAAK4C,MAAM,kBAAkB,GAC7B5C,KAAK4C,MAAM,UAGblB,QAPJ,WAWM1B,KAAK4C,MAAM,mBCjH6U,I,wBCQ1VC,EAAY,eACd,EACA/C,EACA8B,GACA,EACA,KACA,WACA,MAIa,OAAAiB,E,6CCnBf,W,kCCAA,W,uBCAAC,EAAOC,QAAU,IAA0B,0B,oCCA3C,IAAIjD,EAAS,WAAa,IAAIC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACY,YAAY,aAAa,CAAEhB,EAAIiD,QAAQ,GAAgB,cAAE7C,EAAG,MAAM,CAACY,YAAY,uBAAuB,CAACZ,EAAG,IAAI,CAACJ,EAAIkD,GAAG,QAAQlD,EAAImD,GAAGnD,EAAIoD,aAAaC,QAAQ,UAAWrD,EAAIiD,QAAQ,GAAe,aAAE7C,EAAG,IAAI,CAACgB,WAAW,CAAC,CAACC,KAAK,WAAWC,QAAQ,eAAeN,YAAY,SAASF,GAAG,CAAC,MAAQd,EAAIsD,wBAAwB,CAACtD,EAAIkD,GAAG,UAAUlD,EAAI4B,KAAM5B,EAAIiD,QAAQ,GAAe,aAAE7C,EAAG,IAAI,CAACgB,WAAW,CAAC,CAACC,KAAK,WAAWC,QAAQ,eAAeN,YAAY,SAASF,GAAG,CAAC,MAAQd,EAAIuD,2BAA2B,CAACvD,EAAIkD,GAAG,YAAYlD,EAAI4B,KAAKxB,EAAG,KAAMJ,EAAIiD,QAAQ,GAAe,aAAE7C,EAAG,IAAI,CAACgB,WAAW,CAAC,CAACC,KAAK,WAAWC,QAAQ,eAAeN,YAAY,SAASF,GAAG,CAAC,MAAQd,EAAIwD,2BAA2B,CAACxD,EAAIkD,GAAG,YAAYlD,EAAI4B,KAAK5B,EAAImB,GAAG,mBAAmB,GAAGnB,EAAI4B,KAAKxB,EAAG,WAAWJ,EAAIyD,GAAGzD,EAAI0D,GAAG,CAACtC,WAAW,CAAC,CAACC,KAAK,UAAUC,QAAQ,YAAYC,MAAOvB,EAAW,QAAEwB,WAAW,YAAYmC,IAAI,QAAQC,YAAY,CAAC,MAAQ,QAAQtD,MAAM,CAAC,aAAa,IAAI,KAAON,EAAI2C,KAAK,0BAA0B,kBAAkB,oBAAoB,CAAEkB,WAAY,WAAY,YAAY,CAAEC,OAAQ9D,EAAI+D,KAAK,MAAOjD,GAAG,CAAC,mBAAmBd,EAAIgE,wBAAwB,WAAWhE,EAAIiE,QAAO,GAAOjE,EAAIkE,YAAY,CAAClE,EAAImE,GAAInE,EAAW,SAAE,SAASoE,GAAM,MAAO,CAAEA,EAAS,KAAEhE,EAAG,kBAAkB,CAACiE,IAAID,EAAKnC,KAAK3B,MAAM,CAAC,KAAO8D,EAAKnC,KAAK,MAAQmC,EAAK1D,MAAM,WAAaV,EAAIsE,WAAW,MAAQ,YAAYlE,EAAG,kBAAkB,CAACiE,IAAID,EAAKG,KAAKjE,MAAM,CAAC,MAAQ8D,EAAKI,MAAM,KAAOJ,EAAKG,KAAK,KAAOH,EAAKnC,KAAK,MAAQmC,EAAK1D,MAAM,MAAQ0D,EAAKK,OAAOC,YAAY1E,EAAI2E,GAAG,CAAC,CAACN,IAAI,UAAUO,GAAG,SAASjB,GACvnD,IAAIkB,EAAMlB,EAAIkB,IACd,MAAO,CAAET,EAAa,SAAE,CAACpE,EAAImB,GAAGiD,EAAKU,SAAS,KAAK,CAAC,IAAMD,KAAO,CAACzE,EAAG,OAAO,CAACJ,EAAIkD,GAAGlD,EAAImD,GAAG0B,EAAIT,EAAKG,eAAe,MAAK,SAAWnE,EAAG,WAAW,CAAC2E,KAAK,SAAS,CAAC/E,EAAImB,GAAG,SAAQ,WAAW,MAAO,CAAGnB,EAAIgF,QAA+HhF,EAAI4B,KAA1HxB,EAAG,MAAM,CAACY,YAAY,eAAe,CAACZ,EAAG,MAAM,CAACE,MAAM,CAAC,IAAM,EAAQ,QAAmC,IAAM,aAAqB,IAAI,GAAGF,EAAG,aAAa,CAACE,MAAM,CAAC,MAAQN,EAAIiD,QAAQ,GAAGgC,YAAcjF,EAAIiD,QAAQ,GAAGgC,YAAcjF,EAAI+D,KAAK,KAAK,QAAU/D,EAAIiD,QAAQ,GAAGb,QAAQ,MAAQpC,EAAIiD,QAAQ,GAAGzC,OAAOM,GAAG,CAAC,iBAAiB,SAASC,GAAQ,OAAOf,EAAIkF,KAAKlF,EAAIiD,QAAQ,GAAI,UAAWlC,IAAS,aAAef,EAAI2B,SAAS+C,YAAY1E,EAAI2E,GAAG,CAAC,CAACN,IAAI,OAAOO,GAAG,WAAW,MAAO,CAACxE,EAAG,UAAU,CAACA,EAAG,eAAe,CAACA,EAAG,MAAM,CAACY,YAAY,YAAY,CAAChB,EAAIkD,GAAG,IAAIlD,EAAImD,GAAGnD,EAAIiD,QAAQ,GAAGkC,MAAM,UAAU,KAAKC,OAAM,QAAW,IACtzBvD,EAAkB,G,oCCkGtB,GACER,KAAM,WACNS,WAAY,CACVuD,UAAJ,QAEErD,MAAO,CACLiB,QAAS,CACPhB,KAAMqD,MACNnD,QAAS,WAAf,OACA,CAEUF,KAAM,GACNsD,eAAe,EACfC,cAAc,EACdhF,MAAO,GACP2E,KAAM,GACN/C,SAAS,EACT6C,YAAa,OAAvB,OAAuB,CAAvB,SAIIQ,aAAc,CACZxD,KAAMI,QACNF,SAAS,GAEXQ,KAAM,CACJV,KAAMqD,MACNnD,QAAS,WAAf,WAEI6C,QAAS,CACP/C,KAAMI,QACNF,SAAS,IAGbQ,KAlCF,WAmCI,MAAO,CACLS,aAAc,KAGlBR,QAAS,CACPmB,KAAJ,OACIpC,QAFJ,WAGM1B,KAAK4C,MAAM,qBAEbyB,WALJ,SAKA,KAGM,OAFAoB,QAAQC,IAAI,MAAOd,IAEf5E,KAAKwF,cACgB,GAAnBZ,EAAIe,aAQZ5B,sBAjBJ,SAiBA,GACM0B,QAAQC,IAAIhD,GACZ1C,KAAKmD,aAAeT,EAAKkD,KAAI,SAAnC,GACQ,OAAOzB,EAAK0B,MAEd7F,KAAK4C,MAAM,mBAAoBF,IAGjCW,sBAzBJ,WA0BWrD,KAAKmD,aAAaC,OAOvBpD,KAAK4C,MAAM,gBAAiB5C,KAAKmD,cAN/BnD,KAAK8F,WAAWC,QAAQ,CACtBC,QAAS,aAQf1C,yBApCJ,WAqCWtD,KAAKmD,aAAaC,OAOvBpD,KAAK4C,MAAM,mBAAoB5C,KAAKmD,cANlCnD,KAAK8F,WAAWC,QAAQ,CACtBC,QAAS,aAQfzC,yBA/CJ,WAgDWvD,KAAKmD,aAAaC,OAOvBpD,KAAK4C,MAAM,mBAAoB5C,KAAKmD,cANlCnD,KAAK8F,WAAWC,QAAQ,CACtBC,QAAS,aAOfC,aAzDJ,SAyDA,KACMR,QAAQC,IAAI,OACZ1F,KAAKkG,MAAMC,MAAMC,mBAAmBxB,EAAKyB,IAE3CC,SA7DJ,WA6DA,WACMtG,KAAKuG,WAAU,WACb,EAAR,gCC3M8V,I,wBCQ1V1D,EAAY,eACd,EACA/C,EACA8B,GACA,EACA,KACA,WACA,MAIa,OAAAiB,E,sECnBf,IAAI/C,EAAS,WAAa,IAAIC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACY,YAAY,kBAAkB,CAACZ,EAAG,gBAAgB,CAACE,MAAM,CAAC,eAAeN,EAAIyG,WAAWC,QAAQ,aAAa1G,EAAIyG,WAAWE,MAAM,YAAY3G,EAAIyG,WAAWG,KAAK,cAAc5G,EAAIyG,WAAWI,WAAW,OAAS7G,EAAI8G,OAAO,MAAQ9G,EAAIyG,WAAWM,OAAOjG,GAAG,CAAC,cAAcd,EAAIgH,iBAAiB,iBAAiBhH,EAAIiH,wBAAwB,IACtbpF,EAAkB,GCuBtB,GACER,KAAM,gBACNW,MAAO,CACLyE,WAAY,CACVxE,KAAMiF,OACN/E,QAAS,WAAf,OACA,UACA,QACA,QACA,qBACA,QACA,gBAGI2E,OAAQ,CACN7E,KAAMC,OACNC,QAAS,4CAGbQ,KAnBF,WAoBI,MAAO,IAETC,QAAS,CACPoE,iBADJ,SACA,GAEM/G,KAAK4C,MAAM,cAAesE,IAE5BF,oBALJ,SAKA,GAEMhH,KAAK4C,MAAM,iBAAkBsE,MCrD2T,I,wBCQ1VrE,EAAY,eACd,EACA/C,EACA8B,GACA,EACA,KACA,WACA,MAIa,OAAAiB,E,sECnBf,W,gFCAA,IAAI/C,EAAS,WAAa,IAAIC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,OAAO,CAACgB,WAAW,CAAC,CAACC,KAAK,WAAWC,QAAQ,aAAaC,MAAM,IAAMC,WAAW,QAAQR,YAAY,UAAUoG,MAAM,CAACpH,EAAIiC,KAAO,cAAgBjC,EAAIiC,KAAO,IAAInB,GAAG,CAAC,MAAQd,EAAIqH,YAAY,CAACrH,EAAIkD,GAAGlD,EAAImD,GAAGnD,EAAImF,UAC9StD,EAAkB,GCkBtB,GACER,KAAM,UACNW,MAAO,CACLmD,KAAM,CACJlD,KAAMC,OACNC,QAAS,MAEXmF,QAAS,CACPrF,KAAMC,OACNC,QAAS,IAEXF,KAAM,CACJA,KAAMC,OACNC,QAAS,YAGbQ,KAhBF,WAiBI,MAAO,IAETC,QAAS,CACPyE,UADJ,WAEMpH,KAAK4C,MAAM,aCxC6U,I,wBCQ1VC,EAAY,eACd,EACA/C,EACA8B,GACA,EACA,KACA,WACA,MAIa,OAAAiB,E,2CCnBf,W,kCCCA,IAAIyE,EAAI,EAAQ,QACZC,EAAO,EAAQ,QAAgC3B,IAC/C4B,EAA+B,EAAQ,QAEvCC,EAAsBD,EAA6B,OAKvDF,EAAE,CAAEI,OAAQ,QAASC,OAAO,EAAMC,QAASH,GAAuB,CAChE7B,IAAK,SAAaiC,GAChB,OAAON,EAAKvH,KAAM6H,EAAYC,UAAU1E,OAAS,EAAI0E,UAAU,QAAKC,O,yCCZxE,IAAIjI,EAAS,WAAa,IAAIC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACY,YAAY,eAAe,CAACZ,EAAG,MAAM,CAACY,YAAY,aAAa,CAAChB,EAAIiI,GAAG,GAAG7H,EAAG,MAAM,CAACY,YAAY,eAAe,CAACZ,EAAG,MAAM,CAACY,YAAY,aAAa,CAACZ,EAAG,MAAM,CAACY,YAAY,QAAQ,CAACZ,EAAG,OAAO,CAACJ,EAAIkD,GAAG,SAAS9C,EAAG,OAAO,CAACJ,EAAIkD,GAAGlD,EAAImD,GAAGnD,EAAIkI,gBAAgBC,gBAAgB/H,EAAG,MAAM,CAACY,YAAY,QAAQ,CAACZ,EAAG,OAAO,CAACJ,EAAIkD,GAAG,SAAS9C,EAAG,OAAO,CAACJ,EAAIkD,GAAGlD,EAAImD,GAAGnD,EAAIkI,gBAAgBpC,WAAW1F,EAAG,MAAM,CAACY,YAAY,aAAa,CAACZ,EAAG,MAAM,CAACY,YAAY,QAAQ,CAACZ,EAAG,OAAO,CAACJ,EAAIkD,GAAG,UAAU9C,EAAG,OAAO,CAACJ,EAAIkD,GAAGlD,EAAImD,GAAGnD,EAAIkI,gBAAgBE,iBAAiBhI,EAAG,MAAM,CAACY,YAAY,QAAQ,CAACZ,EAAG,OAAO,CAACJ,EAAIkD,GAAG,UAAU9C,EAAG,OAAO,CAACJ,EAAIkD,GAAGlD,EAAImD,GAAGnD,EAAIkI,gBAAgBG,mBAAmBjI,EAAG,MAAM,CAACY,YAAY,aAAa,CAACZ,EAAG,MAAM,CAACY,YAAY,QAAQ,CAACZ,EAAG,OAAO,CAACJ,EAAIkD,GAAG,UAAU9C,EAAG,OAAO,CAACJ,EAAIkD,GAAGlD,EAAImD,GAAGnD,EAAIkI,gBAAgBE,uBAAuBhI,EAAG,MAAM,CAACY,YAAY,gBAAgB,CAAChB,EAAIiI,GAAG,GAAG7H,EAAG,MAAM,CAACY,YAAY,iBAAiB,CAACZ,EAAG,YAAY,CAACE,MAAM,CAAC,QAAUN,EAAIiD,QAAQ,KAAOjD,EAAIsI,UAAU,QAAUtI,EAAIgF,SAASN,YAAY1E,EAAI2E,GAAG,CAAC,CAACN,IAAI,aAAaO,GAAG,SAAS2D,GAAO,MAAO,CAACnI,EAAG,aAAa,CAACE,MAAM,CAAC,UAAY,YAAY,OAAS,QAAQ,eAAe,iBAAiBoE,YAAY1E,EAAI2E,GAAG,CAAC,CAACN,IAAI,UAAUO,GAAG,WAAW,MAAO,CAACxE,EAAG,OAAO,CAACJ,EAAIkD,GAAG,IAAIlD,EAAImD,GAAGoF,EAAM1D,IAAI2D,YAAY,SAASpD,OAAM,IAAO,MAAK,IAAO,CAAChF,EAAG,MAAM,CAACY,YAAY,wBAAwB,CAACZ,EAAG,IAAI,CAACJ,EAAIkD,GAAG,IAAIlD,EAAImD,GAAGnD,EAAIyI,SAASF,EAAM1D,IAAI2D,aAAa,mBAAmB,GAAIxI,EAAIsI,UAAUjF,OAAS,EAAGjD,EAAG,MAAM,CAACY,YAAY,kBAAkB,CAACZ,EAAG,iBAAiB,CAACE,MAAM,CAAC,WAAaN,EAAIyG,YAAY3F,GAAG,CAAC,cAAcd,EAAIgH,iBAAiB,iBAAiBhH,EAAIiH,wBAAwB,GAAGjH,EAAI4B,UAC5tDC,EAAkB,CAAC,WAAa,IAAI7B,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACY,YAAY,mBAAmB,CAACZ,EAAG,MAAM,CAACY,YAAY,QAAQ,CAACZ,EAAG,IAAI,CAACJ,EAAIkD,GAAG,aAAa9C,EAAG,MAAM,CAACY,YAAY,aAAa,WAAa,IAAIhB,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACY,YAAY,uBAAuB,CAACZ,EAAG,MAAM,CAACY,YAAY,QAAQ,CAACZ,EAAG,MAAM,CAACY,YAAY,iBAAiB,CAACZ,EAAG,OAAO,CAACJ,EAAIkD,GAAG,iBAAiB9C,EAAG,MAAM,CAACY,YAAY,c,oFCyFnf,GACEK,KAAM,aACNS,WAAY,CACV4G,SAAJ,OACIC,cAAJ,QAEEhG,KANF,WAOI,MAAO,CACLM,QAAS,CACf,CAAQ,MAAR,QAAQ,KAAR,cACA,CAAQ,MAAR,OAAQ,KAAR,YACA,CAAQ,MAAR,OAAQ,KAAR,cACA,CAAQ,MAAR,OAAQ,KAAR,gBACA,CAAQ,MAAR,OAAQ,KAAR,kBACA,CAAQ,MAAR,OAAQ,KAAR,cACA,CAAQ,MAAR,OAAQ,KAAR,eAEMqF,UAAW,GACXtD,SAAS,EACTyB,WAAY,CACVC,QAAS,EACTK,MAAO,EACP6B,MAAO,EACPjC,MAAO,CAAC,GAAI,GAAI,GAAI,KACpBC,KAAM,GAERxE,SAAS,EACT5B,MAAO,SACPyE,YAAa,QACb4D,SAAU,CACR/C,GAAI,EACJzE,KAAM,GACNyH,YAAa,IAEfC,MAAO,CACL1H,KAAM,CACd,CACU,UAAV,EACU,QAAV,OAEU,UAAV,iBAGQyH,YAAa,CACrB,CACU,UAAV,EACU,QAAV,OACU,UAAV,oBAIME,UAAU,EACVC,UAAU,EACVf,gBAAiB,GACjBgB,sBAAuB,GACvBC,kBAAmB,GACnBC,gBAAiB,GACjBC,QAAS,GACTC,YAAa,SACbC,MAAO,KAGXC,QA9DF,WA+DIvJ,KAAKoJ,QAAUpJ,KAAKwJ,OAAOC,MAAM5D,IAEnC6D,QAjEF,WAkEI1J,KAAK2J,wBAEL3J,KAAK4J,qBAEPjH,QAAS,CACP6F,SADJ,SACA,GACM,OAAOqB,EAAIzG,OAAS,GAAK,GAA/B,gCAEIuG,sBAJJ,WAIA,WACM,OAAN,OAAM,CAAN,qCACQ,GAAgB,KAAZG,EAAIC,KAAa,CACnB,IAAV,GACYlE,GAAIiE,EAAIpH,KAAKmD,GACbtF,MAAOuJ,EAAIpH,KAAKsH,YAElB,EAAV,mCACU,EAAV,4BAKIJ,kBAjBJ,WAiBA,2FACA,uBACWK,EAAOtD,OACVuD,EAAOvD,KAAO,GACduD,EAAOzD,QAAU,GAEnB,OAAN,OAAM,CAAN,GACA,kBACA,aACU,YAAV,WACY,EAAZ,aACA,KACU,EAAV,yBACU,EAAV,8BACU,EAAV,kCACU,EAAV,8BACU,EAAV,6BAEU,EAAV,kBACY,QAAZ,eAIA,oBACQ,YAAR,WACU,EAAV,aACA,SAKIM,iBAhDJ,SAgDA,GACM/G,KAAKwG,WAAWC,QAAU,EAC1BzG,KAAKwG,WAAWG,KAAOO,EACvBlH,KAAK4J,kBAAkB,CACrBR,QAASpJ,KAAKoJ,QACdzC,KAAM3G,KAAKwG,WAAWG,KACtBF,QAASzG,KAAKwG,WAAWC,WAI7BO,oBA1DJ,SA0DA,GACMvB,QAAQC,IAAI1F,KAAKwG,YACjBxG,KAAKwG,WAAWC,QAAUS,EAC1BlH,KAAK4J,kBAAkB,CACrBR,QAASpJ,KAAKoJ,QACdzC,KAAM3G,KAAKwG,WAAWG,KACtBF,QAASzG,KAAKwG,WAAWC,WAG7B0D,WAnEJ,SAmEA,GACM,OAAe,IAARjD,IAAcA,GAGvBkD,aAvEJ,SAuEA,GACMpK,KAAKgK,WAAaK,EAAK/I,MACvBtB,KAAK4J,kBAAkB,CACrBR,QAASpJ,KAAKoJ,QACdY,WAAYK,EAAK/I,SAGrBgJ,qBA9EJ,WA+EMtK,KAAKgK,WAAa,GAClBhK,KAAK4J,kBAAkB,CACrBR,QAASpJ,KAAKoJ,WAIlBmB,UArFJ,SAqFA,OACM,OAAIvK,KAAKmK,WAAW7I,GACXkJ,EAAS,IAAIC,MAAM,YAClC,uBAOQD,IANOA,EACf,UACA,+DAQIE,YAnGJ,SAmGA,KACmB,SAATtJ,IACFpB,KAAK+I,SAAWzH,GAEL,gBAATF,IACFpB,KAAKgJ,SAAW1H,IAIpBqJ,YA5GJ,SA4GA,OACM,IAAK,OAAX,OAAW,CAAX,OACQ,OAAOH,EAAS,IAAIC,MAAM,gBAE1BD,OChRoX,I,wBCQxX3H,EAAY,eACd,EACA/C,EACA8B,GACA,EACA,KACA,WACA,MAIa,aAAAiB,E,2CCnBf,W", "file": "js/chunk-b5da5b02.ff0ba683.js", "sourcesContent": ["/**\r\n *  px 转 vw\r\n * @param {\r\n * } num\r\n * @returns string\r\n */\r\nexport default function (num) {\r\n  if (typeof num === \"number\") {\r\n    return `${(num / 1920) * 100}vw`;\r\n  } else return num;\r\n}\r\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('el-dialog',{attrs:{\"close-on-click-modal\":false,\"custom-class\":\"iot-dialog\",\"top\":_vm.top,\"title\":_vm.title,\"visible\":_vm.dialogVisible,\"width\":_vm.width,\"before-close\":_vm.fn_close,\"append-to-body\":_vm.appendBody,\"modal\":_vm.maskModel},on:{\"update:visible\":function($event){_vm.dialogVisible=$event}}},[_c('div',{staticClass:\"iot-dialog-content\",style:({ maxHeight: _vm.maxHeight })},[_vm._t(\"body\")],2),(_vm.footer)?_c('div',{staticClass:\"footer\"},[_c('iot-button',{directives:[{name:\"throttle\",rawName:\"v-throttle\",value:(500),expression:\"500\"}],attrs:{\"text\":\"取消\",\"type\":\"white\"},on:{\"search\":_vm.fn_close}}),_c('iot-button',{directives:[{name:\"throttle\",rawName:\"v-throttle\",value:(500),expression:\"500\"}],attrs:{\"type\":_vm.btnClass,\"text\":_vm.comfirmText},on:{\"search\":_vm.fn_sure}})],1):_vm._e()])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<!--\r\n * @Description: 1.0\r\n * @Version: 1.0\r\n * @Autor: hs\r\n * @Date: 2021-11-08 15:38:52\r\n * @LastEditors: hs\r\n * @LastEditTime: 2021-11-17 10:52:27\r\n-->\r\n<template>\r\n  <el-dialog\r\n    :close-on-click-modal=\"false\"\r\n    custom-class=\"iot-dialog\"\r\n    :top=\"top\"\r\n    :title=\"title\"\r\n    :visible.sync=\"dialogVisible\"\r\n    :width=\"width\"\r\n    :before-close=\"fn_close\"\r\n    :append-to-body=\"appendBody\"\r\n    :modal=\"maskModel\"\r\n  >\r\n    <div class=\"iot-dialog-content\" :style=\"{ maxHeight: maxHeight }\">\r\n      <slot name=\"body\"></slot>\r\n    </div>\r\n\r\n    <div class=\"footer\" v-if=\"footer\">\r\n      <iot-button\r\n        v-throttle=\"500\"\r\n        text=\"取消\"\r\n        type=\"white\"\r\n        @search=\"fn_close\"\r\n      />\r\n      <iot-button\r\n        v-throttle=\"500\"\r\n        :type=\"btnClass\"\r\n        :text=\"comfirmText\"\r\n        @search=\"fn_sure\"\r\n      />\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n<script>\r\nimport IotButton from \"@/components/iot-button\";\r\nexport default {\r\n  name: \"IotDialog\",\r\n  components: {\r\n    IotButton,\r\n  },\r\n  props: {\r\n    top: {\r\n      type: String,\r\n      default: \"15vh\",\r\n    },\r\n    maxHeight: {\r\n      type: String,\r\n      default: \"65vh\",\r\n    },\r\n    title: {\r\n      type: String,\r\n      default: \"标题\",\r\n    },\r\n    visible: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n    width: {\r\n      type: String,\r\n      default: \"30%\",\r\n    },\r\n    footer: {\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    appendBody: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n    callbackSure: Function,\r\n    comfirmText: {\r\n      type: String,\r\n      default: \"确 定\",\r\n    },\r\n    maskModel: {\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    showLoading: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n    btnClass: {\r\n      type: String,\r\n      default: \"default\",\r\n    },\r\n  },\r\n  computed: {\r\n    dialogVisible() {\r\n      return this.visible;\r\n    },\r\n  },\r\n  data() {\r\n    return {};\r\n  },\r\n  methods: {\r\n    // 取消的回调\r\n    fn_close() {\r\n      this.$emit(\"update:visible\", false);\r\n      this.$emit(\"close\");\r\n    },\r\n    // 确定的回调\r\n    fn_sure() {\r\n      // if (this.callbackSure) {\r\n      //   this.callbackSure()\r\n      // }\r\n      this.$emit(\"callbackSure\");\r\n      // this.$emit('update:visible', false)\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n// .iot-dialog{\r\n//   position: relative;\r\n// }\r\n.iot-dialog-content {\r\n  overflow: auto;\r\n  padding: 20px 32px;\r\n  overflow: auto;\r\n  border-top: 1px solid #eeeff1;\r\n}\r\n.footer {\r\n  width: 100%;\r\n  height: 72px;\r\n  background: #fbfbfb;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n  padding-right: 14px;\r\n  text-align: right;\r\n}\r\n/deep/ {\r\n  .el-dialog__body {\r\n    padding-top: 0px;\r\n    position: relative;\r\n    padding: 0px;\r\n  }\r\n  .iot-btn {\r\n    margin-right: 18px;\r\n  }\r\n  .el-dialog__title {\r\n    margin-left: 10px;\r\n    font-family: H_Medium;\r\n  }\r\n  .el-dialog__headerbtn .el-dialog__close {\r\n    color: rgba(81, 81, 81, 1);\r\n  }\r\n  [class*=\" el-icon-\"],\r\n  [class^=\"el-icon-\"] {\r\n    font-weight: 600;\r\n  }\r\n  .footer {\r\n    height: 58px !important;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=06d31b1a&scoped=true&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=06d31b1a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"06d31b1a\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=50656dbc&lang=scss&scoped=true&\"", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=06d31b1a&lang=scss&scoped=true&\"", "module.exports = __webpack_public_path__ + \"img/empty.85a6a000.png\";", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"iot-table\"},[(_vm.columns[0].selectionText)?_c('div',{staticClass:\"selection-text flex\"},[_c('p',[_vm._v(\"当前已选择\"+_vm._s(_vm.selecionData.length)+\"项数据。\")]),(_vm.columns[0].isShowdelete)?_c('p',{directives:[{name:\"throttle\",rawName:\"v-throttle\"}],staticClass:\"color2\",on:{\"click\":_vm.fn_del_selection_data}},[_vm._v(\" 删除 \")]):_vm._e(),(_vm.columns[0].isShowIgnore)?_c('p',{directives:[{name:\"throttle\",rawName:\"v-throttle\"}],staticClass:\"color2\",on:{\"click\":_vm.fn_ignore_selection_data}},[_vm._v(\" 批量忽略 \")]):_vm._e(),_c('P'),(_vm.columns[0].isShowHandle)?_c('p',{directives:[{name:\"throttle\",rawName:\"v-throttle\"}],staticClass:\"color2\",on:{\"click\":_vm.fn_handle_selection_data}},[_vm._v(\" 批量处理 \")]):_vm._e(),_vm._t(\"multSelectText\")],2):_vm._e(),_c('el-table',_vm._g(_vm._b({directives:[{name:\"loading\",rawName:\"v-loading\",value:(_vm.loading),expression:\"loading\"}],ref:\"table\",staticStyle:{\"width\":\"100%\"},attrs:{\"empty-text\":\" \",\"data\":_vm.data,\"element-loading-spinner\":\"el-icon-loading\",\"header-cell-style\":{ background: '#F7F7F7' },\"row-style\":{ height: _vm.toVW(48) }},on:{\"selection-change\":_vm.handleSelectionChange}},'el-table',_vm.$attrs,false),_vm.$listeners),[_vm._l((_vm.columns),function(item){return [(item.type)?_c('el-table-column',{key:item.type,attrs:{\"type\":item.type,\"width\":item.width,\"selectable\":_vm.selectable,\"align\":\"center\"}}):_c('el-table-column',{key:item.prop,attrs:{\"label\":item.label,\"prop\":item.prop,\"type\":item.type,\"width\":item.width,\"fixed\":item.fixed},scopedSlots:_vm._u([{key:\"default\",fn:function(ref){\nvar row = ref.row;\nreturn [(item.slotName)?[_vm._t(item.slotName,null,{\"row\":row})]:[_c('span',[_vm._v(_vm._s(row[item.prop]))])]]}}],null,true)})]}),_c('template',{slot:\"empty\"},[_vm._t(\"empty\",function(){return [(!_vm.loading)?_c('div',{staticClass:\"table-empty\"},[_c('img',{attrs:{\"src\":require(\"@/assets/images/empty/empty.png\"),\"alt\":\"\"}})]):_vm._e()]})],2)],2),_c('iot-dialog',{attrs:{\"width\":_vm.columns[0].dialogWidth ? _vm.columns[0].dialogWidth : _vm.toVW(550),\"visible\":_vm.columns[0].visible,\"title\":_vm.columns[0].title},on:{\"update:visible\":function($event){return _vm.$set(_vm.columns[0], \"visible\", $event)},\"callbackSure\":_vm.fn_sure},scopedSlots:_vm._u([{key:\"body\",fn:function(){return [_c('el-form',[_c('el-form-item',[_c('div',{staticClass:\"del-tips\"},[_vm._v(\" \"+_vm._s(_vm.columns[0].text)+\" \")])])],1)]},proxy:true}])})],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<!--\r\n * @Description: 1.0\r\n * @Version: 1.0\r\n * @Autor: hs\r\n * @Date: 2021-11-08 20:24:24\r\n * @LastEditors: lb <EMAIL>\r\n * @LastEditTime: 2023-05-16 11:59:38\r\n-->\r\n<template>\r\n  <div class=\"iot-table\">\r\n    <div class=\"selection-text flex\"\r\n         v-if=\"columns[0].selectionText\">\r\n      <p>当前已选择{{ selecionData.length }}项数据。</p>\r\n      <p v-if=\"columns[0].isShowdelete\"\r\n         class=\"color2\"\r\n         @click=\"fn_del_selection_data\"\r\n         v-throttle>\r\n        删除\r\n      </p>\r\n      <p v-if=\"columns[0].isShowIgnore\"\r\n         class=\"color2\"\r\n         @click=\"fn_ignore_selection_data\"\r\n         v-throttle>\r\n        批量忽略\r\n      </p>\r\n      <P></P>\r\n      <p v-if=\"columns[0].isShowHandle\"\r\n         class=\"color2\"\r\n         @click=\"fn_handle_selection_data\"\r\n         v-throttle>\r\n        批量处理\r\n      </p>\r\n      <slot name=\"multSelectText\"></slot>\r\n    </div>\r\n    <el-table ref=\"table\"\r\n              empty-text=\" \"\r\n              :data=\"data\"\r\n              v-loading=\"loading\"\r\n              element-loading-spinner=\"el-icon-loading\"\r\n              style=\"width: 100%\"\r\n              v-bind=\"$attrs\"\r\n              v-on=\"$listeners\"\r\n              :header-cell-style=\"{ background: '#F7F7F7' }\"\r\n              :row-style=\"{ height: toVW(48) }\"\r\n              @selection-change=\"handleSelectionChange\">\r\n      <template v-for=\"item in columns\">\r\n        <el-table-column v-if=\"item.type\"\r\n                         :type=\"item.type\"\r\n                         :key=\"item.type\"\r\n                         :width=\"item.width\"\r\n                         :selectable=\"selectable\"\r\n                         align=\"center\"></el-table-column>\r\n        <el-table-column v-else\r\n                         :key=\"item.prop\"\r\n                         :label=\"item.label\"\r\n                         :prop=\"item.prop\"\r\n                         :type=\"item.type\"\r\n                         :width=\"item.width\"\r\n                         :fixed=\"item.fixed\">\r\n          <template slot-scope=\"{ row }\">\r\n            <template v-if=\"item.slotName\">\r\n              <slot :name=\"item.slotName\"\r\n                    :row=\"row\"></slot>\r\n            </template>\r\n            <template v-else>\r\n              <span>{{ row[item.prop] }}</span>\r\n            </template>\r\n          </template>\r\n        </el-table-column>\r\n      </template>\r\n      <template slot=\"empty\">\r\n        <slot name=\"empty\">\r\n          <div class=\"table-empty\"\r\n               v-if=\"!loading\">\r\n            <img src=\"~@/assets/images/empty/empty.png\"\r\n                 alt />\r\n          </div>\r\n        </slot>\r\n      </template>\r\n    </el-table>\r\n    <iot-dialog :width=\"columns[0].dialogWidth ? columns[0].dialogWidth : toVW(550)\"\r\n                :visible.sync=\"columns[0].visible\"\r\n                :title=\"columns[0].title\"\r\n                @callbackSure=\"fn_sure\">\r\n      <template #body>\r\n        <el-form>\r\n          <el-form-item>\r\n            <div class=\"del-tips\">\r\n              <!-- 删除该设备，设备删除后不可恢复，请确认是否删除该设备？ -->\r\n              {{ columns[0].text }}\r\n            </div>\r\n          </el-form-item>\r\n        </el-form>\r\n      </template>\r\n    </iot-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport IotDialog from '@/components/iot-dialog'\r\nimport toVW from '@/util/toVW.js'\r\nexport default {\r\n  name: 'IotTable',\r\n  components: {\r\n    IotDialog,\r\n  },\r\n  props: {\r\n    columns: {\r\n      type: Array,\r\n      default: () => [\r\n        {\r\n          // 第一个对象，使用多选的时候使用\r\n          type: '',\r\n          selectionText: false,\r\n          isShowdelete: true,\r\n          title: '',\r\n          text: '',\r\n          visible: false,\r\n          dialogWidth: toVW(600), // 弹窗宽度必传\r\n        },\r\n      ],\r\n    },\r\n    isMonitoring: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n    data: {\r\n      type: Array,\r\n      default: () => [],\r\n    },\r\n    loading: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      selecionData: [],\r\n    }\r\n  },\r\n  methods: {\r\n    toVW,\r\n    fn_sure() {\r\n      this.$emit('del-callbackSure')\r\n    },\r\n    selectable(row, rowIndex) {\r\n      console.log('row', row)\r\n      //索引是从0开始，条件1是指只有第2行数据不被禁用\r\n      if (this.isMonitoring) {\r\n        if (row.alarmStatus == 0) {\r\n          return true //不禁用\r\n        } else {\r\n          return false //禁用\r\n        }\r\n      }\r\n      return true\r\n    },\r\n    handleSelectionChange(data) {\r\n      console.log(data)\r\n      this.selecionData = data.map((item) => {\r\n        return item.id\r\n      })\r\n      this.$emit('selection-change', data)\r\n    },\r\n    // 多选删除\r\n    fn_del_selection_data() {\r\n      if (!this.selecionData.length) {\r\n        this.$newNotify.warning({\r\n          message: '请至少选择一项',\r\n        })\r\n        return\r\n      }\r\n\r\n      this.$emit('selection-del', this.selecionData)\r\n    },\r\n\r\n    fn_ignore_selection_data() {\r\n      if (!this.selecionData.length) {\r\n        this.$newNotify.warning({\r\n          message: '请至少选择一项',\r\n        })\r\n        return\r\n      }\r\n\r\n      this.$emit('selection-ignore', this.selecionData)\r\n    },\r\n\r\n    fn_handle_selection_data() {\r\n      if (!this.selecionData.length) {\r\n        this.$newNotify.warning({\r\n          message: '请至少选择一项',\r\n        })\r\n        return\r\n      }\r\n\r\n      this.$emit('selection-handle', this.selecionData)\r\n    },\r\n    toggleSelect(row, select) {\r\n      console.log('set')\r\n      this.$refs.table.toggleRowSelection(row, select)\r\n    },\r\n    doLayout() {\r\n      this.$nextTick(() => {\r\n        this.$refs['table'].doLayout()\r\n      })\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.iot-table {\r\n  .selection-text {\r\n    margin-bottom: 14px;\r\n    p {\r\n      font-size: 14px;\r\n      font-weight: normal;\r\n      cursor: pointer;\r\n      &:nth-child(1) {\r\n        margin-right: 24px;\r\n        letter-spacing: 1px;\r\n        color: #515151;\r\n      }\r\n      &:nth-child(3) {\r\n        cursor: pointer;\r\n        margin: 0 5px;\r\n        border: 1px solid #ededed;\r\n      }\r\n      &:nth-child(4) {\r\n        letter-spacing: 1px;\r\n      }\r\n    }\r\n  }\r\n  /deep/ {\r\n    .el-table thead {\r\n      font-weight: 500 !important;\r\n    }\r\n    .el-table__row:hover {\r\n      .el-table__cell {\r\n        background-color: rgba(1, 138, 255, 0.08) !important;\r\n      }\r\n    }\r\n    .el-table__empty-text {\r\n      line-height: normal;\r\n    }\r\n    .table-empty {\r\n      img {\r\n        margin-top: 84px;\r\n        margin-bottom: 28px;\r\n      }\r\n    }\r\n    .el-table__header-wrapper {\r\n      .el-table__cell {\r\n        padding: 7px 0;\r\n      }\r\n    }\r\n\r\n    // .el-loading-spinner {\r\n    //   background: url('~@/assets/images/index/loading.svg') no-repeat;\r\n    //   background-size: 48px 48px;\r\n    //   width: 100%;\r\n    //   height: 100%;\r\n    //   position: relative;\r\n    //   top: 50%;\r\n    //   left: 45%;\r\n    // }\r\n    .el-table__fixed-right {\r\n      .el-table__header {\r\n        .el-table__cell {\r\n          padding: 7px 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .el-table::before {\r\n    height: 0px;\r\n  }\r\n  .del-tips {\r\n    width: 420px;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=7a36d05f&scoped=true&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=7a36d05f&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7a36d05f\",\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"iot-pagination\"},[_c('el-pagination',{attrs:{\"current-page\":_vm.pagination.current,\"page-sizes\":_vm.pagination.sizes,\"page-size\":_vm.pagination.size,\"pager-count\":_vm.pagination.pagerCount,\"layout\":_vm.layout,\"total\":_vm.pagination.total},on:{\"size-change\":_vm.handleSizeChange,\"current-change\":_vm.handleCurrentChange}})],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<!--\r\n * @Description: 1.0\r\n * @Version: 1.0\r\n * @Autor: hs\r\n * @Date: 2021-11-08 15:31:45\r\n * @LastEditors: Please set LastEditors\r\n * @LastEditTime: 2021-11-25 14:03:46\r\n-->\r\n<template>\r\n  <div class=\"iot-pagination\">\r\n    <el-pagination\r\n      @size-change=\"handleSizeChange\"\r\n      @current-change=\"handleCurrentChange\"\r\n      :current-page=\"pagination.current\"\r\n      :page-sizes=\"pagination.sizes\"\r\n      :page-size=\"pagination.size\"\r\n      :pager-count =\"pagination.pagerCount\"\r\n      :layout=\"layout\"\r\n      :total=\"pagination.total\"\r\n    ></el-pagination>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"IotPagination\",\r\n  props: {\r\n    pagination: {\r\n      type: Object,\r\n      default: () => ({\r\n        current: 1, // 当前页\r\n        total: 0, // 记录条数\r\n        pages: 0, // 总页数\r\n        sizes: [10, 20, 50, 100], // 每一页的数量  [10, 20, 50, 100]\r\n        size: 10,\r\n        pagerCount: 7\r\n      }),\r\n    },\r\n    layout: {\r\n      type: String,\r\n      default: \"total, prev, pager, next, sizes, jumper\",\r\n    },\r\n  },\r\n  data() {\r\n    return {};\r\n  },\r\n  methods: {\r\n    handleSizeChange(val) {\r\n      // console.log(`每页 ${val} 条`)\r\n      this.$emit(\"size-change\", val);\r\n    },\r\n    handleCurrentChange(val) {\r\n      // console.log(`当前页: ${val}`)\r\n      this.$emit(\"current-change\", val);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.iot-pagination {\r\n  .el-pagination {\r\n    margin-right: 16px;\r\n    /deep/.el-input__inner {\r\n      border-radius: 0;\r\n    }\r\n    /deep/ .el-pager li {\r\n      color: rgba(51, 51, 51, 0.85);\r\n      min-width: 28px;\r\n      height: 34px;\r\n      line-height: 34px;\r\n      font-size: 14px;\r\n    }\r\n    /deep/ .el-pager li.active {\r\n    //   border: 1px solid;\r\n      color: #1890ff;\r\n    }\r\n    /deep/.el-pagination__jump {\r\n      margin-left: 0;\r\n    }\r\n    /deep/ .el-pagination__total {\r\n      height: 34px;\r\n      line-height: 34px;\r\n    }\r\n    /deep/.btn-prev {\r\n      height: 34px;\r\n      i {\r\n        font-size: 14px;\r\n      }\r\n    }\r\n    /deep/.btn-next {\r\n      height: 34px;\r\n      i {\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=50656dbc&scoped=true&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=50656dbc&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"50656dbc\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=7022bc2e&lang=scss&scoped=true&\"", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('span',{directives:[{name:\"throttle\",rawName:\"v-throttle\",value:(500),expression:\"500\"}],staticClass:\"iot-btn\",class:[_vm.type ? 'iot-button-' + _vm.type : ''],on:{\"click\":_vm.fn_search}},[_vm._v(_vm._s(_vm.text))])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<!--\r\n * @Description: 1.0\r\n * @Version: 1.0\r\n * @Autor: hs\r\n * @Date: 2021-11-08 14:38:30\r\n * @LastEditors: hs\r\n * @LastEditTime: 2021-11-08 21:29:43\r\n-->\r\n<template>\r\n  <span\r\n    class=\"iot-btn\"\r\n    :class=\"[type ? 'iot-button-' + type : '']\"\r\n    v-throttle=\"500\"\r\n    @click=\"fn_search\"\r\n    >{{ text }}</span\r\n  >\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"Iot-btn\",\r\n  props: {\r\n    text: {\r\n      type: String,\r\n      default: \"搜索\",\r\n    },\r\n    bgcolor: {\r\n      type: String,\r\n      default: \"\",\r\n    },\r\n    type: {\r\n      type: String,\r\n      default: \"default\",\r\n    },\r\n  },\r\n  data() {\r\n    return {};\r\n  },\r\n  methods: {\r\n    fn_search() {\r\n      this.$emit(\"search\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.iot-btn {\r\n  text-align: center;\r\n  // color: #fff;\r\n  // background: linear-gradient(180deg, #0088fe, #006eff 100%);\r\n  // border-radius: 2px;\r\n  cursor: pointer;\r\n  padding: 7px 23px;\r\n  display: inline-block;\r\n  // font-family: H_Black;\r\n  font-size: 14px;\r\n  font-weight: normal;\r\n  // letter-spacing: 2px;\r\n  margin-right: 14px;\r\n}\r\n.iot-button-default {\r\n  background: linear-gradient(180deg, #0088fe, #006eff 100%);\r\n  color: #fff;\r\n  border: 1px solid #0088fe;\r\n}\r\n.iot-button-grey {\r\n  background: #bfbfbf;\r\n  color: #fff;\r\n}\r\n.iot-button-white {\r\n  background: #fff;\r\n  color: #333;\r\n  border: 1px solid #eeeff1;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=7022bc2e&scoped=true&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=7022bc2e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7022bc2e\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=99fd1074&lang=scss&scoped=true&\"", "'use strict';\nvar $ = require('../internals/export');\nvar $map = require('../internals/array-iteration').map;\nvar arrayMethodHasSpeciesSupport = require('../internals/array-method-has-species-support');\n\nvar HAS_SPECIES_SUPPORT = arrayMethodHasSpeciesSupport('map');\n\n// `Array.prototype.map` method\n// https://tc39.es/ecma262/#sec-array.prototype.map\n// with adding support of @@species\n$({ target: 'Array', proto: true, forced: !HAS_SPECIES_SUPPORT }, {\n  map: function map(callbackfn /* , thisArg */) {\n    return $map(this, callbackfn, arguments.length > 1 ? arguments[1] : undefined);\n  }\n});\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"update-info\"},[_c('div',{staticClass:\"info-info\"},[_vm._m(0),_c('div',{staticClass:\"info-detail\"},[_c('div',{staticClass:\"item-rows\"},[_c('div',{staticClass:\"item\"},[_c('span',[_vm._v(\"组名称\")]),_c('span',[_vm._v(_vm._s(_vm.connectorDetail.groupName))])]),_c('div',{staticClass:\"item\"},[_c('span',[_vm._v(\"组ID\")]),_c('span',[_vm._v(_vm._s(_vm.connectorDetail.id))])])]),_c('div',{staticClass:\"item-rows\"},[_c('div',{staticClass:\"item\"},[_c('span',[_vm._v(\"配置时间\")]),_c('span',[_vm._v(_vm._s(_vm.connectorDetail.createTime))])]),_c('div',{staticClass:\"item\"},[_c('span',[_vm._v(\"创建时间\")]),_c('span',[_vm._v(_vm._s(_vm.connectorDetail.updateTime))])])]),_c('div',{staticClass:\"item-rows\"},[_c('div',{staticClass:\"item\"},[_c('span',[_vm._v(\"更新时间\")]),_c('span',[_vm._v(_vm._s(_vm.connectorDetail.createTime))])])])])]),_c('div',{staticClass:\"info-content\"},[_vm._m(1),_c('div',{staticClass:\"content-table\"},[_c('iot-table',{attrs:{\"columns\":_vm.columns,\"data\":_vm.tableData,\"loading\":_vm.loading},scopedSlots:_vm._u([{key:\"configInfo\",fn:function(scope){return [_c('el-tooltip',{attrs:{\"placement\":\"top-start\",\"effect\":\"light\",\"popper-class\":\"event-tooltip\"},scopedSlots:_vm._u([{key:\"content\",fn:function(){return [_c('span',[_vm._v(\" \"+_vm._s(scope.row.configInfo)+\" \")])]},proxy:true}],null,true)},[_c('div',{staticClass:\"alarmContent-tooltip\"},[_c('p',[_vm._v(\" \"+_vm._s(_vm.fn_sub10(scope.row.configInfo))+\" \")])])])]}}])})],1),(_vm.tableData.length > 0)?_c('div',{staticClass:\"content-bottom\"},[_c('iot-pagination',{attrs:{\"pagination\":_vm.pagination},on:{\"size-change\":_vm.handleSizeChange,\"current-change\":_vm.handleCurrentChange}})],1):_vm._e()])])}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"info-title flex\"},[_c('div',{staticClass:\"left\"},[_c('p',[_vm._v(\"设备组信息\")])]),_c('div',{staticClass:\"right\"})])},function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"content-select flex\"},[_c('div',{staticClass:\"left\"},[_c('div',{staticClass:\"content-title\"},[_c('span',[_vm._v(\"关联的设备列表\")])])]),_c('div',{staticClass:\"right\"})])}]\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"update-info\">\r\n    <div class=\"info-info\">\r\n      <div class=\"info-title flex\">\r\n        <div class=\"left\">\r\n          <p>设备组信息</p>\r\n        </div>\r\n        <div class=\"right\">\r\n        </div>\r\n      </div>\r\n      <div class=\"info-detail\">\r\n        <div class=\"item-rows\">\r\n          <div class=\"item\">\r\n            <span>组名称</span>\r\n            <span>{{ connectorDetail.groupName }}</span>\r\n          </div>\r\n          <div class=\"item\">\r\n            <span>组ID</span>\r\n            <span>{{ connectorDetail.id }}</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"item-rows\">\r\n          <div class=\"item\">\r\n            <span>配置时间</span>\r\n            <span>{{ connectorDetail.createTime }}</span>\r\n          </div>\r\n          <div class=\"item\">\r\n            <span>创建时间</span>\r\n            <span>{{ connectorDetail.updateTime }}</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"item-rows\">\r\n          <div class=\"item\">\r\n            <span>更新时间</span>\r\n            <span>{{ connectorDetail.createTime }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"info-content\">\r\n      <div class=\"content-select flex\">\r\n        <div class=\"left\">\r\n          <div class=\"content-title\">\r\n            <span>关联的设备列表</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"right\">\r\n\r\n        </div>\r\n      </div>\r\n      <div class=\"content-table\">\r\n        <iot-table :columns=\"columns\"\r\n                   :data=\"tableData\"\r\n                   :loading=\"loading\">\r\n          <template slot=\"configInfo\"\r\n                    slot-scope=\"scope\">\r\n            <el-tooltip placement=\"top-start\"\r\n                        effect=\"light\"\r\n                        popper-class=\"event-tooltip\">\r\n              <template #content>\r\n                <span>\r\n                  {{scope.row.configInfo}}\r\n                </span>\r\n              </template>\r\n              <div class=\"alarmContent-tooltip\">\r\n                <p>\r\n                  {{fn_sub10(scope.row.configInfo)}}\r\n                </p>\r\n              </div>\r\n            </el-tooltip>\r\n          </template>\r\n        </iot-table>\r\n      </div>\r\n      <div class=\"content-bottom\"\r\n           v-if=\"tableData.length > 0\">\r\n        <iot-pagination :pagination=\"pagination\"\r\n                        @size-change=\"handleSizeChange\"\r\n                        @current-change=\"handleCurrentChange\" />\r\n      </div>\r\n    </div>\r\n\r\n  </div>\r\n</template>\r\n  <script>\r\nimport IotTable from '@/components/iot-table'\r\nimport IotPagination from '@/components/iot-pagination'\r\nimport { reg_seven, reg_sixteen } from '@/util/util.js'\r\nimport { cloneDeep } from 'lodash'\r\nimport { getShieldGroupDetail, getDeviceShieldListLink } from '@/api/device'\r\n\r\nexport default {\r\n  name: 'updateInfo',\r\n  components: {\r\n    IotTable,\r\n    IotPagination,\r\n  },\r\n  data() {\r\n    return {\r\n      columns: [\r\n        { label: '产品Key', prop: 'productKey' },\r\n        { label: '设备SN', prop: 'deviceSn' },\r\n        { label: '设备名称', prop: 'deviceName' },\r\n        { label: '操作时间', prop: 'operatorTime' },\r\n        { label: '操作对象', prop: 'operatorObject' },\r\n        { label: '创建时间', prop: 'createTime' },\r\n        { label: '更新时间', prop: 'updateTime' },\r\n      ],\r\n      tableData: [],\r\n      loading: false,\r\n      pagination: {\r\n        current: 1, // 当前页\r\n        total: 0, // 记录条数\r\n        pages: 0, // 总页数\r\n        sizes: [10, 20, 50, 100], // 每一页的数量  [10, 20, 50, 100]\r\n        size: 4,\r\n      },\r\n      visible: false,\r\n      title: '编辑固件信息',\r\n      dialogWidth: '742px',\r\n      infoForm: {\r\n        id: 0,\r\n        name: '',\r\n        description: '',\r\n      },\r\n      rules: {\r\n        name: [\r\n          {\r\n            required: true,\r\n            trigger: 'blur',\r\n            // message: '须选择所属项目',\r\n            validator: this.checkName,\r\n          },\r\n        ],\r\n        description: [\r\n          {\r\n            required: false,\r\n            trigger: 'blur',\r\n            validator: this.checkLength,\r\n          },\r\n        ],\r\n      },\r\n      nameTrue: true,\r\n      descTrue: true,\r\n      connectorDetail: {},\r\n      firmwareJobDetailForm: {},\r\n      firmwareJobStatic: {},\r\n      firmwareJobList: {},\r\n      groupId: '',\r\n      inputHolder: '输入设备名称',\r\n      jobId: '',\r\n    }\r\n  },\r\n  created() {\r\n    this.groupId = this.$route.query.id\r\n  },\r\n  mounted() {\r\n    this.fn_getConnectorDetail()\r\n    // this.getFirmwareJobUpgradeStatistic()\r\n    this.fn_get_table_data()\r\n  },\r\n  methods: {\r\n    fn_sub10(str) {\r\n      return str.length > 20 ? `${str.substr(0, 20)}...` : str\r\n    },\r\n    fn_getConnectorDetail() {\r\n      getShieldGroupDetail({ id: this.groupId }).then((res) => {\r\n        if (res.code == 200) {\r\n          let data = {\r\n            id: res.data.id,\r\n            title: res.data.deviceName,\r\n          }\r\n          this.$store.dispatch('setLayoutInfo', data)\r\n          this.connectorDetail = res.data\r\n        }\r\n      })\r\n    },\r\n\r\n    fn_get_table_data(params = { groupId: this.groupId }) {\r\n      let others = { ...params }\r\n      if (!params.size) {\r\n        others.size = 10\r\n        others.current = 1\r\n      }\r\n      getDeviceShieldListLink(others)\r\n        .then((res) => {\r\n          if (res.code == 200) {\r\n            setTimeout(() => {\r\n              this.loading = false\r\n            }, 300)\r\n            this.tableData = res.data.records\r\n            this.pagination.total = res.data.total\r\n            this.pagination.current = res.data.current\r\n            this.pagination.pages = res.data.pages\r\n            this.pagination.size = res.data.size\r\n          } else {\r\n            this.$newNotify.error({\r\n              message: res.message,\r\n            })\r\n          }\r\n        })\r\n        .finally(() => {\r\n          setTimeout(() => {\r\n            this.loading = false\r\n          }, 300)\r\n        })\r\n    },\r\n\r\n    // 当前页总条数\r\n    handleSizeChange(val) {\r\n      this.pagination.current = 1\r\n      this.pagination.size = val\r\n      this.fn_get_table_data({\r\n        groupId: this.groupId,\r\n        size: this.pagination.size,\r\n        current: this.pagination.current,\r\n      })\r\n    },\r\n    // 当前页\r\n    handleCurrentChange(val) {\r\n      console.log(this.pagination)\r\n      this.pagination.current = val\r\n      this.fn_get_table_data({\r\n        groupId: this.groupId,\r\n        size: this.pagination.size,\r\n        current: this.pagination.current,\r\n      })\r\n    },\r\n    fn_notNull(val) {\r\n      return val !== 0 && !val\r\n    },\r\n\r\n    handleSearch(from) {\r\n      this.deviceName = from.value\r\n      this.fn_get_table_data({\r\n        groupId: this.groupId,\r\n        deviceName: from.value,\r\n      })\r\n    },\r\n    fn_clear_search_info() {\r\n      this.deviceName = ''\r\n      this.fn_get_table_data({\r\n        groupId: this.groupId,\r\n      })\r\n    },\r\n\r\n    checkName(rule, value, callback) {\r\n      if (this.fn_notNull(value)) {\r\n        return callback(new Error('请输入固件名称'))\r\n      } else if (!reg_sixteen(value)) {\r\n        return callback(\r\n          new Error(\r\n            '支持中文、英文大小写、数字、部分常用符号（下划线，减号，括弧）， 必须以中文、英文或数字开头，长度不超过32个字符'\r\n          )\r\n        )\r\n      } else {\r\n        callback()\r\n      }\r\n    },\r\n    // 表单验证触发\r\n    fn_validate(name, value) {\r\n      if (name === 'name') {\r\n        this.nameTrue = value\r\n      }\r\n      if (name === 'description') {\r\n        this.descTrue = value\r\n      }\r\n    },\r\n    // 长度检验\r\n    checkLength(rule, value, callback) {\r\n      if (!reg_seven(value, 201)) {\r\n        return callback(new Error('最多不超过200个字符'))\r\n      } else {\r\n        callback()\r\n      }\r\n    },\r\n  },\r\n}\r\n</script>\r\n  <style lang=\"scss\" scoped>\r\n@mixin fontStyle400 {\r\n  font-size: 14px;\r\n  font-weight: 400;\r\n}\r\n@mixin fontStyle500 {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n@mixin BoxShadow {\r\n  box-shadow: 0px 5px 18px rgba(0, 0, 0, 0.08);\r\n}\r\n$blue: #018aff;\r\n$green: #00c250;\r\n$purple: #8f01ff;\r\n$red: #ff4d4f;\r\n$yellow: #e6a23c;\r\n.update-info {\r\n  .info-info {\r\n    margin-top: 18px;\r\n    background: #ffffff;\r\n    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);\r\n    border-radius: 2px;\r\n    padding: 20px 32px;\r\n    .info-title {\r\n      justify-content: space-between;\r\n      @include fontStyle500;\r\n      .right {\r\n        img {\r\n          margin-right: 6px;\r\n        }\r\n        span {\r\n          cursor: pointer;\r\n          font-size: 14px;\r\n        }\r\n      }\r\n    }\r\n    .info-detail {\r\n      margin-top: 18px;\r\n      // @include BoxShadow;\r\n      // border-radius: 2px;\r\n      // padding: 17px 0;\r\n      .item-rows {\r\n        margin: 0 auto;\r\n        display: flex;\r\n        flex: 1;\r\n        .item {\r\n          padding: 11px 0;\r\n          width: 50%;\r\n          display: inline-flex;\r\n          span {\r\n            height: 16px;\r\n            line-height: 16px;\r\n            @include fontStyle400;\r\n            &:first-child {\r\n              flex-shrink: 0;\r\n              color: #999999;\r\n              width: 80px;\r\n              text-align: right;\r\n            }\r\n            &:last-child {\r\n              color: #515151;\r\n              margin-left: 48px;\r\n            }\r\n          }\r\n          &:nth-child(1) {\r\n            // margin-left: 6px;\r\n            span {\r\n              &:first-child {\r\n                width: 70px;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .info-census {\r\n    margin-top: 38px;\r\n    .census-title {\r\n      @include fontStyle500;\r\n      background: #ffffff;\r\n      box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);\r\n      border-radius: 2px;\r\n      padding: 20px 32px;\r\n    }\r\n    .census-content {\r\n      margin-top: 18px;\r\n      .content-item {\r\n        flex: 1;\r\n        .item-title {\r\n          p {\r\n            @include fontStyle400;\r\n            height: 12px;\r\n            margin: 4px 8px 0 0;\r\n          }\r\n          span {\r\n            color: #515151;\r\n            font-size: 14px;\r\n          }\r\n        }\r\n        .num {\r\n          margin: 14px 17px 0;\r\n          span {\r\n            font-size: 24px;\r\n            font-weight: 500;\r\n          }\r\n        }\r\n        &:nth-child(1) {\r\n          .item-title {\r\n            p {\r\n              border: 2px solid $blue;\r\n            }\r\n          }\r\n        }\r\n        &:nth-child(2) {\r\n          .item-title {\r\n            p {\r\n              border: 2px solid $green;\r\n            }\r\n          }\r\n        }\r\n        &:nth-child(3) {\r\n          .item-title {\r\n            p {\r\n              border: 2px solid $purple;\r\n            }\r\n          }\r\n        }\r\n        &:nth-child(4) {\r\n          .item-title {\r\n            p {\r\n              border: 2px solid $red;\r\n            }\r\n          }\r\n        }\r\n        &:nth-child(5) {\r\n          .item-title {\r\n            p {\r\n              border: 2px solid $yellow;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .info-content {\r\n    margin-top: 18px;\r\n    background: #ffffff;\r\n    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);\r\n    border-radius: 2px;\r\n    padding: 20px 32px;\r\n    .content-title {\r\n      justify-content: space-between;\r\n    }\r\n    .content-select {\r\n      justify-content: space-between;\r\n    }\r\n    .content-table {\r\n      margin-top: 14px;\r\n\r\n      .table-edit {\r\n        display: flex;\r\n        align-items: center;\r\n        p {\r\n          cursor: pointer;\r\n        }\r\n        .table-line {\r\n          margin: 0px 12px;\r\n          width: 1px;\r\n          height: 13px;\r\n          border: 1px solid #ededed;\r\n        }\r\n      }\r\n    }\r\n    .content-bottom {\r\n      margin-top: 14px;\r\n      text-align: right;\r\n    }\r\n  }\r\n  .info-form {\r\n    /deep/ .el-form-item {\r\n      margin-bottom: 17px;\r\n    }\r\n    .el-form-tips {\r\n      margin-top: -17px;\r\n    }\r\n    .upload-text {\r\n      width: 160px;\r\n      background: #ebf6ff;\r\n      color: #0088fe;\r\n      font-size: 14px;\r\n      font-weight: 400;\r\n      padding: 11px 0;\r\n      user-select: none;\r\n    }\r\n    .el-upload__tip {\r\n      color: #999999;\r\n      font-size: 12px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n  ", "import mod from \"-!../../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js!../../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js!../../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=99fd1074&scoped=true&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=99fd1074&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"99fd1074\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=7a36d05f&lang=scss&scoped=true&\""], "sourceRoot": ""}