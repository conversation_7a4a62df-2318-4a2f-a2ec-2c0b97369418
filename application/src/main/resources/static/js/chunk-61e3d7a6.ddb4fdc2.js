(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-61e3d7a6"],{"0535":function(t,e,n){},"672c":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"update-info"},[n("div",{staticClass:"info-info"},[t._m(0),n("div",{staticClass:"info-detail"},[n("div",{staticClass:"item-rows"},[n("div",{staticClass:"item"},[n("span",[t._v("连接器名称")]),n("span",[t._v(t._s(t.connectorDetail.connectorName))])]),n("div",{staticClass:"item"},[n("span",[t._v("协议方式")]),n("span",[t._v(t._s(t.connectorDetail.protocolType))])])]),n("div",{staticClass:"item-rows"},[n("div",{staticClass:"item"},[n("span",[t._v("是否启用")]),n("span",[t._v(t._s(t.connectorDetail.enableStatusName))])]),n("div",{staticClass:"item"},[n("span",[t._v("应用类型")]),n("span",[t._v(t._s(t.connectorDetail.applicationType))])])]),n("div",{staticClass:"item-rows"},[n("div",{staticClass:"item"},[n("span",[t._v("上行转换器")]),n("span",[t._v(t._s(t.connectorDetail.upLinkName))])]),n("div",{staticClass:"item"},[n("span",[t._v("下行转换器")]),n("span",[t._v(t._s(t.connectorDetail.downLinkName))])])]),n("div",{staticClass:"item-rows"},[n("div",{staticClass:"item",staticStyle:{width:"100%"}},[n("span",[t._v("通用配置")]),n("span",[t._v(t._s(t.connectorDetail.commonConfig))])])]),n("div",{staticClass:"item-rows"},[n("div",{staticClass:"item",staticStyle:{width:"100%"}},[n("span",[t._v("自定义配置")]),n("span",[t._v(t._s(t.connectorDetail.customConfig))])])]),n("div",{staticClass:"item-rows"},[n("div",{staticClass:"item"},[n("span",[t._v("创建时间")]),n("span",[t._v(t._s(t.connectorDetail.createTime))])]),n("div",{staticClass:"item"},[n("span",[t._v("修改时间")]),n("span",[t._v(t._s(t.connectorDetail.updateTime))])])])])]),n("div",{staticClass:"info-content"},[n("div",{staticClass:"content-select flex"},[t._m(1),n("div",{staticClass:"right"},[n("form-search",{attrs:{isSelect:!1,inputHolder:t.inputHolder},on:{search:t.handleSearch,clear:t.fn_clear_search_info}})],1)]),n("div",{staticClass:"content-table"},[n("iot-table",{attrs:{columns:t.columns,data:t.tableData,loading:t.loading},scopedSlots:t._u([{key:"configInfo",fn:function(e){return[n("el-tooltip",{attrs:{placement:"top-start",effect:"light","popper-class":"event-tooltip"},scopedSlots:t._u([{key:"content",fn:function(){return[n("span",[t._v(" "+t._s(e.row.configInfo)+" ")])]},proxy:!0}],null,!0)},[n("div",{staticClass:"alarmContent-tooltip"},[n("p",[t._v(" "+t._s(t.fn_sub10(e.row.configInfo))+" ")])])])]}}])})],1),t.tableData.length>0?n("div",{staticClass:"content-bottom"},[n("iot-pagination",{attrs:{pagination:t.pagination},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1):t._e()])])},i=[function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"info-title flex"},[n("div",{staticClass:"left"},[n("p",[t._v("连接器信息")])]),n("div",{staticClass:"right"})])},function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"left"},[n("div",{staticClass:"content-title"},[n("span",[t._v("设备列表")])])])}],s=n("5530"),o=(n("d3b7"),n("b329")),c=n("673a"),r=n("6e22"),l=n("0e0b"),d=(n("2ef0"),n("aa98")),u={name:"updateInfo",components:{IotTable:c["a"],IotPagination:r["a"],FormSearch:o["a"]},data:function(){return{columns:[{label:"设备名称",prop:"deviceName"},{label:"设备SN",prop:"deviceSn"},{label:"自定义配置",prop:"configInfo",slotName:"configInfo"},{label:"设备状态",prop:"deviceStatusName"},{label:"设备描述",prop:"deviceDesc"},{label:"添加时间",prop:"createTime"}],tableData:[],loading:!1,pagination:{current:1,total:0,pages:0,sizes:[10,20,50,100],size:4},visible:!1,title:"编辑固件信息",dialogWidth:"742px",infoForm:{id:0,name:"",description:""},rules:{name:[{required:!0,trigger:"blur",validator:this.checkName}],description:[{required:!1,trigger:"blur",validator:this.checkLength}]},nameTrue:!0,descTrue:!0,connectorDetail:{},firmwareJobDetailForm:{},firmwareJobStatic:{},firmwareJobList:{},connectorId:"",inputHolder:"输入设备名称",jobId:""}},created:function(){this.connectorId=this.$route.query.id},mounted:function(){this.fn_getConnectorDetail(),this.fn_get_table_data()},methods:{fn_sub10:function(t){return t.length>20?"".concat(t.substr(0,20),"..."):t},fn_getConnectorDetail:function(){var t=this;Object(d["b"])({id:this.connectorId}).then((function(e){if(200==e.code){var n={id:e.data.id,title:e.data.connectorName};t.$store.dispatch("setLayoutInfo",n),t.connectorDetail=e.data}}))},fn_get_table_data:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{connectorId:this.connectorId},n=Object(s["a"])({},e);e.size||(n.size=10,n.current=1),Object(d["c"])(n).then((function(e){200==e.code?(setTimeout((function(){t.loading=!1}),300),t.tableData=e.data.records,t.pagination.total=e.data.total,t.pagination.current=e.data.current,t.pagination.pages=e.data.pages,t.pagination.size=e.data.size):t.$newNotify.error({message:e.message})})).finally((function(){setTimeout((function(){t.loading=!1}),300)}))},handleSizeChange:function(t){this.pagination.current=1,this.pagination.size=t,this.fn_get_table_data({connectorId:this.connectorId,size:this.pagination.size,current:this.pagination.current})},handleCurrentChange:function(t){console.log(this.pagination),this.pagination.current=t,this.fn_get_table_data({connectorId:this.connectorId,size:this.pagination.size,current:this.pagination.current})},fn_notNull:function(t){return 0!==t&&!t},handleSearch:function(t){this.deviceName=t.value,this.fn_get_table_data({connectorId:this.connectorId,deviceName:t.value})},fn_clear_search_info:function(){this.deviceName="",this.fn_get_table_data({connectorId:this.connectorId})},checkName:function(t,e,n){return this.fn_notNull(e)?n(new Error("请输入固件名称")):Object(l["d"])(e)?void n():n(new Error("支持中文、英文大小写、数字、部分常用符号（下划线，减号，括弧）， 必须以中文、英文或数字开头，长度不超过32个字符"))},fn_validate:function(t,e){"name"===t&&(this.nameTrue=e),"description"===t&&(this.descTrue=e)},checkLength:function(t,e,n){if(!Object(l["c"])(e,201))return n(new Error("最多不超过200个字符"));n()}}},p=u,f=(n("6c26"),n("2877")),_=Object(f["a"])(p,a,i,!1,null,"a157f2d2",null);e["default"]=_.exports},"6c26":function(t,e,n){"use strict";n("0535")}}]);
//# sourceMappingURL=chunk-61e3d7a6.ddb4fdc2.js.map