{"version": 3, "sources": ["webpack:///./src/views/empty/404.vue?4150", "webpack:///src/views/empty/404.vue", "webpack:///./src/views/empty/404.vue?e12b", "webpack:///./src/views/empty/404.vue", "webpack:///./src/components/copyright/index.vue?d278", "webpack:///./src/components/copyright/index.vue?89b0", "webpack:///src/components/copyright/index.vue", "webpack:///./src/components/copyright/index.vue?dab6", "webpack:///./src/components/copyright/index.vue", "webpack:///./src/assets/images/empty/404.png", "webpack:///./src/components/iot-button/index.vue?571c", "webpack:///./src/components/iot-button/index.vue?9810", "webpack:///src/components/iot-button/index.vue", "webpack:///./src/components/iot-button/index.vue?972b", "webpack:///./src/components/iot-button/index.vue", "webpack:///./src/views/empty/404.vue?90f5"], "names": ["render", "_vm", "this", "_h", "$createElement", "_c", "_self", "staticClass", "_v", "_s", "count", "attrs", "on", "back", "staticRenderFns", "components", "copyright", "IotButton", "data", "mounted", "countDown", "methods", "$router", "push", "path", "clearInterval", "timer", "component", "_m", "module", "exports", "directives", "name", "rawName", "value", "expression", "class", "type", "fn_search", "text", "props", "String", "default", "bgcolor", "$emit"], "mappings": "kJAAA,IAAIA,EAAS,WAAa,IAAIC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACE,YAAY,UAAU,CAACF,EAAG,MAAM,CAACE,YAAY,oBAAoB,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,IAAI,CAACE,YAAY,YAAY,CAACN,EAAIO,GAAG,WAAWH,EAAG,IAAI,CAACE,YAAY,SAAS,CAACN,EAAIO,GAAG,sBAAsBH,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,OAAO,CAACJ,EAAIO,GAAG,QAAQP,EAAIO,GAAG,KAAKH,EAAG,OAAO,CAACE,YAAY,SAAS,CAACN,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIS,OAAO,OAAOT,EAAIO,GAAG,KAAKH,EAAG,OAAO,CAACJ,EAAIO,GAAG,kBAAkBH,EAAG,aAAa,CAACM,MAAM,CAAC,KAAO,SAASC,GAAG,CAAC,OAASX,EAAIY,SAAS,GAAGR,EAAG,MAAM,CAACM,MAAM,CAAC,IAAM,EAAQ,QAAiC,IAAM,QAAQN,EAAG,YAAY,CAACE,YAAY,eAAe,IAClrBO,EAAkB,G,wBC6BtB,GACEC,WAAY,CACVC,UAAJ,OACIC,UAAJ,QAEEC,KALF,WAMI,MAAO,CACLR,MAAO,IAGXS,QAVF,WAWIjB,KAAKkB,aAEPC,QAAS,CACPR,KADJ,WAEMX,KAAKoB,QAAQC,KAAK,CAAxB,iBAEIH,UAJJ,WAIA,WACA,0BACQ,EAAR,QACY,EAAZ,WACU,EAAV,cACYI,KAAM,WAERC,cAAcC,MAExB,QCxD4V,I,wBCQxVC,EAAY,eACd,EACA3B,EACAc,GACA,EACA,KACA,WACA,MAIa,aAAAa,E,6CCnBf,W,oCCAA,IAAI3B,EAAS,WAAa,IAAIC,EAAIC,KAASC,EAAGF,EAAIG,eAAsBH,EAAIK,MAAMD,GAAO,OAAOJ,EAAI2B,GAAG,IACnGd,EAAkB,CAAC,WAAa,IAAIb,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,OAAO,CAACJ,EAAIO,GAAG,+ECShK,KCV8V,I,wBCQ1VmB,EAAY,eACd,EACA3B,EACAc,GACA,EACA,KACA,WACA,MAIa,OAAAa,E,yDCnBfE,EAAOC,QAAU,IAA0B,wB,6DCA3C,W,kCCAA,IAAI9B,EAAS,WAAa,IAAIC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,OAAO,CAAC0B,WAAW,CAAC,CAACC,KAAK,WAAWC,QAAQ,aAAaC,MAAM,IAAMC,WAAW,QAAQ5B,YAAY,UAAU6B,MAAM,CAACnC,EAAIoC,KAAO,cAAgBpC,EAAIoC,KAAO,IAAIzB,GAAG,CAAC,MAAQX,EAAIqC,YAAY,CAACrC,EAAIO,GAAGP,EAAIQ,GAAGR,EAAIsC,UAC9SzB,EAAkB,GCkBtB,GACEkB,KAAM,UACNQ,MAAO,CACLD,KAAM,CACJF,KAAMI,OACNC,QAAS,MAEXC,QAAS,CACPN,KAAMI,OACNC,QAAS,IAEXL,KAAM,CACJA,KAAMI,OACNC,QAAS,YAGbxB,KAhBF,WAiBI,MAAO,IAETG,QAAS,CACPiB,UADJ,WAEMpC,KAAK0C,MAAM,aCxC6U,I,wBCQ1VjB,EAAY,eACd,EACA3B,EACAc,GACA,EACA,KACA,WACA,MAIa,OAAAa,E,2CCnBf", "file": "js/chunk-5ed55f75.ab2a2fc8.js", "sourcesContent": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"layout\"},[_c('div',{staticClass:\"content-404 flex\"},[_c('div',{staticClass:\"content-to\"},[_c('p',{staticClass:\"big-font\"},[_vm._v(\"Oops!\")]),_c('p',{staticClass:\"sorry\"},[_vm._v(\"sorry，您访问的页面不存在~\")]),_c('div',{staticClass:\"seconds\"},[_c('span',[_vm._v(\"将于\")]),_vm._v(\" \"),_c('span',{staticClass:\"color\"},[_vm._v(_vm._s(_vm.count)+\" \")]),_vm._v(\" \"),_c('span',[_vm._v(\" 秒后自动跳转登录页\")])]),_c('iot-button',{attrs:{\"text\":'前往登录页'},on:{\"search\":_vm.back}})],1),_c('img',{attrs:{\"src\":require(\"@/assets/images/empty/404.png\"),\"alt\":\"\"}})]),_c('copyright',{staticClass:\"copyright\"})],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<!--\r\n * @Description: 1.0\r\n * @Version: 1.0\r\n * @Autor: hs\r\n * @Date: 2021-11-08 09:51:45\r\n * @LastEditors: lb <EMAIL>\r\n * @LastEditTime: 2022-07-11 15:20:07\r\n-->\r\n<template>\r\n  <div class=\"layout\">\r\n    <div class=\"content-404 flex\">\r\n      <div class=\"content-to\">\r\n        <p class=\"big-font\">Oops!</p>\r\n        <p class=\"sorry\">sorry，您访问的页面不存在~</p>\r\n        <div class=\"seconds\">\r\n          <span>将于</span> <span class=\"color\">{{ count }} </span> <span> 秒后自动跳转登录页</span>\r\n        </div>\r\n        <iot-button :text=\"'前往登录页'\"\r\n                    @search=\"back\"></iot-button>\r\n      </div>\r\n      <img src=\"~@/assets/images/empty/404.png\"\r\n           alt=\"\" />\r\n    </div>\r\n    <copyright class=\"copyright\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport copyright from '@/components/copyright'\r\nimport IotButton from '@/components/iot-button'\r\nexport default {\r\n  components: {\r\n    copyright,\r\n    IotButton,\r\n  },\r\n  data() {\r\n    return {\r\n      count: 5,\r\n    }\r\n  },\r\n  mounted() {\r\n    this.countDown()\r\n  },\r\n  methods: {\r\n    back() {\r\n      this.$router.push({ path: '/login' })\r\n    },\r\n    countDown() {\r\n      let timer = setInterval(() => {\r\n        this.count--\r\n        if (this.count <= 0) {\r\n          this.$router.push({\r\n            path: '/login',\r\n          })\r\n          clearInterval(timer)\r\n        }\r\n      }, 1000)\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.layout {\r\n  height: 100vh;\r\n  background: #f8fcfd;\r\n  .content-404 {\r\n    justify-content: center;\r\n    .content-to {\r\n      margin-top: 250px;\r\n      margin-right: 120px;\r\n      .big-font {\r\n        font-family: HarmonyOS Sans SC;\r\n        margin-top: 40px;\r\n        font-size: 40px;\r\n        font-weight: bold;\r\n        color: #333333;\r\n      }\r\n      .sorry {\r\n        color: #515151;\r\n        font-size: 18px;\r\n        line-height: 21px;\r\n        font-weight: 400;\r\n        margin-top: 24px;\r\n      }\r\n      .seconds {\r\n        color: #999999;\r\n        font-size: 14px;\r\n        line-height: 16px;\r\n        margin-top: 10px;\r\n        margin-bottom: 32px;\r\n        .color {\r\n          color: #018aff;\r\n        }\r\n      }\r\n    }\r\n    img {\r\n      width: 600px;\r\n      margin-top: 250px;\r\n    }\r\n  }\r\n  .copyright {\r\n    position: absolute;\r\n    left: 38%;\r\n    bottom: 0;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./404.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./404.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./404.vue?vue&type=template&id=78d43903&scoped=true&\"\nimport script from \"./404.vue?vue&type=script&lang=js&\"\nexport * from \"./404.vue?vue&type=script&lang=js&\"\nimport style0 from \"./404.vue?vue&type=style&index=0&id=78d43903&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"78d43903\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=a9e39914&lang=scss&scoped=true&\"", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _vm._m(0)}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"copyright\"},[_c('span',[_vm._v(\"© 2009-2021 福建钰辰微电子有限公司 Fujian Yuchen Microelectronics Co. ,Ltd. 版权所有\")])])}]\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"copyright\">\r\n    <span\r\n      >© 2009-2021 福建钰辰微电子有限公司 Fujian Yuchen Microelectronics Co.\r\n      ,Ltd. 版权所有</span\r\n    >\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.copyright {\r\n  // position: absolute;\r\n  padding: 20px 0 32px;\r\n  // left: 50%;\r\n  // transform: translateX(-50%);\r\n  text-align: center;\r\n  span {\r\n    text-align: center;\r\n    font-style: normal;\r\n    font-weight: normal;\r\n    font-size: 12px;\r\n    line-height: 14px;\r\n    color: #b2b5bc;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=a9e39914&scoped=true&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=a9e39914&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"a9e39914\",\n  null\n  \n)\n\nexport default component.exports", "module.exports = __webpack_public_path__ + \"img/404.8ae6e8ee.png\";", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=7022bc2e&lang=scss&scoped=true&\"", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('span',{directives:[{name:\"throttle\",rawName:\"v-throttle\",value:(500),expression:\"500\"}],staticClass:\"iot-btn\",class:[_vm.type ? 'iot-button-' + _vm.type : ''],on:{\"click\":_vm.fn_search}},[_vm._v(_vm._s(_vm.text))])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<!--\r\n * @Description: 1.0\r\n * @Version: 1.0\r\n * @Autor: hs\r\n * @Date: 2021-11-08 14:38:30\r\n * @LastEditors: hs\r\n * @LastEditTime: 2021-11-08 21:29:43\r\n-->\r\n<template>\r\n  <span\r\n    class=\"iot-btn\"\r\n    :class=\"[type ? 'iot-button-' + type : '']\"\r\n    v-throttle=\"500\"\r\n    @click=\"fn_search\"\r\n    >{{ text }}</span\r\n  >\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"Iot-btn\",\r\n  props: {\r\n    text: {\r\n      type: String,\r\n      default: \"搜索\",\r\n    },\r\n    bgcolor: {\r\n      type: String,\r\n      default: \"\",\r\n    },\r\n    type: {\r\n      type: String,\r\n      default: \"default\",\r\n    },\r\n  },\r\n  data() {\r\n    return {};\r\n  },\r\n  methods: {\r\n    fn_search() {\r\n      this.$emit(\"search\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.iot-btn {\r\n  text-align: center;\r\n  // color: #fff;\r\n  // background: linear-gradient(180deg, #0088fe, #006eff 100%);\r\n  // border-radius: 2px;\r\n  cursor: pointer;\r\n  padding: 7px 23px;\r\n  display: inline-block;\r\n  // font-family: H_Black;\r\n  font-size: 14px;\r\n  font-weight: normal;\r\n  // letter-spacing: 2px;\r\n  margin-right: 14px;\r\n}\r\n.iot-button-default {\r\n  background: linear-gradient(180deg, #0088fe, #006eff 100%);\r\n  color: #fff;\r\n  border: 1px solid #0088fe;\r\n}\r\n.iot-button-grey {\r\n  background: #bfbfbf;\r\n  color: #fff;\r\n}\r\n.iot-button-white {\r\n  background: #fff;\r\n  color: #333;\r\n  border: 1px solid #eeeff1;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=7022bc2e&scoped=true&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=7022bc2e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7022bc2e\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./404.vue?vue&type=style&index=0&id=78d43903&lang=scss&scoped=true&\""], "sourceRoot": ""}