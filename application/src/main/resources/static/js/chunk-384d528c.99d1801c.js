(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-384d528c"],{7687:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"device"},[a("div",{staticClass:"device-top"},[a("div",{staticClass:"device-top-search"},[a("div",{staticClass:"top-left"}),a("div",{staticClass:"top-right"},[a("el-input",{attrs:{clearable:"",placeholder:"输入设备名称"},on:{clear:e.handleClear},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.fn_handle__query.apply(null,arguments)}},model:{value:e.deviceName,callback:function(t){e.deviceName=t},expression:"deviceName"}},[a("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"suffix"},on:{click:e.fn_handle__query},slot:"suffix"})])],1)])]),a("div",{staticClass:"device-table"},[a("iot-table",{attrs:{columns:e.columns,data:e.tableData,loading:e.loading},on:{"selection-change":e.fn_select_more_data,"selection-del":e.fn_del_more_data,"del-callbackSure":e.fn_del_sure},scopedSlots:e._u([{key:"deviceName",fn:function(t){return[a("el-tooltip",{attrs:{placement:"top-start",effect:"light","popper-class":"event-tooltip"},scopedSlots:e._u([{key:"content",fn:function(){return[a("span",[e._v(" "+e._s(t.row.deviceName)+" ")])]},proxy:!0}],null,!0)},[a("div",{staticClass:"alarmContent-tooltip"},[a("p",[e._v(" "+e._s(e.fn_sub10(t.row.deviceName))+" ")])])])]}},{key:"deviceStatusName",fn:function(t){return[a("div",{staticClass:"table-status"},[3==t.row.deviceStatus?a("div",{staticClass:"status flex"},[a("div",{staticClass:"red"}),a("div",[e._v("离线")])]):e._e(),2==t.row.deviceStatus?a("div",{staticClass:"status flex"},[a("div",{staticClass:"green"}),a("div",[e._v("在线")])]):e._e(),1==t.row.deviceStatus?a("div",{staticClass:"status flex"},[a("div",{staticClass:"yellow"}),a("div",[e._v("未激活")])]):e._e()])]}},{key:"configInfo",fn:function(t){return[a("el-tooltip",{attrs:{placement:"top-start",effect:"light","popper-class":"event-tooltip"},scopedSlots:e._u([{key:"content",fn:function(){return[a("span",[e._v(" "+e._s(t.row.configInfo)+" ")])]},proxy:!0}],null,!0)},[a("div",{staticClass:"alarmContent-tooltip"},[a("p",[e._v(" "+e._s(e.fn_sub10(t.row.configInfo))+" ")])])])]}},{key:"operation",fn:function(t){return[a("div",{staticClass:"flex table-edit"},[a("p",{staticClass:"color2",on:{click:function(a){return e.fn_check(t.row)}}},[e._v("设备详情")])])]}}])})],1),e.tableData.length?a("div",{staticClass:"device-bottom"},[a("iot-pagination",{attrs:{pagination:e.pagination},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1):e._e()])},n=[],s=a("5530"),o=(a("ac1f"),a("5319"),a("d3b7"),a("d81d"),a("a15b"),a("b329"),a("6e22")),l=a("673a"),c=a("aa98"),r={name:"Device",components:{IotPagination:o["a"],IotTable:l["a"]},data:function(){return{deviceName:"",columns:[{label:"设备名称",prop:"deviceName",slotName:"deviceName"},{label:"所属厂商",prop:"vendorName"},{label:"所属连接器",prop:"connectorName"},{label:"设备SN号",prop:"deviceSn"},{label:"自定义配置",prop:"configInfo",slotName:"configInfo"},{label:"状态",prop:"deviceStatusName",slotName:"deviceStatusName"},{label:"创建时间",prop:"createTime"},{label:"操作",prop:"operation",slotName:"operation",width:120}],enableList:[{value:0,label:"未启用"},{value:1,label:"已启用"}],upLinkList:[],downLinkList:[],analyticModeList:[{value:"JS",label:"JS脚本解析"},{value:"JAR",label:"动态jar包解析"},{value:"INTERNAL",label:"内置解析"}],converterTypeList:[{value:"UP_LINK",label:"上行转换器"},{value:"DOWN_LINK",label:"下行转换器"}],tableData:[],pagination:{current:1,total:0,pages:0,sizes:[10,20,50,100],size:10},loading:!1,dialogWidth:"792px",type:1,visible:!1,inputHolder:"请输入搜索关键词",selectHolder:"请选择设备名称",searchValue:{productId:""},productOptions:[],productOptionsCopy:[],deviceOptions:[{id:"2",name:"设备厂商"}],statusCount:{totalNum:0,activeNum:0,onlineNum:0},deviceNameTrue:!0,analyticCodeTrue:!0,converterDescTrue:!0,title:"",delId:"",delIds:[]}},created:function(){},watch:{visible:function(e){e||1!=this.type||this.$refs["converterForm"]&&this.$refs["converterForm"].resetFields()}},mounted:function(){this.$route.query.id&&(this.searchValue.productId=this.$route.query.id);var e=Object(s["a"])(Object(s["a"])({},this.searchValue),{},{current:this.pagination.current,size:this.pagination.size});this.fn_get_table_data(e)},methods:{fn_check:function(e,t){var a=e.id,i=e.deviceSn;this.$router.push({path:"/equipmentDetail",query:{id:a,sn:i,num:t}})},handleReset:function(){this.pagination.current=1,this.fn_get_table_data()},relationDevice:function(e){this.$refs.relation.open(e)},fn_sub10:function(e){if(e)return e.length>20?"".concat(e.substr(0,20),"..."):e},fn_notNull:function(e){return 0!==e&&!e},fn_handle__query:function(){var e={deviceName:this.deviceName,current:1,size:this.pagination.size};this.fn_get_table_data(e)},handleClear:function(){this.fn_get_table_data()},calcul_long_text:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e.replace(/[\u0391-\uFFE5\u0800-\u4e00 ]/g,"aa").length},fn_select:function(){var e=Object(s["a"])({},this.searchValue);this.fn_get_table_data(e)},fn_get_table_data:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=Object(s["a"])({},t);t.size||(a.size=10,a.current=1),Object(c["p"])(a).then((function(t){200==t.code?(setTimeout((function(){e.loading=!1}),300),e.tableData=t.data.records,e.pagination.total=t.data.total,e.pagination.current=t.data.current,e.pagination.pages=t.data.pages,e.pagination.size=t.data.size):e.$newNotify.error({message:t.message})})).finally((function(){setTimeout((function(){e.loading=!1}),300)}))},fn_validate:function(e,t){"deviceName"===e&&(this.deviceNameTrue=t),"converterDesc"===e&&(this.converterDescTrue=t),"analyticCode"===e&&(this.analyticCodeTrue=t)},fn_select_more_data:function(e){this.delIds=e.map((function(e){return e.id}))},fn_del_more_data:function(){0!==this.delIds.length&&(this.columns[0].visible=!0)},fn_del_sure:function(){var e={ids:this.delIds.join(",")};this.fn_del_table_data(e)},fn_open:function(){this.title="添加转换器",this.type=1,this.dialogWidth="792px",this.visible=!0},fn_edit:function(e){this.converterForm=JSON.parse(JSON.stringify(e)),this.title="编辑转换器",this.type=1,this.dialogWidth="792px",this.visible=!0},fn_search_table_data:function(e){console.log(e),"1"===e.id?this.searchValue.aliasName=e.value:this.searchValue.deviceName=e.value;var t=Object(s["a"])({},this.searchValue);t.size=this.pagination.size,this.fn_get_table_data(t)},handleSizeChange:function(e){this.pagination.size=e;var t={size:this.pagination.size,current:1};this.fn_get_table_data(t)},handleCurrentChange:function(e){this.pagination.current=e;var t=Object(s["a"])(Object(s["a"])({},this.searchValue),{},{current:this.pagination.current,size:this.pagination.size});this.fn_get_table_data(t)},fn_clear_search_info:function(){this.searchValue.aliasName="",this.searchValue.deviceName="";var e=Object(s["a"])({},this.searchValue);this.fn_get_table_data(e)},fn_close:function(){this.connectorNameTrue=!0,this.vendorNameTrue=!0,this.applicationTypeTrue=!0,this.commonConfigTrue=!0,this.customConfigTrue=!0,this.connectorDescTrue=!0},fn_del:function(e){console.log(e),this.delId=e,this.title="确定删除该转换器？",this.type=2,this.dialogWidth="550px",this.visible=!0}}},u=r,d=(a("7a26"),a("2877")),f=Object(d["a"])(u,i,n,!1,null,"2675e123",null);t["default"]=f.exports},"7a26":function(e,t,a){"use strict";a("bc79")},a15b:function(e,t,a){"use strict";var i=a("23e7"),n=a("e330"),s=a("44ad"),o=a("fc6a"),l=a("a640"),c=n([].join),r=s!=Object,u=l("join",",");i({target:"Array",proto:!0,forced:r||!u},{join:function(e){return c(o(this),void 0===e?",":e)}})},bc79:function(e,t,a){}}]);
//# sourceMappingURL=chunk-384d528c.99d1801c.js.map