(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5ed55f75"],{"09b0":function(t,n,e){},"100e":function(t,n,e){"use strict";e.r(n);var s=function(){var t=this,n=t.$createElement,s=t._self._c||n;return s("div",{staticClass:"layout"},[s("div",{staticClass:"content-404 flex"},[s("div",{staticClass:"content-to"},[s("p",{staticClass:"big-font"},[t._v("Oops!")]),s("p",{staticClass:"sorry"},[t._v("sorry，您访问的页面不存在~")]),s("div",{staticClass:"seconds"},[s("span",[t._v("将于")]),t._v(" "),s("span",{staticClass:"color"},[t._v(t._s(t.count)+" ")]),t._v(" "),s("span",[t._v(" 秒后自动跳转登录页")])]),s("iot-button",{attrs:{text:"前往登录页"},on:{search:t.back}})],1),s("img",{attrs:{src:e("6ee6"),alt:""}})]),s("copyright",{staticClass:"copyright"})],1)},c=[],a=e("2f08"),o=e("c2a2"),i={components:{copyright:a["a"],IotButton:o["a"]},data:function(){return{count:5}},mounted:function(){this.countDown()},methods:{back:function(){this.$router.push({path:"/login"})},countDown:function(){var t=this,n=setInterval((function(){t.count--,t.count<=0&&(t.$router.push({path:"/login"}),clearInterval(n))}),1e3)}}},r=i,u=(e("c35f"),e("2877")),l=Object(u["a"])(r,s,c,!1,null,"78d43903",null);n["default"]=l.exports},"157e":function(t,n,e){"use strict";e("5ce1")},"2f08":function(t,n,e){"use strict";var s=function(){var t=this,n=t.$createElement;t._self._c;return t._m(0)},c=[function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("div",{staticClass:"copyright"},[e("span",[t._v("© 2009-2021 福建钰辰微电子有限公司 Fujian Yuchen Microelectronics Co. ,Ltd. 版权所有")])])}],a={},o=a,i=(e("157e"),e("2877")),r=Object(i["a"])(o,s,c,!1,null,"a9e39914",null);n["a"]=r.exports},"5ce1":function(t,n,e){},"6ee6":function(t,n,e){t.exports=e.p+"img/404.8ae6e8ee.png"},"7f0e":function(t,n,e){},"81a7":function(t,n,e){"use strict";e("7f0e")},c2a2:function(t,n,e){"use strict";var s=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("span",{directives:[{name:"throttle",rawName:"v-throttle",value:500,expression:"500"}],staticClass:"iot-btn",class:[t.type?"iot-button-"+t.type:""],on:{click:t.fn_search}},[t._v(t._s(t.text))])},c=[],a={name:"Iot-btn",props:{text:{type:String,default:"搜索"},bgcolor:{type:String,default:""},type:{type:String,default:"default"}},data:function(){return{}},methods:{fn_search:function(){this.$emit("search")}}},o=a,i=(e("81a7"),e("2877")),r=Object(i["a"])(o,s,c,!1,null,"7022bc2e",null);n["a"]=r.exports},c35f:function(t,n,e){"use strict";e("09b0")}}]);
//# sourceMappingURL=chunk-5ed55f75.ab2a2fc8.js.map