{"version": 3, "sources": ["webpack:///./src/views/equipment/index.vue?8055", "webpack:///src/views/equipment/index.vue", "webpack:///./src/views/equipment/index.vue?cc99", "webpack:///./src/views/equipment/index.vue", "webpack:///./src/views/equipment/index.vue?0cd7", "webpack:///./node_modules/core-js/modules/es.array.join.js"], "names": ["render", "_vm", "this", "_h", "$createElement", "_c", "_self", "staticClass", "attrs", "on", "handleClear", "nativeOn", "$event", "type", "indexOf", "_k", "keyCode", "key", "fn_handle__query", "apply", "arguments", "model", "value", "callback", "$$v", "deviceName", "expression", "slot", "columns", "tableData", "loading", "fn_select_more_data", "fn_del_more_data", "fn_del_sure", "scopedSlots", "_u", "fn", "scope", "_v", "_s", "row", "proxy", "fn_sub10", "deviceStatus", "_e", "configInfo", "fn_check", "pagination", "handleSizeChange", "handleCurrentChange", "staticRenderFns", "name", "components", "IotPagination", "IotTable", "data", "enableList", "upLinkList", "downLinkList", "analyticModeList", "converterTypeList", "current", "total", "pages", "sizes", "size", "dialogWidth", "visible", "inputHolder", "selectHolder", "searchValue", "productId", "productOptions", "productOptionsCopy", "deviceOptions", "statusCount", "totalNum", "activeNum", "onlineNum", "deviceNameTrue", "analyticCodeTrue", "converterDescTrue", "title", "delId", "delIds", "created", "watch", "val", "$refs", "resetFields", "mounted", "$route", "query", "id", "fn_get_table_data", "methods", "$router", "push", "path", "sn", "num", "handleReset", "relationDevice", "relation", "open", "str", "length", "fn_notNull", "params", "calcul_long_text", "replace", "fn_select", "others", "fn_validate", "map", "item", "ids", "join", "fn_del_table_data", "fn_open", "fn_edit", "converterForm", "JSON", "parse", "stringify", "fn_search_table_data", "console", "log", "<PERSON><PERSON><PERSON>", "fn_clear_search_info", "fn_close", "connectorNameTrue", "vendorNameTrue", "applicationTypeTrue", "commonConfigTrue", "customConfigTrue", "connectorDescTrue", "fn_del", "component", "$", "uncurryThis", "IndexedObject", "toIndexedObject", "arrayMethodIsStrict", "un$Join", "ES3_STRINGS", "Object", "STRICT_METHOD", "target", "proto", "forced", "separator", "undefined"], "mappings": "uHAAA,IAAIA,EAAS,WAAa,IAAIC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACE,YAAY,UAAU,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,MAAM,CAACE,YAAY,aAAaF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,WAAW,CAACG,MAAM,CAAC,UAAY,GAAG,YAAc,UAAUC,GAAG,CAAC,MAAQR,EAAIS,aAAaC,SAAS,CAAC,MAAQ,SAASC,GAAQ,OAAIA,EAAOC,KAAKC,QAAQ,QAAQb,EAAIc,GAAGH,EAAOI,QAAQ,QAAQ,GAAGJ,EAAOK,IAAI,SAAkB,KAAchB,EAAIiB,iBAAiBC,MAAM,KAAMC,aAAaC,MAAM,CAACC,MAAOrB,EAAc,WAAEsB,SAAS,SAAUC,GAAMvB,EAAIwB,WAAWD,GAAKE,WAAW,eAAe,CAACrB,EAAG,IAAI,CAACE,YAAY,gCAAgCC,MAAM,CAAC,KAAO,UAAUC,GAAG,CAAC,MAAQR,EAAIiB,kBAAkBS,KAAK,cAAc,OAAOtB,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,YAAY,CAACG,MAAM,CAAC,QAAUP,EAAI2B,QAAQ,KAAO3B,EAAI4B,UAAU,QAAU5B,EAAI6B,SAASrB,GAAG,CAAC,mBAAmBR,EAAI8B,oBAAoB,gBAAgB9B,EAAI+B,iBAAiB,mBAAmB/B,EAAIgC,aAAaC,YAAYjC,EAAIkC,GAAG,CAAC,CAAClB,IAAI,aAAamB,GAAG,SAASC,GAAO,MAAO,CAAChC,EAAG,aAAa,CAACG,MAAM,CAAC,UAAY,YAAY,OAAS,QAAQ,eAAe,iBAAiB0B,YAAYjC,EAAIkC,GAAG,CAAC,CAAClB,IAAI,UAAUmB,GAAG,WAAW,MAAO,CAAC/B,EAAG,OAAO,CAACJ,EAAIqC,GAAG,IAAIrC,EAAIsC,GAAGF,EAAMG,IAAIf,YAAY,SAASgB,OAAM,IAAO,MAAK,IAAO,CAACpC,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,IAAI,CAACJ,EAAIqC,GAAG,IAAIrC,EAAIsC,GAAGtC,EAAIyC,SAASL,EAAMG,IAAIf,aAAa,cAAc,CAACR,IAAI,mBAAmBmB,GAAG,SAASC,GAAO,MAAO,CAAChC,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAA4B,GAA1B8B,EAAMG,IAAIG,aAAmBtC,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,QAAQF,EAAG,MAAM,CAACJ,EAAIqC,GAAG,UAAUrC,EAAI2C,KAAgC,GAA1BP,EAAMG,IAAIG,aAAmBtC,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,UAAUF,EAAG,MAAM,CAACJ,EAAIqC,GAAG,UAAUrC,EAAI2C,KAAgC,GAA1BP,EAAMG,IAAIG,aAAmBtC,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,WAAWF,EAAG,MAAM,CAACJ,EAAIqC,GAAG,WAAWrC,EAAI2C,UAAU,CAAC3B,IAAI,aAAamB,GAAG,SAASC,GAAO,MAAO,CAAChC,EAAG,aAAa,CAACG,MAAM,CAAC,UAAY,YAAY,OAAS,QAAQ,eAAe,iBAAiB0B,YAAYjC,EAAIkC,GAAG,CAAC,CAAClB,IAAI,UAAUmB,GAAG,WAAW,MAAO,CAAC/B,EAAG,OAAO,CAACJ,EAAIqC,GAAG,IAAIrC,EAAIsC,GAAGF,EAAMG,IAAIK,YAAY,SAASJ,OAAM,IAAO,MAAK,IAAO,CAACpC,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,IAAI,CAACJ,EAAIqC,GAAG,IAAIrC,EAAIsC,GAAGtC,EAAIyC,SAASL,EAAMG,IAAIK,aAAa,cAAc,CAAC5B,IAAI,YAAYmB,GAAG,SAASC,GAAO,MAAO,CAAChC,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,IAAI,CAACE,YAAY,SAASE,GAAG,CAAC,MAAQ,SAASG,GAAQ,OAAOX,EAAI6C,SAAST,EAAMG,QAAQ,CAACvC,EAAIqC,GAAG,oBAAoB,GAAIrC,EAAI4B,UAAgB,OAAExB,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,iBAAiB,CAACG,MAAM,CAAC,WAAaP,EAAI8C,YAAYtC,GAAG,CAAC,cAAcR,EAAI+C,iBAAiB,iBAAiB/C,EAAIgD,wBAAwB,GAAGhD,EAAI2C,QACjvFM,EAAkB,G,8GCqHtB,GACEC,KAAM,SACNC,WAAY,CAEVC,cAAJ,OAEIC,SAAJ,QAEEC,KARF,WASI,MAAO,CACL9B,WAAY,GACZG,QAAS,CACf,CACQ,MAAR,OACQ,KAAR,aACQ,SAAR,cAEA,CACQ,MAAR,OACQ,KAAR,cAEA,CACQ,MAAR,QACQ,KAAR,iBAEA,CACQ,MAAR,QACQ,KAAR,YAEA,CACQ,MAAR,QACQ,KAAR,aACQ,SAAR,cAEA,CACQ,MAAR,KACQ,KAAR,mBACQ,SAAR,oBAEA,CAAQ,MAAR,OAAQ,KAAR,cACA,CACQ,MAAR,KACQ,KAAR,YACQ,SAAR,YACQ,MAAR,MAGM4B,WAAY,CAClB,CACQ,MAAR,EACQ,MAAR,OAEA,CACQ,MAAR,EACQ,MAAR,QAGMC,WAAY,GACZC,aAAc,GACdC,iBAAkB,CACxB,CACQ,MAAR,KACQ,MAAR,UAEA,CACQ,MAAR,MACQ,MAAR,YAEA,CACQ,MAAR,WACQ,MAAR,SAGMC,kBAAmB,CACzB,CACQ,MAAR,UACQ,MAAR,SAEA,CACQ,MAAR,YACQ,MAAR,UAGM/B,UAAW,GACXkB,WAAY,CACVc,QAAS,EACTC,MAAO,EACPC,MAAO,EACPC,MAAO,CAAC,GAAI,GAAI,GAAI,KACpBC,KAAM,IAGRnC,SAAS,EACToC,YAAa,QACbrD,KAAM,EACNsD,SAAS,EACTC,YAAa,WACbC,aAAc,UACdC,YAAa,CACXC,UAAW,IAGbC,eAAgB,GAChBC,mBAAoB,GAEpBC,cAAe,CACrB,CACQ,GAAR,IACQ,KAAR,SAGMC,YAAa,CACXC,SAAU,EACVC,UAAW,EACXC,UAAW,GAEbC,gBAAgB,EAChBC,kBAAkB,EAClBC,mBAAmB,EACnBC,MAAO,GACPC,MAAO,GAEPC,OAAQ,KAGZC,QA7HF,aA8HEC,MAAO,CACLnB,QADJ,SACA,GACWoB,GAAoB,GAAbrF,KAAKW,MACfX,KAAKsF,MAAM,kBAAoBtF,KAAKsF,MAAM,iBAAiBC,gBAKjEC,QAtIF,WAuIQxF,KAAKyF,OAAOC,MAAMC,KACpB3F,KAAKoE,YAAYC,UAAYrE,KAAKyF,OAAOC,MAAMC,IAEjD,IAAJ,mCACA,kBADA,IAEMhC,QAAS3D,KAAK6C,WAAWc,QACzBI,KAAM/D,KAAK6C,WAAWkB,OAGxB/D,KAAK4F,kBAAkBvC,IAEzBwC,QAAS,CACPjD,SADJ,SACA,KACM,IAAN,OACA,aACM5C,KAAK8F,QAAQC,KAAK,CAChBC,KAAM,mBACNN,MAAO,CACLC,GAAIA,EACJM,GAAIA,EACJC,IAAKA,MAIXC,YAbJ,WAcMnG,KAAK6C,WAAWc,QAAU,EAC1B3D,KAAK4F,qBAEPQ,eAjBJ,SAiBA,GACMpG,KAAKsF,MAAMe,SAASC,KAAKX,IAE3BnD,SApBJ,SAoBA,GACM,GAAI+D,EAAK,OAAOA,EAAIC,OAAS,GAAK,GAAxC,gCAEIC,WAvBJ,SAuBA,GACM,OAAe,IAARpB,IAAcA,GAGvBrE,iBA3BJ,WA4BM,IAAN,GACQO,WAAYvB,KAAKuB,WACjBoC,QAAS,EACTI,KAAM/D,KAAK6C,WAAWkB,MAExB/D,KAAK4F,kBAAkBc,IAEzBlG,YAnCJ,WAoCMR,KAAK4F,qBAEPe,iBAtCJ,WAsCA,gEACM,OAAOtB,EAAIuB,QAAQ,iCAAkC,MAAMJ,QAE7DK,UAzCJ,WA0CM,IAAN,sCACM7G,KAAK4F,kBAAkBvC,IAGzBuC,kBA9CJ,WA8CA,uEACA,uBACWc,EAAO3C,OACV+C,EAAO/C,KAAO,GACd+C,EAAOnD,QAAU,GAEnB,OAAN,OAAM,CAAN,GACA,kBACA,aACU,YAAV,WACY,EAAZ,aACA,KACU,EAAV,yBACU,EAAV,8BACU,EAAV,kCACU,EAAV,8BACU,EAAV,6BAEU,EAAV,kBACY,QAAZ,eAIA,oBACQ,YAAR,WACU,EAAV,aACA,SAIIoD,YA5EJ,SA4EA,KACmB,eAAT9D,IACFjD,KAAK6E,eAAiBzD,GAEX,kBAAT6B,IACFjD,KAAK+E,kBAAoB3D,GAGd,iBAAT6B,IACFjD,KAAK8E,iBAAmB1D,IAI5BS,oBAzFJ,SAyFA,GAEM7B,KAAKkF,OAAS7B,EAAK2D,KAAI,SAA7B,GACQ,OAAOC,EAAKtB,OAIhB7D,iBAhGJ,WAiGiC,IAAvB9B,KAAKkF,OAAOsB,SACdxG,KAAK0B,QAAQ,GAAGuC,SAAU,IAM9BlC,YAxGJ,WAyGM,IAAN,GACQmF,IAAKlH,KAAKkF,OAAOiC,KAAK,MAExBnH,KAAKoH,kBAAkB/D,IAGzBgE,QA/GJ,WAgHMrH,KAAKgF,MAAQ,QACbhF,KAAKW,KAAO,EACZX,KAAKgE,YAAc,QACnBhE,KAAKiE,SAAU,GAEjBqD,QArHJ,SAqHA,GACMtH,KAAKuH,cAAgBC,KAAKC,MAAMD,KAAKE,UAAUpF,IAC/CtC,KAAKgF,MAAQ,QACbhF,KAAKW,KAAO,EACZX,KAAKgE,YAAc,QACnBhE,KAAKiE,SAAU,GAGjB0D,qBA7HJ,SA6HA,GACMC,QAAQC,IAAInB,GACM,MAAdA,EAAOf,GACT3F,KAAKoE,YAAY0D,UAAYpB,EAAOtF,MAEpCpB,KAAKoE,YAAY7C,WAAamF,EAAOtF,MAEvC,IAAN,sCACMiC,EAAKU,KAAO/D,KAAK6C,WAAWkB,KAC5B/D,KAAK4F,kBAAkBvC,IAGzBP,iBAzIJ,SAyIA,GAEM9C,KAAK6C,WAAWkB,KAAOsB,EACvB,IAAN,GACQtB,KAAM/D,KAAK6C,WAAWkB,KACtBJ,QAAS,GAEX3D,KAAK4F,kBAAkBc,IAGzB3D,oBAnJJ,SAmJA,GAEM/C,KAAK6C,WAAWc,QAAU0B,EAC1B,IAAN,mCACA,kBADA,IAEQ1B,QAAS3D,KAAK6C,WAAWc,QACzBI,KAAM/D,KAAK6C,WAAWkB,OAExB/D,KAAK4F,kBAAkBc,IAGzBqB,qBA9JJ,WA+JM/H,KAAKoE,YAAY0D,UAAY,GAC7B9H,KAAKoE,YAAY7C,WAAa,GAC9B,IAAN,sCACMvB,KAAK4F,kBAAkBvC,IAEzB2E,SApKJ,WAqKMhI,KAAKiI,mBAAoB,EACzBjI,KAAKkI,gBAAiB,EACtBlI,KAAKmI,qBAAsB,EAC3BnI,KAAKoI,kBAAmB,EACxBpI,KAAKqI,kBAAmB,EACxBrI,KAAKsI,mBAAoB,GAG3BC,OA7KJ,SA6KA,GACMX,QAAQC,IAAIlC,GACZ3F,KAAKiF,MAAQU,EACb3F,KAAKgF,MAAQ,YACbhF,KAAKW,KAAO,EACZX,KAAKgE,YAAc,QACnBhE,KAAKiE,SAAU,KC3byU,I,wBCQ1VuE,EAAY,eACd,EACA1I,EACAkD,GACA,EACA,KACA,WACA,MAIa,aAAAwF,E,6CCnBf,W,kCCCA,IAAIC,EAAI,EAAQ,QACZC,EAAc,EAAQ,QACtBC,EAAgB,EAAQ,QACxBC,EAAkB,EAAQ,QAC1BC,EAAsB,EAAQ,QAE9BC,EAAUJ,EAAY,GAAGvB,MAEzB4B,EAAcJ,GAAiBK,OAC/BC,EAAgBJ,EAAoB,OAAQ,KAIhDJ,EAAE,CAAES,OAAQ,QAASC,OAAO,EAAMC,OAAQL,IAAgBE,GAAiB,CACzE9B,KAAM,SAAckC,GAClB,OAAOP,EAAQF,EAAgB5I,WAAqBsJ,IAAdD,EAA0B,IAAMA,O", "file": "js/chunk-384d528c.99d1801c.js", "sourcesContent": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"device\"},[_c('div',{staticClass:\"device-top\"},[_c('div',{staticClass:\"device-top-search\"},[_c('div',{staticClass:\"top-left\"}),_c('div',{staticClass:\"top-right\"},[_c('el-input',{attrs:{\"clearable\":\"\",\"placeholder\":\"输入设备名称\"},on:{\"clear\":_vm.handleClear},nativeOn:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\")){ return null; }return _vm.fn_handle__query.apply(null, arguments)}},model:{value:(_vm.deviceName),callback:function ($$v) {_vm.deviceName=$$v},expression:\"deviceName\"}},[_c('i',{staticClass:\"el-input__icon el-icon-search\",attrs:{\"slot\":\"suffix\"},on:{\"click\":_vm.fn_handle__query},slot:\"suffix\"})])],1)])]),_c('div',{staticClass:\"device-table\"},[_c('iot-table',{attrs:{\"columns\":_vm.columns,\"data\":_vm.tableData,\"loading\":_vm.loading},on:{\"selection-change\":_vm.fn_select_more_data,\"selection-del\":_vm.fn_del_more_data,\"del-callbackSure\":_vm.fn_del_sure},scopedSlots:_vm._u([{key:\"deviceName\",fn:function(scope){return [_c('el-tooltip',{attrs:{\"placement\":\"top-start\",\"effect\":\"light\",\"popper-class\":\"event-tooltip\"},scopedSlots:_vm._u([{key:\"content\",fn:function(){return [_c('span',[_vm._v(\" \"+_vm._s(scope.row.deviceName)+\" \")])]},proxy:true}],null,true)},[_c('div',{staticClass:\"alarmContent-tooltip\"},[_c('p',[_vm._v(\" \"+_vm._s(_vm.fn_sub10(scope.row.deviceName))+\" \")])])])]}},{key:\"deviceStatusName\",fn:function(scope){return [_c('div',{staticClass:\"table-status\"},[(scope.row.deviceStatus == 3)?_c('div',{staticClass:\"status flex\"},[_c('div',{staticClass:\"red\"}),_c('div',[_vm._v(\"离线\")])]):_vm._e(),(scope.row.deviceStatus == 2)?_c('div',{staticClass:\"status flex\"},[_c('div',{staticClass:\"green\"}),_c('div',[_vm._v(\"在线\")])]):_vm._e(),(scope.row.deviceStatus == 1)?_c('div',{staticClass:\"status flex\"},[_c('div',{staticClass:\"yellow\"}),_c('div',[_vm._v(\"未激活\")])]):_vm._e()])]}},{key:\"configInfo\",fn:function(scope){return [_c('el-tooltip',{attrs:{\"placement\":\"top-start\",\"effect\":\"light\",\"popper-class\":\"event-tooltip\"},scopedSlots:_vm._u([{key:\"content\",fn:function(){return [_c('span',[_vm._v(\" \"+_vm._s(scope.row.configInfo)+\" \")])]},proxy:true}],null,true)},[_c('div',{staticClass:\"alarmContent-tooltip\"},[_c('p',[_vm._v(\" \"+_vm._s(_vm.fn_sub10(scope.row.configInfo))+\" \")])])])]}},{key:\"operation\",fn:function(scope){return [_c('div',{staticClass:\"flex table-edit\"},[_c('p',{staticClass:\"color2\",on:{\"click\":function($event){return _vm.fn_check(scope.row)}}},[_vm._v(\"设备详情\")])])]}}])})],1),(_vm.tableData.length)?_c('div',{staticClass:\"device-bottom\"},[_c('iot-pagination',{attrs:{\"pagination\":_vm.pagination},on:{\"size-change\":_vm.handleSizeChange,\"current-change\":_vm.handleCurrentChange}})],1):_vm._e()])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<!--\r\n * @Description: 1.0\r\n * @Version: 1.0\r\n * @Autor: hs\r\n * @Date: 2021-11-08 09:51:45\r\n * @LastEditors: lb <EMAIL>\r\n * @LastEditTime: 2023-05-16 15:46:53\r\n-->\r\n<template>\r\n  <div class=\"device\">\r\n    <div class=\"device-top\">\r\n      <div class=\"device-top-search\">\r\n        <div class=\"top-left\">\r\n\r\n        </div>\r\n        <div class=\"top-right\">\r\n          <!-- 搜索栏 -->\r\n          <el-input v-model=\"deviceName\"\r\n                    @keyup.enter.native=\"fn_handle__query\"\r\n                    clearable\r\n                    placeholder=\"输入设备名称\"\r\n                    @clear=\"handleClear\">\r\n            <i slot=\"suffix\"\r\n               class=\"el-input__icon el-icon-search\"\r\n               @click=\"fn_handle__query\"></i>\r\n          </el-input>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"device-table\">\r\n      <!-- 表格 -->\r\n      <iot-table :columns=\"columns\"\r\n                 :data=\"tableData\"\r\n                 :loading=\"loading\"\r\n                 @selection-change=\"fn_select_more_data\"\r\n                 @selection-del=\"fn_del_more_data\"\r\n                 @del-callbackSure=\"fn_del_sure\">\r\n        <template slot=\"deviceName\"\r\n                  slot-scope=\"scope\">\r\n          <el-tooltip placement=\"top-start\"\r\n                      effect=\"light\"\r\n                      popper-class=\"event-tooltip\">\r\n            <template #content>\r\n              <span>\r\n                {{scope.row.deviceName}}\r\n              </span>\r\n            </template>\r\n            <div class=\"alarmContent-tooltip\">\r\n              <p>\r\n                {{fn_sub10(scope.row.deviceName)}}\r\n              </p>\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot=\"deviceStatusName\"\r\n                  slot-scope=\"scope\">\r\n          <div class=\"table-status\">\r\n            <div class=\"status flex\"\r\n                 v-if=\"scope.row.deviceStatus == 3\">\r\n              <div class=\"red\"></div>\r\n              <div>离线</div>\r\n            </div>\r\n            <div class=\"status flex\"\r\n                 v-if=\"scope.row.deviceStatus == 2\">\r\n              <div class=\"green\"></div>\r\n              <div>在线</div>\r\n            </div>\r\n            <div class=\"status flex\"\r\n                 v-if=\"scope.row.deviceStatus == 1\">\r\n              <div class=\"yellow\"></div>\r\n              <div>未激活</div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n        <template slot=\"configInfo\"\r\n                  slot-scope=\"scope\">\r\n          <el-tooltip placement=\"top-start\"\r\n                      effect=\"light\"\r\n                      popper-class=\"event-tooltip\">\r\n            <template #content>\r\n              <span>\r\n                {{scope.row.configInfo}}\r\n              </span>\r\n            </template>\r\n            <div class=\"alarmContent-tooltip\">\r\n              <p>\r\n                {{fn_sub10(scope.row.configInfo)}}\r\n              </p>\r\n            </div>\r\n          </el-tooltip>\r\n        </template>\r\n        <template slot=\"operation\"\r\n                  slot-scope=\"scope\">\r\n          <div class=\"flex table-edit\">\r\n            <p @click=\"fn_check(scope.row)\"\r\n               class=\"color2\">设备详情</p>\r\n          </div>\r\n        </template>\r\n      </iot-table>\r\n    </div>\r\n\r\n    <div class=\"device-bottom\"\r\n         v-if=\"tableData.length\">\r\n      <!-- 分页 -->\r\n      <iot-pagination :pagination=\"pagination\"\r\n                      @size-change=\"handleSizeChange\"\r\n                      @current-change=\"handleCurrentChange\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport FormSearch from '@/components/form-search'\r\nimport IotPagination from '@/components/iot-pagination'\r\nimport IotTable from '@/components/iot-table'\r\nimport { getEquipmentList } from '@/api/device'\r\n\r\nexport default {\r\n  name: 'Device',\r\n  components: {\r\n    // IotForm,\r\n    IotPagination,\r\n    // IotDialog,\r\n    IotTable,\r\n  },\r\n  data() {\r\n    return {\r\n      deviceName: '',\r\n      columns: [\r\n        {\r\n          label: '设备名称',\r\n          prop: 'deviceName',\r\n          slotName: 'deviceName',\r\n        },\r\n        {\r\n          label: '所属厂商',\r\n          prop: 'vendorName',\r\n        },\r\n        {\r\n          label: '所属连接器',\r\n          prop: 'connectorName',\r\n        },\r\n        {\r\n          label: '设备SN号',\r\n          prop: 'deviceSn',\r\n        },\r\n        {\r\n          label: '自定义配置',\r\n          prop: 'configInfo',\r\n          slotName: 'configInfo',\r\n        },\r\n        {\r\n          label: '状态',\r\n          prop: 'deviceStatusName',\r\n          slotName: 'deviceStatusName',\r\n        },\r\n        { label: '创建时间', prop: 'createTime' },\r\n        {\r\n          label: '操作',\r\n          prop: 'operation',\r\n          slotName: 'operation',\r\n          width: 120,\r\n        },\r\n      ],\r\n      enableList: [\r\n        {\r\n          value: 0,\r\n          label: '未启用',\r\n        },\r\n        {\r\n          value: 1,\r\n          label: '已启用',\r\n        },\r\n      ],\r\n      upLinkList: [],\r\n      downLinkList: [],\r\n      analyticModeList: [\r\n        {\r\n          value: 'JS',\r\n          label: 'JS脚本解析',\r\n        },\r\n        {\r\n          value: 'JAR',\r\n          label: '动态jar包解析',\r\n        },\r\n        {\r\n          value: 'INTERNAL',\r\n          label: '内置解析',\r\n        },\r\n      ],\r\n      converterTypeList: [\r\n        {\r\n          value: 'UP_LINK',\r\n          label: '上行转换器',\r\n        },\r\n        {\r\n          value: 'DOWN_LINK',\r\n          label: '下行转换器',\r\n        },\r\n      ],\r\n      tableData: [],\r\n      pagination: {\r\n        current: 1, // 当前页\r\n        total: 0, // 记录条数\r\n        pages: 0, // 总页数\r\n        sizes: [10, 20, 50, 100], // 每一页的数量  [10, 20, 50, 100]\r\n        size: 10,\r\n      },\r\n      // 加载效果开关\r\n      loading: false,\r\n      dialogWidth: '792px',\r\n      type: 1, //1 查看 2 删除\r\n      visible: false,\r\n      inputHolder: '请输入搜索关键词',\r\n      selectHolder: '请选择设备名称',\r\n      searchValue: {\r\n        productId: '',\r\n      },\r\n      // 产品列表\r\n      productOptions: [],\r\n      productOptionsCopy: [],\r\n      // 设备列表\r\n      deviceOptions: [\r\n        {\r\n          id: '2',\r\n          name: '设备厂商',\r\n        },\r\n      ],\r\n      statusCount: {\r\n        totalNum: 0,\r\n        activeNum: 0,\r\n        onlineNum: 0,\r\n      },\r\n      deviceNameTrue: true,\r\n      analyticCodeTrue: true,\r\n      converterDescTrue: true,\r\n      title: '',\r\n      delId: '',\r\n      // 多选删除\r\n      delIds: [],\r\n    }\r\n  },\r\n  created() {},\r\n  watch: {\r\n    visible(val) {\r\n      if (!val && this.type == 1) {\r\n        this.$refs['converterForm'] && this.$refs['converterForm'].resetFields()\r\n      }\r\n    },\r\n  },\r\n  // keepalive 生命周期      //组件激活时触发\r\n  mounted() {\r\n    if (this.$route.query.id) {\r\n      this.searchValue.productId = this.$route.query.id\r\n    }\r\n    let data = {\r\n      ...this.searchValue,\r\n      current: this.pagination.current,\r\n      size: this.pagination.size,\r\n    }\r\n\r\n    this.fn_get_table_data(data)\r\n  },\r\n  methods: {\r\n    fn_check(data, num) {\r\n      let id = data.id\r\n      let sn = data.deviceSn\r\n      this.$router.push({\r\n        path: '/equipmentDetail',\r\n        query: {\r\n          id: id,\r\n          sn: sn,\r\n          num: num,\r\n        },\r\n      })\r\n    },\r\n    handleReset() {\r\n      this.pagination.current = 1\r\n      this.fn_get_table_data()\r\n    },\r\n    relationDevice(id) {\r\n      this.$refs.relation.open(id)\r\n    },\r\n    fn_sub10(str) {\r\n      if (str) return str.length > 20 ? `${str.substr(0, 20)}...` : str\r\n    },\r\n    fn_notNull(val) {\r\n      return val !== 0 && !val\r\n    },\r\n    // 输入框icon查询\r\n    fn_handle__query() {\r\n      let params = {\r\n        deviceName: this.deviceName,\r\n        current: 1,\r\n        size: this.pagination.size,\r\n      }\r\n      this.fn_get_table_data(params)\r\n    },\r\n    handleClear() {\r\n      this.fn_get_table_data()\r\n    },\r\n    calcul_long_text(val = '') {\r\n      return val.replace(/[\\u0391-\\uFFE5\\u0800-\\u4e00 ]/g, 'aa').length\r\n    },\r\n    fn_select() {\r\n      let data = { ...this.searchValue }\r\n      this.fn_get_table_data(data)\r\n    },\r\n    // 设备列表-表格数据接口\r\n    fn_get_table_data(params = {}) {\r\n      let others = { ...params }\r\n      if (!params.size) {\r\n        others.size = 10\r\n        others.current = 1\r\n      }\r\n      getEquipmentList(others)\r\n        .then((res) => {\r\n          if (res.code == 200) {\r\n            setTimeout(() => {\r\n              this.loading = false\r\n            }, 300)\r\n            this.tableData = res.data.records\r\n            this.pagination.total = res.data.total\r\n            this.pagination.current = res.data.current\r\n            this.pagination.pages = res.data.pages\r\n            this.pagination.size = res.data.size\r\n          } else {\r\n            this.$newNotify.error({\r\n              message: res.message,\r\n            })\r\n          }\r\n        })\r\n        .finally(() => {\r\n          setTimeout(() => {\r\n            this.loading = false\r\n          }, 300)\r\n        })\r\n    },\r\n    // 表单验证触发\r\n    fn_validate(name, value) {\r\n      if (name === 'deviceName') {\r\n        this.deviceNameTrue = value\r\n      }\r\n      if (name === 'converterDesc') {\r\n        this.converterDescTrue = value\r\n      }\r\n\r\n      if (name === 'analyticCode') {\r\n        this.analyticCodeTrue = value\r\n      }\r\n    },\r\n    // 多选数据\r\n    fn_select_more_data(data) {\r\n      // console.log(data)\r\n      this.delIds = data.map((item) => {\r\n        return item.id\r\n      })\r\n    },\r\n    // 多选删除按钮\r\n    fn_del_more_data() {\r\n      if (this.delIds.length !== 0) {\r\n        this.columns[0].visible = true\r\n      } else {\r\n        return\r\n      }\r\n    },\r\n    // 多选删除确定按钮\r\n    fn_del_sure() {\r\n      let data = {\r\n        ids: this.delIds.join(','),\r\n      }\r\n      this.fn_del_table_data(data)\r\n    },\r\n    // 打开dialog\r\n    fn_open() {\r\n      this.title = '添加转换器'\r\n      this.type = 1\r\n      this.dialogWidth = '792px'\r\n      this.visible = true\r\n    },\r\n    fn_edit(row) {\r\n      this.converterForm = JSON.parse(JSON.stringify(row))\r\n      this.title = '编辑转换器'\r\n      this.type = 1\r\n      this.dialogWidth = '792px'\r\n      this.visible = true\r\n    },\r\n    // 搜索\r\n    fn_search_table_data(params) {\r\n      console.log(params)\r\n      if (params.id === '1') {\r\n        this.searchValue.aliasName = params.value\r\n      } else {\r\n        this.searchValue.deviceName = params.value\r\n      }\r\n      let data = { ...this.searchValue }\r\n      data.size = this.pagination.size\r\n      this.fn_get_table_data(data)\r\n    },\r\n    // 当前页总条数\r\n    handleSizeChange(val) {\r\n      // console.log(`每页 ${val} 条`)\r\n      this.pagination.size = val\r\n      let params = {\r\n        size: this.pagination.size,\r\n        current: 1,\r\n      }\r\n      this.fn_get_table_data(params)\r\n    },\r\n    // 当前页\r\n    handleCurrentChange(val) {\r\n      // console.log(`当前页: ${val}`)\r\n      this.pagination.current = val\r\n      let params = {\r\n        ...this.searchValue,\r\n        current: this.pagination.current,\r\n        size: this.pagination.size,\r\n      }\r\n      this.fn_get_table_data(params)\r\n    },\r\n    // 清除输入搜索\r\n    fn_clear_search_info() {\r\n      this.searchValue.aliasName = ''\r\n      this.searchValue.deviceName = ''\r\n      let data = { ...this.searchValue }\r\n      this.fn_get_table_data(data)\r\n    },\r\n    fn_close() {\r\n      this.connectorNameTrue = true\r\n      this.vendorNameTrue = true\r\n      this.applicationTypeTrue = true\r\n      this.commonConfigTrue = true\r\n      this.customConfigTrue = true\r\n      this.connectorDescTrue = true\r\n    },\r\n    // 行删除\r\n    fn_del(id) {\r\n      console.log(id)\r\n      this.delId = id\r\n      this.title = '确定删除该转换器？'\r\n      this.type = 2\r\n      this.dialogWidth = '550px'\r\n      this.visible = true\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.device {\r\n  padding-bottom: 20px;\r\n  .device-top {\r\n    font-family: HarmonyOS Sans SC;\r\n    .device-top-count {\r\n      margin-top: 18px;\r\n      .el-select {\r\n        margin-right: 48px;\r\n      }\r\n      /deep/ .el-input__inner {\r\n        border-radius: 0;\r\n      }\r\n      .point {\r\n        margin: 0 10px;\r\n      }\r\n      p {\r\n        display: inline-block;\r\n        font-size: 14px;\r\n        line-height: 16px;\r\n        letter-spacing: 1px;\r\n        font-weight: normal;\r\n      }\r\n      span {\r\n        font-size: 18px;\r\n        line-height: 20px;\r\n        margin: 0 6px;\r\n        font-weight: 500;\r\n      }\r\n    }\r\n    .device-top-search {\r\n      margin: 18px 0 18px 0;\r\n      width: 100%;\r\n      display: flex;\r\n      justify-content: space-between;\r\n    }\r\n  }\r\n  .device-table {\r\n    .table-edit {\r\n      display: flex;\r\n      align-items: center;\r\n      p {\r\n        flex-shrink: 0;\r\n        cursor: pointer;\r\n      }\r\n      p:nth-child(2) {\r\n        margin: 0px 12px;\r\n        width: 1px;\r\n        height: 13px;\r\n        border: 1px solid #ededed;\r\n      }\r\n      p:nth-child(4) {\r\n        margin: 0px 12px;\r\n        width: 1px;\r\n        height: 13px;\r\n        border: 1px solid #ededed;\r\n      }\r\n      p:nth-child(6) {\r\n        margin: 0px 12px;\r\n        width: 1px;\r\n        height: 13px;\r\n        border: 1px solid #ededed;\r\n      }\r\n    }\r\n    .table-status {\r\n      .status {\r\n        .red {\r\n          background: #ff4d4f;\r\n          width: 8px;\r\n          height: 8px;\r\n          border-radius: 4px;\r\n          margin-top: 8px;\r\n          margin-right: 6px;\r\n        }\r\n        .green {\r\n          background: #00c250;\r\n          width: 8px;\r\n          height: 8px;\r\n          border-radius: 4px;\r\n          margin-top: 8px;\r\n          margin-right: 6px;\r\n        }\r\n        .yellow {\r\n          background: #e6a23c;\r\n          width: 8px;\r\n          height: 8px;\r\n          border-radius: 4px;\r\n          margin-top: 8px;\r\n          margin-right: 6px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .device-bottom {\r\n    text-align: right;\r\n    margin-top: 14px;\r\n  }\r\n  .del-tips {\r\n    width: 420px;\r\n  }\r\n  .converterForm {\r\n    /deep/ .el-form-item {\r\n      margin-bottom: 24px;\r\n    }\r\n    .el-form-tips {\r\n      // margin-top: -17px;\r\n      margin-top: -22px;\r\n      margin-bottom: 6px;\r\n    }\r\n  }\r\n  .specialDesc {\r\n    padding: 11px 0 11px 14px;\r\n    background-color: rgba(1, 138, 255, 0.08);\r\n    margin-bottom: 18px;\r\n    span {\r\n      font-size: 12px;\r\n      line-height: 14px;\r\n    }\r\n    img {\r\n      display: inline-block;\r\n      width: 14px;\r\n      height: 14px;\r\n      line-height: 14px;\r\n      margin-right: 10px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=2675e123&scoped=true&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=2675e123&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2675e123\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=2675e123&lang=scss&scoped=true&\"", "'use strict';\nvar $ = require('../internals/export');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar IndexedObject = require('../internals/indexed-object');\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar arrayMethodIsStrict = require('../internals/array-method-is-strict');\n\nvar un$Join = uncurryThis([].join);\n\nvar ES3_STRINGS = IndexedObject != Object;\nvar STRICT_METHOD = arrayMethodIsStrict('join', ',');\n\n// `Array.prototype.join` method\n// https://tc39.es/ecma262/#sec-array.prototype.join\n$({ target: 'Array', proto: true, forced: ES3_STRINGS || !STRICT_METHOD }, {\n  join: function join(separator) {\n    return un$Join(toIndexedObject(this), separator === undefined ? ',' : separator);\n  }\n});\n"], "sourceRoot": ""}