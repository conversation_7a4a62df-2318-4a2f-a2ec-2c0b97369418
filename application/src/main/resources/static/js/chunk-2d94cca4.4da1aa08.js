(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d94cca4"],{"8b90":function(t,e,i){},9927:function(t,e,i){"use strict";i("8b90")},c329:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"update-info"},[i("div",{staticClass:"info-info"},[t._m(0),i("div",{staticClass:"info-detail"},[i("div",{staticClass:"item-rows"},[i("div",{staticClass:"item"},[i("span",[t._v("产品key")]),i("span",[t._v(t._s(t.connectorDetail.productKey))])]),i("div",{staticClass:"item"},[i("span",[t._v("产品名称")]),i("span",[t._v(t._s(t.connectorDetail.productName))])])]),i("div",{staticClass:"item-rows"},[i("div",{staticClass:"item"},[i("span",[t._v("设备SN")]),i("span",[t._v(t._s(t.connectorDetail.deviceSn))])]),i("div",{staticClass:"item"},[i("span",[t._v("设备名称")]),i("span",[t._v(t._s(t.connectorDetail.deviceName))])])]),i("div",{staticClass:"item-rows"},[i("div",{staticClass:"item"},[i("span",[t._v("操作时间")]),i("span",[t._v(t._s(t.connectorDetail.operatorTime))])]),i("div",{staticClass:"item"},[i("span",[t._v("操作对象")]),i("span",[t._v(t._s(t.connectorDetail.operatorObject))])])]),i("div",{staticClass:"item-rows"},[i("div",{staticClass:"item"},[i("span",[t._v("组名称")]),i("span",[t._v(t._s(t.connectorDetail.groupName))])]),i("div",{staticClass:"item"},[i("span",[t._v("设备类型")]),i("span",[t._v(t._s(t.connectorDetail.deviceTypeName))])])]),i("div",{staticClass:"item-rows"},[i("div",{staticClass:"item"},[i("span",[t._v("创建时间")]),i("span",[t._v(t._s(t.connectorDetail.createTime))])]),i("div",{staticClass:"item"},[i("span",[t._v("更新时间")]),i("span",[t._v(t._s(t.connectorDetail.updateTime))])])])])])])},n=[function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"info-title flex"},[i("div",{staticClass:"left"},[i("p",[t._v("组设备信息")])]),i("div",{staticClass:"right"})])}],s=i("0e0b"),c=(i("2ef0"),i("aa98")),o={name:"updateInfo",components:{},data:function(){return{columns:[{label:"设备Key",prop:"deviceName"},{label:"设备SN",prop:"deviceSn"},{label:"设备名称",prop:"deviceName"},{label:"操作时间",prop:"createTime"},{label:"操作对象",prop:"deviceStatusName"},{label:"创建时间",prop:"createTime"},{label:"更新时间",prop:"createTime"}],tableData:[],loading:!1,pagination:{current:1,total:0,pages:0,sizes:[10,20,50,100],size:4},visible:!1,title:"编辑固件信息",dialogWidth:"742px",infoForm:{id:0,name:"",description:""},rules:{name:[{required:!0,trigger:"blur",validator:this.checkName}],description:[{required:!1,trigger:"blur",validator:this.checkLength}]},nameTrue:!0,descTrue:!0,connectorDetail:{},firmwareJobDetailForm:{},firmwareJobStatic:{},firmwareJobList:{},groupId:"",inputHolder:"输入设备名称",jobId:""}},created:function(){this.groupId=this.$route.query.id},mounted:function(){this.fn_getConnectorDetail(),this.fn_get_table_data()},methods:{fn_sub10:function(t){return t.length>20?"".concat(t.substr(0,20),"..."):t},fn_getConnectorDetail:function(){var t=this;Object(c["h"])({id:this.groupId}).then((function(e){if(200==e.code){var i={id:e.data.id,title:e.data.connectorName};t.$store.dispatch("setLayoutInfo",i),t.connectorDetail=e.data}}))},fn_notNull:function(t){return 0!==t&&!t},handleSearch:function(t){this.deviceName=t.value,this.fn_get_table_data({groupId:this.groupId,deviceName:t.value})},fn_clear_search_info:function(){this.deviceName="",this.fn_get_table_data({groupId:this.groupId})},checkName:function(t,e,i){return this.fn_notNull(e)?i(new Error("请输入固件名称")):Object(s["d"])(e)?void i():i(new Error("支持中文、英文大小写、数字、部分常用符号（下划线，减号，括弧）， 必须以中文、英文或数字开头，长度不超过32个字符"))},fn_validate:function(t,e){"name"===t&&(this.nameTrue=e),"description"===t&&(this.descTrue=e)},checkLength:function(t,e,i){if(!Object(s["c"])(e,201))return i(new Error("最多不超过200个字符"));i()}}},r=o,l=(i("9927"),i("2877")),d=Object(l["a"])(r,a,n,!1,null,"2c3e977c",null);e["default"]=d.exports}}]);
//# sourceMappingURL=chunk-2d94cca4.4da1aa08.js.map