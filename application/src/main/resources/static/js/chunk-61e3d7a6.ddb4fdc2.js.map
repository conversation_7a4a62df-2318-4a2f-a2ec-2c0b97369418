{"version": 3, "sources": ["webpack:///./src/views/connector/list/connectorDetail/index.vue?0cca", "webpack:///src/views/connector/list/connectorDetail/index.vue", "webpack:///./src/views/connector/list/connectorDetail/index.vue?3b89", "webpack:///./src/views/connector/list/connectorDetail/index.vue", "webpack:///./src/views/connector/list/connectorDetail/index.vue?42b9"], "names": ["render", "_vm", "this", "_h", "$createElement", "_c", "_self", "staticClass", "_m", "_v", "_s", "connectorDetail", "connectorName", "protocolType", "enableStatusName", "applicationType", "upLinkName", "downLinkName", "staticStyle", "commonConfig", "customConfig", "createTime", "updateTime", "attrs", "inputHolder", "on", "handleSearch", "fn_clear_search_info", "columns", "tableData", "loading", "scopedSlots", "_u", "key", "fn", "scope", "row", "configInfo", "proxy", "fn_sub10", "length", "pagination", "handleSizeChange", "handleCurrentChange", "_e", "staticRenderFns", "name", "components", "IotTable", "IotPagination", "FormSearch", "data", "current", "total", "pages", "sizes", "size", "visible", "title", "dialogWidth", "infoForm", "id", "description", "rules", "nameTrue", "<PERSON>c<PERSON><PERSON>", "firmwareJobDetailForm", "firmwareJobStatic", "firmwareJobList", "connectorId", "jobId", "created", "$route", "query", "mounted", "fn_getConnectorDetail", "fn_get_table_data", "methods", "str", "res", "code", "params", "others", "val", "console", "log", "fn_notNull", "deviceName", "from", "value", "checkName", "callback", "Error", "fn_validate", "checkLength", "component"], "mappings": "kJAAA,IAAIA,EAAS,WAAa,IAAIC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACN,EAAIO,GAAG,GAAGH,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,OAAO,CAACJ,EAAIQ,GAAG,WAAWJ,EAAG,OAAO,CAACJ,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,gBAAgBC,oBAAoBP,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,OAAO,CAACJ,EAAIQ,GAAG,UAAUJ,EAAG,OAAO,CAACJ,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,gBAAgBE,qBAAqBR,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,OAAO,CAACJ,EAAIQ,GAAG,UAAUJ,EAAG,OAAO,CAACJ,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,gBAAgBG,uBAAuBT,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,OAAO,CAACJ,EAAIQ,GAAG,UAAUJ,EAAG,OAAO,CAACJ,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,gBAAgBI,wBAAwBV,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,OAAO,CAACJ,EAAIQ,GAAG,WAAWJ,EAAG,OAAO,CAACJ,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,gBAAgBK,iBAAiBX,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,OAAO,CAACJ,EAAIQ,GAAG,WAAWJ,EAAG,OAAO,CAACJ,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,gBAAgBM,qBAAqBZ,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,OAAOW,YAAY,CAAC,MAAQ,SAAS,CAACb,EAAG,OAAO,CAACJ,EAAIQ,GAAG,UAAUJ,EAAG,OAAO,CAACJ,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,gBAAgBQ,qBAAqBd,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,OAAOW,YAAY,CAAC,MAAQ,SAAS,CAACb,EAAG,OAAO,CAACJ,EAAIQ,GAAG,WAAWJ,EAAG,OAAO,CAACJ,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,gBAAgBS,qBAAqBf,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,OAAO,CAACJ,EAAIQ,GAAG,UAAUJ,EAAG,OAAO,CAACJ,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,gBAAgBU,iBAAiBhB,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,OAAO,CAACJ,EAAIQ,GAAG,UAAUJ,EAAG,OAAO,CAACJ,EAAIQ,GAAGR,EAAIS,GAAGT,EAAIU,gBAAgBW,uBAAuBjB,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACN,EAAIO,GAAG,GAAGH,EAAG,MAAM,CAACE,YAAY,SAAS,CAACF,EAAG,cAAc,CAACkB,MAAM,CAAC,UAAW,EAAM,YAActB,EAAIuB,aAAaC,GAAG,CAAC,OAASxB,EAAIyB,aAAa,MAAQzB,EAAI0B,yBAAyB,KAAKtB,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,YAAY,CAACkB,MAAM,CAAC,QAAUtB,EAAI2B,QAAQ,KAAO3B,EAAI4B,UAAU,QAAU5B,EAAI6B,SAASC,YAAY9B,EAAI+B,GAAG,CAAC,CAACC,IAAI,aAAaC,GAAG,SAASC,GAAO,MAAO,CAAC9B,EAAG,aAAa,CAACkB,MAAM,CAAC,UAAY,YAAY,OAAS,QAAQ,eAAe,iBAAiBQ,YAAY9B,EAAI+B,GAAG,CAAC,CAACC,IAAI,UAAUC,GAAG,WAAW,MAAO,CAAC7B,EAAG,OAAO,CAACJ,EAAIQ,GAAG,IAAIR,EAAIS,GAAGyB,EAAMC,IAAIC,YAAY,SAASC,OAAM,IAAO,MAAK,IAAO,CAACjC,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,IAAI,CAACJ,EAAIQ,GAAG,IAAIR,EAAIS,GAAGT,EAAIsC,SAASJ,EAAMC,IAAIC,aAAa,mBAAmB,GAAIpC,EAAI4B,UAAUW,OAAS,EAAGnC,EAAG,MAAM,CAACE,YAAY,kBAAkB,CAACF,EAAG,iBAAiB,CAACkB,MAAM,CAAC,WAAatB,EAAIwC,YAAYhB,GAAG,CAAC,cAAcxB,EAAIyC,iBAAiB,iBAAiBzC,EAAI0C,wBAAwB,GAAG1C,EAAI2C,UACzuFC,EAAkB,CAAC,WAAa,IAAI5C,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,IAAI,CAACJ,EAAIQ,GAAG,aAAaJ,EAAG,MAAM,CAACE,YAAY,aAAa,WAAa,IAAIN,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACE,YAAY,QAAQ,CAACF,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,OAAO,CAACJ,EAAIQ,GAAG,gB,gGC0H9Z,GACEqC,KAAM,aACNC,WAAY,CACVC,SAAJ,OACIC,cAAJ,OACIC,WAAJ,QAEEC,KAPF,WAQI,MAAO,CACLvB,QAAS,CACf,CAAQ,MAAR,OAAQ,KAAR,cACA,CAAQ,MAAR,OAAQ,KAAR,YACA,CAAQ,MAAR,QAAQ,KAAR,aAAQ,SAAR,cACA,CAAQ,MAAR,OAAQ,KAAR,oBACA,CAAQ,MAAR,OAAQ,KAAR,cACA,CAAQ,MAAR,OAAQ,KAAR,eAEMC,UAAW,GACXC,SAAS,EACTW,WAAY,CACVW,QAAS,EACTC,MAAO,EACPC,MAAO,EACPC,MAAO,CAAC,GAAI,GAAI,GAAI,KACpBC,KAAM,GAERC,SAAS,EACTC,MAAO,SACPC,YAAa,QACbC,SAAU,CACRC,GAAI,EACJf,KAAM,GACNgB,YAAa,IAEfC,MAAO,CACLjB,KAAM,CACd,CACU,UAAV,EACU,QAAV,OAEU,UAAV,iBAGQgB,YAAa,CACrB,CACU,UAAV,EACU,QAAV,OACU,UAAV,oBAIME,UAAU,EACVC,UAAU,EACVtD,gBAAiB,GACjBuD,sBAAuB,GACvBC,kBAAmB,GACnBC,gBAAiB,GACjBC,YAAa,GACb7C,YAAa,SACb8C,MAAO,KAGXC,QA9DF,WA+DIrE,KAAKmE,YAAcnE,KAAKsE,OAAOC,MAAMZ,IAEvCa,QAjEF,WAkEIxE,KAAKyE,wBAELzE,KAAK0E,qBAEPC,QAAS,CACPtC,SADJ,SACA,GACM,OAAOuC,EAAItC,OAAS,GAAK,GAA/B,gCAEImC,sBAJJ,WAIA,WACM,OAAN,OAAM,CAAN,yCACQ,GAAgB,KAAZI,EAAIC,KAAa,CACnB,IAAV,GACYnB,GAAIkB,EAAI5B,KAAKU,GACbH,MAAOqB,EAAI5B,KAAKvC,eAElB,EAAV,mCACU,EAAV,4BAKIgE,kBAjBJ,WAiBA,mGACA,uBACWK,EAAOzB,OACV0B,EAAO1B,KAAO,GACd0B,EAAO9B,QAAU,GAEnB,OAAN,OAAM,CAAN,GACA,kBACA,aACU,YAAV,WACY,EAAZ,aACA,KACU,EAAV,yBACU,EAAV,8BACU,EAAV,kCACU,EAAV,8BACU,EAAV,6BAEU,EAAV,kBACY,QAAZ,eAIA,oBACQ,YAAR,WACU,EAAV,aACA,SAKIV,iBAhDJ,SAgDA,GACMxC,KAAKuC,WAAWW,QAAU,EAC1BlD,KAAKuC,WAAWe,KAAO2B,EACvBjF,KAAK0E,kBAAkB,CACrBP,YAAanE,KAAKmE,YAClBb,KAAMtD,KAAKuC,WAAWe,KACtBJ,QAASlD,KAAKuC,WAAWW,WAI7BT,oBA1DJ,SA0DA,GACMyC,QAAQC,IAAInF,KAAKuC,YACjBvC,KAAKuC,WAAWW,QAAU+B,EAC1BjF,KAAK0E,kBAAkB,CACrBP,YAAanE,KAAKmE,YAClBb,KAAMtD,KAAKuC,WAAWe,KACtBJ,QAASlD,KAAKuC,WAAWW,WAG7BkC,WAnEJ,SAmEA,GACM,OAAe,IAARH,IAAcA,GAGvBzD,aAvEJ,SAuEA,GACMxB,KAAKqF,WAAaC,EAAKC,MACvBvF,KAAK0E,kBAAkB,CACrBP,YAAanE,KAAKmE,YAClBkB,WAAYC,EAAKC,SAGrB9D,qBA9EJ,WA+EMzB,KAAKqF,WAAa,GAClBrF,KAAK0E,kBAAkB,CACrBP,YAAanE,KAAKmE,eAItBqB,UArFJ,SAqFA,OACM,OAAIxF,KAAKoF,WAAWG,GACXE,EAAS,IAAIC,MAAM,YAClC,uBAOQD,IANOA,EACf,UACA,+DAQIE,YAnGJ,SAmGA,KACmB,SAAT/C,IACF5C,KAAK8D,SAAWyB,GAEL,gBAAT3C,IACF5C,KAAK+D,SAAWwB,IAIpBK,YA5GJ,SA4GA,OACM,IAAK,OAAX,OAAW,CAAX,OACQ,OAAOH,EAAS,IAAIC,MAAM,gBAE1BD,OCjToX,I,wBCQxXI,EAAY,eACd,EACA/F,EACA6C,GACA,EACA,KACA,WACA,MAIa,aAAAkD,E,6CCnBf", "file": "js/chunk-61e3d7a6.ddb4fdc2.js", "sourcesContent": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"update-info\"},[_c('div',{staticClass:\"info-info\"},[_vm._m(0),_c('div',{staticClass:\"info-detail\"},[_c('div',{staticClass:\"item-rows\"},[_c('div',{staticClass:\"item\"},[_c('span',[_vm._v(\"连接器名称\")]),_c('span',[_vm._v(_vm._s(_vm.connectorDetail.connectorName))])]),_c('div',{staticClass:\"item\"},[_c('span',[_vm._v(\"协议方式\")]),_c('span',[_vm._v(_vm._s(_vm.connectorDetail.protocolType))])])]),_c('div',{staticClass:\"item-rows\"},[_c('div',{staticClass:\"item\"},[_c('span',[_vm._v(\"是否启用\")]),_c('span',[_vm._v(_vm._s(_vm.connectorDetail.enableStatusName))])]),_c('div',{staticClass:\"item\"},[_c('span',[_vm._v(\"应用类型\")]),_c('span',[_vm._v(_vm._s(_vm.connectorDetail.applicationType))])])]),_c('div',{staticClass:\"item-rows\"},[_c('div',{staticClass:\"item\"},[_c('span',[_vm._v(\"上行转换器\")]),_c('span',[_vm._v(_vm._s(_vm.connectorDetail.upLinkName))])]),_c('div',{staticClass:\"item\"},[_c('span',[_vm._v(\"下行转换器\")]),_c('span',[_vm._v(_vm._s(_vm.connectorDetail.downLinkName))])])]),_c('div',{staticClass:\"item-rows\"},[_c('div',{staticClass:\"item\",staticStyle:{\"width\":\"100%\"}},[_c('span',[_vm._v(\"通用配置\")]),_c('span',[_vm._v(_vm._s(_vm.connectorDetail.commonConfig))])])]),_c('div',{staticClass:\"item-rows\"},[_c('div',{staticClass:\"item\",staticStyle:{\"width\":\"100%\"}},[_c('span',[_vm._v(\"自定义配置\")]),_c('span',[_vm._v(_vm._s(_vm.connectorDetail.customConfig))])])]),_c('div',{staticClass:\"item-rows\"},[_c('div',{staticClass:\"item\"},[_c('span',[_vm._v(\"创建时间\")]),_c('span',[_vm._v(_vm._s(_vm.connectorDetail.createTime))])]),_c('div',{staticClass:\"item\"},[_c('span',[_vm._v(\"修改时间\")]),_c('span',[_vm._v(_vm._s(_vm.connectorDetail.updateTime))])])])])]),_c('div',{staticClass:\"info-content\"},[_c('div',{staticClass:\"content-select flex\"},[_vm._m(1),_c('div',{staticClass:\"right\"},[_c('form-search',{attrs:{\"isSelect\":false,\"inputHolder\":_vm.inputHolder},on:{\"search\":_vm.handleSearch,\"clear\":_vm.fn_clear_search_info}})],1)]),_c('div',{staticClass:\"content-table\"},[_c('iot-table',{attrs:{\"columns\":_vm.columns,\"data\":_vm.tableData,\"loading\":_vm.loading},scopedSlots:_vm._u([{key:\"configInfo\",fn:function(scope){return [_c('el-tooltip',{attrs:{\"placement\":\"top-start\",\"effect\":\"light\",\"popper-class\":\"event-tooltip\"},scopedSlots:_vm._u([{key:\"content\",fn:function(){return [_c('span',[_vm._v(\" \"+_vm._s(scope.row.configInfo)+\" \")])]},proxy:true}],null,true)},[_c('div',{staticClass:\"alarmContent-tooltip\"},[_c('p',[_vm._v(\" \"+_vm._s(_vm.fn_sub10(scope.row.configInfo))+\" \")])])])]}}])})],1),(_vm.tableData.length > 0)?_c('div',{staticClass:\"content-bottom\"},[_c('iot-pagination',{attrs:{\"pagination\":_vm.pagination},on:{\"size-change\":_vm.handleSizeChange,\"current-change\":_vm.handleCurrentChange}})],1):_vm._e()])])}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"info-title flex\"},[_c('div',{staticClass:\"left\"},[_c('p',[_vm._v(\"连接器信息\")])]),_c('div',{staticClass:\"right\"})])},function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"left\"},[_c('div',{staticClass:\"content-title\"},[_c('span',[_vm._v(\"设备列表\")])])])}]\n\nexport { render, staticRenderFns }", "<template>\r\n  <div class=\"update-info\">\r\n    <div class=\"info-info\">\r\n      <div class=\"info-title flex\">\r\n        <div class=\"left\">\r\n          <p>连接器信息</p>\r\n        </div>\r\n        <div class=\"right\">\r\n        </div>\r\n      </div>\r\n      <div class=\"info-detail\">\r\n        <div class=\"item-rows\">\r\n          <div class=\"item\">\r\n            <span>连接器名称</span>\r\n            <span>{{ connectorDetail.connectorName }}</span>\r\n          </div>\r\n          <div class=\"item\">\r\n            <span>协议方式</span>\r\n            <span>{{ connectorDetail.protocolType }}</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"item-rows\">\r\n          <div class=\"item\">\r\n            <span>是否启用</span>\r\n            <span>{{ connectorDetail.enableStatusName }}</span>\r\n          </div>\r\n          <div class=\"item\">\r\n            <span>应用类型</span>\r\n            <span>{{ connectorDetail.applicationType }}</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"item-rows\">\r\n          <div class=\"item\">\r\n            <span>上行转换器</span>\r\n            <span>{{ connectorDetail.upLinkName }}</span>\r\n          </div>\r\n          <div class=\"item\">\r\n            <span>下行转换器</span>\r\n            <span>{{ connectorDetail.downLinkName }}</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"item-rows\">\r\n          <div class=\"item\"\r\n               style=\"width: 100%\">\r\n            <span>通用配置</span>\r\n            <span>{{ connectorDetail.commonConfig }}</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"item-rows\">\r\n          <div class=\"item\"\r\n               style=\"width: 100%\">\r\n            <span>自定义配置</span>\r\n            <span>{{ connectorDetail.customConfig }}</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"item-rows\">\r\n          <div class=\"item\">\r\n            <span>创建时间</span>\r\n            <span>{{ connectorDetail.createTime }}</span>\r\n          </div>\r\n          <div class=\"item\">\r\n            <span>修改时间</span>\r\n            <span>{{ connectorDetail.updateTime }}</span>\r\n          </div>\r\n        </div>\r\n\r\n      </div>\r\n    </div>\r\n    <div class=\"info-content\">\r\n      <div class=\"content-select flex\">\r\n        <div class=\"left\">\r\n          <div class=\"content-title\">\r\n            <span>设备列表</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"right\">\r\n          <form-search :isSelect=\"false\"\r\n                       @search=\"handleSearch\"\r\n                       @clear=\"fn_clear_search_info\"\r\n                       :inputHolder=\"inputHolder\" />\r\n        </div>\r\n      </div>\r\n      <div class=\"content-table\">\r\n        <iot-table :columns=\"columns\"\r\n                   :data=\"tableData\"\r\n                   :loading=\"loading\">\r\n          <template slot=\"configInfo\"\r\n                    slot-scope=\"scope\">\r\n            <el-tooltip placement=\"top-start\"\r\n                        effect=\"light\"\r\n                        popper-class=\"event-tooltip\">\r\n              <template #content>\r\n                <span>\r\n                  {{scope.row.configInfo}}\r\n                </span>\r\n              </template>\r\n              <div class=\"alarmContent-tooltip\">\r\n                <p>\r\n                  {{fn_sub10(scope.row.configInfo)}}\r\n                </p>\r\n              </div>\r\n            </el-tooltip>\r\n          </template>\r\n        </iot-table>\r\n      </div>\r\n      <div class=\"content-bottom\"\r\n           v-if=\"tableData.length > 0\">\r\n        <iot-pagination :pagination=\"pagination\"\r\n                        @size-change=\"handleSizeChange\"\r\n                        @current-change=\"handleCurrentChange\" />\r\n      </div>\r\n    </div>\r\n\r\n  </div>\r\n</template>\r\n  <script>\r\nimport FormSearch from '@/components/form-search'\r\nimport IotTable from '@/components/iot-table'\r\nimport IotPagination from '@/components/iot-pagination'\r\nimport { reg_seven, reg_sixteen } from '@/util/util.js'\r\nimport { cloneDeep } from 'lodash'\r\nimport { getConnectorDetail, getConnectorLinkDeviceList } from '@/api/device'\r\n\r\nexport default {\r\n  name: 'updateInfo',\r\n  components: {\r\n    IotTable,\r\n    IotPagination,\r\n    FormSearch,\r\n  },\r\n  data() {\r\n    return {\r\n      columns: [\r\n        { label: '设备名称', prop: 'deviceName' },\r\n        { label: '设备SN', prop: 'deviceSn' },\r\n        { label: '自定义配置', prop: 'configInfo', slotName: 'configInfo' },\r\n        { label: '设备状态', prop: 'deviceStatusName' },\r\n        { label: '设备描述', prop: 'deviceDesc' },\r\n        { label: '添加时间', prop: 'createTime' },\r\n      ],\r\n      tableData: [],\r\n      loading: false,\r\n      pagination: {\r\n        current: 1, // 当前页\r\n        total: 0, // 记录条数\r\n        pages: 0, // 总页数\r\n        sizes: [10, 20, 50, 100], // 每一页的数量  [10, 20, 50, 100]\r\n        size: 4,\r\n      },\r\n      visible: false,\r\n      title: '编辑固件信息',\r\n      dialogWidth: '742px',\r\n      infoForm: {\r\n        id: 0,\r\n        name: '',\r\n        description: '',\r\n      },\r\n      rules: {\r\n        name: [\r\n          {\r\n            required: true,\r\n            trigger: 'blur',\r\n            // message: '须选择所属项目',\r\n            validator: this.checkName,\r\n          },\r\n        ],\r\n        description: [\r\n          {\r\n            required: false,\r\n            trigger: 'blur',\r\n            validator: this.checkLength,\r\n          },\r\n        ],\r\n      },\r\n      nameTrue: true,\r\n      descTrue: true,\r\n      connectorDetail: {},\r\n      firmwareJobDetailForm: {},\r\n      firmwareJobStatic: {},\r\n      firmwareJobList: {},\r\n      connectorId: '',\r\n      inputHolder: '输入设备名称',\r\n      jobId: '',\r\n    }\r\n  },\r\n  created() {\r\n    this.connectorId = this.$route.query.id\r\n  },\r\n  mounted() {\r\n    this.fn_getConnectorDetail()\r\n    // this.getFirmwareJobUpgradeStatistic()\r\n    this.fn_get_table_data()\r\n  },\r\n  methods: {\r\n    fn_sub10(str) {\r\n      return str.length > 20 ? `${str.substr(0, 20)}...` : str\r\n    },\r\n    fn_getConnectorDetail() {\r\n      getConnectorDetail({ id: this.connectorId }).then((res) => {\r\n        if (res.code == 200) {\r\n          let data = {\r\n            id: res.data.id,\r\n            title: res.data.connectorName,\r\n          }\r\n          this.$store.dispatch('setLayoutInfo', data)\r\n          this.connectorDetail = res.data\r\n        }\r\n      })\r\n    },\r\n\r\n    fn_get_table_data(params = { connectorId: this.connectorId }) {\r\n      let others = { ...params }\r\n      if (!params.size) {\r\n        others.size = 10\r\n        others.current = 1\r\n      }\r\n      getConnectorLinkDeviceList(others)\r\n        .then((res) => {\r\n          if (res.code == 200) {\r\n            setTimeout(() => {\r\n              this.loading = false\r\n            }, 300)\r\n            this.tableData = res.data.records\r\n            this.pagination.total = res.data.total\r\n            this.pagination.current = res.data.current\r\n            this.pagination.pages = res.data.pages\r\n            this.pagination.size = res.data.size\r\n          } else {\r\n            this.$newNotify.error({\r\n              message: res.message,\r\n            })\r\n          }\r\n        })\r\n        .finally(() => {\r\n          setTimeout(() => {\r\n            this.loading = false\r\n          }, 300)\r\n        })\r\n    },\r\n\r\n    // 当前页总条数\r\n    handleSizeChange(val) {\r\n      this.pagination.current = 1\r\n      this.pagination.size = val\r\n      this.fn_get_table_data({\r\n        connectorId: this.connectorId,\r\n        size: this.pagination.size,\r\n        current: this.pagination.current,\r\n      })\r\n    },\r\n    // 当前页\r\n    handleCurrentChange(val) {\r\n      console.log(this.pagination)\r\n      this.pagination.current = val\r\n      this.fn_get_table_data({\r\n        connectorId: this.connectorId,\r\n        size: this.pagination.size,\r\n        current: this.pagination.current,\r\n      })\r\n    },\r\n    fn_notNull(val) {\r\n      return val !== 0 && !val\r\n    },\r\n\r\n    handleSearch(from) {\r\n      this.deviceName = from.value\r\n      this.fn_get_table_data({\r\n        connectorId: this.connectorId,\r\n        deviceName: from.value,\r\n      })\r\n    },\r\n    fn_clear_search_info() {\r\n      this.deviceName = ''\r\n      this.fn_get_table_data({\r\n        connectorId: this.connectorId,\r\n      })\r\n    },\r\n\r\n    checkName(rule, value, callback) {\r\n      if (this.fn_notNull(value)) {\r\n        return callback(new Error('请输入固件名称'))\r\n      } else if (!reg_sixteen(value)) {\r\n        return callback(\r\n          new Error(\r\n            '支持中文、英文大小写、数字、部分常用符号（下划线，减号，括弧）， 必须以中文、英文或数字开头，长度不超过32个字符'\r\n          )\r\n        )\r\n      } else {\r\n        callback()\r\n      }\r\n    },\r\n    // 表单验证触发\r\n    fn_validate(name, value) {\r\n      if (name === 'name') {\r\n        this.nameTrue = value\r\n      }\r\n      if (name === 'description') {\r\n        this.descTrue = value\r\n      }\r\n    },\r\n    // 长度检验\r\n    checkLength(rule, value, callback) {\r\n      if (!reg_seven(value, 201)) {\r\n        return callback(new Error('最多不超过200个字符'))\r\n      } else {\r\n        callback()\r\n      }\r\n    },\r\n  },\r\n}\r\n</script>\r\n  <style lang=\"scss\" scoped>\r\n@mixin fontStyle400 {\r\n  font-size: 14px;\r\n  font-weight: 400;\r\n}\r\n@mixin fontStyle500 {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n@mixin BoxShadow {\r\n  box-shadow: 0px 5px 18px rgba(0, 0, 0, 0.08);\r\n}\r\n$blue: #018aff;\r\n$green: #00c250;\r\n$purple: #8f01ff;\r\n$red: #ff4d4f;\r\n$yellow: #e6a23c;\r\n.update-info {\r\n  .info-info {\r\n    margin-top: 18px;\r\n    background: #ffffff;\r\n    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);\r\n    border-radius: 2px;\r\n    padding: 20px 32px;\r\n    .info-title {\r\n      justify-content: space-between;\r\n      @include fontStyle500;\r\n      .right {\r\n        img {\r\n          margin-right: 6px;\r\n        }\r\n        span {\r\n          cursor: pointer;\r\n          font-size: 14px;\r\n        }\r\n      }\r\n    }\r\n    .info-detail {\r\n      margin-top: 18px;\r\n      // @include BoxShadow;\r\n      // border-radius: 2px;\r\n      // padding: 17px 0;\r\n      .item-rows {\r\n        margin: 0 auto;\r\n        display: flex;\r\n        flex: 1;\r\n        .item {\r\n          padding: 11px 0;\r\n          width: 50%;\r\n          display: inline-flex;\r\n          span {\r\n            height: 16px;\r\n            line-height: 16px;\r\n            @include fontStyle400;\r\n            &:first-child {\r\n              flex-shrink: 0;\r\n              color: #999999;\r\n              width: 80px;\r\n              text-align: right;\r\n            }\r\n            &:last-child {\r\n              color: #515151;\r\n              margin-left: 48px;\r\n            }\r\n          }\r\n          &:nth-child(1) {\r\n            // margin-left: 6px;\r\n            span {\r\n              &:first-child {\r\n                width: 70px;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .info-census {\r\n    margin-top: 38px;\r\n    .census-title {\r\n      @include fontStyle500;\r\n      background: #ffffff;\r\n      box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);\r\n      border-radius: 2px;\r\n      padding: 20px 32px;\r\n    }\r\n    .census-content {\r\n      margin-top: 18px;\r\n      .content-item {\r\n        flex: 1;\r\n        .item-title {\r\n          p {\r\n            @include fontStyle400;\r\n            height: 12px;\r\n            margin: 4px 8px 0 0;\r\n          }\r\n          span {\r\n            color: #515151;\r\n            font-size: 14px;\r\n          }\r\n        }\r\n        .num {\r\n          margin: 14px 17px 0;\r\n          span {\r\n            font-size: 24px;\r\n            font-weight: 500;\r\n          }\r\n        }\r\n        &:nth-child(1) {\r\n          .item-title {\r\n            p {\r\n              border: 2px solid $blue;\r\n            }\r\n          }\r\n        }\r\n        &:nth-child(2) {\r\n          .item-title {\r\n            p {\r\n              border: 2px solid $green;\r\n            }\r\n          }\r\n        }\r\n        &:nth-child(3) {\r\n          .item-title {\r\n            p {\r\n              border: 2px solid $purple;\r\n            }\r\n          }\r\n        }\r\n        &:nth-child(4) {\r\n          .item-title {\r\n            p {\r\n              border: 2px solid $red;\r\n            }\r\n          }\r\n        }\r\n        &:nth-child(5) {\r\n          .item-title {\r\n            p {\r\n              border: 2px solid $yellow;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .info-content {\r\n    margin-top: 18px;\r\n    background: #ffffff;\r\n    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);\r\n    border-radius: 2px;\r\n    padding: 20px 32px;\r\n    .content-title {\r\n      justify-content: space-between;\r\n    }\r\n    .content-select {\r\n      justify-content: space-between;\r\n    }\r\n    .content-table {\r\n      margin-top: 14px;\r\n\r\n      .table-edit {\r\n        display: flex;\r\n        align-items: center;\r\n        p {\r\n          cursor: pointer;\r\n        }\r\n        .table-line {\r\n          margin: 0px 12px;\r\n          width: 1px;\r\n          height: 13px;\r\n          border: 1px solid #ededed;\r\n        }\r\n      }\r\n    }\r\n    .content-bottom {\r\n      margin-top: 14px;\r\n      text-align: right;\r\n    }\r\n  }\r\n  .info-form {\r\n    /deep/ .el-form-item {\r\n      margin-bottom: 17px;\r\n    }\r\n    .el-form-tips {\r\n      margin-top: -17px;\r\n    }\r\n    .upload-text {\r\n      width: 160px;\r\n      background: #ebf6ff;\r\n      color: #0088fe;\r\n      font-size: 14px;\r\n      font-weight: 400;\r\n      padding: 11px 0;\r\n      user-select: none;\r\n    }\r\n    .el-upload__tip {\r\n      color: #999999;\r\n      font-size: 12px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n  ", "import mod from \"-!../../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js!../../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../node_modules/babel-loader/lib/index.js!../../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=a157f2d2&scoped=true&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=a157f2d2&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"a157f2d2\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=a157f2d2&lang=scss&scoped=true&\""], "sourceRoot": ""}