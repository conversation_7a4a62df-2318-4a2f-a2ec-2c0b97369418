(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-59f625bf"],{"83e4":function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"device"},[n("div",{staticClass:"device-top"},[n("div",{staticClass:"device-top-search"},[n("div",{staticClass:"top-left"},[n("iot-button",{attrs:{text:"添加设备组"},on:{search:e.fn_open}})],1),n("div",{staticClass:"top-right"},[n("el-input",{attrs:{clearable:"",placeholder:"输入组名称"},on:{clear:e.handleClear},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.fn_handle__query.apply(null,arguments)}},model:{value:e.groupName,callback:function(t){e.groupName=t},expression:"groupName"}},[n("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"suffix"},on:{click:e.fn_handle__query},slot:"suffix"})])],1)])]),n("div",{staticClass:"device-table"},[n("iot-table",{attrs:{columns:e.columns,data:e.tableData,loading:e.loading},on:{"selection-change":e.fn_select_more_data,"selection-del":e.fn_del_more_data,"del-callbackSure":e.fn_del_sure},scopedSlots:e._u([{key:"operation",fn:function(t){return[n("div",{staticClass:"flex table-edit"},[n("p",{staticClass:"color2",on:{click:function(n){return e.fn_edit(t.row)}}},[e._v("修改")]),n("p"),n("p",{staticClass:"color2",on:{click:function(n){return e.fn_del(t.row.id)}}},[e._v("删除")]),n("p"),n("p",{staticClass:"color2",on:{click:function(n){return e.fn_check(t.row.id)}}},[e._v("详情")]),n("p"),n("p",{staticClass:"color2",attrs:{slot:"operation"},on:{click:function(n){return e.relationDevice(t.row.id)}},slot:"operation"},[e._v(" 关联设备 ")])])]}}])})],1),e.tableData.length?n("div",{staticClass:"device-bottom"},[n("iot-pagination",{attrs:{pagination:e.pagination},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1):e._e(),n("iot-dialog",{attrs:{visible:e.visible,title:e.title,width:e.dialogWidth},on:{"update:visible":function(t){e.visible=t},callbackSure:e.fn_sure,close:e.fn_close},scopedSlots:e._u([{key:"body",fn:function(){return[1==e.type?n("iot-form",[n("el-form",{ref:"groupForm",staticClass:"groupForm",attrs:{"label-position":"top",model:e.groupForm,rules:e.rules,"label-width":"80px"},on:{validate:e.fn_validate}},[n("el-form-item",{attrs:{label:"组名称",prop:"groupName"}},[n("el-input",{model:{value:e.groupForm.groupName,callback:function(t){e.$set(e.groupForm,"groupName",t)},expression:"groupForm.groupName"}})],1),e.groupNameTrue?n("div",{staticClass:"el-form-tips"},[e._v(" 长度限制为4-30个字符; ")]):e._e(),n("el-form-item",{attrs:{label:"配置时间",prop:"configTime"}},[n("el-input",{model:{value:e.groupForm.configTime,callback:function(t){e.$set(e.groupForm,"configTime",t)},expression:"groupForm.configTime"}})],1),e.configTimeTrue?n("div",{staticClass:"el-form-tips"},[e._v(" 支持正整数1-100; ")]):e._e()],1)],1):e._e(),2==e.type?n("div",[n("iot-form",{scopedSlots:e._u([{key:"default",fn:function(){return[n("el-form",[n("el-form-item",[n("div",{staticClass:"del-tips"},[e._v(" 删除之后，该组设备会进行解绑，请确认是否删除？ ")])])],1)]},proxy:!0}],null,!1,1221460110)})],1):e._e()]},proxy:!0}])}),n("relation",{ref:"relation",on:{close:e.handleReset}})],1)},a=[],o=n("5530"),r=(n("ac1f"),n("5319"),n("a9e3"),n("d3b7"),n("d81d"),n("a15b"),n("b329"),n("6e22")),s=n("c2a2"),c=n("7413"),l=n("673a"),u=n("511c"),d=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("iot-dialog",{attrs:{title:"组关联设备",top:"10vh",maxHeight:"auto",visible:e.visible,width:e.width,appendBody:!0,footer:!1},on:{"update:visible":function(t){e.visible=t},close:e.handleClose},scopedSlots:e._u([{key:"body",fn:function(){return[i("div",{staticClass:"content"},[i("div",{staticClass:"device flex"},[i("div",{staticClass:"device-data not"},[i("h4",[e._v("待关联设备："+e._s(e.waitCount))]),i("div",{staticClass:"table"},[i("div",{staticClass:"form-item"},[i("el-input",{attrs:{placeholder:"请输入请输入设备名称",clearable:""},on:{clear:function(t){return e.fn_handle__query(!0)}},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.fn_handle__query(!0)}},model:{value:e.notSearchVal,callback:function(t){e.notSearchVal=t},expression:"notSearchVal"}},[i("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"suffix"},on:{click:function(t){return e.fn_handle__query(!0)}},slot:"suffix"})])],1),i("div",{staticClass:"device-table-content"},[i("iot-table",{attrs:{columns:e.notColumns,data:e.notSource,loading:e.notLoading},on:{"selection-change":function(t){return e.selectionChange(t,!0)}},scopedSlots:e._u([{key:"empty",fn:function(){return[e.isEmpty?i("div",{staticClass:"empty"},[e._v(" 该产品暂无设备，请先去"),i("span",{on:{click:e.routeDevice}},[e._v("添加设备")])]):e._e()]},proxy:!0}])}),i("div",{staticClass:"pagination flex"},[i("iot-pagination",{attrs:{pagination:e.notPagination,layout:"total, prev, pager, next,  jumper"},on:{"current-change":function(t){return e.handleCurrentChange(t,!0)}}})],1)],1)])]),i("div",{staticClass:"action flex"},[i("p",{staticClass:"bind",on:{click:function(t){return e.submitBind(!0)}}},[i("span",[e._v("绑定")]),i("img",{attrs:{src:n("93ed"),alt:""}})]),i("p",{staticClass:"unbound",on:{click:function(t){return e.submitBind(!1)}}},[i("img",{attrs:{src:n("93ed"),alt:""}}),i("span",[e._v("解绑")])])]),i("div",{staticClass:"device-data already"},[i("h4",[e._v("已关联设备："+e._s(e.doneCount))]),i("div",{staticClass:"table"},[i("div",{staticClass:"form-item"},[i("el-input",{attrs:{placeholder:"请输入设备名称",clearable:""},on:{clear:function(t){return e.fn_handle__query(!1)}},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.fn_handle__query(!1)}},model:{value:e.alreadySearchVal,callback:function(t){e.alreadySearchVal=t},expression:"alreadySearchVal"}},[i("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"suffix"},on:{click:function(t){return e.fn_handle__query(!1)}},slot:"suffix"})])],1),i("div",{staticClass:"device-table-content"},[i("iot-table",{attrs:{columns:e.notColumns,data:e.alreadySource,loading:e.alreadyLoading},on:{"selection-change":function(t){return e.selectionChange(t,!1)}}}),i("div",{staticClass:"pagination flex"},[i("iot-pagination",{attrs:{pagination:e.alreadyPagination,layout:"total, prev, pager, next,  jumper"},on:{"current-change":function(t){return e.handleCurrentChange(t,!1)}}})],1)],1)])])])])]},proxy:!0}])})],1)},h=[],f=(n("4de4"),n("0e0b")),g=n("aa98"),p={data:function(){return{type:1,visible:!1,deviceVisible:!1,product:"",c:{},deviceForm:{configInfo:"",deviceSn:"",vendorName:"",deviceDesc:""},options:[{value:"1",label:"测试"}],notSearchVal:"",notColumns:[{type:"selection"},{prop:"productKey",label:"ProductKey",width:180},{prop:"deviceName",label:"设备名称"},{prop:"deviceSn",label:"设备SN"},{prop:"deviceStatusName",label:"在线状态"}],notSource:[],notLoading:!1,notPagination:{current:1,size:7,total:0},notSelectList:[],alreadySearchVal:"",alreadySource:[],alreadyLoading:!1,alreadyPagination:{current:1,size:7,total:0},alreadySelectList:[],groupId:"",isEmpty:!1,waitCount:0,doneCount:0,width:"".concat(1330/1920*100,"vw"),configInfoTrue:!0,deviceSnTrue:!0,vendorNameTrue:!0,deviceDescTrue:!0}},components:{iotDialog:u["a"],iotTable:l["a"],iotPagination:r["a"]},props:{hostProductKey:{type:String},hostDeviceName:{type:String}},methods:{fn_edit:function(e){console.log("row",e),this.deviceForm=JSON.parse(JSON.stringify(e)),this.deviceForm.configInfo=JSON.stringify(JSON.parse(this.deviceForm.configInfo),null,2),this.deviceVisible=!0},checkDeviceSn:function(e,t,n){return this.fn_notNull(t)?n(new Error("请输入设备SN")):Object(f["i"])(t)?void n():n(new Error("支持中文、英文字母、数字、和特殊字符_-@()，长度限制 4~64个字符，中文算 2 个字符"))},checkVendorName:function(e,t,n){if(!Object(f["i"])(t,201))return n(new Error("支持中文、英文字母、数字、和特殊字符_-@()，长度限制 4~64个字符，中文算 2 个字符"));n()},checkConfigInfoLength:function(e,t,n){return t=JSON.stringify(t),this.fn_notNull(t)?n(new Error("请输入自定义配置信息")):Object(f["c"])(t,2001)?Object(f["b"])(t)?void n():n(new Error("请输入正确的JSON格式")):n(new Error("最多不超过2000个字符"))},checkDeviceSnLength:function(e,t,n){return this.fn_notNull(t)?n(new Error("请输入设备SN")):Object(f["c"])(t,201)?void n():n(new Error("最多不超过200个字符"))},fn_notNull:function(e){return 0!==e&&!e},checkLength:function(e,t,n){if(!Object(f["c"])(t,201))return n(new Error("最多不超过200个字符"));n()},open:function(e){this.groupId=e,this.visible=!0,this.getProductKey(0,!0,!0),this.getProductKey(1,!0,!0)},selectChange:function(e){this.notPagination.current=1,this.alreadyPagination.current=1;var t=this.options.filter((function(t){return t.id==e}))[0];this.productInfo=t,this.getProductKey(0,!0,!0),this.getProductKey(1,!0,!0)},fn_handle__query:function(e){e?(this.notPagination.current=1,this.getProductKey(0)):(this.alreadyPagination.current=1,this.getProductKey(1))},getProductKey:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a={};e?(this.alreadySearchVal=i?"":this.alreadySearchVal,a={groupId:this.groupId,current:this.alreadyPagination.current,size:this.alreadyPagination.size,deviceName:i?"":this.alreadySearchVal},Object(g["i"])(a).then((function(e){if(200==e.code){var a=e.data;t.alreadySource=a.records||[],t.doneCount=i?a.total:t.doneCount,t.alreadyPagination.total=a.total||0}else t.alreadySource=[],t.doneCount=0,t.alreadyPagination.total=0,n&&t.$newNotify.warning({message:e.message})}))):(this.notSearchVal=i?"":this.notSearchVal,a={current:this.notPagination.current,size:this.notPagination.size,deviceName:i?"":this.notSearchVal},Object(g["k"])(a).then((function(e){if(200==e.code){var a=e.data;4603==e.code?t.isEmpty=!0:t.isEmpty=!1,t.notSource=a.records||[],t.waitCount=i?a.total:t.waitCount,t.notPagination.total=a.total||0}else t.notSource=[],t.waitCount=0,t.notPagination.total=0,n&&t.$newNotify.warning({message:e.message})})))},handleClear:function(){},selectionChange:function(e,t){t?this.notSelectList=e.map((function(e){return e.id})):this.alreadySelectList=e.map((function(e){return e.id}))},handleCurrentChange:function(e,t){t?(this.notPagination.current=e,this.getProductKey(0)):(this.alreadyPagination.current=e,this.getProductKey(1))},submitBind:function(e){var t=this;if(e){if(0==this.notSelectList.length)return void this.$newNotify.warning({message:"请选择未关联设备"});Object(g["Q"])({groupId:this.groupId,deviceList:this.notSelectList}).then((function(e){200==e.code?(t.$newNotify.success({message:e.message}),t.notPagination.current=1,t.alreadyPagination.current=1,t.getProductKey(0,!1,!0),t.getProductKey(1,!1,!0)):t.$newNotify.error({message:e.message})}))}else{if(0==this.alreadySelectList.length)return void this.$newNotify.warning({message:"请选择已关联设备"});Object(g["T"])({groupId:this.groupId,deviceList:this.alreadySelectList}).then((function(e){200==e.code?(t.$newNotify.success({message:e.message}),t.notPagination.current=1,t.alreadyPagination.current=1,t.getProductKey(0,!1,!0),t.getProductKey(1,!1,!0)):t.$newNotify.error({message:e.message})}))}},routeDevice:function(){this.$router.replace({path:"/device"})},handleClose:function(){this.product="",this.productInfo={},this.notSearchVal="",this.alreadySearchVal="",this.notSource=[],this.alreadySource=[],this.notPagination.current=1,this.alreadyPagination.current=1,this.notPagination.total=0,this.alreadyPagination.total=0,this.$emit("close")}}},m=p,_=(n("e949"),n("2877")),v=Object(_["a"])(m,d,h,!1,null,"7a860f5c",null),b=v.exports,y=(n("1503"),{name:"Device",components:{IotPagination:r["a"],IotTable:l["a"],relation:b,IotDialog:u["a"],IotButton:s["a"],IotForm:c["a"]},data:function(){return{groupName:"",columns:[{label:"组ID",prop:"id"},{label:"组名称",prop:"groupName"},{label:"配置时间",prop:"configTime"},{label:"创建时间",prop:"createTime"},{label:"更新时间",prop:"updateTime"},{label:"操作",prop:"operation",slotName:"operation",width:240}],enableList:[{value:0,label:"未启用"},{value:1,label:"已启用"}],tableData:[],pagination:{current:1,total:0,pages:0,sizes:[10,20,50,100],size:10},loading:!1,dialogWidth:"792px",type:1,visible:!1,inputHolder:"请输入搜索关键词",selectHolder:"请选择设备名称",searchValue:{productId:""},productOptions:[],productOptionsCopy:[],deviceOptions:[{id:"2",name:"设备厂商"}],statusCount:{totalNum:0,activeNum:0,onlineNum:0},groupForm:{groupName:"",configTime:""},groupNameTrue:!0,configTimeTrue:!0,rules:{groupName:[{required:!0,trigger:"blur",validator:this.checkGroupName}],configTime:[{required:!0,trigger:"blur",validator:this.checkConfigTime}]},title:"",delId:"",delIds:[]}},created:function(){},watch:{visible:function(e){e||1!=this.type||(this.groupForm={},this.$refs["groupForm"]&&this.$refs["groupForm"].resetFields())}},mounted:function(){this.$route.query.id&&(this.searchValue.productId=this.$route.query.id);var e=Object(o["a"])(Object(o["a"])({},this.searchValue),{},{current:this.pagination.current,size:this.pagination.size});this.fn_get_table_data(e)},methods:{handleReset:function(){this.pagination.current=1,this.fn_get_table_data()},relationDevice:function(e){this.$refs.relation.open(e)},fn_sub10:function(e){if(e)return e.length>20?"".concat(e.substr(0,20),"..."):e},fn_notNull:function(e){return 0!==e&&!e},fn_handle__query:function(){var e={groupName:this.groupName,current:1,size:this.pagination.size};this.fn_get_table_data(e)},handleClear:function(){this.fn_get_table_data()},calcul_long_text:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e.replace(/[\u0391-\uFFE5\u0800-\u4e00 ]/g,"aa").length},fn_select:function(){var e=Object(o["a"])({},this.searchValue);this.fn_get_table_data(e)},checkGroupName:function(e,t,n){var i=this,a=!1;Object(g["R"])({groupName:t,id:this.groupForm.id}).then((function(e){return 200==e.code&&(a=e.data),i.fn_notNull(t)?n(new Error("请输入连接器名称")):t.length>30||t.length<4?n(new Error("长度限制为4-30个字符;")):a?n(new Error("接入连接器名称不充许重复")):void n()}))},checkConfigTime:function(e,t,n){return this.fn_notNull(t)?n(new Error("请输入设备厂商")):Number(t)>100||Number(t)<1?n(new Error("支持正整数1-100")):void n()},fn_get_device_status_count:function(){var e=this;Object(g["l"])().then((function(t){e.statusCount=t.data}))},fn_get_table_data:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=Object(o["a"])({},t);t.size||(n.size=10,n.current=1),Object(g["s"])(n).then((function(t){200==t.code?(setTimeout((function(){e.loading=!1}),300),e.tableData=t.data.records,e.pagination.total=t.data.total,e.pagination.current=t.data.current,e.pagination.pages=t.data.pages,e.pagination.size=t.data.size):e.$newNotify.error({message:t.message})})).finally((function(){setTimeout((function(){e.loading=!1}),300)}))},fn_validate:function(e,t){"groupName"===e&&(this.groupNameTrue=t),"configTime"===e&&(this.configTimeTrue=t)},fn_select_more_data:function(e){this.delIds=e.map((function(e){return e.id}))},fn_del_more_data:function(){0!==this.delIds.length&&(this.columns[0].visible=!0)},fn_del_sure:function(){var e={ids:this.delIds.join(",")};this.fn_del_table_data(e)},fn_open:function(){this.title="添加设备组",this.type=1,this.dialogWidth="792px",this.visible=!0},fn_edit:function(e){this.groupForm=JSON.parse(JSON.stringify(e)),this.title="编辑设备组",this.type=1,this.dialogWidth="792px",this.visible=!0},fn_format_select:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return e.map((function(e){return{id:e.id,converterName:e.converterName,bindStatusName:e.bindStatusName,disabled:"已绑定"==e.bindStatusName&&e.id!=t}}))},fn_search_table_data:function(e){console.log(e),"1"===e.id?this.searchValue.aliasName=e.value:this.searchValue.deviceName=e.value;var t=Object(o["a"])({},this.searchValue);t.size=this.pagination.size,this.fn_get_table_data(t)},handleSizeChange:function(e){this.pagination.size=e;var t={size:this.pagination.size,current:1};this.fn_get_table_data(t)},handleCurrentChange:function(e){this.pagination.current=e;var t=Object(o["a"])(Object(o["a"])({},this.searchValue),{},{current:this.pagination.current,size:this.pagination.size});this.fn_get_table_data(t)},fn_clear_search_info:function(){this.searchValue.aliasName="",this.searchValue.deviceName="";var e=Object(o["a"])({},this.searchValue);this.fn_get_table_data(e)},fn_check:function(e,t){this.$router.push({path:"/deviceGroupDetail",query:{id:e,num:t}})},fn_sure:function(){var e=this;if(2===this.type){var t={id:this.delId};Object(g["S"])(t).then((function(t){200==t.code?(e.$newNotify.success({message:t.message}),e.pagination.current=1,e.fn_get_table_data({size:e.pagination.size,current:1}),e.visible=!1):e.$newNotify.error({message:t.message})}))}else 1===this.type&&this.$refs["groupForm"].validate((function(t){if(t){var n=e.groupForm.id?g["U"]:g["P"];n(e.groupForm).then((function(t){200==t.code?(e.$newNotify.success({message:t.message}),e.pagination.current=1,e.searchValue.productId="",e.fn_get_table_data({size:e.pagination.size,current:1}),e.visible=!1,e.fn_get_upLink_select(),e.fn_get_downLink_select()):e.$newNotify.error({message:t.message})}))}}))},fn_close:function(){this.groupNameTrue=!0,this.configTimeTrue=!0},fn_del:function(e){console.log(e),this.delId=e,this.title="确定删除该组？",this.type=2,this.dialogWidth="550px",this.visible=!0}}}),C=y,N=(n("d1e2"),Object(_["a"])(C,i,a,!1,null,"772d27ae",null));t["default"]=N.exports},cb18:function(e,t,n){},d1e2:function(e,t,n){"use strict";n("cb18")},e949:function(e,t,n){"use strict";n("f372")},f372:function(e,t,n){}}]);
//# sourceMappingURL=chunk-59f625bf.294edbfb.js.map