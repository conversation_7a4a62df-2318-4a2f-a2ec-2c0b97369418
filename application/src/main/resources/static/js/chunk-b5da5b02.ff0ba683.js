(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-b5da5b02"],{"0dc3":function(t,e,n){"use strict";e["a"]=function(t){return"number"===typeof t?"".concat(t/1920*100,"vw"):t}},"511c":function(t,e,n){"use strict";var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("el-dialog",{attrs:{"close-on-click-modal":!1,"custom-class":"iot-dialog",top:t.top,title:t.title,visible:t.dialogVisible,width:t.width,"before-close":t.fn_close,"append-to-body":t.appendBody,modal:t.maskModel},on:{"update:visible":function(e){t.dialogVisible=e}}},[n("div",{staticClass:"iot-dialog-content",style:{maxHeight:t.maxHeight}},[t._t("body")],2),t.footer?n("div",{staticClass:"footer"},[n("iot-button",{directives:[{name:"throttle",rawName:"v-throttle",value:500,expression:"500"}],attrs:{text:"取消",type:"white"},on:{search:t.fn_close}}),n("iot-button",{directives:[{name:"throttle",rawName:"v-throttle",value:500,expression:"500"}],attrs:{type:t.btnClass,text:t.comfirmText},on:{search:t.fn_sure}})],1):t._e()])},i=[],o=n("c2a2"),s={name:"IotDialog",components:{IotButton:o["a"]},props:{top:{type:String,default:"15vh"},maxHeight:{type:String,default:"65vh"},title:{type:String,default:"标题"},visible:{type:Boolean,default:!1},width:{type:String,default:"30%"},footer:{type:Boolean,default:!0},appendBody:{type:Boolean,default:!1},callbackSure:Function,comfirmText:{type:String,default:"确 定"},maskModel:{type:Boolean,default:!0},showLoading:{type:Boolean,default:!1},btnClass:{type:String,default:"default"}},computed:{dialogVisible:function(){return this.visible}},data:function(){return{}},methods:{fn_close:function(){this.$emit("update:visible",!1),this.$emit("close")},fn_sure:function(){this.$emit("callbackSure")}}},l=s,r=(n("6018"),n("2877")),c=Object(r["a"])(l,a,i,!1,null,"06d31b1a",null);e["a"]=c.exports},"54f2":function(t,e,n){"use strict";n("fe0d")},6018:function(t,e,n){"use strict";n("b58a")},"63ed":function(t,e,n){t.exports=n.p+"img/empty.85a6a000.png"},"673a":function(t,e,n){"use strict";var a=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"iot-table"},[t.columns[0].selectionText?a("div",{staticClass:"selection-text flex"},[a("p",[t._v("当前已选择"+t._s(t.selecionData.length)+"项数据。")]),t.columns[0].isShowdelete?a("p",{directives:[{name:"throttle",rawName:"v-throttle"}],staticClass:"color2",on:{click:t.fn_del_selection_data}},[t._v(" 删除 ")]):t._e(),t.columns[0].isShowIgnore?a("p",{directives:[{name:"throttle",rawName:"v-throttle"}],staticClass:"color2",on:{click:t.fn_ignore_selection_data}},[t._v(" 批量忽略 ")]):t._e(),a("P"),t.columns[0].isShowHandle?a("p",{directives:[{name:"throttle",rawName:"v-throttle"}],staticClass:"color2",on:{click:t.fn_handle_selection_data}},[t._v(" 批量处理 ")]):t._e(),t._t("multSelectText")],2):t._e(),a("el-table",t._g(t._b({directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"table",staticStyle:{width:"100%"},attrs:{"empty-text":" ",data:t.data,"element-loading-spinner":"el-icon-loading","header-cell-style":{background:"#F7F7F7"},"row-style":{height:t.toVW(48)}},on:{"selection-change":t.handleSelectionChange}},"el-table",t.$attrs,!1),t.$listeners),[t._l(t.columns,(function(e){return[e.type?a("el-table-column",{key:e.type,attrs:{type:e.type,width:e.width,selectable:t.selectable,align:"center"}}):a("el-table-column",{key:e.prop,attrs:{label:e.label,prop:e.prop,type:e.type,width:e.width,fixed:e.fixed},scopedSlots:t._u([{key:"default",fn:function(n){var i=n.row;return[e.slotName?[t._t(e.slotName,null,{row:i})]:[a("span",[t._v(t._s(i[e.prop]))])]]}}],null,!0)})]})),a("template",{slot:"empty"},[t._t("empty",(function(){return[t.loading?t._e():a("div",{staticClass:"table-empty"},[a("img",{attrs:{src:n("63ed"),alt:""}})])]}))],2)],2),a("iot-dialog",{attrs:{width:t.columns[0].dialogWidth?t.columns[0].dialogWidth:t.toVW(550),visible:t.columns[0].visible,title:t.columns[0].title},on:{"update:visible":function(e){return t.$set(t.columns[0],"visible",e)},callbackSure:t.fn_sure},scopedSlots:t._u([{key:"body",fn:function(){return[a("el-form",[a("el-form-item",[a("div",{staticClass:"del-tips"},[t._v(" "+t._s(t.columns[0].text)+" ")])])],1)]},proxy:!0}])})],1)},i=[],o=(n("d81d"),n("511c")),s=n("0dc3"),l={name:"IotTable",components:{IotDialog:o["a"]},props:{columns:{type:Array,default:function(){return[{type:"",selectionText:!1,isShowdelete:!0,title:"",text:"",visible:!1,dialogWidth:Object(s["a"])(600)}]}},isMonitoring:{type:Boolean,default:!1},data:{type:Array,default:function(){return[]}},loading:{type:Boolean,default:!1}},data:function(){return{selecionData:[]}},methods:{toVW:s["a"],fn_sure:function(){this.$emit("del-callbackSure")},selectable:function(t,e){return console.log("row",t),!this.isMonitoring||0==t.alarmStatus},handleSelectionChange:function(t){console.log(t),this.selecionData=t.map((function(t){return t.id})),this.$emit("selection-change",t)},fn_del_selection_data:function(){this.selecionData.length?this.$emit("selection-del",this.selecionData):this.$newNotify.warning({message:"请至少选择一项"})},fn_ignore_selection_data:function(){this.selecionData.length?this.$emit("selection-ignore",this.selecionData):this.$newNotify.warning({message:"请至少选择一项"})},fn_handle_selection_data:function(){this.selecionData.length?this.$emit("selection-handle",this.selecionData):this.$newNotify.warning({message:"请至少选择一项"})},toggleSelect:function(t,e){console.log("set"),this.$refs.table.toggleRowSelection(t,e)},doLayout:function(){var t=this;this.$nextTick((function(){t.$refs["table"].doLayout()}))}}},r=l,c=(n("f7d8"),n("2877")),u=Object(c["a"])(r,a,i,!1,null,"7a36d05f",null);e["a"]=u.exports},"6c74":function(t,e,n){},"6e22":function(t,e,n){"use strict";var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"iot-pagination"},[n("el-pagination",{attrs:{"current-page":t.pagination.current,"page-sizes":t.pagination.sizes,"page-size":t.pagination.size,"pager-count":t.pagination.pagerCount,layout:t.layout,total:t.pagination.total},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1)},i=[],o={name:"IotPagination",props:{pagination:{type:Object,default:function(){return{current:1,total:0,pages:0,sizes:[10,20,50,100],size:10,pagerCount:7}}},layout:{type:String,default:"total, prev, pager, next, sizes, jumper"}},data:function(){return{}},methods:{handleSizeChange:function(t){this.$emit("size-change",t)},handleCurrentChange:function(t){this.$emit("current-change",t)}}},s=o,l=(n("54f2"),n("2877")),r=Object(l["a"])(s,a,i,!1,null,"50656dbc",null);e["a"]=r.exports},"7f0e":function(t,e,n){},"81a7":function(t,e,n){"use strict";n("7f0e")},aa32:function(t,e,n){},b58a:function(t,e,n){},c2a2:function(t,e,n){"use strict";var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("span",{directives:[{name:"throttle",rawName:"v-throttle",value:500,expression:"500"}],staticClass:"iot-btn",class:[t.type?"iot-button-"+t.type:""],on:{click:t.fn_search}},[t._v(t._s(t.text))])},i=[],o={name:"Iot-btn",props:{text:{type:String,default:"搜索"},bgcolor:{type:String,default:""},type:{type:String,default:"default"}},data:function(){return{}},methods:{fn_search:function(){this.$emit("search")}}},s=o,l=(n("81a7"),n("2877")),r=Object(l["a"])(s,a,i,!1,null,"7022bc2e",null);e["a"]=r.exports},d69b:function(t,e,n){"use strict";n("6c74")},d81d:function(t,e,n){"use strict";var a=n("23e7"),i=n("b727").map,o=n("1dde"),s=o("map");a({target:"Array",proto:!0,forced:!s},{map:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})},f2bc:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"update-info"},[n("div",{staticClass:"info-info"},[t._m(0),n("div",{staticClass:"info-detail"},[n("div",{staticClass:"item-rows"},[n("div",{staticClass:"item"},[n("span",[t._v("组名称")]),n("span",[t._v(t._s(t.connectorDetail.groupName))])]),n("div",{staticClass:"item"},[n("span",[t._v("组ID")]),n("span",[t._v(t._s(t.connectorDetail.id))])])]),n("div",{staticClass:"item-rows"},[n("div",{staticClass:"item"},[n("span",[t._v("配置时间")]),n("span",[t._v(t._s(t.connectorDetail.createTime))])]),n("div",{staticClass:"item"},[n("span",[t._v("创建时间")]),n("span",[t._v(t._s(t.connectorDetail.updateTime))])])]),n("div",{staticClass:"item-rows"},[n("div",{staticClass:"item"},[n("span",[t._v("更新时间")]),n("span",[t._v(t._s(t.connectorDetail.createTime))])])])])]),n("div",{staticClass:"info-content"},[t._m(1),n("div",{staticClass:"content-table"},[n("iot-table",{attrs:{columns:t.columns,data:t.tableData,loading:t.loading},scopedSlots:t._u([{key:"configInfo",fn:function(e){return[n("el-tooltip",{attrs:{placement:"top-start",effect:"light","popper-class":"event-tooltip"},scopedSlots:t._u([{key:"content",fn:function(){return[n("span",[t._v(" "+t._s(e.row.configInfo)+" ")])]},proxy:!0}],null,!0)},[n("div",{staticClass:"alarmContent-tooltip"},[n("p",[t._v(" "+t._s(t.fn_sub10(e.row.configInfo))+" ")])])])]}}])})],1),t.tableData.length>0?n("div",{staticClass:"content-bottom"},[n("iot-pagination",{attrs:{pagination:t.pagination},on:{"size-change":t.handleSizeChange,"current-change":t.handleCurrentChange}})],1):t._e()])])},i=[function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"info-title flex"},[n("div",{staticClass:"left"},[n("p",[t._v("设备组信息")])]),n("div",{staticClass:"right"})])},function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"content-select flex"},[n("div",{staticClass:"left"},[n("div",{staticClass:"content-title"},[n("span",[t._v("关联的设备列表")])])]),n("div",{staticClass:"right"})])}],o=n("5530"),s=(n("d3b7"),n("673a")),l=n("6e22"),r=n("0e0b"),c=(n("2ef0"),n("aa98")),u={name:"updateInfo",components:{IotTable:s["a"],IotPagination:l["a"]},data:function(){return{columns:[{label:"产品Key",prop:"productKey"},{label:"设备SN",prop:"deviceSn"},{label:"设备名称",prop:"deviceName"},{label:"操作时间",prop:"operatorTime"},{label:"操作对象",prop:"operatorObject"},{label:"创建时间",prop:"createTime"},{label:"更新时间",prop:"updateTime"}],tableData:[],loading:!1,pagination:{current:1,total:0,pages:0,sizes:[10,20,50,100],size:4},visible:!1,title:"编辑固件信息",dialogWidth:"742px",infoForm:{id:0,name:"",description:""},rules:{name:[{required:!0,trigger:"blur",validator:this.checkName}],description:[{required:!1,trigger:"blur",validator:this.checkLength}]},nameTrue:!0,descTrue:!0,connectorDetail:{},firmwareJobDetailForm:{},firmwareJobStatic:{},firmwareJobList:{},groupId:"",inputHolder:"输入设备名称",jobId:""}},created:function(){this.groupId=this.$route.query.id},mounted:function(){this.fn_getConnectorDetail(),this.fn_get_table_data()},methods:{fn_sub10:function(t){return t.length>20?"".concat(t.substr(0,20),"..."):t},fn_getConnectorDetail:function(){var t=this;Object(c["r"])({id:this.groupId}).then((function(e){if(200==e.code){var n={id:e.data.id,title:e.data.deviceName};t.$store.dispatch("setLayoutInfo",n),t.connectorDetail=e.data}}))},fn_get_table_data:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{groupId:this.groupId},n=Object(o["a"])({},e);e.size||(n.size=10,n.current=1),Object(c["i"])(n).then((function(e){200==e.code?(setTimeout((function(){t.loading=!1}),300),t.tableData=e.data.records,t.pagination.total=e.data.total,t.pagination.current=e.data.current,t.pagination.pages=e.data.pages,t.pagination.size=e.data.size):t.$newNotify.error({message:e.message})})).finally((function(){setTimeout((function(){t.loading=!1}),300)}))},handleSizeChange:function(t){this.pagination.current=1,this.pagination.size=t,this.fn_get_table_data({groupId:this.groupId,size:this.pagination.size,current:this.pagination.current})},handleCurrentChange:function(t){console.log(this.pagination),this.pagination.current=t,this.fn_get_table_data({groupId:this.groupId,size:this.pagination.size,current:this.pagination.current})},fn_notNull:function(t){return 0!==t&&!t},handleSearch:function(t){this.deviceName=t.value,this.fn_get_table_data({groupId:this.groupId,deviceName:t.value})},fn_clear_search_info:function(){this.deviceName="",this.fn_get_table_data({groupId:this.groupId})},checkName:function(t,e,n){return this.fn_notNull(e)?n(new Error("请输入固件名称")):Object(r["d"])(e)?void n():n(new Error("支持中文、英文大小写、数字、部分常用符号（下划线，减号，括弧）， 必须以中文、英文或数字开头，长度不超过32个字符"))},fn_validate:function(t,e){"name"===t&&(this.nameTrue=e),"description"===t&&(this.descTrue=e)},checkLength:function(t,e,n){if(!Object(r["c"])(e,201))return n(new Error("最多不超过200个字符"));n()}}},d=u,p=(n("d69b"),n("2877")),f=Object(p["a"])(d,a,i,!1,null,"99fd1074",null);e["default"]=f.exports},f7d8:function(t,e,n){"use strict";n("aa32")},fe0d:function(t,e,n){}}]);
//# sourceMappingURL=chunk-b5da5b02.ff0ba683.js.map