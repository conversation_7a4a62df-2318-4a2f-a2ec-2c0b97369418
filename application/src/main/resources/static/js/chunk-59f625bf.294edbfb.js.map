{"version": 3, "sources": ["webpack:///./src/views/deviceGroup/list/index.vue?98bc", "webpack:///./src/views/deviceGroup/list/components/relation/index.vue?dc4a", "webpack:///src/views/deviceGroup/list/components/relation/index.vue", "webpack:///./src/views/deviceGroup/list/components/relation/index.vue?48d4", "webpack:///./src/views/deviceGroup/list/components/relation/index.vue", "webpack:///src/views/deviceGroup/list/index.vue", "webpack:///./src/views/deviceGroup/list/index.vue?9044", "webpack:///./src/views/deviceGroup/list/index.vue", "webpack:///./src/views/deviceGroup/list/index.vue?6202", "webpack:///./src/views/deviceGroup/list/components/relation/index.vue?605e"], "names": ["render", "_vm", "this", "_h", "$createElement", "_c", "_self", "staticClass", "attrs", "on", "fn_open", "handleClear", "nativeOn", "$event", "type", "indexOf", "_k", "keyCode", "key", "fn_handle__query", "apply", "arguments", "model", "value", "callback", "$$v", "groupName", "expression", "slot", "columns", "tableData", "loading", "fn_select_more_data", "fn_del_more_data", "fn_del_sure", "scopedSlots", "_u", "fn", "scope", "fn_edit", "row", "_v", "fn_del", "id", "fn_check", "relationDevice", "pagination", "handleSizeChange", "handleCurrentChange", "_e", "visible", "title", "dialogWidth", "fn_sure", "fn_close", "ref", "groupForm", "rules", "fn_validate", "$set", "proxy", "handleReset", "staticRenderFns", "width", "handleClose", "_s", "waitCount", "notSearchVal", "notColumns", "notSource", "notLoading", "data", "selectionChange", "routeDevice", "notPagination", "submitBind", "doneCount", "alreadySearchVal", "alreadySource", "alreadyLoading", "alreadyPagination", "deviceVisible", "product", "c", "deviceForm", "configInfo", "deviceSn", "vendorName", "deviceDesc", "options", "current", "size", "total", "notSelectList", "alreadySelectList", "groupId", "isEmpty", "configInfoTrue", "deviceSnTrue", "vendorNameTrue", "deviceDescTrue", "components", "props", "hostProductKey", "String", "hostDeviceName", "methods", "console", "log", "JSON", "parse", "stringify", "checkDeviceSn", "fn_notNull", "Error", "checkVendorName", "checkConfigInfoLength", "checkDeviceSnLength", "val", "checkLength", "open", "getProductKey", "selectChange", "productInfo", "object", "flag", "isTotal", "params", "deviceName", "res", "code", "isTips", "message", "map", "length", "$newNotify", "warning", "deviceList", "$router", "replace", "path", "$emit", "component", "name", "IotPagination", "IotTable", "relation", "IotDialog", "IotButton", "IotForm", "enableList", "pages", "sizes", "inputHolder", "selectHolder", "searchValue", "productId", "productOptions", "productOptionsCopy", "deviceOptions", "statusCount", "totalNum", "activeNum", "onlineNum", "configTime", "groupNameTrue", "configTimeTrue", "delId", "delIds", "created", "watch", "$refs", "resetFields", "mounted", "$route", "query", "fn_get_table_data", "fn_sub10", "str", "calcul_long_text", "fn_select", "checkGroupName", "checkConfigTime", "fn_get_device_status_count", "others", "item", "ids", "join", "fn_del_table_data", "fn_format_select", "list", "converterName", "bindStatusName", "disabled", "fn_search_table_data", "<PERSON><PERSON><PERSON>", "fn_clear_search_info", "push", "num", "validate", "valid", "postUrl"], "mappings": "yHAAA,IAAIA,EAAS,WAAa,IAAIC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACE,YAAY,UAAU,CAACF,EAAG,MAAM,CAACE,YAAY,cAAc,CAACF,EAAG,MAAM,CAACE,YAAY,qBAAqB,CAACF,EAAG,MAAM,CAACE,YAAY,YAAY,CAAC<PERSON>,EAAG,aAAa,CAACG,MAAM,CAAC,KAAO,SAASC,GAAG,CAAC,OAASR,EAAIS,YAAY,GAAGL,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,WAAW,CAACG,MAAM,CAAC,UAAY,GAAG,YAAc,SAASC,GAAG,CAAC,MAAQR,EAAIU,aAAaC,SAAS,CAAC,MAAQ,SAASC,GAAQ,OAAIA,EAAOC,KAAKC,QAAQ,QAAQd,EAAIe,GAAGH,EAAOI,QAAQ,QAAQ,GAAGJ,EAAOK,IAAI,SAAkB,KAAcjB,EAAIkB,iBAAiBC,MAAM,KAAMC,aAAaC,MAAM,CAACC,MAAOtB,EAAa,UAAEuB,SAAS,SAAUC,GAAMxB,EAAIyB,UAAUD,GAAKE,WAAW,cAAc,CAACtB,EAAG,IAAI,CAACE,YAAY,gCAAgCC,MAAM,CAAC,KAAO,UAAUC,GAAG,CAAC,MAAQR,EAAIkB,kBAAkBS,KAAK,cAAc,OAAOvB,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACF,EAAG,YAAY,CAACG,MAAM,CAAC,QAAUP,EAAI4B,QAAQ,KAAO5B,EAAI6B,UAAU,QAAU7B,EAAI8B,SAAStB,GAAG,CAAC,mBAAmBR,EAAI+B,oBAAoB,gBAAgB/B,EAAIgC,iBAAiB,mBAAmBhC,EAAIiC,aAAaC,YAAYlC,EAAImC,GAAG,CAAC,CAAClB,IAAI,YAAYmB,GAAG,SAASC,GAAO,MAAO,CAACjC,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,IAAI,CAACE,YAAY,SAASE,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOZ,EAAIsC,QAAQD,EAAME,QAAQ,CAACvC,EAAIwC,GAAG,QAAQpC,EAAG,KAAKA,EAAG,IAAI,CAACE,YAAY,SAASE,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOZ,EAAIyC,OAAOJ,EAAME,IAAIG,OAAO,CAAC1C,EAAIwC,GAAG,QAAQpC,EAAG,KAAKA,EAAG,IAAI,CAACE,YAAY,SAASE,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOZ,EAAI2C,SAASN,EAAME,IAAIG,OAAO,CAAC1C,EAAIwC,GAAG,QAAQpC,EAAG,KAAKA,EAAG,IAAI,CAACE,YAAY,SAASC,MAAM,CAAC,KAAO,aAAaC,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOZ,EAAI4C,eAAeP,EAAME,IAAIG,MAAMf,KAAK,aAAa,CAAC3B,EAAIwC,GAAG,sBAAsB,GAAIxC,EAAI6B,UAAgB,OAAEzB,EAAG,MAAM,CAACE,YAAY,iBAAiB,CAACF,EAAG,iBAAiB,CAACG,MAAM,CAAC,WAAaP,EAAI6C,YAAYrC,GAAG,CAAC,cAAcR,EAAI8C,iBAAiB,iBAAiB9C,EAAI+C,wBAAwB,GAAG/C,EAAIgD,KAAK5C,EAAG,aAAa,CAACG,MAAM,CAAC,QAAUP,EAAIiD,QAAQ,MAAQjD,EAAIkD,MAAM,MAAQlD,EAAImD,aAAa3C,GAAG,CAAC,iBAAiB,SAASI,GAAQZ,EAAIiD,QAAQrC,GAAQ,aAAeZ,EAAIoD,QAAQ,MAAQpD,EAAIqD,UAAUnB,YAAYlC,EAAImC,GAAG,CAAC,CAAClB,IAAI,OAAOmB,GAAG,WAAW,MAAO,CAAc,GAAZpC,EAAIa,KAAWT,EAAG,WAAW,CAACA,EAAG,UAAU,CAACkD,IAAI,YAAYhD,YAAY,YAAYC,MAAM,CAAC,iBAAiB,MAAM,MAAQP,EAAIuD,UAAU,MAAQvD,EAAIwD,MAAM,cAAc,QAAQhD,GAAG,CAAC,SAAWR,EAAIyD,cAAc,CAACrD,EAAG,eAAe,CAACG,MAAM,CAAC,MAAQ,MAAM,KAAO,cAAc,CAACH,EAAG,WAAW,CAACiB,MAAM,CAACC,MAAOtB,EAAIuD,UAAmB,UAAEhC,SAAS,SAAUC,GAAMxB,EAAI0D,KAAK1D,EAAIuD,UAAW,YAAa/B,IAAME,WAAW,0BAA0B,GAAI1B,EAAiB,cAAEI,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACN,EAAIwC,GAAG,qBAAqBxC,EAAIgD,KAAK5C,EAAG,eAAe,CAACG,MAAM,CAAC,MAAQ,OAAO,KAAO,eAAe,CAACH,EAAG,WAAW,CAACiB,MAAM,CAACC,MAAOtB,EAAIuD,UAAoB,WAAEhC,SAAS,SAAUC,GAAMxB,EAAI0D,KAAK1D,EAAIuD,UAAW,aAAc/B,IAAME,WAAW,2BAA2B,GAAI1B,EAAkB,eAAEI,EAAG,MAAM,CAACE,YAAY,gBAAgB,CAACN,EAAIwC,GAAG,mBAAmBxC,EAAIgD,MAAM,IAAI,GAAGhD,EAAIgD,KAAkB,GAAZhD,EAAIa,KAAWT,EAAG,MAAM,CAACA,EAAG,WAAW,CAAC8B,YAAYlC,EAAImC,GAAG,CAAC,CAAClB,IAAI,UAAUmB,GAAG,WAAW,MAAO,CAAChC,EAAG,UAAU,CAACA,EAAG,eAAe,CAACA,EAAG,MAAM,CAACE,YAAY,YAAY,CAACN,EAAIwC,GAAG,kCAAkC,KAAKmB,OAAM,IAAO,MAAK,EAAM,eAAe,GAAG3D,EAAIgD,OAAOW,OAAM,OAAUvD,EAAG,WAAW,CAACkD,IAAI,WAAW9C,GAAG,CAAC,MAAQR,EAAI4D,gBAAgB,IACl2GC,EAAkB,G,gJCDlB,EAAS,WAAa,IAAI7D,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACA,EAAG,aAAa,CAACG,MAAM,CAAC,MAAQ,QAAQ,IAAM,OAAO,UAAY,OAAO,QAAUP,EAAIiD,QAAQ,MAAQjD,EAAI8D,MAAM,YAAa,EAAK,QAAS,GAAOtD,GAAG,CAAC,iBAAiB,SAASI,GAAQZ,EAAIiD,QAAQrC,GAAQ,MAAQZ,EAAI+D,aAAa7B,YAAYlC,EAAImC,GAAG,CAAC,CAAClB,IAAI,OAAOmB,GAAG,WAAW,MAAO,CAAChC,EAAG,MAAM,CAACE,YAAY,WAAW,CAACF,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,KAAK,CAACJ,EAAIwC,GAAG,SAASxC,EAAIgE,GAAGhE,EAAIiE,cAAc7D,EAAG,MAAM,CAACE,YAAY,SAAS,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,WAAW,CAACG,MAAM,CAAC,YAAc,aAAa,UAAY,IAAIC,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOZ,EAAIkB,kBAAiB,KAAQP,SAAS,CAAC,MAAQ,SAASC,GAAQ,OAAIA,EAAOC,KAAKC,QAAQ,QAAQd,EAAIe,GAAGH,EAAOI,QAAQ,QAAQ,GAAGJ,EAAOK,IAAI,SAAkB,KAAcjB,EAAIkB,kBAAiB,KAAQG,MAAM,CAACC,MAAOtB,EAAgB,aAAEuB,SAAS,SAAUC,GAAMxB,EAAIkE,aAAa1C,GAAKE,WAAW,iBAAiB,CAACtB,EAAG,IAAI,CAACE,YAAY,gCAAgCC,MAAM,CAAC,KAAO,UAAUC,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOZ,EAAIkB,kBAAiB,KAAQS,KAAK,cAAc,GAAGvB,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,YAAY,CAACG,MAAM,CAAC,QAAUP,EAAImE,WAAW,KAAOnE,EAAIoE,UAAU,QAAUpE,EAAIqE,YAAY7D,GAAG,CAAC,mBAAmB,SAAU8D,GAAQ,OAAOtE,EAAIuE,gBAAgBD,GAAM,KAAUpC,YAAYlC,EAAImC,GAAG,CAAC,CAAClB,IAAI,QAAQmB,GAAG,WAAW,MAAO,CAAEpC,EAAW,QAAEI,EAAG,MAAM,CAACE,YAAY,SAAS,CAACN,EAAIwC,GAAG,gBAAgBpC,EAAG,OAAO,CAACI,GAAG,CAAC,MAAQR,EAAIwE,cAAc,CAACxE,EAAIwC,GAAG,YAAYxC,EAAIgD,OAAOW,OAAM,OAAUvD,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,iBAAiB,CAACG,MAAM,CAAC,WAAaP,EAAIyE,cAAc,OAAS,qCAAqCjE,GAAG,CAAC,iBAAiB,SAAU8D,GAAQ,OAAOtE,EAAI+C,oBAAoBuB,GAAM,QAAa,IAAI,OAAOlE,EAAG,MAAM,CAACE,YAAY,eAAe,CAACF,EAAG,IAAI,CAACE,YAAY,OAAOE,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOZ,EAAI0E,YAAW,MAAS,CAACtE,EAAG,OAAO,CAACJ,EAAIwC,GAAG,QAAQpC,EAAG,MAAM,CAACG,MAAM,CAAC,IAAM,EAAQ,QAAwC,IAAM,QAAQH,EAAG,IAAI,CAACE,YAAY,UAAUE,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOZ,EAAI0E,YAAW,MAAU,CAACtE,EAAG,MAAM,CAACG,MAAM,CAAC,IAAM,EAAQ,QAAwC,IAAM,MAAMH,EAAG,OAAO,CAACJ,EAAIwC,GAAG,YAAYpC,EAAG,MAAM,CAACE,YAAY,uBAAuB,CAACF,EAAG,KAAK,CAACJ,EAAIwC,GAAG,SAASxC,EAAIgE,GAAGhE,EAAI2E,cAAcvE,EAAG,MAAM,CAACE,YAAY,SAAS,CAACF,EAAG,MAAM,CAACE,YAAY,aAAa,CAACF,EAAG,WAAW,CAACG,MAAM,CAAC,YAAc,UAAU,UAAY,IAAIC,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOZ,EAAIkB,kBAAiB,KAASP,SAAS,CAAC,MAAQ,SAASC,GAAQ,OAAIA,EAAOC,KAAKC,QAAQ,QAAQd,EAAIe,GAAGH,EAAOI,QAAQ,QAAQ,GAAGJ,EAAOK,IAAI,SAAkB,KAAcjB,EAAIkB,kBAAiB,KAASG,MAAM,CAACC,MAAOtB,EAAoB,iBAAEuB,SAAS,SAAUC,GAAMxB,EAAI4E,iBAAiBpD,GAAKE,WAAW,qBAAqB,CAACtB,EAAG,IAAI,CAACE,YAAY,gCAAgCC,MAAM,CAAC,KAAO,UAAUC,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOZ,EAAIkB,kBAAiB,KAASS,KAAK,cAAc,GAAGvB,EAAG,MAAM,CAACE,YAAY,wBAAwB,CAACF,EAAG,YAAY,CAACG,MAAM,CAAC,QAAUP,EAAImE,WAAW,KAAOnE,EAAI6E,cAAc,QAAU7E,EAAI8E,gBAAgBtE,GAAG,CAAC,mBAAmB,SAAU8D,GAAQ,OAAOtE,EAAIuE,gBAAgBD,GAAM,OAAalE,EAAG,MAAM,CAACE,YAAY,mBAAmB,CAACF,EAAG,iBAAiB,CAACG,MAAM,CAAC,WAAaP,EAAI+E,kBAAkB,OAAS,qCAAqCvE,GAAG,CAAC,iBAAiB,SAAU8D,GAAQ,OAAOtE,EAAI+C,oBAAoBuB,GAAM,QAAc,IAAI,aAAaX,OAAM,QAAW,IAC1/G,EAAkB,G,oCCgHtB,GACEW,KADF,WAEI,MAAO,CACLzD,KAAM,EACNoC,SAAS,EACT+B,eAAe,EACfC,QAAS,GACTC,EAAG,GACHC,WAAY,CACVC,WAAY,GACZC,SAAU,GACVC,WAAY,GACZC,WAAY,IAEdC,QAAS,CACf,CACQ,MAAR,IACQ,MAAR,OAGMtB,aAAc,GACdC,WAAY,CAClB,CACQ,KAAR,aAEA,CACQ,KAAR,aACQ,MAAR,aACQ,MAAR,KAEA,CACQ,KAAR,aACQ,MAAR,QAEA,CACQ,KAAR,WACQ,MAAR,QAEA,CACQ,KAAR,mBACQ,MAAR,SAGMC,UAAW,GACXC,YAAY,EACZI,cAAe,CACbgB,QAAS,EACTC,KAAM,EACNC,MAAO,GAETC,cAAe,GACfhB,iBAAkB,GAClBC,cAAe,GACfC,gBAAgB,EAChBC,kBAAmB,CACjBU,QAAS,EACTC,KAAM,EACNC,MAAO,GAETE,kBAAmB,GAEnBC,QAAS,GACTC,SAAS,EACT9B,UAAW,EACXU,UAAW,EACXb,MAAO,GAAb,2BAEMkC,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,EAChBC,gBAAgB,IAGpBC,WAAY,CAAd,uDACEC,MAAO,CACLC,eAAgB,CACdzF,KAAM0F,QAERC,eAAgB,CACd3F,KAAM0F,SAGVE,QAAS,CACPnE,QADJ,SACA,GACMoE,QAAQC,IAAI,MAAOpE,GACnBtC,KAAKkF,WAAayB,KAAKC,MAAMD,KAAKE,UAAUvE,IAC5CtC,KAAKkF,WAAWC,WAAawB,KAAKE,UACxC,uCACA,KACA,GAEM7G,KAAK+E,eAAgB,GAEvB+B,cAXJ,SAWA,OACM,OAAI9G,KAAK+G,WAAW1F,GACXC,EAAS,IAAI0F,MAAM,YAClC,uBAOQ1F,IANOA,EACf,UACA,oDAOI2F,gBAxBJ,SAwBA,OACM,IAAK,OAAX,OAAW,CAAX,OACQ,OAAO3F,EACf,UACA,mDAIQA,KAGJ4F,sBAnCJ,SAmCA,OAGM,OAFA7F,EAAQsF,KAAKE,UAAUxF,GAEnBrB,KAAK+G,WAAW1F,GACXC,EAAS,IAAI0F,MAAM,eAClC,uBAGW,OAAX,OAAW,CAAX,QAGQ1F,IAFOA,EAAS,IAAI0F,MAAM,iBAHnB1F,EAAS,IAAI0F,MAAM,kBAS9BG,oBAlDJ,SAkDA,OACM,OAAInH,KAAK+G,WAAW1F,GACXC,EAAS,IAAI0F,MAAM,YAClC,2BAGQ1F,IAFOA,EAAS,IAAI0F,MAAM,iBAK9BD,WA3DJ,SA2DA,GACM,OAAe,IAARK,IAAcA,GAEvBC,YA9DJ,SA8DA,OACM,IAAK,OAAX,OAAW,CAAX,OACQ,OAAO/F,EAAS,IAAI0F,MAAM,gBAE1B1F,KAGJgG,KArEJ,SAqEA,GACMtH,KAAK6F,QAAUA,EACf7F,KAAKgD,SAAU,EAGfhD,KAAKuH,cAAc,GAAG,GAAM,GAE5BvH,KAAKuH,cAAc,GAAG,GAAM,IAE9BC,aA9EJ,SA8EA,GACMxH,KAAKwE,cAAcgB,QAAU,EAC7BxF,KAAK8E,kBAAkBU,QAAU,EACjC,IAAN,wDAEMxF,KAAKyH,YAAcC,EACnB1H,KAAKuH,cAAc,GAAG,GAAM,GAE5BvH,KAAKuH,cAAc,GAAG,GAAM,IAE9BtG,iBAxFJ,SAwFA,GACU0G,GAEF3H,KAAKwE,cAAcgB,QAAU,EAC7BxF,KAAKuH,cAAc,KAGnBvH,KAAK8E,kBAAkBU,QAAU,EACjCxF,KAAKuH,cAAc,KAGvBA,cAnGJ,SAmGA,kIACA,KACUI,GAEF3H,KAAK2E,iBAAmBiD,EAAU,GAAK5H,KAAK2E,iBAC5CkD,EAAS,CACPhC,QAAS7F,KAAK6F,QACdL,QAASxF,KAAK8E,kBAAkBU,QAChCC,KAAMzF,KAAK8E,kBAAkBW,KAC7BqC,WAAYF,EAAU,GAAK5H,KAAK2E,kBAGlC,OAAR,OAAQ,CAAR,qBACU,GAAgB,KAAZoD,EAAIC,KAAa,CACnB,IAAZ,SAEY,EAAZ,4BACY,EAAZ,gCACY,EAAZ,wCAGY,EAAZ,iBACY,EAAZ,YACY,EAAZ,0BACgBC,GACF,EAAd,oBACgBC,QAASH,EAAIG,eAOrBlI,KAAKiE,aAAe2D,EAAU,GAAK5H,KAAKiE,aACxC4D,EAAS,CACPrC,QAASxF,KAAKwE,cAAcgB,QAC5BC,KAAMzF,KAAKwE,cAAciB,KACzBqC,WAAYF,EAAU,GAAK5H,KAAKiE,cAElC,OAAR,OAAQ,CAAR,qBACU,GAAgB,KAAZ8D,EAAIC,KAAa,CACnB,IAAZ,SAE4B,MAAZD,EAAIC,KACN,EAAd,WAEc,EAAd,WAEY,EAAZ,wBACY,EAAZ,gCACY,EAAZ,oCAEY,EAAZ,aACY,EAAZ,YACY,EAAZ,sBACgBC,GACF,EAAd,oBACgBC,QAASH,EAAIG,eAOzBzH,YAnKJ,aAoKI6D,gBApKJ,SAoKA,KACUqD,EAEF3H,KAAK2F,cAAgBtB,EAAK8D,KAAI,SAAtC,kBAGQnI,KAAK4F,kBAAoBvB,EAAK8D,KAAI,SAA1C,mBAGIrF,oBA7KJ,SA6KA,KACU6E,GAEF3H,KAAKwE,cAAcgB,QAAUnB,EAC7BrE,KAAKuH,cAAc,KAGnBvH,KAAK8E,kBAAkBU,QAAUnB,EACjCrE,KAAKuH,cAAc,KAGvB9C,WAxLJ,SAwLA,cACM,GAAIkD,EAAM,CAER,GAAiC,GAA7B3H,KAAK2F,cAAcyC,OAIrB,YAHApI,KAAKqI,WAAWC,QAAQ,CACtBJ,QAAS,aAIb,OAAR,OAAQ,CAAR,CACUrC,QAAS7F,KAAK6F,QACd0C,WAAYvI,KAAK2F,gBAC3B,kBAC0B,KAAZoC,EAAIC,MACN,EAAZ,oBACcE,QAASH,EAAIG,UAEf,EAAZ,wBACY,EAAZ,4BAEY,EAAZ,uBAEY,EAAZ,wBAEY,EAAZ,kBACcA,QAASH,EAAIG,iBAI3B,CAEQ,GAAqC,GAAjClI,KAAK4F,kBAAkBwC,OAIzB,YAHApI,KAAKqI,WAAWC,QAAQ,CACtBJ,QAAS,aAIb,OAAR,OAAQ,CAAR,CACUrC,QAAS7F,KAAK6F,QACd0C,WAAYvI,KAAK4F,oBAC3B,kBAC0B,KAAZmC,EAAIC,MACN,EAAZ,oBACcE,QAASH,EAAIG,UAEf,EAAZ,wBACY,EAAZ,4BAEY,EAAZ,uBAEY,EAAZ,wBAEY,EAAZ,kBACcA,QAASH,EAAIG,eAMvB3D,YAnPJ,WAoPMvE,KAAKwI,QAAQC,QAAQ,CACnBC,KAAM,aAGV5E,YAxPJ,WAyPM9D,KAAKgF,QAAU,GACfhF,KAAKyH,YAAc,GACnBzH,KAAKiE,aAAe,GACpBjE,KAAK2E,iBAAmB,GACxB3E,KAAKmE,UAAY,GACjBnE,KAAK4E,cAAgB,GACrB5E,KAAKwE,cAAcgB,QAAU,EAC7BxF,KAAK8E,kBAAkBU,QAAU,EACjCxF,KAAKwE,cAAckB,MAAQ,EAC3B1F,KAAK8E,kBAAkBY,MAAQ,EAC/B1F,KAAK2I,MAAM,YCtc0X,I,wBCQvYC,EAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,MAIa,EAAAA,E,QC0If,G,UAAA,CACEC,KAAM,SACN1C,WAAY,CACV2C,cAAJ,OACIC,SAAJ,OACIC,SAAJ,EACIC,UAAJ,OACIC,UAAJ,OACIC,QAAJ,QAEE9E,KAVF,WAWI,MAAO,CACL7C,UAAW,GACXG,QAAS,CACf,CAAQ,MAAR,MAAQ,KAAR,MACA,CACQ,MAAR,MACQ,KAAR,aAEA,CACQ,MAAR,OACQ,KAAR,cAEA,CACQ,MAAR,OACQ,KAAR,cAEA,CACQ,MAAR,OACQ,KAAR,cAEA,CACQ,MAAR,KACQ,KAAR,YACQ,SAAR,YACQ,MAAR,MAGMyH,WAAY,CAClB,CACQ,MAAR,EACQ,MAAR,OAEA,CACQ,MAAR,EACQ,MAAR,QAGMxH,UAAW,GACXgB,WAAY,CACV4C,QAAS,EACTE,MAAO,EACP2D,MAAO,EACPC,MAAO,CAAC,GAAI,GAAI,GAAI,KACpB7D,KAAM,IAGR5D,SAAS,EACTqB,YAAa,QACbtC,KAAM,EACNoC,SAAS,EACTuG,YAAa,WACbC,aAAc,UACdC,YAAa,CACXC,UAAW,IAGbC,eAAgB,GAChBC,mBAAoB,GAEpBC,cAAe,CACrB,CACQ,GAAR,IACQ,KAAR,SAGMC,YAAa,CACXC,SAAU,EACVC,UAAW,EACXC,UAAW,GAGb3G,UAAW,CACT9B,UAAW,GACX0I,WAAY,IAGdC,eAAe,EACfC,gBAAgB,EAEhB7G,MAAO,CACL/B,UAAW,CACnB,CACU,UAAV,EAEU,QAAV,OACU,UAAV,sBAGQ0I,WAAY,CACpB,CACU,UAAV,EAEU,QAAV,OACU,UAAV,wBAIMjH,MAAO,GACPoH,MAAO,GAEPC,OAAQ,KAGZC,QAlHF,aAmHEC,MAAO,CACLxH,QADJ,SACA,GACWoE,GAAoB,GAAbpH,KAAKY,OACfZ,KAAKsD,UAAY,GACjBtD,KAAKyK,MAAM,cAAgBzK,KAAKyK,MAAM,aAAaC,iBAKzDC,QA5HF,WA6HQ3K,KAAK4K,OAAOC,MAAMpI,KACpBzC,KAAKyJ,YAAYC,UAAY1J,KAAK4K,OAAOC,MAAMpI,IAEjD,IAAJ,mCACA,kBADA,IAEM+C,QAASxF,KAAK4C,WAAW4C,QACzBC,KAAMzF,KAAK4C,WAAW6C,OAGxBzF,KAAK8K,kBAAkBzG,IAEzBmC,QAAS,CACP7C,YADJ,WAEM3D,KAAK4C,WAAW4C,QAAU,EAC1BxF,KAAK8K,qBAEPnI,eALJ,SAKA,GACM3C,KAAKyK,MAAMzB,SAAS1B,KAAK7E,IAE3BsI,SARJ,SAQA,GACM,GAAIC,EAAK,OAAOA,EAAI5C,OAAS,GAAK,GAAxC,gCAEIrB,WAXJ,SAWA,GACM,OAAe,IAARK,IAAcA,GAGvBnG,iBAfJ,WAgBM,IAAN,GACQO,UAAWxB,KAAKwB,UAChBgE,QAAS,EACTC,KAAMzF,KAAK4C,WAAW6C,MAExBzF,KAAK8K,kBAAkBjD,IAEzBpH,YAvBJ,WAwBMT,KAAK8K,qBAEPG,iBA1BJ,WA0BA,gEACM,OAAO7D,EAAIqB,QAAQ,iCAAkC,MAAML,QAE7D8C,UA7BJ,WA8BM,IAAN,sCACMlL,KAAK8K,kBAAkBzG,IAGzB8G,eAlCJ,SAkCA,kBACA,KACM,OAAN,OAAM,CAAN,CACQ3J,UAAWH,EACXoB,GAAIzC,KAAKsD,UAAUb,KAC3B,kBAKQ,OAJgB,KAAZsF,EAAIC,OACNL,EAAOI,EAAI1D,MAGT,EAAZ,cACiB/C,EAAS,IAAI0F,MAAM,aACpC,wBACiB1F,EAAS,IAAI0F,MAAM,kBACpC,EACiB1F,EAAS,IAAI0F,MAAM,sBAE1B1F,QAKN8J,gBAxDJ,SAwDA,OACM,OAAIpL,KAAK+G,WAAW1F,GACXC,EAAS,IAAI0F,MAAM,YAClC,2BACe1F,EAAS,IAAI0F,MAAM,oBAE1B1F,KAIJ+J,2BAlEJ,WAkEA,WACM,OAAN,OAAM,GAAN,kBACQ,EAAR,uBAIIP,kBAxEJ,WAwEA,uEACA,uBACWjD,EAAOpC,OACV6F,EAAO7F,KAAO,GACd6F,EAAO9F,QAAU,GAEnB,OAAN,OAAM,CAAN,GACA,kBACA,aACU,YAAV,WACY,EAAZ,aACA,KACU,EAAV,yBACU,EAAV,8BACU,EAAV,kCACU,EAAV,8BACU,EAAV,6BAEU,EAAV,kBACY,QAAZ,eAIA,oBACQ,YAAR,WACU,EAAV,aACA,SAIIhC,YAtGJ,SAsGA,KACmB,cAATqF,IACF7I,KAAKmK,cAAgB9I,GAEV,eAATwH,IACF7I,KAAKoK,eAAiB/I,IAI1BS,oBA/GJ,SA+GA,GAEM9B,KAAKsK,OAASjG,EAAK8D,KAAI,SAA7B,GACQ,OAAOoD,EAAK9I,OAIhBV,iBAtHJ,WAuHiC,IAAvB/B,KAAKsK,OAAOlC,SACdpI,KAAK2B,QAAQ,GAAGqB,SAAU,IAM9BhB,YA9HJ,WA+HM,IAAN,GACQwJ,IAAKxL,KAAKsK,OAAOmB,KAAK,MAExBzL,KAAK0L,kBAAkBrH,IAGzB7D,QArIJ,WAsIMR,KAAKiD,MAAQ,QACbjD,KAAKY,KAAO,EACZZ,KAAKkD,YAAc,QACnBlD,KAAKgD,SAAU,GAEjBX,QA3IJ,SA2IA,GACMrC,KAAKsD,UAAYqD,KAAKC,MAAMD,KAAKE,UAAUvE,IAC3CtC,KAAKiD,MAAQ,QACbjD,KAAKY,KAAO,EACZZ,KAAKkD,YAAc,QACnBlD,KAAKgD,SAAU,GAGjB2I,iBAnJJ,SAmJA,mEACM,OAAOC,EAAKzD,KAAI,SAAtB,GACQ,MAAO,CACL1F,GAAI8I,EAAK9I,GACToJ,cAAeN,EAAKM,cACpBC,eAAgBP,EAAKO,eACrBC,SAAiC,OAAvBR,EAAKO,gBAA2BP,EAAK9I,IAAMA,OAK3DuJ,qBA9JJ,SA8JA,GACMvF,QAAQC,IAAImB,GACM,MAAdA,EAAOpF,GACTzC,KAAKyJ,YAAYwC,UAAYpE,EAAOxG,MAEpCrB,KAAKyJ,YAAY3B,WAAaD,EAAOxG,MAEvC,IAAN,sCACMgD,EAAKoB,KAAOzF,KAAK4C,WAAW6C,KAC5BzF,KAAK8K,kBAAkBzG,IAGzBxB,iBA1KJ,SA0KA,GAEM7C,KAAK4C,WAAW6C,KAAO2B,EACvB,IAAN,GACQ3B,KAAMzF,KAAK4C,WAAW6C,KACtBD,QAAS,GAEXxF,KAAK8K,kBAAkBjD,IAGzB/E,oBApLJ,SAoLA,GAEM9C,KAAK4C,WAAW4C,QAAU4B,EAC1B,IAAN,mCACA,kBADA,IAEQ5B,QAASxF,KAAK4C,WAAW4C,QACzBC,KAAMzF,KAAK4C,WAAW6C,OAExBzF,KAAK8K,kBAAkBjD,IAGzBqE,qBA/LJ,WAgMMlM,KAAKyJ,YAAYwC,UAAY,GAC7BjM,KAAKyJ,YAAY3B,WAAa,GAC9B,IAAN,sCACM9H,KAAK8K,kBAAkBzG,IAGzB3B,SAtMJ,SAsMA,KACM1C,KAAKwI,QAAQ2D,KAAK,CAChBzD,KAAM,qBACNmC,MAAO,CACLpI,GAAI4B,EACJ+H,IAAKA,MAKXjJ,QAhNJ,WAgNA,WAEM,GAAkB,IAAdnD,KAAKY,KAAY,CACnB,IAAR,GACU6B,GAAIzC,KAAKqK,OAEX,OAAR,OAAQ,CAAR,qBAC0B,KAAZtC,EAAIC,MACN,EAAZ,oBACcE,QAASH,EAAIG,UAEf,EAAZ,qBAEY,EAAZ,mBACczC,KAAM,EAApB,gBACcD,QAAS,IAEX,EAAZ,YAEY,EAAZ,kBACc0C,QAASH,EAAIG,kBAM3B,eACQlI,KAAKyK,MAAM,aAAa4B,UAAS,SAAzC,GACU,GAAIC,EAAO,CACT,IAAZ,iBACA,OACA,OAEYC,EAAQ,EAApB,6BAC8B,KAAZxE,EAAIC,MACN,EAAhB,oBACkBE,QAASH,EAAIG,UAEf,EAAhB,qBACgB,EAAhB,yBAEgB,EAAhB,mBACkBzC,KAAM,EAAxB,gBACkBD,QAAS,IAEX,EAAhB,WACgB,EAAhB,uBACgB,EAAhB,0BAEgB,EAAhB,kBACkB0C,QAASH,EAAIG,kBAQ3B9E,SA1QJ,WA2QMpD,KAAKmK,eAAgB,EACrBnK,KAAKoK,gBAAiB,GAGxB5H,OA/QJ,SA+QA,GACMiE,QAAQC,IAAIjE,GACZzC,KAAKqK,MAAQ5H,EACbzC,KAAKiD,MAAQ,UACbjD,KAAKY,KAAO,EACZZ,KAAKkD,YAAc,QACnBlD,KAAKgD,SAAU,MC1jBwV,ICQzW,G,UAAY,eACd,EACAlD,EACA8D,GACA,EACA,KACA,WACA,OAIa,e,kECnBf,W,kCCAA,W", "file": "js/chunk-59f625bf.294edbfb.js", "sourcesContent": ["var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"device\"},[_c('div',{staticClass:\"device-top\"},[_c('div',{staticClass:\"device-top-search\"},[_c('div',{staticClass:\"top-left\"},[_c('iot-button',{attrs:{\"text\":\"添加设备组\"},on:{\"search\":_vm.fn_open}})],1),_c('div',{staticClass:\"top-right\"},[_c('el-input',{attrs:{\"clearable\":\"\",\"placeholder\":\"输入组名称\"},on:{\"clear\":_vm.handleClear},nativeOn:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\")){ return null; }return _vm.fn_handle__query.apply(null, arguments)}},model:{value:(_vm.groupName),callback:function ($$v) {_vm.groupName=$$v},expression:\"groupName\"}},[_c('i',{staticClass:\"el-input__icon el-icon-search\",attrs:{\"slot\":\"suffix\"},on:{\"click\":_vm.fn_handle__query},slot:\"suffix\"})])],1)])]),_c('div',{staticClass:\"device-table\"},[_c('iot-table',{attrs:{\"columns\":_vm.columns,\"data\":_vm.tableData,\"loading\":_vm.loading},on:{\"selection-change\":_vm.fn_select_more_data,\"selection-del\":_vm.fn_del_more_data,\"del-callbackSure\":_vm.fn_del_sure},scopedSlots:_vm._u([{key:\"operation\",fn:function(scope){return [_c('div',{staticClass:\"flex table-edit\"},[_c('p',{staticClass:\"color2\",on:{\"click\":function($event){return _vm.fn_edit(scope.row)}}},[_vm._v(\"修改\")]),_c('p'),_c('p',{staticClass:\"color2\",on:{\"click\":function($event){return _vm.fn_del(scope.row.id)}}},[_vm._v(\"删除\")]),_c('p'),_c('p',{staticClass:\"color2\",on:{\"click\":function($event){return _vm.fn_check(scope.row.id)}}},[_vm._v(\"详情\")]),_c('p'),_c('p',{staticClass:\"color2\",attrs:{\"slot\":\"operation\"},on:{\"click\":function($event){return _vm.relationDevice(scope.row.id)}},slot:\"operation\"},[_vm._v(\" 关联设备 \")])])]}}])})],1),(_vm.tableData.length)?_c('div',{staticClass:\"device-bottom\"},[_c('iot-pagination',{attrs:{\"pagination\":_vm.pagination},on:{\"size-change\":_vm.handleSizeChange,\"current-change\":_vm.handleCurrentChange}})],1):_vm._e(),_c('iot-dialog',{attrs:{\"visible\":_vm.visible,\"title\":_vm.title,\"width\":_vm.dialogWidth},on:{\"update:visible\":function($event){_vm.visible=$event},\"callbackSure\":_vm.fn_sure,\"close\":_vm.fn_close},scopedSlots:_vm._u([{key:\"body\",fn:function(){return [(_vm.type == 1)?_c('iot-form',[_c('el-form',{ref:\"groupForm\",staticClass:\"groupForm\",attrs:{\"label-position\":'top',\"model\":_vm.groupForm,\"rules\":_vm.rules,\"label-width\":\"80px\"},on:{\"validate\":_vm.fn_validate}},[_c('el-form-item',{attrs:{\"label\":\"组名称\",\"prop\":\"groupName\"}},[_c('el-input',{model:{value:(_vm.groupForm.groupName),callback:function ($$v) {_vm.$set(_vm.groupForm, \"groupName\", $$v)},expression:\"groupForm.groupName\"}})],1),(_vm.groupNameTrue)?_c('div',{staticClass:\"el-form-tips\"},[_vm._v(\" 长度限制为4-30个字符; \")]):_vm._e(),_c('el-form-item',{attrs:{\"label\":\"配置时间\",\"prop\":\"configTime\"}},[_c('el-input',{model:{value:(_vm.groupForm.configTime),callback:function ($$v) {_vm.$set(_vm.groupForm, \"configTime\", $$v)},expression:\"groupForm.configTime\"}})],1),(_vm.configTimeTrue)?_c('div',{staticClass:\"el-form-tips\"},[_vm._v(\" 支持正整数1-100; \")]):_vm._e()],1)],1):_vm._e(),(_vm.type == 2)?_c('div',[_c('iot-form',{scopedSlots:_vm._u([{key:\"default\",fn:function(){return [_c('el-form',[_c('el-form-item',[_c('div',{staticClass:\"del-tips\"},[_vm._v(\" 删除之后，该组设备会进行解绑，请确认是否删除？ \")])])],1)]},proxy:true}],null,false,1221460110)})],1):_vm._e()]},proxy:true}])}),_c('relation',{ref:\"relation\",on:{\"close\":_vm.handleReset}})],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('iot-dialog',{attrs:{\"title\":\"组关联设备\",\"top\":\"10vh\",\"maxHeight\":\"auto\",\"visible\":_vm.visible,\"width\":_vm.width,\"appendBody\":true,\"footer\":false},on:{\"update:visible\":function($event){_vm.visible=$event},\"close\":_vm.handleClose},scopedSlots:_vm._u([{key:\"body\",fn:function(){return [_c('div',{staticClass:\"content\"},[_c('div',{staticClass:\"device flex\"},[_c('div',{staticClass:\"device-data not\"},[_c('h4',[_vm._v(\"待关联设备：\"+_vm._s(_vm.waitCount))]),_c('div',{staticClass:\"table\"},[_c('div',{staticClass:\"form-item\"},[_c('el-input',{attrs:{\"placeholder\":\"请输入请输入设备名称\",\"clearable\":\"\"},on:{\"clear\":function($event){return _vm.fn_handle__query(true)}},nativeOn:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\")){ return null; }return _vm.fn_handle__query(true)}},model:{value:(_vm.notSearchVal),callback:function ($$v) {_vm.notSearchVal=$$v},expression:\"notSearchVal\"}},[_c('i',{staticClass:\"el-input__icon el-icon-search\",attrs:{\"slot\":\"suffix\"},on:{\"click\":function($event){return _vm.fn_handle__query(true)}},slot:\"suffix\"})])],1),_c('div',{staticClass:\"device-table-content\"},[_c('iot-table',{attrs:{\"columns\":_vm.notColumns,\"data\":_vm.notSource,\"loading\":_vm.notLoading},on:{\"selection-change\":function (data) { return _vm.selectionChange(data, true); }},scopedSlots:_vm._u([{key:\"empty\",fn:function(){return [(_vm.isEmpty)?_c('div',{staticClass:\"empty\"},[_vm._v(\" 该产品暂无设备，请先去\"),_c('span',{on:{\"click\":_vm.routeDevice}},[_vm._v(\"添加设备\")])]):_vm._e()]},proxy:true}])}),_c('div',{staticClass:\"pagination flex\"},[_c('iot-pagination',{attrs:{\"pagination\":_vm.notPagination,\"layout\":\"total, prev, pager, next,  jumper\"},on:{\"current-change\":function (data) { return _vm.handleCurrentChange(data, true); }}})],1)],1)])]),_c('div',{staticClass:\"action flex\"},[_c('p',{staticClass:\"bind\",on:{\"click\":function($event){return _vm.submitBind(true)}}},[_c('span',[_vm._v(\"绑定\")]),_c('img',{attrs:{\"src\":require(\"@/assets/images/index/arrow-icon.png\"),\"alt\":\"\"}})]),_c('p',{staticClass:\"unbound\",on:{\"click\":function($event){return _vm.submitBind(false)}}},[_c('img',{attrs:{\"src\":require(\"@/assets/images/index/arrow-icon.png\"),\"alt\":\"\"}}),_c('span',[_vm._v(\"解绑\")])])]),_c('div',{staticClass:\"device-data already\"},[_c('h4',[_vm._v(\"已关联设备：\"+_vm._s(_vm.doneCount))]),_c('div',{staticClass:\"table\"},[_c('div',{staticClass:\"form-item\"},[_c('el-input',{attrs:{\"placeholder\":\"请输入设备名称\",\"clearable\":\"\"},on:{\"clear\":function($event){return _vm.fn_handle__query(false)}},nativeOn:{\"keyup\":function($event){if(!$event.type.indexOf('key')&&_vm._k($event.keyCode,\"enter\",13,$event.key,\"Enter\")){ return null; }return _vm.fn_handle__query(false)}},model:{value:(_vm.alreadySearchVal),callback:function ($$v) {_vm.alreadySearchVal=$$v},expression:\"alreadySearchVal\"}},[_c('i',{staticClass:\"el-input__icon el-icon-search\",attrs:{\"slot\":\"suffix\"},on:{\"click\":function($event){return _vm.fn_handle__query(false)}},slot:\"suffix\"})])],1),_c('div',{staticClass:\"device-table-content\"},[_c('iot-table',{attrs:{\"columns\":_vm.notColumns,\"data\":_vm.alreadySource,\"loading\":_vm.alreadyLoading},on:{\"selection-change\":function (data) { return _vm.selectionChange(data, false); }}}),_c('div',{staticClass:\"pagination flex\"},[_c('iot-pagination',{attrs:{\"pagination\":_vm.alreadyPagination,\"layout\":\"total, prev, pager, next,  jumper\"},on:{\"current-change\":function (data) { return _vm.handleCurrentChange(data, false); }}})],1)],1)])])])])]},proxy:true}])})],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div>\r\n    <iot-dialog title=\"组关联设备\"\r\n                top=\"10vh\"\r\n                maxHeight=\"auto\"\r\n                :visible.sync=\"visible\"\r\n                :width=\"width\"\r\n                :appendBody=\"true\"\r\n                :footer=\"false\"\r\n                @close=\"handleClose\">\r\n      <template #body>\r\n        <div class=\"content\">\r\n          <div class=\"device flex\">\r\n            <div class=\"device-data not\">\r\n              <h4>待关联设备：{{ waitCount }}</h4>\r\n              <div class=\"table\">\r\n                <div class=\"form-item\">\r\n                  <el-input v-model=\"notSearchVal\"\r\n                            placeholder=\"请输入请输入设备名称\"\r\n                            clearable\r\n                            @keyup.enter.native=\"fn_handle__query(true)\"\r\n                            @clear=\"fn_handle__query(true)\">\r\n                    <i slot=\"suffix\"\r\n                       class=\"el-input__icon el-icon-search\"\r\n                       @click=\"fn_handle__query(true)\"></i>\r\n                  </el-input>\r\n                </div>\r\n                <div class=\"device-table-content\">\r\n                  <iot-table :columns=\"notColumns\"\r\n                             :data=\"notSource\"\r\n                             :loading=\"notLoading\"\r\n                             @selection-change=\"(data) => selectionChange(data, true)\">\r\n                    <template #empty>\r\n                      <div class=\"empty\"\r\n                           v-if=\"isEmpty\">\r\n                        该产品暂无设备，请先去<span @click=\"routeDevice\">添加设备</span>\r\n                      </div>\r\n                    </template>\r\n\r\n                  </iot-table>\r\n                  <div class=\"pagination flex\">\r\n                    <iot-pagination :pagination=\"notPagination\"\r\n                                    layout=\"total, prev, pager, next,  jumper\"\r\n                                    @current-change=\"(data) => handleCurrentChange(data, true)\" />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"action flex\">\r\n              <p class=\"bind\"\r\n                 @click=\"submitBind(true)\">\r\n                <span>绑定</span>\r\n                <img src=\"~@/assets/images/index/arrow-icon.png\"\r\n                     alt=\"\" />\r\n              </p>\r\n              <p class=\"unbound\"\r\n                 @click=\"submitBind(false)\">\r\n                <img src=\"~@/assets/images/index/arrow-icon.png\"\r\n                     alt=\"\" />\r\n                <span>解绑</span>\r\n              </p>\r\n            </div>\r\n            <div class=\"device-data already\">\r\n              <h4>已关联设备：{{ doneCount }}</h4>\r\n              <div class=\"table\">\r\n                <div class=\"form-item\">\r\n                  <el-input v-model=\"alreadySearchVal\"\r\n                            placeholder=\"请输入设备名称\"\r\n                            clearable\r\n                            @keyup.enter.native=\"fn_handle__query(false)\"\r\n                            @clear=\"fn_handle__query(false)\">\r\n                    <i slot=\"suffix\"\r\n                       class=\"el-input__icon el-icon-search\"\r\n                       @click=\"fn_handle__query(false)\"></i>\r\n                  </el-input>\r\n                </div>\r\n                <div class=\"device-table-content\">\r\n                  <iot-table :columns=\"notColumns\"\r\n                             :data=\"alreadySource\"\r\n                             :loading=\"alreadyLoading\"\r\n                             @selection-change=\"(data) => selectionChange(data, false)\">\r\n\r\n                  </iot-table>\r\n                  <div class=\"pagination flex\">\r\n                    <iot-pagination :pagination=\"alreadyPagination\"\r\n                                    layout=\"total, prev, pager, next,  jumper\"\r\n                                    @current-change=\"(data) => handleCurrentChange(data, false)\" />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <!-- <div class=\"mask flex\">\r\n          <span>请先选择产品</span>\r\n        </div> -->\r\n\r\n        </div>\r\n      </template>\r\n    </iot-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport iotDialog from '@/components/iot-dialog'\r\nimport iotTable from '@/components/iot-table'\r\nimport iotPagination from '@/components/iot-pagination'\r\nimport { reg_seven, twenty_three, isJSON } from '@/util/util.js'\r\nimport {\r\n  postShieldGroupBind,\r\n  postShieldGroupUnBind,\r\n  getDeviceShieldListWaitLink,\r\n  getDeviceShieldListLink,\r\n} from '@/api/device.js'\r\nexport default {\r\n  data() {\r\n    return {\r\n      type: 1,\r\n      visible: false,\r\n      deviceVisible: false,\r\n      product: '',\r\n      c: {},\r\n      deviceForm: {\r\n        configInfo: '',\r\n        deviceSn: '',\r\n        vendorName: '',\r\n        deviceDesc: '',\r\n      },\r\n      options: [\r\n        {\r\n          value: '1',\r\n          label: '测试',\r\n        },\r\n      ],\r\n      notSearchVal: '',\r\n      notColumns: [\r\n        {\r\n          type: 'selection',\r\n        },\r\n        {\r\n          prop: 'productKey',\r\n          label: 'ProductKey',\r\n          width: 180,\r\n        },\r\n        {\r\n          prop: 'deviceName',\r\n          label: '设备名称',\r\n        },\r\n        {\r\n          prop: 'deviceSn',\r\n          label: '设备SN',\r\n        },\r\n        {\r\n          prop: 'deviceStatusName',\r\n          label: '在线状态',\r\n        },\r\n      ],\r\n      notSource: [],\r\n      notLoading: false,\r\n      notPagination: {\r\n        current: 1,\r\n        size: 7,\r\n        total: 0,\r\n      },\r\n      notSelectList: [],\r\n      alreadySearchVal: '',\r\n      alreadySource: [],\r\n      alreadyLoading: false,\r\n      alreadyPagination: {\r\n        current: 1,\r\n        size: 7,\r\n        total: 0,\r\n      },\r\n      alreadySelectList: [],\r\n\r\n      groupId: '', //连接器id\r\n      isEmpty: false, //左侧table empty 特殊处理\r\n      waitCount: 0,\r\n      doneCount: 0,\r\n      width: `${(1330 / 1920) * 100}vw`, // postcss 计算方法\r\n\r\n      configInfoTrue: true,\r\n      deviceSnTrue: true,\r\n      vendorNameTrue: true,\r\n      deviceDescTrue: true,\r\n    }\r\n  },\r\n  components: { iotDialog, iotTable, iotPagination },\r\n  props: {\r\n    hostProductKey: {\r\n      type: String,\r\n    },\r\n    hostDeviceName: {\r\n      type: String,\r\n    },\r\n  },\r\n  methods: {\r\n    fn_edit(row) {\r\n      console.log('row', row)\r\n      this.deviceForm = JSON.parse(JSON.stringify(row))\r\n      this.deviceForm.configInfo = JSON.stringify(\r\n        JSON.parse(this.deviceForm.configInfo),\r\n        null,\r\n        2\r\n      )\r\n      this.deviceVisible = true\r\n    },\r\n    checkDeviceSn(rule, value, callback) {\r\n      if (this.fn_notNull(value)) {\r\n        return callback(new Error('请输入设备SN'))\r\n      } else if (!twenty_three(value)) {\r\n        return callback(\r\n          new Error(\r\n            '支持中文、英文字母、数字、和特殊字符_-@()，长度限制 4~64个字符，中文算 2 个字符'\r\n          )\r\n        )\r\n      } else {\r\n        callback()\r\n      }\r\n    },\r\n    checkVendorName(rule, value, callback) {\r\n      if (!twenty_three(value, 201)) {\r\n        return callback(\r\n          new Error(\r\n            '支持中文、英文字母、数字、和特殊字符_-@()，长度限制 4~64个字符，中文算 2 个字符'\r\n          )\r\n        )\r\n      } else {\r\n        callback()\r\n      }\r\n    },\r\n    checkConfigInfoLength(rule, value, callback) {\r\n      value = JSON.stringify(value)\r\n\r\n      if (this.fn_notNull(value)) {\r\n        return callback(new Error('请输入自定义配置信息'))\r\n      } else if (!reg_seven(value, 2001)) {\r\n        return callback(new Error('最多不超过2000个字符'))\r\n      }\r\n      if (!isJSON(value)) {\r\n        return callback(new Error('请输入正确的JSON格式'))\r\n      } else {\r\n        callback()\r\n      }\r\n    },\r\n\r\n    checkDeviceSnLength(rule, value, callback) {\r\n      if (this.fn_notNull(value)) {\r\n        return callback(new Error('请输入设备SN'))\r\n      } else if (!reg_seven(value, 201)) {\r\n        return callback(new Error('最多不超过200个字符'))\r\n      } else {\r\n        callback()\r\n      }\r\n    },\r\n    fn_notNull(val) {\r\n      return val !== 0 && !val\r\n    },\r\n    checkLength(rule, value, callback) {\r\n      if (!reg_seven(value, 201)) {\r\n        return callback(new Error('最多不超过200个字符'))\r\n      } else {\r\n        callback()\r\n      }\r\n    },\r\n    open(groupId) {\r\n      this.groupId = groupId\r\n      this.visible = true\r\n\r\n      // 未绑定的\r\n      this.getProductKey(0, true, true)\r\n      // 已绑定的\r\n      this.getProductKey(1, true, true)\r\n    },\r\n    selectChange(data) {\r\n      this.notPagination.current = 1\r\n      this.alreadyPagination.current = 1\r\n      let object = this.options.filter((item) => item.id == data)[0]\r\n      // 未绑定的\r\n      this.productInfo = object\r\n      this.getProductKey(0, true, true)\r\n      // 已绑定的\r\n      this.getProductKey(1, true, true)\r\n    },\r\n    fn_handle__query(flag) {\r\n      if (flag) {\r\n        // 搜索待关联\r\n        this.notPagination.current = 1\r\n        this.getProductKey(0)\r\n      } else {\r\n        // 搜索已关联\r\n        this.alreadyPagination.current = 1\r\n        this.getProductKey(1)\r\n      }\r\n    },\r\n    getProductKey(flag, isTips = false, isTotal = false) {\r\n      let params = {}\r\n      if (flag) {\r\n        // 已绑定\r\n        this.alreadySearchVal = isTotal ? '' : this.alreadySearchVal\r\n        params = {\r\n          groupId: this.groupId,\r\n          current: this.alreadyPagination.current,\r\n          size: this.alreadyPagination.size,\r\n          deviceName: isTotal ? '' : this.alreadySearchVal,\r\n        }\r\n\r\n        getDeviceShieldListLink(params).then((res) => {\r\n          if (res.code == 200) {\r\n            let data = res.data\r\n            // 已绑定\r\n            this.alreadySource = data.records || []\r\n            this.doneCount = isTotal ? data.total : this.doneCount\r\n            this.alreadyPagination.total = data.total || 0\r\n          } else {\r\n            // 已绑定\r\n            this.alreadySource = []\r\n            this.doneCount = 0\r\n            this.alreadyPagination.total = 0\r\n            if (isTips) {\r\n              this.$newNotify.warning({\r\n                message: res.message,\r\n              })\r\n            }\r\n          }\r\n        })\r\n      } else {\r\n        // 未绑定\r\n        this.notSearchVal = isTotal ? '' : this.notSearchVal\r\n        params = {\r\n          current: this.notPagination.current,\r\n          size: this.notPagination.size,\r\n          deviceName: isTotal ? '' : this.notSearchVal,\r\n        }\r\n        getDeviceShieldListWaitLink(params).then((res) => {\r\n          if (res.code == 200) {\r\n            let data = res.data\r\n            // 未绑定\r\n            if (res.code == 4603) {\r\n              this.isEmpty = true\r\n            } else {\r\n              this.isEmpty = false\r\n            }\r\n            this.notSource = data.records || []\r\n            this.waitCount = isTotal ? data.total : this.waitCount\r\n            this.notPagination.total = data.total || 0\r\n          } else {\r\n            this.notSource = []\r\n            this.waitCount = 0\r\n            this.notPagination.total = 0\r\n            if (isTips) {\r\n              this.$newNotify.warning({\r\n                message: res.message,\r\n              })\r\n            }\r\n          }\r\n        })\r\n      }\r\n    },\r\n    handleClear() {},\r\n    selectionChange(data, flag) {\r\n      if (flag) {\r\n        // 未关联数组\r\n        this.notSelectList = data.map((item) => item.id)\r\n      } else {\r\n        // 已关联数组\r\n        this.alreadySelectList = data.map((item) => item.id)\r\n      }\r\n    },\r\n    handleCurrentChange(data, flag) {\r\n      if (flag) {\r\n        // 未绑定\r\n        this.notPagination.current = data\r\n        this.getProductKey(0)\r\n      } else {\r\n        // 已绑定\r\n        this.alreadyPagination.current = data\r\n        this.getProductKey(1)\r\n      }\r\n    },\r\n    submitBind(flag) {\r\n      if (flag) {\r\n        //绑定\r\n        if (this.notSelectList.length == 0) {\r\n          this.$newNotify.warning({\r\n            message: '请选择未关联设备',\r\n          })\r\n          return\r\n        }\r\n        postShieldGroupBind({\r\n          groupId: this.groupId,\r\n          deviceList: this.notSelectList,\r\n        }).then((res) => {\r\n          if (res.code == 200) {\r\n            this.$newNotify.success({\r\n              message: res.message,\r\n            })\r\n            this.notPagination.current = 1\r\n            this.alreadyPagination.current = 1\r\n            // 未绑定的\r\n            this.getProductKey(0, false, true)\r\n            // 已绑定的\r\n            this.getProductKey(1, false, true)\r\n          } else {\r\n            this.$newNotify.error({\r\n              message: res.message,\r\n            })\r\n          }\r\n        })\r\n      } else {\r\n        // 解绑\r\n        if (this.alreadySelectList.length == 0) {\r\n          this.$newNotify.warning({\r\n            message: '请选择已关联设备',\r\n          })\r\n          return\r\n        }\r\n        postShieldGroupUnBind({\r\n          groupId: this.groupId,\r\n          deviceList: this.alreadySelectList,\r\n        }).then((res) => {\r\n          if (res.code == 200) {\r\n            this.$newNotify.success({\r\n              message: res.message,\r\n            })\r\n            this.notPagination.current = 1\r\n            this.alreadyPagination.current = 1\r\n            // 未绑定的\r\n            this.getProductKey(0, false, true)\r\n            // 已绑定的\r\n            this.getProductKey(1, false, true)\r\n          } else {\r\n            this.$newNotify.error({\r\n              message: res.message,\r\n            })\r\n          }\r\n        })\r\n      }\r\n    },\r\n    routeDevice() {\r\n      this.$router.replace({\r\n        path: '/device',\r\n      })\r\n    },\r\n    handleClose() {\r\n      this.product = ''\r\n      this.productInfo = {}\r\n      this.notSearchVal = ''\r\n      this.alreadySearchVal = ''\r\n      this.notSource = []\r\n      this.alreadySource = []\r\n      this.notPagination.current = 1\r\n      this.alreadyPagination.current = 1\r\n      this.notPagination.total = 0\r\n      this.alreadyPagination.total = 0\r\n      this.$emit('close')\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  padding-bottom: 12px;\r\n  position: relative;\r\n  h5 {\r\n    color: #666666;\r\n    font-size: 14px;\r\n    line-height: 16px;\r\n    font-weight: normal;\r\n    padding: 8px 0;\r\n    span {\r\n      color: #ff0000;\r\n    }\r\n  }\r\n  .item {\r\n    padding-bottom: 26px;\r\n  }\r\n\r\n  .device {\r\n    align-items: center;\r\n\r\n    .device-data {\r\n      width: 586px;\r\n      h4 {\r\n        padding-left: 14px;\r\n        position: relative;\r\n        color: #262626;\r\n        font-size: 18px;\r\n        font-weight: 500;\r\n      }\r\n      h4::before {\r\n        content: '';\r\n        width: 4px;\r\n        height: 14px;\r\n        background: #1890ff;\r\n        position: absolute;\r\n        left: 0;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n      }\r\n      .table {\r\n        margin-top: 18px;\r\n        height: 520px;\r\n        background: #ffffff;\r\n        border: 1px solid #ececec;\r\n        padding: 18px;\r\n        .form-item {\r\n          padding-bottom: 18px;\r\n        }\r\n      }\r\n      .pagination {\r\n        padding-top: 14px;\r\n        justify-content: flex-end;\r\n      }\r\n    }\r\n    .action {\r\n      flex-direction: column;\r\n      align-items: center;\r\n      justify-content: center;\r\n      padding: 0 12px;\r\n      p {\r\n        width: 72px;\r\n        height: 32px;\r\n        text-align: center;\r\n        border-radius: 3px;\r\n        cursor: pointer;\r\n        transition: all 0.3s;\r\n        span {\r\n          color: #515151;\r\n          font-size: 14px;\r\n          line-height: 32px;\r\n        }\r\n        img {\r\n          width: 12px;\r\n          height: 9px;\r\n        }\r\n      }\r\n      .bind {\r\n        background: linear-gradient(\r\n          270deg,\r\n          #eeeeee 0%,\r\n          #dadddf 75%,\r\n          #c9c9c9 100%\r\n        );\r\n        background-size: 200%;\r\n        background-position: 100% 0;\r\n        margin-bottom: 14px;\r\n        span {\r\n          padding-right: 8px;\r\n        }\r\n      }\r\n      .bind:hover {\r\n        // background: linear-gradient(270deg, #e7e7e7 0%, #c9c9c9 100%);\r\n        background-position: 0;\r\n      }\r\n      .unbound {\r\n        background: linear-gradient(\r\n          90deg,\r\n          #eeeeee 0%,\r\n          #dadddf 75%,\r\n          #c9c9c9 100%\r\n        );\r\n        background-size: 200%;\r\n\r\n        span {\r\n          padding-left: 8px;\r\n        }\r\n        img {\r\n          transform: rotate(180deg);\r\n        }\r\n      }\r\n      .unbound:hover {\r\n        // background: linear-gradient(90deg, #e7e7e7 0%, #c9c9c9 100%);\r\n        background-position: 100% 0;\r\n      }\r\n    }\r\n  }\r\n  .mask {\r\n    position: absolute;\r\n    width: 100%;\r\n    height: calc(100% - 92px);\r\n    position: absolute;\r\n    top: 92px;\r\n    left: 0;\r\n    background: rgba(0, 0, 0, 0.7);\r\n    align-items: center;\r\n    justify-content: center;\r\n    z-index: 99;\r\n    span {\r\n      color: #ffffff;\r\n      text-align: center;\r\n      font-family: H_Medium;\r\n      font-size: 18px;\r\n    }\r\n  }\r\n  .item {\r\n    /deep/ {\r\n      .el-input__inner {\r\n        width: 586px;\r\n        border-radius: 0;\r\n      }\r\n    }\r\n  }\r\n\r\n  .device {\r\n    /deep/ .el-select {\r\n      .el-input__inner::placeholder {\r\n        color: #515151;\r\n      }\r\n    }\r\n    /deep/ {\r\n      .el-table__header {\r\n        tr {\r\n          height: 42px !important;\r\n        }\r\n        .el-table__cell {\r\n          padding: 0;\r\n        }\r\n      }\r\n      .el-table__row {\r\n        height: 42px !important;\r\n        .el-table__cell {\r\n          padding: 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n.deviceForm {\r\n  /deep/ .el-form-item {\r\n    margin-bottom: 24px;\r\n  }\r\n  .el-form-tips {\r\n    // margin-top: -17px;\r\n    margin-top: -22px;\r\n    margin-bottom: 6px;\r\n  }\r\n}\r\n/deep/ .empty {\r\n  padding-top: 68px;\r\n  color: #888888;\r\n  font-size: 14px;\r\n  span {\r\n    color: #018aff;\r\n    cursor: pointer;\r\n  }\r\n}\r\n.device-table-content {\r\n  .iot-table {\r\n    height: 378px;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../../node_modules/babel-loader/lib/index.js!../../../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../../../node_modules/thread-loader/dist/cjs.js!../../../../../../node_modules/babel-loader/lib/index.js!../../../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=7a860f5c&scoped=true&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=7a860f5c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7a860f5c\",\n  null\n  \n)\n\nexport default component.exports", "<!--\r\n * @Description: 1.0\r\n * @Version: 1.0\r\n * @Autor: hs\r\n * @Date: 2021-11-08 09:51:45\r\n * @LastEditors: lb <EMAIL>\r\n * @LastEditTime: 2023-05-20 17:12:07\r\n-->\r\n<template>\r\n  <div class=\"device\">\r\n    <div class=\"device-top\">\r\n      <div class=\"device-top-search\">\r\n        <div class=\"top-left\">\r\n          <iot-button text=\"添加设备组\"\r\n                      @search=\"fn_open\"></iot-button>\r\n        </div>\r\n        <div class=\"top-right\">\r\n          <!-- 搜索栏 -->\r\n          <el-input v-model=\"groupName\"\r\n                    @keyup.enter.native=\"fn_handle__query\"\r\n                    clearable\r\n                    placeholder=\"输入组名称\"\r\n                    @clear=\"handleClear\">\r\n            <i slot=\"suffix\"\r\n               class=\"el-input__icon el-icon-search\"\r\n               @click=\"fn_handle__query\"></i>\r\n          </el-input>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"device-table\">\r\n      <!-- 表格 -->\r\n      <iot-table :columns=\"columns\"\r\n                 :data=\"tableData\"\r\n                 :loading=\"loading\"\r\n                 @selection-change=\"fn_select_more_data\"\r\n                 @selection-del=\"fn_del_more_data\"\r\n                 @del-callbackSure=\"fn_del_sure\">\r\n        <template slot=\"operation\"\r\n                  slot-scope=\"scope\">\r\n          <div class=\"flex table-edit\">\r\n            <p @click=\"fn_edit(scope.row)\"\r\n               class=\"color2\">修改</p>\r\n            <p></p>\r\n\r\n            <p @click=\"fn_del(scope.row.id)\"\r\n               class=\"color2\">删除</p>\r\n            <p></p>\r\n            <p @click=\"fn_check(scope.row.id)\"\r\n               class=\"color2\">详情</p>\r\n            <p></p>\r\n            <p slot=\"operation\"\r\n               @click=\"relationDevice(scope.row.id)\"\r\n               class=\"color2\">\r\n              关联设备\r\n            </p>\r\n\r\n          </div>\r\n        </template>\r\n      </iot-table>\r\n    </div>\r\n\r\n    <div class=\"device-bottom\"\r\n         v-if=\"tableData.length\">\r\n      <!-- 分页 -->\r\n      <iot-pagination :pagination=\"pagination\"\r\n                      @size-change=\"handleSizeChange\"\r\n                      @current-change=\"handleCurrentChange\" />\r\n    </div>\r\n\r\n    <iot-dialog :visible.sync=\"visible\"\r\n                :title=\"title\"\r\n                :width=\"dialogWidth\"\r\n                @callbackSure=\"fn_sure\"\r\n                @close=\"fn_close\">\r\n      <template #body>\r\n        <!-- 新增和修改 -->\r\n        <iot-form v-if=\"type == 1\">\r\n          <el-form class=\"groupForm\"\r\n                   ref=\"groupForm\"\r\n                   :label-position=\"'top'\"\r\n                   :model=\"groupForm\"\r\n                   :rules=\"rules\"\r\n                   @validate=\"fn_validate\"\r\n                   label-width=\"80px\">\r\n\r\n            <el-form-item label=\"组名称\"\r\n                          prop=\"groupName\">\r\n              <el-input v-model=\"groupForm.groupName\"></el-input>\r\n            </el-form-item>\r\n            <div class=\"el-form-tips\"\r\n                 v-if=\"groupNameTrue\">\r\n              长度限制为4-30个字符;\r\n            </div>\r\n            <el-form-item label=\"配置时间\"\r\n                          prop=\"configTime\">\r\n              <el-input v-model=\"groupForm.configTime\"></el-input>\r\n            </el-form-item>\r\n            <div class=\"el-form-tips\"\r\n                 v-if=\"configTimeTrue\">\r\n              支持正整数1-100;\r\n            </div>\r\n          </el-form>\r\n\r\n        </iot-form>\r\n        <div v-if=\"type == 2\">\r\n          <iot-form>\r\n            <template #default>\r\n              <el-form>\r\n                <el-form-item>\r\n                  <div class=\"del-tips\">\r\n                    删除之后，该组设备会进行解绑，请确认是否删除？\r\n                  </div>\r\n                </el-form-item>\r\n              </el-form>\r\n            </template>\r\n          </iot-form>\r\n        </div>\r\n      </template>\r\n    </iot-dialog>\r\n\r\n    <relation ref=\"relation\"\r\n              @close=\"handleReset\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport FormSearch from '@/components/form-search'\r\nimport IotPagination from '@/components/iot-pagination'\r\nimport IotButton from '@/components/iot-button'\r\nimport IotForm from '@/components/iot-form'\r\n\r\nimport IotTable from '@/components/iot-table'\r\nimport IotDialog from '@/components/iot-dialog'\r\nimport relation from './components/relation'\r\nimport {\r\n  getDeviceStatusNum,\r\n  getShieldGroupList,\r\n  postShieldGroupAdd,\r\n  postShieldGroupUpdate,\r\n  getUpLinkList,\r\n  getDownLinkList,\r\n  postShieldGroupCheck,\r\n  postShieldGroupDelete,\r\n} from '@/api/device'\r\nimport {\r\n  reg_two,\r\n  reg_seven,\r\n  twenty_four,\r\n  twenty_one,\r\n  twenty_two,\r\n  twenty_three,\r\n  isJSON,\r\n} from '@/util/util.js'\r\nimport { json } from 'body-parser'\r\n\r\nexport default {\r\n  name: 'Device',\r\n  components: {\r\n    IotPagination,\r\n    IotTable,\r\n    relation,\r\n    IotDialog,\r\n    IotButton,\r\n    IotForm,\r\n  },\r\n  data() {\r\n    return {\r\n      groupName: '',\r\n      columns: [\r\n        { label: '组ID', prop: 'id' },\r\n        {\r\n          label: '组名称',\r\n          prop: 'groupName',\r\n        },\r\n        {\r\n          label: '配置时间',\r\n          prop: 'configTime',\r\n        },\r\n        {\r\n          label: '创建时间',\r\n          prop: 'createTime',\r\n        },\r\n        {\r\n          label: '更新时间',\r\n          prop: 'updateTime',\r\n        },\r\n        {\r\n          label: '操作',\r\n          prop: 'operation',\r\n          slotName: 'operation',\r\n          width: 240,\r\n        },\r\n      ],\r\n      enableList: [\r\n        {\r\n          value: 0,\r\n          label: '未启用',\r\n        },\r\n        {\r\n          value: 1,\r\n          label: '已启用',\r\n        },\r\n      ],\r\n      tableData: [],\r\n      pagination: {\r\n        current: 1, // 当前页\r\n        total: 0, // 记录条数\r\n        pages: 0, // 总页数\r\n        sizes: [10, 20, 50, 100], // 每一页的数量  [10, 20, 50, 100]\r\n        size: 10,\r\n      },\r\n      // 加载效果开关\r\n      loading: false,\r\n      dialogWidth: '792px',\r\n      type: 1, //1 查看 2 删除\r\n      visible: false,\r\n      inputHolder: '请输入搜索关键词',\r\n      selectHolder: '请选择设备名称',\r\n      searchValue: {\r\n        productId: '',\r\n      },\r\n      // 产品列表\r\n      productOptions: [],\r\n      productOptionsCopy: [],\r\n      // 设备列表\r\n      deviceOptions: [\r\n        {\r\n          id: '2',\r\n          name: '设备厂商',\r\n        },\r\n      ],\r\n      statusCount: {\r\n        totalNum: 0,\r\n        activeNum: 0,\r\n        onlineNum: 0,\r\n      },\r\n      // 表单数据\r\n      groupForm: {\r\n        groupName: '', // 连接器名称\r\n        configTime: '', // 应用\r\n      },\r\n\r\n      groupNameTrue: true,\r\n      configTimeTrue: true,\r\n\r\n      rules: {\r\n        groupName: [\r\n          {\r\n            required: true,\r\n            // message: '支持中文、英文、数字、下划线的组合，最多不超过32个字符',\r\n            trigger: 'blur',\r\n            validator: this.checkGroupName,\r\n          },\r\n        ],\r\n        configTime: [\r\n          {\r\n            required: true,\r\n            // message: '支持中文、英文、数字、下划线的组合，最多不超过120个字符',\r\n            trigger: 'blur',\r\n            validator: this.checkConfigTime,\r\n          },\r\n        ],\r\n      },\r\n      title: '',\r\n      delId: '',\r\n      // 多选删除\r\n      delIds: [],\r\n    }\r\n  },\r\n  created() {},\r\n  watch: {\r\n    visible(val) {\r\n      if (!val && this.type == 1) {\r\n        this.groupForm = {}\r\n        this.$refs['groupForm'] && this.$refs['groupForm'].resetFields()\r\n      }\r\n    },\r\n  },\r\n  // keepalive 生命周期      //组件激活时触发\r\n  mounted() {\r\n    if (this.$route.query.id) {\r\n      this.searchValue.productId = this.$route.query.id\r\n    }\r\n    let data = {\r\n      ...this.searchValue,\r\n      current: this.pagination.current,\r\n      size: this.pagination.size,\r\n    }\r\n\r\n    this.fn_get_table_data(data)\r\n  },\r\n  methods: {\r\n    handleReset() {\r\n      this.pagination.current = 1\r\n      this.fn_get_table_data()\r\n    },\r\n    relationDevice(id) {\r\n      this.$refs.relation.open(id)\r\n    },\r\n    fn_sub10(str) {\r\n      if (str) return str.length > 20 ? `${str.substr(0, 20)}...` : str\r\n    },\r\n    fn_notNull(val) {\r\n      return val !== 0 && !val\r\n    },\r\n    // 输入框icon查询\r\n    fn_handle__query() {\r\n      let params = {\r\n        groupName: this.groupName,\r\n        current: 1,\r\n        size: this.pagination.size,\r\n      }\r\n      this.fn_get_table_data(params)\r\n    },\r\n    handleClear() {\r\n      this.fn_get_table_data()\r\n    },\r\n    calcul_long_text(val = '') {\r\n      return val.replace(/[\\u0391-\\uFFE5\\u0800-\\u4e00 ]/g, 'aa').length\r\n    },\r\n    fn_select() {\r\n      let data = { ...this.searchValue }\r\n      this.fn_get_table_data(data)\r\n    },\r\n    // 名称校验\r\n    checkGroupName(rule, value, callback) {\r\n      let flag = false\r\n      postShieldGroupCheck({\r\n        groupName: value,\r\n        id: this.groupForm.id,\r\n      }).then((res) => {\r\n        if (res.code == 200) {\r\n          flag = res.data\r\n        }\r\n\r\n        if (this.fn_notNull(value)) {\r\n          return callback(new Error('请输入连接器名称'))\r\n        } else if (value.length > 30 || value.length < 4) {\r\n          return callback(new Error('长度限制为4-30个字符;'))\r\n        } else if (flag) {\r\n          return callback(new Error('接入连接器名称不充许重复'))\r\n        } else {\r\n          callback()\r\n        }\r\n      })\r\n    },\r\n    // 名称校验\r\n    checkConfigTime(rule, value, callback) {\r\n      if (this.fn_notNull(value)) {\r\n        return callback(new Error('请输入设备厂商'))\r\n      } else if (Number(value) > 100 || Number(value) < 1) {\r\n        return callback(new Error('支持正整数1-100'))\r\n      } else {\r\n        callback()\r\n      }\r\n    },\r\n\r\n    fn_get_device_status_count() {\r\n      getDeviceStatusNum().then((res) => {\r\n        this.statusCount = res.data\r\n      })\r\n    },\r\n    // 设备列表-表格数据接口\r\n    fn_get_table_data(params = {}) {\r\n      let others = { ...params }\r\n      if (!params.size) {\r\n        others.size = 10\r\n        others.current = 1\r\n      }\r\n      getShieldGroupList(others)\r\n        .then((res) => {\r\n          if (res.code == 200) {\r\n            setTimeout(() => {\r\n              this.loading = false\r\n            }, 300)\r\n            this.tableData = res.data.records\r\n            this.pagination.total = res.data.total\r\n            this.pagination.current = res.data.current\r\n            this.pagination.pages = res.data.pages\r\n            this.pagination.size = res.data.size\r\n          } else {\r\n            this.$newNotify.error({\r\n              message: res.message,\r\n            })\r\n          }\r\n        })\r\n        .finally(() => {\r\n          setTimeout(() => {\r\n            this.loading = false\r\n          }, 300)\r\n        })\r\n    },\r\n    // 表单验证触发\r\n    fn_validate(name, value) {\r\n      if (name === 'groupName') {\r\n        this.groupNameTrue = value\r\n      }\r\n      if (name === 'configTime') {\r\n        this.configTimeTrue = value\r\n      }\r\n    },\r\n    // 多选数据\r\n    fn_select_more_data(data) {\r\n      // console.log(data)\r\n      this.delIds = data.map((item) => {\r\n        return item.id\r\n      })\r\n    },\r\n    // 多选删除按钮\r\n    fn_del_more_data() {\r\n      if (this.delIds.length !== 0) {\r\n        this.columns[0].visible = true\r\n      } else {\r\n        return\r\n      }\r\n    },\r\n    // 多选删除确定按钮\r\n    fn_del_sure() {\r\n      let data = {\r\n        ids: this.delIds.join(','),\r\n      }\r\n      this.fn_del_table_data(data)\r\n    },\r\n    // 打开dialog\r\n    fn_open() {\r\n      this.title = '添加设备组'\r\n      this.type = 1\r\n      this.dialogWidth = '792px'\r\n      this.visible = true\r\n    },\r\n    fn_edit(row) {\r\n      this.groupForm = JSON.parse(JSON.stringify(row))\r\n      this.title = '编辑设备组'\r\n      this.type = 1\r\n      this.dialogWidth = '792px'\r\n      this.visible = true\r\n    },\r\n\r\n    fn_format_select(list, id = '') {\r\n      return list.map((item) => {\r\n        return {\r\n          id: item.id,\r\n          converterName: item.converterName,\r\n          bindStatusName: item.bindStatusName,\r\n          disabled: item.bindStatusName == '已绑定' && item.id != id,\r\n        }\r\n      })\r\n    },\r\n    // 搜索\r\n    fn_search_table_data(params) {\r\n      console.log(params)\r\n      if (params.id === '1') {\r\n        this.searchValue.aliasName = params.value\r\n      } else {\r\n        this.searchValue.deviceName = params.value\r\n      }\r\n      let data = { ...this.searchValue }\r\n      data.size = this.pagination.size\r\n      this.fn_get_table_data(data)\r\n    },\r\n    // 当前页总条数\r\n    handleSizeChange(val) {\r\n      // console.log(`每页 ${val} 条`)\r\n      this.pagination.size = val\r\n      let params = {\r\n        size: this.pagination.size,\r\n        current: 1,\r\n      }\r\n      this.fn_get_table_data(params)\r\n    },\r\n    // 当前页\r\n    handleCurrentChange(val) {\r\n      // console.log(`当前页: ${val}`)\r\n      this.pagination.current = val\r\n      let params = {\r\n        ...this.searchValue,\r\n        current: this.pagination.current,\r\n        size: this.pagination.size,\r\n      }\r\n      this.fn_get_table_data(params)\r\n    },\r\n    // 清除输入搜索\r\n    fn_clear_search_info() {\r\n      this.searchValue.aliasName = ''\r\n      this.searchValue.deviceName = ''\r\n      let data = { ...this.searchValue }\r\n      this.fn_get_table_data(data)\r\n    },\r\n    // 查看详情的跳转\r\n    fn_check(data, num) {\r\n      this.$router.push({\r\n        path: '/deviceGroupDetail',\r\n        query: {\r\n          id: data,\r\n          num: num,\r\n        },\r\n      })\r\n    },\r\n    // 弹窗确认按钮\r\n    fn_sure() {\r\n      // 删除确认\r\n      if (this.type === 2) {\r\n        let data = {\r\n          id: this.delId,\r\n        }\r\n        postShieldGroupDelete(data).then((res) => {\r\n          if (res.code == 200) {\r\n            this.$newNotify.success({\r\n              message: res.message,\r\n            })\r\n            this.pagination.current = 1\r\n            // this.fn_get_device_status_count()\r\n            this.fn_get_table_data({\r\n              size: this.pagination.size,\r\n              current: 1,\r\n            })\r\n            this.visible = false\r\n          } else {\r\n            this.$newNotify.error({\r\n              message: res.message,\r\n            })\r\n          }\r\n        })\r\n      }\r\n      // 新增确认\r\n      else if (this.type === 1) {\r\n        this.$refs['groupForm'].validate((valid) => {\r\n          if (valid) {\r\n            let postUrl = this.groupForm.id\r\n              ? postShieldGroupUpdate\r\n              : postShieldGroupAdd\r\n\r\n            postUrl(this.groupForm).then((res) => {\r\n              if (res.code == 200) {\r\n                this.$newNotify.success({\r\n                  message: res.message,\r\n                })\r\n                this.pagination.current = 1\r\n                this.searchValue.productId = ''\r\n                // this.fn_get_device_status_count()\r\n                this.fn_get_table_data({\r\n                  size: this.pagination.size,\r\n                  current: 1,\r\n                })\r\n                this.visible = false\r\n                this.fn_get_upLink_select()\r\n                this.fn_get_downLink_select()\r\n              } else {\r\n                this.$newNotify.error({\r\n                  message: res.message,\r\n                })\r\n              }\r\n            })\r\n          }\r\n        })\r\n      }\r\n    },\r\n    fn_close() {\r\n      this.groupNameTrue = true\r\n      this.configTimeTrue = true\r\n    },\r\n    // 行删除\r\n    fn_del(id) {\r\n      console.log(id)\r\n      this.delId = id\r\n      this.title = '确定删除该组？'\r\n      this.type = 2\r\n      this.dialogWidth = '550px'\r\n      this.visible = true\r\n    },\r\n  },\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.device {\r\n  padding-bottom: 20px;\r\n  .device-top {\r\n    font-family: HarmonyOS Sans SC;\r\n    .device-top-count {\r\n      margin-top: 18px;\r\n      .el-select {\r\n        margin-right: 48px;\r\n      }\r\n      /deep/ .el-input__inner {\r\n        border-radius: 0;\r\n      }\r\n      .point {\r\n        margin: 0 10px;\r\n      }\r\n      p {\r\n        display: inline-block;\r\n        font-size: 14px;\r\n        line-height: 16px;\r\n        letter-spacing: 1px;\r\n        font-weight: normal;\r\n      }\r\n      span {\r\n        font-size: 18px;\r\n        line-height: 20px;\r\n        margin: 0 6px;\r\n        font-weight: 500;\r\n      }\r\n    }\r\n    .device-top-search {\r\n      margin: 18px 0 18px 0;\r\n      width: 100%;\r\n      display: flex;\r\n      justify-content: space-between;\r\n    }\r\n  }\r\n  .device-table {\r\n    .table-edit {\r\n      display: flex;\r\n      align-items: center;\r\n      p {\r\n        flex-shrink: 0;\r\n        cursor: pointer;\r\n      }\r\n      p:nth-child(2) {\r\n        margin: 0px 12px;\r\n        width: 1px;\r\n        height: 13px;\r\n        border: 1px solid #ededed;\r\n      }\r\n      p:nth-child(4) {\r\n        margin: 0px 12px;\r\n        width: 1px;\r\n        height: 13px;\r\n        border: 1px solid #ededed;\r\n      }\r\n      p:nth-child(6) {\r\n        margin: 0px 12px;\r\n        width: 1px;\r\n        height: 13px;\r\n        border: 1px solid #ededed;\r\n      }\r\n    }\r\n    .table-status {\r\n      .status {\r\n        .red {\r\n          background: #ff4d4f;\r\n          width: 8px;\r\n          height: 8px;\r\n          border-radius: 4px;\r\n          margin-top: 8px;\r\n          margin-right: 6px;\r\n        }\r\n        .green {\r\n          background: #00c250;\r\n          width: 8px;\r\n          height: 8px;\r\n          border-radius: 4px;\r\n          margin-top: 8px;\r\n          margin-right: 6px;\r\n        }\r\n        .yellow {\r\n          background: #e6a23c;\r\n          width: 8px;\r\n          height: 8px;\r\n          border-radius: 4px;\r\n          margin-top: 8px;\r\n          margin-right: 6px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .device-bottom {\r\n    text-align: right;\r\n    margin-top: 14px;\r\n  }\r\n  .del-tips {\r\n    width: 420px;\r\n  }\r\n  .groupForm {\r\n    /deep/ .el-form-item {\r\n      margin-bottom: 24px;\r\n    }\r\n    .el-form-tips {\r\n      // margin-top: -17px;\r\n      margin-top: -22px;\r\n      margin-bottom: 6px;\r\n    }\r\n  }\r\n  .specialDesc {\r\n    padding: 11px 0 11px 14px;\r\n    background-color: rgba(1, 138, 255, 0.08);\r\n    margin-bottom: 18px;\r\n    span {\r\n      font-size: 12px;\r\n      line-height: 14px;\r\n    }\r\n    img {\r\n      display: inline-block;\r\n      width: 14px;\r\n      height: 14px;\r\n      line-height: 14px;\r\n      margin-right: 10px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=772d27ae&scoped=true&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=772d27ae&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"772d27ae\",\n  null\n  \n)\n\nexport default component.exports", "export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=772d27ae&lang=scss&scoped=true&\"", "export * from \"-!../../../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=7a860f5c&lang=scss&scoped=true&\""], "sourceRoot": ""}