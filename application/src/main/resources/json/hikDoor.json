{"profile": {"productKey": "Fsod5zVVEfCLJXTJ", "version": "1.0"}, "properties": [{"id": "workMode", "name": "脱机模式开关", "desc": "", "mode": "rw", "data": {"type": "bool", "rules": {"0": "关闭脱机模式", "1": "开启脱机模式"}}}], "events": [{"id": "manualDoor", "name": "控制门禁状态", "desc": "", "action": "thing.service.manualDoor.call", "type": "async", "out": [{"name": "控制结果", "id": "state", "data": {"type": "bool", "rules": {"0": "控制失败", "1": "控制成功"}}}], "in": [{"name": "控制命令", "id": "command", "data": {"type": "enum", "rules": {"0": "关闭", "1": "开启", "2": "常开", "3": "常关"}}}]}], "services": []}