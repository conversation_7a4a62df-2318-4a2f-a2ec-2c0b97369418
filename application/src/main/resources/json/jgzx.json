[{"id": "1", "name": "hik-camera", "type": "ISAPI_OLD_PARKING", "routingKey": "v2parking", "enabled": true, "allowCreateDevice": false, "configuration": {"metadata": {}, "enableSecurity": false, "headersFilter": {}}, "upLink": {"id": "1", "name": "old parking up link converter", "type": "INTERNAL", "scriptType": "UP_LINK", "scriptCode": "old-parking-up-link"}, "downLink": {"id": "2", "name": "old parking down link converter", "type": "INTERNAL", "scriptType": "DOWN_LINK", "scriptCode": "old-parking-down-link"}, "devices": [{"connectorId": "1", "productKey": "kWJUPZJ3vb9N7z0t", "deviceName": "cD9f7tU4M6sHJ413", "integrateNo": "*************", "configuration": {"ipAddress": "*************", "port": "80", "username": "admin", "password": "lst123456", "channel": "1", "laneNo": "1", "ledIpAddress": "*************", "ledPort": "10000", "waitTime": "600"}, "endPointClassName": "com.yuchen.iot.gateway.application.integration.endpoint.special.parking.ParkingEndpointHandler", "describe": "加工中心地面入口"}, {"connectorId": "1", "productKey": "kWJUPZJ3vb9N7z0t", "deviceName": "nk7H8sDFmo2c83t1", "integrateNo": "*************", "configuration": {"ipAddress": "*************", "port": "80", "username": "admin", "password": "lst123456", "channel": "1", "laneNo": "1", "ledIpAddress": "*************", "ledPort": "10000", "waitTime": "600"}, "endPointClassName": "com.yuchen.iot.gateway.application.integration.endpoint.special.parking.ParkingEndpointHandler", "describe": "加工中心地面出口"}, {"connectorId": "1", "productKey": "kWJUPZJ3vb9N7z0t", "deviceName": "Mk6o8i0br055Z7OA", "integrateNo": "*************", "configuration": {"ipAddress": "*************", "port": "80", "username": "admin", "password": "lst123456", "channel": "1", "laneNo": "1", "ledIpAddress": "*************", "ledPort": "10000", "waitTime": "600"}, "endPointClassName": "com.yuchen.iot.gateway.application.integration.endpoint.special.parking.ParkingEndpointHandler", "describe": "加工中心地库入口1"}, {"connectorId": "1", "productKey": "kWJUPZJ3vb9N7z0t", "deviceName": "S3w2m7BpsvDLR192", "integrateNo": "*************", "configuration": {"ipAddress": "*************", "port": "80", "username": "admin", "password": "lst123456", "channel": "1", "laneNo": "1", "ledIpAddress": "*************", "ledPort": "10000", "waitTime": "600"}, "endPointClassName": "com.yuchen.iot.gateway.application.integration.endpoint.special.parking.ParkingEndpointHandler", "describe": "加工中心地库出口1"}, {"connectorId": "1", "productKey": "kWJUPZJ3vb9N7z0t", "deviceName": "R06or0S7R0e4ki0r", "integrateNo": "*************", "configuration": {"ipAddress": "*************", "port": "80", "username": "admin", "password": "lst123456", "channel": "1", "laneNo": "1", "ledIpAddress": "*************", "ledPort": "10000", "waitTime": "600"}, "endPointClassName": "com.yuchen.iot.gateway.application.integration.endpoint.special.parking.ParkingEndpointHandler", "describe": "加工中心地库入口2"}, {"connectorId": "1", "productKey": "kWJUPZJ3vb9N7z0t", "deviceName": "PDhdO1k4k1m4UOFz", "integrateNo": "*************", "configuration": {"ipAddress": "*************", "port": "80", "username": "admin", "password": "lst123456", "channel": "1", "laneNo": "1", "ledIpAddress": "*************", "ledPort": "10000", "waitTime": "600"}, "endPointClassName": "com.yuchen.iot.gateway.application.integration.endpoint.special.parking.ParkingEndpointHandler", "describe": "加工中心地库出口2"}], "createTime": 1653256254, "description": "加工中心抓拍机专用连接器"}]