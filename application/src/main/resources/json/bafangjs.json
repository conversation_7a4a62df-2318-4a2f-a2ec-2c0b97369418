[{"id": "1", "name": "<PERSON><PERSON><PERSON>-camera", "type": "JIESHUN_PARKING", "routingKey": "bafangjsparking", "enabled": true, "allowCreateDevice": false, "configuration": {"metadata": {"ossPrefixKey": "https://minio.api.fj-yuchen.com/gateway"}, "windowsLibrary": "D:\\jieshun\\libs\\windows64\\JSStandardSDK.dll", "linuxLibrary": "", "jsstFilePath": "D:\\SDKFile\\", "licenseFileName": "D:\\SDKFile\\LS202212010129.dat", "authKey": "p220851383", "restSeconds": 5, "enableSecurity": false, "headersFilter": {}}, "upLink": {"id": "1", "name": "old parking up link converter", "type": "INTERNAL", "scriptType": "UP_LINK", "scriptCode": "jieshun-sdk-parking-up-link"}, "downLink": {"id": "2", "name": "old parking down link converter", "type": "INTERNAL", "scriptType": "DOWN_LINK", "scriptCode": "jieshun-sdk-parking-down-link"}, "devices": [{"connectorId": "1", "productKey": "kWJUPZJ3vb9N7z0t", "deviceName": "bafangjsdevice", "integrateNo": "bafangjsdevice", "configuration": {"waitTime": "600"}, "endPointClassName": "com.yuchen.iot.gateway.application.integration.endpoint.special.parking.ParkingEndpointHandler", "describe": "南门出口车牌抓拍机1"}], "createTime": **********, "description": "管委会抓拍机专用连接器"}]