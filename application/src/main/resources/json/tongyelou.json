[{"id": "2", "name": "hik-camera", "type": "ISAPI_TONGYE_PARKING", "routingKey": "v3parking", "enabled": true, "allowCreateDevice": false, "configuration": {"metadata": {}, "enableSecurity": false, "headersFilter": {}}, "upLink": {"id": "1", "name": "tongye parking up link converter", "type": "INTERNAL", "scriptType": "UP_LINK", "scriptCode": "tongye-parking-up-link"}, "downLink": {"id": "2", "name": "tongye parking down link converter", "type": "INTERNAL", "scriptType": "DOWN_LINK", "scriptCode": "tongye-parking-down-link"}, "devices": [{"connectorId": "2", "productKey": "kWJUPZJ3vb9N7z0t", "deviceName": "24e128141b263284", "integrateNo": "**************", "configuration": {"ipAddress": "**************", "port": "80", "username": "admin", "password": "tyl123456", "channel": "1", "waitTime": "600"}, "endPointClassName": "com.yuchen.iot.gateway.application.integration.endpoint.special.parking.ParkingEndpointHandler", "describe": "西入口(南)抓拍"}, {"connectorId": "2", "productKey": "kWJUPZJ3vb9N7z0t", "deviceName": "24e129141b263284", "integrateNo": "**************", "configuration": {"ipAddress": "**************", "port": "80", "username": "admin", "password": "tyl123456", "channel": "1", "waitTime": "600"}, "endPointClassName": "com.yuchen.iot.gateway.application.integration.endpoint.special.parking.ParkingEndpointHandler", "describe": "西入口(北)抓拍"}, {"connectorId": "2", "productKey": "kWJUPZJ3vb9N7z0t", "deviceName": "24f129141b263284", "integrateNo": "**************", "configuration": {"ipAddress": "**************", "port": "80", "username": "admin", "password": "tyl123456", "channel": "1", "waitTime": "600"}, "endPointClassName": "com.yuchen.iot.gateway.application.integration.endpoint.special.parking.ParkingEndpointHandler", "describe": "西出口抓拍"}, {"connectorId": "2", "productKey": "kWJUPZJ3vb9N7z0t", "deviceName": "24t129141b263284", "integrateNo": "**************", "configuration": {"ipAddress": "**************", "port": "80", "username": "admin", "password": "tyl123456", "channel": "1", "waitTime": "600"}, "endPointClassName": "com.yuchen.iot.gateway.application.integration.endpoint.special.parking.ParkingEndpointHandler", "describe": "北出口抓拍"}, {"connectorId": "2", "productKey": "kWJUPZJ3vb9N7z0t", "deviceName": "24t129141g263284", "integrateNo": "**************", "configuration": {"ipAddress": "**************", "port": "80", "username": "admin", "password": "tyl123456", "channel": "1", "waitTime": "600"}, "endPointClassName": "com.yuchen.iot.gateway.application.integration.endpoint.special.parking.ParkingEndpointHandler", "describe": "北入口抓拍"}], "createTime": 1653256254, "description": "同业楼抓拍机专用连接器"}]