package com.yuchen.iot.gateway.application.web.util;

import com.yuchen.iot.gateway.application.response.GatewayUserResponse;
import com.yuchen.iot.gateway.application.web.core.exception.IotGatewayAuthException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @author: 肖祥
 * @description: 保存用户信息,线程变量隔离，每个线程都会绑定一个ThreadLocal，这样就不会起冲突
 * @date: 2023/2/23 14:08
 * @version: 1.0
 */
public class UserThreadLocalUtil {
    private static final Logger log = LoggerFactory.getLogger(UserThreadLocalUtil.class);

    private UserThreadLocalUtil() {
    }

    private static final ThreadLocal<GatewayUserResponse> LOCAL = new ThreadLocal<>();

    public static void put(GatewayUserResponse sysUser) {
        log.debug("登录成功,保存用户信息");
        LOCAL.set(sysUser);
    }

    public static GatewayUserResponse get() {
        if(LOCAL.get() == null){
                throw new IotGatewayAuthException("请重新登录");
        }
        return LOCAL.get();
    }

    public static void remove() {
        log.debug("方法执行完,删除用户信息");
        LOCAL.remove();
    }
}
