package com.yuchen.iot.gateway.application.web.core.api;

import com.yuchen.iot.gateway.util.ObjectUtil;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.springframework.lang.Nullable;

import java.io.Serializable;
import java.util.Optional;

/**
 * @ClassName Result
 * @Description api返回结果类
 * <AUTHOR>
 * @Date 2022/9/24 13:57
 * @Version 1.0。0
 **/
@Getter
@Setter
@ToString
@NoArgsConstructor
public class Result<T> implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     *状态码
     */
    private int code;
    /**
     *是否成功
     */
    private boolean success;
    /**
     *承载数据
     */
    private T data;
    /**
     *返回消息
     */
    private String message;

    private Result(IResultCode resultCode) {
        this(resultCode, (T) null, resultCode.getMessage());
    }

    private Result(IResultCode resultCode, String message) {
        this(resultCode, (T) null, message);
    }

    private Result(IResultCode resultCode, T data) {
        this(resultCode, data, resultCode.getMessage());
    }

    private Result(IResultCode resultCode, T data, String message) {
        this(resultCode.getCode(), data, message);
    }

    private Result(int code, T data, String message) {
        this.code = code;
        this.data = data;
        this.message = message;
        this.success = ResultCode.SUCCESS.code == code;
    }

    public String textCode() {
        return String.valueOf(this.code);
    }

    public String getMessage() {
        return this.message;
    }

    public static boolean isSuccess(@Nullable Result<?> result) {
        return (Boolean) Optional.ofNullable(result).map((x) -> {
            return ObjectUtil.nullSafeEquals(ResultCode.SUCCESS.code, x.code);
        }).orElse(Boolean.FALSE);
    }

    public static boolean isNotSuccess(@Nullable Result<?> result) {
        return !isSuccess(result);
    }

    public static <T> Result<T> data(T data) {
        return data(data, "操作成功");
    }

    public static <T> Result<T> data(T data, String msg) {
        return data(200, data, msg);
    }

    public static <T> Result<T> data(int code, T data, String msg) {
        return new Result(code, data, data == null ? "暂无承载数据" : msg);
    }

    public static <T> Result<T> data(String code, T data, String msg) {
        return new Result(Integer.valueOf(code), data, data == null ? "暂无承载数据" : msg);
    }

    public static <T> Result<T> success() {
        return new Result(ResultCode.SUCCESS, (String)null);
    }

    public static <T> Result<T> success(String msg) {
        return new Result(ResultCode.SUCCESS, msg);
    }

    public static <T> Result<T> success(IResultCode resultCode) {
        return new Result(resultCode);
    }

    public static <T> Result<T> success(IResultCode resultCode, String msg) {
        return new Result(resultCode, msg);
    }

    public static <T> Result<T> erroResult(String msg) {
        return new Result(ResultCode.FAILURE, msg);
    }

    public static <T> Result<T> erroResult(int code, String msg) {
        return new Result(code, (Object)null, msg);
    }

    public static <T> Result<T> erroResult(String code, String msg) {
        return new Result(Integer.valueOf(code), (Object)null, msg);
    }

    public static <T> Result<T> erroResult(IResultCode resultCode) {
        return new Result(resultCode);
    }

    public static <T> Result<T> erroResult(IResultCode resultCode, String msg) {
        return new Result(resultCode, msg);
    }

    public static <T> Result<T> fail(String msg) {
        return new Result(ResultCode.FAILURE, msg);
    }

    public static <T> Result<T> fail(int code, String msg) {
        return new Result(code, (Object)null, msg);
    }

    public static <T> Result<T> fail(String code, String msg) {
        return new Result(Integer.valueOf(code), (Object)null, msg);
    }

    public static <T> Result<T> fail(IResultCode resultCode) {
        return new Result(resultCode);
    }

    public static <T> Result<T> fail(IResultCode resultCode, String msg) {
        return new Result(resultCode, msg);
    }

    public static <T> Result<T> status(boolean flag) {
        return flag ? success("操作成功") : fail("操作失败");
    }

    public int getCode() {
        return this.code;
    }

    public boolean isSuccess() {
        return this.success;
    }

    public T getData() {
        return this.data;
    }

    public void setCode(final int code) {
        this.code = code;
    }

    public void setSuccess(final boolean success) {
        this.success = success;
    }

    public void setData(final T data) {
        this.data = data;
    }

    public void setMessage(final String message) {
        this.message = message;
    }
}
