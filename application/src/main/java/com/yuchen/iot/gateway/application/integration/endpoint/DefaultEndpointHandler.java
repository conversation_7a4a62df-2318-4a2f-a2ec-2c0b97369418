package com.yuchen.iot.gateway.application.integration.endpoint;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.thinkunion.dm.api.DeviceInfo;
import com.thinkunion.dm.api.IDMCallback;
import com.thinkunion.dm.api.IThing;
import com.thinkunion.dm.api.InitResult;
import com.thinkunion.module.channel.gateway.api.subdevice.ISubDeviceActionListener;
import com.thinkunion.module.tmp.api.OutputParams;
import com.thinkunion.module.tmp.device.payload.ValueWrapper;
import com.thinkunion.module.tmp.listener.IPublishResourceListener;
import com.thinkunion.module.tools.NestError;
import com.yuchen.iot.gateway.application.constant.IotGwConstant;
import com.yuchen.iot.gateway.application.integration.north.AbstractCallServiceHandler;
import com.yuchen.iot.gateway.application.integration.north.DefaultNorthService;
import com.yuchen.iot.gateway.connector.api.SouthConnector;
import com.yuchen.iot.gateway.connector.api.UpLinkDataListener;
import com.yuchen.iot.gateway.connector.api.WaitCallResponseCallback;
import com.yuchen.iot.gateway.connector.api.data.UpLinkData;
import com.yuchen.iot.gateway.connector.api.data.message.Event;
import com.yuchen.iot.gateway.connector.api.message.ThingMessage;
import com.yuchen.iot.gateway.dao.entity.IotGwDevice;
import com.yuchen.iot.gateway.things.IoTThreadFactory;
import com.yuchen.iot.gateway.things.api.NorthClient;
import com.yuchen.iot.gateway.things.api.ValueParser;
import com.yuchen.iot.gateway.things.event.ReceiveDataEvent;
import com.yuchen.iot.gateway.things.event.ReportDataEvent;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 集成设备端点实现
 *
 * <AUTHOR>
 * @version 2.0.0
 * @date 2022/11/14 上午11:03
 */
public class DefaultEndpointHandler extends AbstractCallServiceHandler implements EndpointHandler {

    private final Logger logger = LoggerFactory.getLogger(DefaultEndpointHandler.class);

    private ScheduledExecutorService scheduledExecutorService = Executors.newScheduledThreadPool(20);
    // 存储定时任务引用，用于在初始化成功后取消
    private java.util.concurrent.ScheduledFuture<?> scheduledFuture;

    protected DeviceInfo northDeviceInfo;

    protected Map<String, String> metaData;

    protected NorthClient northClient;

    protected SouthConnector<?> southConnector;

    protected String profile;

    protected IThing thing;

    protected IotGwDevice device;

    private volatile boolean isInit = false;

    private Gson gson = new Gson();

    protected ApplicationEventPublisher applicationEventPublisher;

    protected DefaultNorthService defaultNorthService;

    private static ExecutorService deviceReConnExecutor;

    // 添加重连控制变量
    private volatile int reconnectAttempts = 0;
    private static final int MAX_RECONNECT_ATTEMPTS = 10; // 最大重连次数
    private volatile long lastReconnectTime = 0;
    private static final long RECONNECT_INTERVAL = 30000; // 30秒重连间隔

    static {
        deviceReConnExecutor = Executors.newFixedThreadPool(1, IoTThreadFactory.forName("sub-device-reconnect"));
    }

    public DefaultEndpointHandler(IotGwDevice device, NorthClient northClient,
                                  SouthConnector<?> southConnector,
                                  ApplicationEventPublisher applicationEventPublisher,
                                  DefaultNorthService defaultNorthService) {
        this.device = device;
        this.northDeviceInfo = new DeviceInfo();
        this.northDeviceInfo.productKey = device.getProductKey();
        this.northDeviceInfo.deviceName = device.getDeviceName();
        this.profile = device.getProfile();
        Map deviceConfigMap = JSON.parseObject(device.getConfigInfo(), Map.class);
        this.metaData = deviceConfigMap;
        this.northClient = northClient;
        this.southConnector = southConnector;
        this.applicationEventPublisher = applicationEventPublisher;
        this.defaultNorthService = defaultNorthService;
    }

    @Override
    public void init() {
        logger.debug("endpointHandler init success, device no: {}", device.getDeviceSn());
        southConnector.listenOnEndpoint(device.getDeviceSn(), metaData, new EndpointUpLinkDataListener());
        logger.debug("device no: {}, device meta data： {}", device.getDeviceSn(), JSON.toJSONString(metaData));
    }

    @Override
    public void reportEvent(UpLinkData upLinkData) {
        Event event = upLinkData.getEvent();
        Map<String, ValueWrapper> values = ValueParser.getEventValue(event.getIdentifier(), thing.getEvents(),
                event.getValue());
        if (values == null || values.isEmpty()) {
            logger.warn("即将上报的事件为空 identifier: {}, device no: {}", event.getIdentifier(), JSON.toJSONString(event));
        } else {
            this.thing.thingEventPost(event.getIdentifier(), new OutputParams(values), new IPublishResourceListener() {
                @Override
                public void onSuccess() {
                    logger.debug("upload event success: event :{}", JSON.toJSONString(event));
                    //上传事件
                    Map<String, Object> reportDataMap = new HashMap<>();
                    reportDataMap.put("upLinkData", upLinkData);
                    applicationEventPublisher.publishEvent(new ReportDataEvent(JSON.toJSONString(reportDataMap)));
                }

                @Override
                public void onError(NestError nestError) {
                    logger.debug("upload event fail: event: {}, error: {}", JSON.toJSONString(event), nestError.getMessage());
                }
            });
        }
    }

    @Override
    public void reportProperty(UpLinkData upLinkData) {
        Map<String, ValueWrapper> values = ValueParser.getPropertyValue(upLinkData.getProperty(),
                thing.getProperties());
        if (values == null || values.isEmpty()) {
            logger.warn("upload property is empty, device no: {}", upLinkData.getIntegrateNo());
        } else {
            this.thing.thingPropertyPost(values, new IPublishResourceListener() {
                @Override
                public void onSuccess() {
                    logger.debug("upload property success:  device no: {}", upLinkData.getIntegrateNo());
                }

                @Override
                public void onError(NestError nestError) {
                    logger.debug("upload property fail: device no: {}, error: {}",
                            upLinkData.getIntegrateNo(), nestError.getMessage());
                }
            });
        }
    }

    @Override
    public void settingProperty(String identifier, Map<String, Object> properties, WaitCallResponseCallback callback) {
        logger.info("Received iot setting : device info {} {} , identifier = {}, body = {} ",
                device.getProductKey(), device.getDeviceName(), identifier, this.gson.toJson(properties));
        this.sendToConnector(identifier, properties, this.metaData, callback);
    }

    @Override
    public void gettingProperty(String identifier, List<String> properties, WaitCallResponseCallback callback) {
        logger.info("Received iot getting : device info {} {} , identifier = {}, body = {} ",
                device.getProductKey(), device.getDeviceName(), identifier, this.gson.toJson(properties));
        this.sendToConnector(identifier, properties, this.metaData, callback);
    }

    @Override
    public void callingService(String identifier, Map<String, Object> inParams,
                               WaitCallResponseCallback callback) {
        logger.info("Received iot calling : device info {} {} , identifier = {}, body = {} ",
                device.getProductKey(), device.getDeviceName(), identifier, this.gson.toJson(inParams));
        this.sendToConnector(identifier, inParams, this.metaData, callback);
    }

    @Override
    public IThing getThing() {
        return this.thing;
    }

    private void sendToConnector(String identifier, Object data, Map<String, String> deviceMeta,
                                 WaitCallResponseCallback callback) {
        ThingMessage message = new ThingMessage();
        message.setIdentifier(identifier);
        message.setData(data);
        deviceMeta.put("ProductKey",this.device.getProductKey());
        deviceMeta.put("DeviceName",this.device.getDeviceName());
        message.setDeviceMetaData(deviceMeta);

        message.setIntegrateNo(this.device.getDeviceSn());
        this.southConnector.onDownLinkMessage(message, callback);
        //下行数据保存
        Map<String, Object> receiveDataMap = new HashMap<>();
        receiveDataMap.put("downLinkData", message);
        applicationEventPublisher.publishEvent(new ReceiveDataEvent(JSON.toJSONString(receiveDataMap)));
    }

    @Override
    public void doNorthConnect() {
        logger.debug("doNorthConnect isInit: {}, device info: {}", isInit, device);
        
        // 如果已经初始化成功，直接返回
        if (isInit) {
            logger.debug("设备已初始化，跳过连接: {}", device.getDeviceSn());
            return;
        }
        
        // 取消之前的定时任务
        if (scheduledFuture != null && !scheduledFuture.isCancelled()) {
            boolean cancelled = scheduledFuture.cancel(false);
            logger.debug("取消之前的北向连接定时任务: {}, 设备: {}", cancelled ? "成功" : "失败", device.getDeviceSn());
        }
        
        JSONObject jsonObject = JSON.parseObject(device.getConfigInfo());
        
        scheduledFuture = scheduledExecutorService.scheduleAtFixedRate(() -> {
            // 再次检查是否已初始化，避免重复执行
            if (isInit) {
                if (scheduledFuture != null && !scheduledFuture.isCancelled()) {
                    scheduledFuture.cancel(false);
                    logger.debug("设备已初始化，取消定时任务: {}", device.getDeviceSn());
                }
                return;
            }

            // 检查是否超过最大重连次数
            if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
                logger.error("设备 {} 重连次数已达到最大值 {}，停止定时任务", device.getDeviceSn(), MAX_RECONNECT_ATTEMPTS);
                if (scheduledFuture != null && !scheduledFuture.isCancelled()) {
                    scheduledFuture.cancel(false);
                }
                return;
            }
            
            if (jsonObject != null && "master".equals(jsonObject.getString(IotGwConstant.GATEWAY_DEVICE_TYPE))) {
                logger.debug("doNorthConnect master");
                thing = defaultNorthService.getMasterThing().get(IotGwConstant.MASTER_DEVICE_INFO);
                if (thing != null) {
                    setServiceHandler();
                    isInit = true;
                    // 主设备初始化成功后也要取消定时任务
                    if (scheduledFuture != null && !scheduledFuture.isCancelled()) {
                        scheduledFuture.cancel(false);
                        logger.info("主设备初始化成功，取消定时任务: {}", device.getDeviceSn());
                    }
                }
            } else if (thing == null) {
                logger.debug("device init info start: {}", northDeviceInfo);
                northClient.subDeviceConnect(northDeviceInfo, new ISubDeviceActionListener() {
                    @Override
                    public void onSuccess() {
                        if (!isInit) {
                            northClient.initSubDeviceThing(profile, northDeviceInfo,
                                    new IDMCallback<InitResult>() {
                                        @Override
                                        public void onSuccess(InitResult result) {
                                            logger.debug("device connect success,device info: {}",
                                                    northDeviceInfo);
                                            thing = northClient.getSubDeviceThing(northDeviceInfo);
                                            setServiceHandler();
                                            isInit = true;
                                            // 重置重连计数器
                                            reconnectAttempts = 0;
                                            logger.info("设备初始化成功，重置重连计数器，设备: {}", device.getDeviceSn());

                                            // 初始化成功后，取消定时任务
                                            if (scheduledFuture != null && !scheduledFuture.isCancelled()) {
                                                boolean cancelled = scheduledFuture.cancel(false);
                                                logger.info("设备初始化成功，取消北向设备连接定时任务: {}, 设备: {}",
                                                        cancelled ? "成功" : "失败", device.getDeviceSn());
                                            }
                                        }

                                        @Override
                                        public void onFailure(NestError nestError) {
                                            logger.warn("device init failed {} ,device info: {}",
                                                    nestError != null ? nestError.getMessage() : "错误信息为空",
                                                    northDeviceInfo);
                                        }
                                    });
                        }
                    }

                    @Override
                    public void onFailed(NestError nestError) {
                        String errorMsg = nestError != null ? nestError.getMessage() : "错误信息为空";
                        logger.warn("device connect failed code ={}, device: {}", errorMsg, device.getDeviceSn());

                        if (!isInit) {
                            reconnectAttempts++;
                            long currentTime = System.currentTimeMillis();

                            // 检查是否超过最大重连次数
                            if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
                                logger.error("设备 {} 重连次数已达到最大值 {}，停止重连", device.getDeviceSn(), MAX_RECONNECT_ATTEMPTS);
                                // 取消定时任务
                                if (scheduledFuture != null && !scheduledFuture.isCancelled()) {
                                    scheduledFuture.cancel(false);
                                    logger.warn("设备 {} 重连失败次数过多，取消定时任务", device.getDeviceSn());
                                }
                                return;
                            }

                            // 检查重连间隔
                            if (currentTime - lastReconnectTime < RECONNECT_INTERVAL) {
                                logger.debug("设备 {} 重连间隔未到，跳过本次重连", device.getDeviceSn());
                                return;
                            }

                            lastReconnectTime = currentTime;
                            logger.info("设备 {} 第 {} 次重连尝试", device.getDeviceSn(), reconnectAttempts);

                            // 不再通过 deviceReConnExecutor 重连，让定时任务处理
                            // 这样避免双重重连机制
                        }
                    }
                });
            }
        }, 0, 30, TimeUnit.SECONDS); // 修改为30秒间隔，减少频繁重连
    }

    private void setServiceHandler() {
        if (thing != null) {
            for (int i = 0; thing.getServices() != null && i < thing.getServices().size(); i++) {
                String id = thing.getServices().get(i).getId();
                thing.setServiceHandler(id, this);
            }
        }
    }

	@Override
	public void doNorthDisconnect() {
        // 取消定时任务
        if (scheduledFuture != null && !scheduledFuture.isCancelled()) {
            boolean cancelled = scheduledFuture.cancel(false);
            logger.debug("断开连接时取消定时任务: {}, 设备: {}", cancelled ? "成功" : "失败", device.getDeviceSn());
        }
        
        this.northClient.subDeviceDisconnect(northDeviceInfo, new ISubDeviceActionListener() {
            @Override
            public void onSuccess() {
                logger.info("device disconnect deviceName: {}", northDeviceInfo.deviceName);
                northClient.deinitSubDeviceThing(northDeviceInfo);
                isInit = false;
                thing = null;
            }

            @Override
            public void onFailed(NestError nestError) {
                logger.info("device disconnect failed {},device info：{}", nestError.getMessage(), northDeviceInfo);
            }
        });
    }


    @Override
    public void onConnect() {
        if (isInit) {
            setServiceHandler();
        }
    }

    @Override
    public void onDisconnect() {

    }

    @Override
    public void onConnectFailed(String s) {

    }

    @Override
    public void destroy() {
        // 取消定时任务
        if (scheduledFuture != null && !scheduledFuture.isCancelled()) {
            boolean cancelled = scheduledFuture.cancel(false);
            logger.debug("销毁时取消定时任务: {}, 设备: {}", cancelled ? "成功" : "失败", device.getDeviceSn());
        }
        
        this.southConnector.listenOffEndpoint(device.getDeviceSn(), this.metaData);
        //元素数据清空
        metaData.clear();
        this.isInit = false;
    }

    public class EndpointUpLinkDataListener implements UpLinkDataListener {

        @Override
        public void onSouthMessage(UpLinkData upLinkData) {
            if (upLinkData.hasEvent()) {
                logger.debug("上传事件到平台: {} {}", upLinkData.getIntegrateNo(), upLinkData.getEvent().getIdentifier());
                reportEvent(upLinkData);
            }
            if (upLinkData.hasProperties()) {
                logger.debug("上传属性到平台: {} {}", upLinkData.getIntegrateNo(), upLinkData.getProperty());
                reportProperty(upLinkData);
            }
            if (upLinkData.hasCallResponse()) {
                logger.debug("上传服务调用响应到平台: {} {} {}", upLinkData.getIntegrateNo(), upLinkData.getCallId(),
                        upLinkData.getCallResponse());
                if (StringUtils.isNotBlank(upLinkData.getCallId())) {
                    responseCallService(upLinkData.getCallId(), upLinkData.getCallResponse());
                }
            }
        }

        @Override
        public void onSouthConnect() {
            doNorthConnect();
        }

        @Override
        public void onSouthDisconnect() {
            doNorthDisconnect();
        }
    }
}
