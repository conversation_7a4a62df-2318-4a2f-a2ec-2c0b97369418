package com.yuchen.iot.gateway.application.web.core.interceptor;

import cn.hutool.core.util.StrUtil;
import com.auth0.jwt.exceptions.AlgorithmMismatchException;
import com.auth0.jwt.exceptions.InvalidClaimException;
import com.auth0.jwt.exceptions.SignatureVerificationException;
import com.auth0.jwt.exceptions.TokenExpiredException;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.yuchen.iot.gateway.application.constant.IotGwConstant;
import com.yuchen.iot.gateway.application.response.GatewayUserResponse;
import com.yuchen.iot.gateway.application.web.cache.UserLoginCache;
import com.yuchen.iot.gateway.application.web.core.exception.IotGatewayAuthException;
import com.yuchen.iot.gateway.application.web.util.JWTUtil;
import com.yuchen.iot.gateway.application.web.util.UserThreadLocalUtil;
import com.yuchen.iot.gateway.connector.api.annotation.IgnoreToken;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ValidationException;
import java.lang.reflect.Method;
import java.util.Map;

/**
 * @author: 肖祥
 * @description: JSON web token拦截器
 * @date: 2023/2/11 15:26
 * @version: 1.0
 */
public class JwtInterceptor implements HandlerInterceptor {
    private static final Logger log = LoggerFactory.getLogger(JwtInterceptor.class);

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        log.info("请求方法 uri {} ",request.getRequestURI());
        if("OPTIONS".equals(request.getMethod())){
            response.setStatus(HttpServletResponse.SC_OK);
            return true;
        }
        if(IotGwConstant.BLACK_LIST.contains(request.getRequestURI())){
            log.debug("black list: {}", IotGwConstant.BLACK_LIST);
            return false;
        }
        response.setContentType("application/json;charset=utf-8");
        if (handler instanceof HandlerMethod) {
            //如果接口或者类上有@IgnoreToken注解，意思该接口不需要token就能访问，需要放行
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            Method method = handlerMethod.getMethod();
            //先从类上获取该注解，判断类上是否加了IgnoreToken ，代表不需要token，直接放行
            IgnoreToken annotation = handlerMethod.getBeanType().getAnnotation(IgnoreToken.class);
            if (annotation == null) {
                //再从方法上获取该注解
                if (method.isAnnotationPresent(IgnoreToken.class)) {
                    annotation = method.getAnnotation(IgnoreToken.class);
                    log.info("请求方法 {} 上有注解 {} ", method.getName(), annotation);
                }
            }
            if (annotation != null) {
                return true;
            }
        }

       String token = request.getHeader(IotGwConstant.AUTH);
        if(StringUtils.isBlank(token)){
            // 登录已退出,请重新登录
            throw new IotGatewayAuthException("请重新登录");
        }
        GatewayUserResponse gatewayUserResponse =  UserLoginCache.getUserRequestHashMap().get(token);
        if(gatewayUserResponse == null){
            // 登录已退出,请重新登录
            throw new IotGatewayAuthException("请重新登录");
        }

        if (StrUtil.isEmpty(token)) {
            throw new ValidationException("用户未登录");
        }

        try {
            DecodedJWT jwt = JWTUtil.verify(token);
            Map<String, Claim> claims = jwt.getClaims();
            Claim claim = claims.get("userId");
            String userId = claim.asString();
            log.debug("登录的用户：{}", userId);
            //登录成功,保存用户信息
            UserThreadLocalUtil.put(gatewayUserResponse);
        } catch (SignatureVerificationException e) {
            e.printStackTrace();
            throw new IotGatewayAuthException("token 签名不一致异常");
        } catch (TokenExpiredException e) {
            e.printStackTrace();
            throw new IotGatewayAuthException("token 令牌过期异常");
        } catch (AlgorithmMismatchException e) {
            e.printStackTrace();
            throw new IotGatewayAuthException("token 算法不匹配异常");
        } catch (InvalidClaimException e) {
            e.printStackTrace();
            throw new IotGatewayAuthException("token 失效的payload异常");
        } catch (Exception e) {
            e.printStackTrace();
            throw new IotGatewayAuthException(e.getMessage());
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        //最后执行结束需要将ThreadLocal中的信息删除  不删除会有内存泄露的风险
        UserThreadLocalUtil.remove();
    }
}
