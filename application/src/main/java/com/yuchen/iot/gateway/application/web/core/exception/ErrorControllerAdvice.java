package com.yuchen.iot.gateway.application.web.core.exception;


import com.yuchen.iot.gateway.application.web.core.api.Result;
import com.yuchen.iot.gateway.application.web.core.api.ResultCode;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * @ClassName ErrorControllerAdvice
 * @Description iot网关统一异常拦截
 * <AUTHOR>
 * @Date 2022/9/24 14:43
 * @Version 1.0。0
 **/
@RestControllerAdvice(basePackages = {"com.yuchen.iot.gateway.application.controller"})
@Order(Ordered.HIGHEST_PRECEDENCE)
public class ErrorControllerAdvice {

    /**
     * 处理 iotGatewayException 异常
     *
     * @param iotGatewayException：异常
     * @return
     */
    @ExceptionHandler(value = IotGatewayException.class)
    public Result<?> handleBusinessException(IotGatewayException iotGatewayException) {
        return Result.erroResult(ResultCode.INTERNAL_SERVER_ERROR, iotGatewayException.getMessage());
    }

    /**
     * 处理 Exception 异常
     * @param e：异常
     * @return
     */
    @ExceptionHandler(value = Exception.class)
    public Result<?> exceptionHandler(Exception e) {
        e.printStackTrace();
        return Result.erroResult(ResultCode.INTERNAL_SERVER_ERROR,e.getMessage());
}

    /**
     * 处理 IotGatewayAuthException 异常
     * @return
     */
    @ExceptionHandler(value = IotGatewayAuthException.class)
    public Result<?> handleHandleException(IotGatewayAuthException iotGatewayAuthException) {
        return Result.erroResult(ResultCode.UN_AUTHORIZED, iotGatewayAuthException.getMessage());
    }

    /**
     * 处理 IotGatewayCheckException 异常
     * @return
     */
    @ExceptionHandler(value = IotGatewayCheckException.class)
    public Result<?> handleHandleCheckException(IotGatewayCheckException iotGatewayCheckException) {
        return Result.erroResult(ResultCode.FAILURE, iotGatewayCheckException.getMessage());
    }
}
