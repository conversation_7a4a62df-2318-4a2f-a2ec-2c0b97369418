package com.yuchen.iot.gateway.application.web.page;

import com.yuchen.iot.gateway.dao.base.BaseEntity;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: 肖祥
 * @description: 分页请求基类
 * @date: 2023/2/15 11:46
 * @version: 1.0
 */
@Data
public class PageRequest extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1;

    /**
     * 当前页
     */
    private Integer current = 1;
    /**
     * 每页的数量
     */
    private Integer size = 10;
}
