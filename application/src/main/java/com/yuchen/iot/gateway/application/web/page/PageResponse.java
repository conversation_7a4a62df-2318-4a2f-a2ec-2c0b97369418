package com.yuchen.iot.gateway.application.web.page;

import lombok.Data;

import java.util.List;

/**
 * @author: 肖祥
 * @description: 分页响应基类
 * @date: 2023/2/15 11:50
 * @version: 1.0
 */
@Data
public class PageResponse<T> {
    /**
     * 当前页码
     */
    private int current;
    /**
     * 每页条数
     */
    private int size;
    /**
     * 数据总条数
     */
    private int total;
    /**
     * 总页数
     */
    private int pages;
    /**
     * 返回的数据
     */
    private List<T> records;


    public int getPages() {
        if (this.getSize() == 0) {
            return 0;
        } else {
            int pages = this.getTotal() / this.getSize();
            if (this.getTotal() % this.getSize() != 0) {
                ++pages;
            }
            return pages;
        }
    }
}
