package com.yuchen.iot.gateway.application.web.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * @author: 肖祥
 * @description: JSON工具类
 * @date: 2023/3/1 10:00
 * @version: 1.0
 */
public class JsonUtil {

    /**
     * 判断是JsonObject
     * @param obj
     * @return
     */
    public static boolean isJsonObject(Object obj) {
        String content = obj.toString();
        try {
            JSONObject.parseObject(content);
            if (content.startsWith("{")) {
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 判断是JsonArray
     * @param obj
     * @return
     */
    public static boolean isJsonArray(Object obj) {
        String content= obj.toString();
        try {
            JSONArray.parseArray(content);
            if (content.startsWith("[")) {
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            return false;
        }
    }
}
