<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>iot-gateway</artifactId>
        <groupId>com.thinkunion.iot.gateway</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>application</artifactId>
    <packaging>jar</packaging>
    <dependencies>
        <dependency>
            <groupId>com.thinkunion.iot.gateway</groupId>
            <artifactId>connector-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

     <!--         <dependency>
                   <groupId>com.thinkunion.iot.gateway</groupId>
                   <artifactId>sdk-connectors</artifactId>
                  <version>1.0.0-SNAPSHOT</version>
               </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.thinkunion.iot.gateway</groupId>-->
<!--            <artifactId>nbIot-connectors</artifactId>-->
<!--            <version>1.0.0-SNAPSHOT</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.thinkunion.iot.gateway</groupId>-->
<!--            <artifactId>hik-sdk-connectors</artifactId>-->
<!--            <version>1.0.0-SNAPSHOT</version>-->
<!--        </dependency>-->
        <!--  <dependency>
              <groupId>com.thinkunion.iot.gateway</groupId>
              <artifactId>http-connectors</artifactId>
              <version>1.0.0-SNAPSHOT</version>
          </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.thinkunion.iot.gateway</groupId>-->
<!--            <artifactId>tcp-connectors</artifactId>-->
<!--            <version>1.0.0-SNAPSHOT</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.thinkunion.iot.gateway</groupId>-->
<!--            <artifactId>sdk-connectors</artifactId>-->
<!--            <version>1.0.0-SNAPSHOT</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.thinkunion.iot.gateway</groupId>-->
<!--            <artifactId>mqtt-connectors</artifactId>-->
<!--            <version>1.0.0-SNAPSHOT</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.thinkunion.iot.gateway</groupId>
            <artifactId>dao</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.thinkunion.iot.gateway</groupId>
            <artifactId>things-iot</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <version>8.2.1</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.thinkunion.iot.gateway</groupId>-->
<!--            <artifactId>ws-connectors</artifactId>-->
<!--            <version>1.0.0-SNAPSHOT</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.github.oshi</groupId>
            <artifactId>oshi-core</artifactId>
            <version>3.12.2</version>
        </dependency>
        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna</artifactId>
            <version>5.2.0</version>
        </dependency>
        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna-platform</artifactId>
            <version>5.2.0</version>
        </dependency>

        <dependency>
            <groupId>com.thinkunion.iot.gateway</groupId>
            <artifactId>plugin</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.name}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring.boot.version}</version>
                    <configuration>
                        <fork>true</fork>
                        <finalName>${project.build.finalName}</finalName>
                        <includeSystemScope>true</includeSystemScope>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>


</project>