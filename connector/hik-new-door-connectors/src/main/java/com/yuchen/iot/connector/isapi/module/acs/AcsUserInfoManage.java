package com.yuchen.iot.connector.isapi.module.acs;


import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yuchen.iot.connector.isapi.entity.DeviceInfoDTO;
import com.yuchen.iot.connector.isapi.utils.ConfigFileUtil;
import com.yuchen.iot.connector.isapi.utils.HTTPClientUtil;
import com.yuchen.iot.connector.isapi.utils.HttpsClientUtil;
import com.yuchen.iot.gateway.connector.api.data.DownLinkData;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 门禁人员信息管理，包含人员增删改查操作
 *
 * @Author: jiangxin14
 * @Date: 2024-01-15  14:13
 */
@Slf4j
public class AcsUserInfoManage {

    public static final int _BYHTTP_ = 0;

    public static DateTimeFormatter originalFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    // 定义目标日期时间字符串的格式（ISO 8601）
    public static DateTimeFormatter targetFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");

    /**
     * 获取门禁人员信息能力集
     *
     * @param deviceInfoDTO 设备参数
     * @return
     */
    public static String GetAcsUserInfoCap(DeviceInfoDTO deviceInfoDTO) {
        String deviceInfoUrl = "/ISAPI/AccessControl/UserInfo/capabilities?format=json";
        if (deviceInfoDTO.httpType == _BYHTTP_) {
            try {
                String response = HTTPClientUtil.doGet(deviceInfoDTO, deviceInfoUrl);
                return response;
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        } else {
            try {
                String response = HttpsClientUtil.httpsGet(deviceInfoUrl);
                return response;
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }
    }

    /**
     * 下发门禁人员信息
     *
     * @param deviceInfoDTO 设备参数
     * @param mapper
     * @return 下发返回信息
     */
    public static String AddUserInfo(DeviceInfoDTO deviceInfoDTO, DownLinkData downLinkData, ObjectMapper mapper) {
        //重点测试中文姓名情况
        String deviceInfoUrl = "/ISAPI/AccessControl/UserInfo/Record?format=json";
        JsonNode jsonNode = null;
        try {
            jsonNode = mapper.readTree(downLinkData.getData());

            JsonNode listFaceNode = jsonNode.path("listFace");



            LocalDateTime dateTime = LocalDateTime.parse(listFaceNode.get(0).get("startTime").asText(), originalFormatter);

            // 将日期时间格式化为目标格式的字符串
            String beginTime = dateTime.format(targetFormatter);

            LocalDateTime dateTime1 = LocalDateTime.parse(listFaceNode.get(0).get("endTime").asText(), originalFormatter);

            // 将日期时间格式化为目标格式的字符串
            String endTime = dateTime1.format(targetFormatter);


        if (deviceInfoDTO.httpType == _BYHTTP_) {
            //从AddUserInfo.json引入入参
            Map<String, Object> parameter = new HashMap<>();
            parameter.put("employeeNo",listFaceNode.get(0).get("userId").asText());
            parameter.put("userType","normal");
            parameter.put("name",listFaceNode.get(0).get("userName").asText());
            parameter.put("enable",true);
            parameter.put("beginTime",beginTime);
            parameter.put("endTime",endTime);
            parameter.put("timeType","local");
            String input = ConfigFileUtil.getReqBodyFromTemplate("/acs/AcsUserInfoManage/AddUserInfo.json", parameter);
            log.info("AddUserInfo====="+input);

            try {
                String response = HTTPClientUtil.doPost(deviceInfoDTO, deviceInfoUrl, input);
                return response;
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        } else {
            return null;
        }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

/*
    public static void main(String[] args) {
        Map<String, Object> parameter = new HashMap<>();
        parameter.put("employeeNo",1);
        parameter.put("userType",true);
        parameter.put("name",1);
        parameter.put("enable",true);
        parameter.put("beginTime",1);
        parameter.put("endTime",true);
        parameter.put("timeType","local");
        String input = ConfigFileUtil.getReqBodyFromTemplate("acs/AcsUserInfoManage/AddUserInfo.json", parameter);
        System.out.println(input);
    }
*/

    /**
     * 指定工号获取人员信息
     *
     * @param deviceInfoDTO 设备参数
     * @return
     */
    public static String GetUserInfo(DeviceInfoDTO deviceInfoDTO) {
        String deviceInfoUrl = "/ISAPI/AccessControl/UserInfo/Search?format=json";
        if (deviceInfoDTO.httpType == _BYHTTP_) {
            //从GetUserInfo.json引入入参
            Map<String, Object> parameter = new HashMap<>();
            String input = ConfigFileUtil.getReqBodyFromTemplate("acs/AcsUserInfoManage/GetUserInfo.json", parameter);
            try {
                String response = HTTPClientUtil.doPost(deviceInfoDTO, deviceInfoUrl, input);
                return response;
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * 获取所有人员信息
     *
     * @param deviceInfoDTO 设备参数
     * @return
     */
    public static String GetAllUserInfo(DeviceInfoDTO deviceInfoDTO) {
        String deviceInfoUrl = "/ISAPI/AccessControl/UserInfo/Search?format=json";
        if (deviceInfoDTO.httpType == _BYHTTP_) {
            //从GetAllUserInfo.json引入入参

            Map<String, Object> parameter = new HashMap<>();
            String input = ConfigFileUtil.getReqBodyFromTemplate("acs/AcsUserInfoManage/GetAllUserInfo.json", parameter);
            try {
                String response = HTTPClientUtil.doPost(deviceInfoDTO, deviceInfoUrl, input);
                return response;
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        } else {
            return null;
        }

    }

    /**
     * 删除人员信息
     *
     * @param deviceInfoDTO 设备参数
     * @return
     */
    public static String DeleteUserInfo(DeviceInfoDTO deviceInfoDTO, DownLinkData downLinkData, ObjectMapper mapper) {
        String deviceInfoUrl = "/ISAPI/AccessControl/UserInfo/Delete?format=json";
        try {
        if (deviceInfoDTO.httpType == _BYHTTP_) {
            JsonNode jsonNode = null;

            jsonNode = mapper.readTree(downLinkData.getData());

            JsonNode listFaceNode = jsonNode.path("listFace");
            //从DeleteUserInfo.json引入入参
         //   Map<String, Object> parameter = new HashMap<>();

            //  JsonNode listFace = jsonNode.path("listFace");

            Map<String, Object> data = new HashMap<>();
            data.put("userId", listFaceNode.get(0).get("userId"));


            String input = ConfigFileUtil.getReqBodyFromTemplate("/acs/AcsUserInfoManage/DeleteUserInfo.json", data);
            //       try {
            String response = HTTPClientUtil.doPut(deviceInfoDTO, deviceInfoUrl, input);
            return response;
     /*       } catch (Exception e) {
                e.printStackTrace();
                return null;
            }*/
        }
        } catch (IOException e) {
                throw new RuntimeException(e);


        }
        return null;
    }

    /**
     * 获取删除人员信息进度
     *
     * @param deviceInfoDTO 设备参数
     * @return
     */
    public static String DeleteProcess(DeviceInfoDTO deviceInfoDTO) {
        String deviceInfoUrl = "/ISAPI/AccessControl/UserInfoDetail/DeleteProcess?format=json";
        if (deviceInfoDTO.httpType == _BYHTTP_) {
            try {
                String response = HTTPClientUtil.doGet(deviceInfoDTO, deviceInfoUrl);
                return response;
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        } else {
            try {
                String response = HttpsClientUtil.httpsGet(deviceInfoUrl);

                return response;
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }
    }


}
