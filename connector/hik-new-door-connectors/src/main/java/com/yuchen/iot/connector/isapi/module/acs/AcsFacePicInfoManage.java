package com.yuchen.iot.connector.isapi.module.acs;



import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yuchen.iot.connector.isapi.entity.DeviceInfoDTO;
import com.yuchen.iot.connector.isapi.utils.ConfigFileUtil;
import com.yuchen.iot.connector.isapi.utils.FileUtil;
import com.yuchen.iot.connector.isapi.utils.HTTPClientUtil;
import com.yuchen.iot.connector.isapi.utils.HttpsClientUtil;
import com.yuchen.iot.gateway.connector.api.data.DownLinkData;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 门禁图片信息管理操作，包括图片增删改查操作
 *
 * @Author: jiangxin14
 * @Date: 2024-01-15  14:15
 */
@Slf4j
public class AcsFacePicInfoManage {

    public static final int _BYHTTP_ = 0;//HTTP协议与设备交互

    /**
     * 获取图片库能力集
     *
     * @param deviceInfoDTO 设备信息
     * @return 返回门禁图片库能力集
     */
    public static String GetFaceLibCap(DeviceInfoDTO deviceInfoDTO) {
        String deviceInfoUrl = "/ISAPI/AccessControl/CardInfo/capabilities?format=json";
        if (deviceInfoDTO.httpType == _BYHTTP_) {
            try {
                String response = HTTPClientUtil.doGet(deviceInfoDTO, deviceInfoUrl);
                return response;
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        } else {
            try {
                String response = HttpsClientUtil.httpsGet(deviceInfoUrl);
                return response;
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }
    }

    /**
     * 在线采集人员图片信息
     *
     * @param deviceInfoDTO 设备信息
     * @return
     */
    public static String CaptureFacePicInfo(DeviceInfoDTO deviceInfoDTO) {
        String deviceInfoUrl = "/ISAPI/AccessControl/CaptureFaceData";
        if (deviceInfoDTO.httpType == _BYHTTP_) {
            //从CaptureFacePicInfo.xml引入入参
            Map<String, Object> parameter = new HashMap<>();
            String input = ConfigFileUtil.getReqBodyFromTemplate("acs/AcsFacePicInfoManage/CaptureFacePicInfo.xml", parameter);
            try {
                String response = HTTPClientUtil.doPost(deviceInfoDTO, deviceInfoUrl, input);
                return response;
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * 下发门禁图片
     *
     * @param deviceInfoDTO 设备信息
     * @return
     */
    public static String AddFacePicInfo(DeviceInfoDTO deviceInfoDTO, DownLinkData downLinkData, ObjectMapper mapper) {
        String deviceInfoUrl = "/ISAPI/Intelligent/FDLib/FaceDataRecord?format=json";
        JsonNode jsonNode = null;

        try {
            jsonNode = mapper.readTree(downLinkData.getData());


        JsonNode listFaceNode = jsonNode.path("listFace");
        if (deviceInfoDTO.httpType == _BYHTTP_) {
            //从AddFacePicInfo.json引入入参




            Map<String, Object> parameter = new HashMap<>();
            parameter.put("faceURL",listFaceNode.get(0).get("faceUrl").asText());
            parameter.put("faceLibType","blackFD");
            parameter.put("FDID",listFaceNode.get(0).get("cardNo").asText());
            parameter.put("FPID",listFaceNode.get(0).get("userId").asText());

            String input = ConfigFileUtil.getReqBodyFromTemplate("/acs/AcsFacePicInfoManage/AddFacePicInfo.json", parameter);
           log.info("AddFacePicInfo====="+input);
            try {
                String response = HTTPClientUtil.doPost(deviceInfoDTO, deviceInfoUrl, input);
                return response;
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        } else {
            return null;
        }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 下发门禁图片
     *
     * @param deviceInfoDTO 设备信息
     * @return
     */
    public static String AddFacePicInfoFrom(DeviceInfoDTO deviceInfoDTO, DownLinkData downLinkData, ObjectMapper mapper) {
        String deviceInfoUrl = "/ISAPI/Intelligent/FDLib/FaceDataRecord?format=json";
        JsonNode jsonNode = null;

        try {
            jsonNode = mapper.readTree(downLinkData.getData());


            JsonNode listFaceNode = jsonNode.path("listFace");
            if (deviceInfoDTO.httpType == _BYHTTP_) {
                //从AddFacePicInfo.json引入入参




                Map<String, Object> parameter = new HashMap<>();
           //     parameter.put("faceURL",listFaceNode.get(0).get("faceUrl").asText());
                parameter.put("faceLibType","blackFD");
              //  parameter.put("FDID",listFaceNode.get(0).get("cardNo").asText());
                parameter.put("FDID","1");
                parameter.put("FPID",listFaceNode.get(0).get("userId").asText());

                String input = ConfigFileUtil.getReqBodyFromTemplate("/acs/AcsFacePicInfoManage/AddFacePicInfo.json", parameter);


                URL url = new URL(listFaceNode.get(0).get("faceUrl").asText());
                InputStream inputStream = url.openStream();
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }

                byte[] imageBytes = outputStream.toByteArray();
                String filePath=System.getProperty("user.dir") + "\\doorPic\\";
                String fileName=UUID.randomUUID().toString();
                FileUtil.byteAry2File(System.getProperty("user.dir") + "\\doorPic\\", fileName,".jpg",imageBytes);

                log.info("AddFacePicInfo====="+input);
                log.info("filePath====="+filePath+fileName+".jpg");
                try {
                    String response = HTTPClientUtil.doPostUploadPhoto(deviceInfoDTO, deviceInfoUrl, input,filePath+fileName+".jpg");
                   // String response = HTTPClientUtil.doPost(deviceInfoDTO, deviceInfoUrl, input);
                    return response;
                } catch (Exception e) {
                    e.printStackTrace();
                    return null;
                }
            } else {
                return null;
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
    /**
     * 查询人脸信息，根据工号查询人脸数据
     *
     * @param deviceInfoDTO 设备信息
     * @return
     */
    public static String GetFacePicInfo(DeviceInfoDTO deviceInfoDTO) {
        String deviceInfoUrl = "/ISAPI/Intelligent/FDLib/FDSearch?format=json";
        if (deviceInfoDTO.httpType == _BYHTTP_) {
            //从GetFacePicInfo.json引入入参
            Map<String, Object> parameter = new HashMap<>();
            String input = ConfigFileUtil.getReqBodyFromTemplate("acs/AcsFacePicInfoManage/GetFacePicInfo.json", parameter);
            try {
                String response = HTTPClientUtil.doPost(deviceInfoDTO, deviceInfoUrl, input);
                return response;
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        } else {
            return null;
        }

    }

    /**
     * 删除人员，删除人脸库中的人脸数据，支持批量删除，URI里面需要指定人脸库FDID和人脸库类型
     *
     * @param deviceInfoDTO 设备信息
     * @return
     */
    public static String DeleteFacePicInfo(DeviceInfoDTO deviceInfoDTO) {
        String deviceInfoUrl = "/ISAPI/Intelligent/FDLib/FDSearch/Delete?format=json&FDID=1&faceLibType=blackFD";
        if (deviceInfoDTO.httpType == _BYHTTP_) {
            //从DeleteFacePicInfo.json引入入参
            Map<String, Object> parameter = new HashMap<>();
            String input = ConfigFileUtil.getReqBodyFromTemplate("acs/AcsFacePicInfoManage/DeleteFacePicInfo.json", parameter);
            try {
                String response = HTTPClientUtil.doPut(deviceInfoDTO, deviceInfoUrl, input);
                return response;
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        } else {
            return null;
        }
    }
}
