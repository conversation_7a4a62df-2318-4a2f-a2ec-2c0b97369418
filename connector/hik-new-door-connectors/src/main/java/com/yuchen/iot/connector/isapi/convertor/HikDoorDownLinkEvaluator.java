package com.yuchen.iot.connector.isapi.convertor;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.yuchen.iot.gateway.connector.api.converter.AbstractKeyEvaluator;
import com.yuchen.iot.gateway.connector.api.converter.evaluator.DownLinkEvaluator;
import com.yuchen.iot.gateway.connector.api.converter.internal.InternalEvaluatorSet;
import com.yuchen.iot.gateway.connector.api.message.ThingMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.pf4j.Extension;
import org.springframework.context.annotation.Lazy;

import javax.annotation.Resource;
import javax.script.ScriptException;
import java.util.Map;

/**
 * 海康门禁命令下发转换器
 */
@Slf4j
@Extension
public class HikDoorDownLinkEvaluator extends AbstractKeyEvaluator implements DownLinkEvaluator {
    @Resource
    @Lazy
    private InternalEvaluatorSet internalEvaluatorSet;

    @Override
    public String getKey() {
            return "hik-door-down-link";
    }

    @Override
    public JsonNode execute(ThingMessage message, Map<String, String> metadata) throws ScriptException {
        ObjectNode resultData = this.objectMapper.createObjectNode();
        resultData.put("contentType", "JSON");

        String identifier = message.getIdentifier();
        if (StringUtils.isEmpty(identifier)) {
            throw new ScriptException("param cmd can not be null");
        }
        metadata.put("integrateNo",message.getIntegrateNo());
        ObjectNode resultMeta = this.objectMapper.createObjectNode();
        resultMeta.put("integrateNo", message.getIntegrateNo());
        resultData.put("metadata",resultMeta);
        resultData.put("data", JSONUtil.toJsonStr(message.getData()));
        return resultData;
   /*     try {
            Method method = MaiChiRequestFactory.class.getMethod(identifier, ThingMessage.class);
            String msg = (String) method.invoke(null, message);
            resultData.put("data", msg);

            return resultData;
        } catch (NoSuchMethodException e) {
            throw new ScriptException("identifier cmd is notfound");
        } catch (InvocationTargetException | IllegalAccessException e) {
            throw new ScriptException("identifier cmd fail");
        }*/
    }
    @Override
    public void destroy() {
        internalEvaluatorSet.removeUpLinkEvaluator(this.getKey());
    }
}
