package com.yuchen.iot.connector.isapi.module;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.yuchen.iot.connector.isapi.entity.DeviceInfoDTO;
import com.yuchen.iot.connector.isapi.module.acs.*;
import com.yuchen.iot.gateway.connector.api.data.DownLinkData;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/1/9 16:53
 * @desc 门禁相关demo功能点路由
 */
@Slf4j
public class AcsModuleDispatch {

        final ObjectMapper mapper = new ObjectMapper();



    public   Map<String, String>  dispatch(DeviceInfoDTO deviceInfoDTO, String command, DownLinkData downLinkData) {
        String respone="";
        Map<String, String> responMap = null;
        switch (command) {
            case "10001": {
                log.info("\n[Function]设备信息获取");
                respone=AcsCfgInfo.GetDeviceInfo(deviceInfoDTO);
                break;
            }
            case "10002": {
                log.info("\n[Function]获取门禁主机配置信息");
                respone=AcsCfgInfo.GetAcsConfig(deviceInfoDTO);
                break;
            }
            case "10003": {
                log.info("\n[Function]配置门禁主机参数");
                respone=AcsCfgInfo.SetAcsConfig(deviceInfoDTO);
                break;
            }
            case "doorOperation": {
                log.info("\n[Function]远程控门");
                respone=AcsCfgInfo.RemoteDoorCfg(deviceInfoDTO,downLinkData);
                responMap = new HashMap<>();
                responMap.put("code","0");
                responMap.put("msg", respone);
                break;
            }
            case "10005": {
                log.info("\n[Function]获取门禁状态");
                respone=AcsCfgInfo.AcsWorkStatus(deviceInfoDTO);
                break;
            }
            case "10006": {
                log.info("\n[Function]获取门禁人员信息能力集");
                respone=AcsUserInfoManage.GetAcsUserInfoCap(deviceInfoDTO);
                break;
            }
            case "addFace": {
                log.info("\n[Function]下发人员");
                respone=AcsUserInfoManage.AddUserInfo(deviceInfoDTO,downLinkData,mapper);
              //  respone=AcsFacePicInfoManage.AddFacePicInfo(deviceInfoDTO,downLinkData,mapper);
                respone=AcsFacePicInfoManage.AddFacePicInfoFrom(deviceInfoDTO,downLinkData,mapper);

                responMap = new HashMap<>();
                responMap.put("code","0");
                responMap.put("msg", respone);
                break;
            }
            case "10009": {
                log.info("\n[Function]查询人员");
                respone=AcsUserInfoManage.GetUserInfo(deviceInfoDTO);
                break;
            }
            case "10010": {
                log.info("\n[Function]查询所有人员");
                respone=AcsUserInfoManage.GetAllUserInfo(deviceInfoDTO);
                break;
            }
            case "faceDelete": {
                log.info("\n[Function]删除人员");
                respone=AcsUserInfoManage.DeleteUserInfo(deviceInfoDTO,downLinkData,mapper);
                responMap = new HashMap<>();
                responMap.put("code","0");
                responMap.put("msg", respone);
                break;
            }
            case "10012": {
                log.info("\n[Function]获取删除人员进度");
                respone=AcsUserInfoManage.DeleteProcess(deviceInfoDTO);
                break;
            }
            case "10013": {
                log.info("\n[Function]获取门禁卡号信息能力集");
                respone=AcsCardInfoManage.GetCardInfoCap(deviceInfoDTO);
                break;
            }
            case "10014": {
                log.info("\n[Function]下发卡号");
                respone=AcsCardInfoManage.AddCardInfo(deviceInfoDTO);
                break;
            }
            case "10015": {
                log.info("\n[Function]获取一张卡号");
                respone=AcsCardInfoManage.GetOneCardInfo(deviceInfoDTO);
                break;
            }
            case "10016": {
                log.info("\n[Function]获取所有卡号");
                respone=AcsCardInfoManage.GetAllCardInfo(deviceInfoDTO);
                break;
            }
            case "10017": {
                log.info("\n[Function]删除卡号");
                respone=AcsCardInfoManage.DeleteCardInfo(deviceInfoDTO);
                break;
            }
            case "10018": {
                log.info("\n[Function]获取人员图片能力集");
                respone=AcsFacePicInfoManage.GetFaceLibCap(deviceInfoDTO);
                break;
            }
            case "10019": {
                log.info("\n[Function]下发人员图片");
                respone=AcsFacePicInfoManage.AddFacePicInfo(deviceInfoDTO,downLinkData,mapper);
                responMap = new HashMap<>();
                responMap.put("code","0");
                responMap.put("msg", respone);
                break;
            }
            case "10020": {
                log.info("\n[Function]查询人员图片");
                respone=AcsFacePicInfoManage.GetFacePicInfo(deviceInfoDTO);
                break;
            }
            case "10021": {
                log.info("\n[Function]删除人员图片");
                respone=AcsFacePicInfoManage.DeleteFacePicInfo(deviceInfoDTO);
                break;
            }
            case "10022": {
                log.info("\n[Function]在线采集人员图片");
                respone=AcsFacePicInfoManage.CaptureFacePicInfo(deviceInfoDTO);
                break;
            }
            case "10023": {
                log.info("\n[Function]获取指纹能力集");
                respone=AcsFingerInfoManage.GetFingerInfoCap(deviceInfoDTO);
                break;
            }
            case "10024": {
                log.info("\n[Function]下发指纹数据");
                respone=AcsFingerInfoManage.AddFingerInfo(deviceInfoDTO);
                break;
            }
            case "10025": {
                log.info("\n[Function]获取指纹数据");
                respone=AcsFingerInfoManage.GetFingerInfo(deviceInfoDTO);
                break;
            }
            case "10026": {
                log.info("\n[Function]删除指纹数据");
                respone=AcsFingerInfoManage.DeleteFingerInfo(deviceInfoDTO);
                break;
            }
            case "10027": {
                log.info("\n[Function]在线采集指纹数据");
                respone=AcsFingerInfoManage.captureFingerInfo(deviceInfoDTO);
                break;
            }
            case "10028": {
                log.info("\n[Function]获取门禁事件总条数");
                respone=AcsEventSearch.GetEventNum(deviceInfoDTO);
                break;
            }
            case "10029": {
                log.info("\n[Function]查询门禁事件");
                respone=AcsEventSearch.GetEventDetailInfo(deviceInfoDTO);
                break;
            }
            case "10030": {
                log.info("\n[Function]获取人员周计划参数");
                respone=AcsUserPlanManage.GetUserRightWeekPlanCfg(deviceInfoDTO, "1");
                break;
            }
            case "10031": {
                log.info("\n[Function]设置人员周计划参数");
                respone=AcsUserPlanManage.SetUserRightWeekPlanCfg(deviceInfoDTO, "1");
                break;
            }
            case "10032": {
                log.info("\n[Function]获取人员假日计划参数");
                respone=AcsUserPlanManage.GetUserRightHolidayPlan(deviceInfoDTO, "1");
                break;
            }
            case "10033": {
                log.info("\n[Function]设置人员假日计划参数");
                respone=AcsUserPlanManage.SetUserRightHolidayPlan(deviceInfoDTO, "1");
                break;
            }
            case "10034": {
                log.info("\n[Function]获取人员假日组计划参数");
                respone=AcsUserPlanManage.GetUserRightHolidayPlanGroup(deviceInfoDTO, "1");
                break;
            }
            case "10035": {
                log.info("\n[Function]设置人员假日组计划参数");
                respone=AcsUserPlanManage.SetUserRightHolidayPlanGroup(deviceInfoDTO, "1");
                break;
            }
            case "10036": {
                log.info("\n[Function]获取人员计划模板");
                respone=AcsUserPlanManage.GetUserRightPlanTemplate(deviceInfoDTO, "1");
                break;
            }
            case "10037": {
                log.info("\n[Function]设置人员计划模板");
                respone=AcsUserPlanManage.SetUserRightPlanTemplate(deviceInfoDTO, "1");
                break;
            }




        }
        return  responMap;
    }
}
