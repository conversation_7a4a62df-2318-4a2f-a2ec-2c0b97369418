package com.yuchen.iot.connector.isapi.controller;


import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.util.concurrent.ListenableFuture;
import com.yuchen.iot.connector.isapi.entity.VisitorDto;
import com.yuchen.iot.gateway.connector.api.SouthConnector;
import com.yuchen.iot.gateway.connector.api.controller.BaseConnectorController;
import com.yuchen.iot.gateway.connector.api.controller.HttpConnectorMessage;
import com.yuchen.iot.gateway.connector.api.controller.JsonHttpConnectorMessage;
import com.yuchen.iot.gateway.dao.integration.IntegrationType;
import com.yuchen.iot.gateway.util.DateUtils;
import com.yuchen.iot.gateway.util.DonAsynchronous;
import jdk.nashorn.api.scripting.JSObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2021-07-08-15:33
 */
@Slf4j
@RestController
@RequestMapping({"/api/v1/hikDoor/http"})
public class HikDoorController  extends BaseConnectorController {

    static {
        mapper = new ObjectMapper();

    }
    private static final ObjectMapper mapper;

    /**
     * 监听接口事件解析，设备监听页面设置URL为“/test”
     * @throws ServletException
     * @throws IOException
     */
  //  @PostMapping("/test")
/*    @RequestMapping(
            value = {"/{routingKey}", "/{routingKey}/{suffix}"},
            method = {RequestMethod.POST, RequestMethod.PUT},
            consumes = {"multipart/form-data"}
    )
    public void doPost(@PathVariable("routingKey") String routingKey,
                       @PathVariable("suffix") Optional<String> suffix,@RequestHeader Map<String, String> requestHeaders,HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        //解析multipart/form-data类型报文

        this.processRequest(routingKey, suffix, new JsonHttpConnectorMessage(requestHeaders, this.convertFilesToJsonFormat(routingKey,request,response),
                new DeferredResult<>()));

    }*/

    @PostMapping("/getMessage")
    public void doPost(@RequestBody VisitorDto visitorDto) throws ServletException, IOException {
        //解析multipart/form-data类型报文
        JsonNode jsonNode = mapper.valueToTree(visitorDto);

        String aa=JSONObject.toJSONString(visitorDto);
        System.out.println("aa======="+aa);
        Map<String, String> map =new HashMap<>();
        this.processRequest("hikdoor", Optional.of(".jpg"), new JsonHttpConnectorMessage(map, jsonNode,
                new DeferredResult<>()));

    }



    private DeferredResult<ResponseEntity<?>> processRequest(String routingKey, Optional<String> suffix,
                                                             HttpConnectorMessage message) {
        DeferredResult<ResponseEntity<?>> result = message.getCallback();
        ListenableFuture<SouthConnector> connectorFuture = this.api.getConnectorByRoutingKey(routingKey);
        //使用了异步回调监听,不阻碍主线程
        DonAsynchronous.withCallback(connectorFuture, (connector) -> {
            if (!this.checkConnector(result, connector, Arrays.asList(IntegrationType.HTTP))) {
                suffix.ifPresent((suffixStr) -> {
                    message.getHeaders().put("suffix", suffixStr);
                });
                this.api.process(connector, message);
            }
        }, (failure) -> {
            log.trace("[{}] Failed to fetch connector by routing key", routingKey, failure);
            result.setResult(new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR));
        }, this.api.getCallbackExecutor());
        return result;
    }



    private ObjectNode convertFilesToJsonFormat(String routingKey, HttpServletRequest request, HttpServletResponse response) throws IOException {

        FileItemFactory factory = new DiskFileItemFactory();
        ServletFileUpload sf = new ServletFileUpload(factory);
        try {
            if (!ServletFileUpload.isMultipartContent(request)) {
                throw new Exception("no multipartcontent");
            }
            ObjectNode message = mapper.createObjectNode();
            List<FileItem> formData = sf.parseRequest(request);
            for (FileItem fi : formData) {
                if (fi.getContentType().equals("application/json")) {
                    //解析报文中json
                    log.info("field_name:" + fi.getFieldName());
                    //打印json格式上传的事件报文
                    //   log.info(fi.getString("UTF-8"));
                    //通过表单name属性进行解析报文
                    switch (fi.getFieldName()) {
                        case "visitor"://门禁、身份证事件解析
                            log.info("receive Acs event");
                            JsonNode content = mapper.readTree(fi.getString("UTF-8"));
                            message.set("content", content);

                            break;
                        default:
                            log.info("unknow data");
                    }
                } else {
                    //保存报文中的图片数据
                    String image_name = fi.getName();
                    log.info("image_name:" + image_name);

                    String image_dir_path = "C:\\image\\";

                    File image_dir = new File(image_dir_path);
                    if (!image_dir.exists()) {
                        image_dir.mkdir();
                    }
                    String file_name = UUID.randomUUID().toString();
                    String suffix2 = ".jpg";
                    log.info("图片报文路径:" + image_dir_path);
                    log.info("图片名称:" + file_name);
                    System.out.println("图片格式:" + suffix2);
                    fi.write(new File(image_dir_path, file_name + suffix2));


                    try (InputStream inputStream = fi.getInputStream()) {
                        // 创建一个缓冲区来保存二进制数据
                        byte[] buffer = new byte[(int) fi.getSize()];
                        int bytesRead = inputStream.read(buffer);

                        // 确保所有数据都被读取
                        if (bytesRead != buffer.length) {
                            throw new IOException("Could not completely read file");
                        }
                        String ossKey = routingKey.concat(StrUtil.SLASH)
                                .concat(DateUtils.getFormatDate(new Date()))
                                .concat(StrUtil.SLASH).concat(file_name)
                                .concat(suffix2);

                        //异步上传文件
                        uploadPicExecutor.submit(() -> {
                            try {
                                this.fileObjectService.putObject(buffer, ossKey);
                            } catch (Exception ex) {
                                ex.printStackTrace();
                                log.error("上传图片失败: {}", ex.getMessage());
                            }
                        });
                        ArrayNode imageNode = mapper.createArrayNode();
                        imageNode.add("https://polyminio.fj-yuchen.com:9001/gateway".concat(ossKey));
                        message.set("picUrl", imageNode);
                    } catch (Exception e) {
                        e.printStackTrace(); // 应该处理或者记录日志，而不是仅仅打印堆栈跟踪
                        return null;
                    }


                }

            }
            //正常接收平台响应回复
            response.getWriter().write("success");

        return message;
        } catch (Exception e) {
            e.printStackTrace();
            response.getWriter().write("false");
        }

        return null;

    }

}
