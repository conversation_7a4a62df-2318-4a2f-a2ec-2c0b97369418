package com.yuchen.iot.gateway.connector.http.parking.isapi.parking;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.yuchen.iot.gateway.connector.api.ConnectorInitParams;
import com.yuchen.iot.gateway.connector.api.WaitCallResponseCallback;
import com.yuchen.iot.gateway.connector.api.controller.JsonHttpConnectorMessage;
import com.yuchen.iot.gateway.connector.api.data.DownLinkData;
import com.yuchen.iot.gateway.connector.api.data.UpLinkData;
import com.yuchen.iot.gateway.connector.api.data.message.Event;
import com.yuchen.iot.gateway.connector.api.data.message.EventValue;
import com.yuchen.iot.gateway.connector.api.message.ThingMessage;
import com.yuchen.iot.gateway.connector.api.parking.cache.HkWarnEventCache;
import com.yuchen.iot.gateway.connector.api.parking.cache.LockDeviceCache;
import com.yuchen.iot.gateway.connector.api.parking.constant.HttpConnectorConstant;
import com.yuchen.iot.gateway.connector.api.parking.enums.BarrierGateResCode;
import com.yuchen.iot.gateway.connector.api.util.SpecialBeanUtil;
import com.yuchen.iot.gateway.connector.http.parking.basic.BasicHttpConnector;
/*import com.yuchen.iot.gateway.connector.http.parking.cache.HkWarnEventCache;
import com.yuchen.iot.gateway.connector.http.parking.cache.LockDeviceCache;*/

import com.yuchen.iot.gateway.connector.http.parking.controller.http.HttpConnectorController;

import com.yuchen.iot.gateway.connector.http.parking.factory.HkParkingThreadFactory;
import com.yuchen.iot.gateway.util.BusinessUtil;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.PutMethod;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import org.pf4j.Extension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.InputStream;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

/**
 * 停车道闸一体机连接器(含抓拍机和显示播报器设备)型号DS-TCG205-A
 *
 * <AUTHOR>
 * @version 2.0.0
 * @date 2022/11/24 下午5:04
 */
@Extension
public class ParkingHttpConnector extends BasicHttpConnector<JsonHttpConnectorMessage> {
    private static ExecutorService deviceAlarmEventExecutor;

    static {
        deviceAlarmEventExecutor = Executors.newFixedThreadPool(20, HkParkingThreadFactory.forName("hk-device-alarm-event"));
    }

    private static final Logger log = LoggerFactory.getLogger(ParkingHttpConnector.class);

    private final ObjectMapper xmlMapper = new XmlMapper();

    private ExecutorService executorService;

    @Override
    public void init(ConnectorInitParams params) {
        super.init(params);
        this.executorService = new ThreadPoolExecutor(0, Integer.MAX_VALUE, 60L,
                TimeUnit.SECONDS, new SynchronousQueue());
        //Executors.newFixedThreadPool(1, GatewayThreadFactory.forName("down-link-processor"));
        //定时上报异常事件
        /*Executors.newSingleThreadScheduledExecutor().scheduleAtFixedRate(() -> {//todo 作为测试用 后面记得删除掉
            try {
                int randomNumber = ThreadLocalRandom.current().nextInt(0, 9); // 生成0到9之间的随机数
                buildGateAlarm("192.168.8.191",randomNumber+"");
            }catch (Exception e){
                e.printStackTrace();
            }
        }, 0, 1, TimeUnit.MINUTES);*/
        try {
            SpecialBeanUtil.registerController(HttpConnectorController.class);
        } catch (Exception e) {
            log.error("注入controller 接口失败, 原因:{}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    protected void processUpLinkData(JsonHttpConnectorMessage message) throws Exception {
        List<UpLinkData> upLinkDataList = this.convert(message);
        if (upLinkDataList != null && !upLinkDataList.isEmpty()) {
            for (UpLinkData upLinkData : upLinkDataList) {
                this.processUpLinkData(upLinkData);
                log.debug("[{}] Processing up link data", upLinkData);
            }
        }
    }

    @Override
    public void onDownLinkMessage(ThingMessage message, WaitCallResponseCallback callback) {
        if (this.downLinkConverter != null) {
            this.executorService.execute(() -> processDownLinkMessage(message, callback));
        }
    }

    private void processDownLinkMessage(ThingMessage message, WaitCallResponseCallback callback) {
        Map<String, String> mdMap = new HashMap<>(this.metadataTemplate.getKvMap());
        try {
            List<DownLinkData> result = this.downLinkConverter
                    .convertDownLink(Collections.singletonList(message), mdMap);
            if (!result.isEmpty()) {
                this.doProcessDownLinkMessage(message.getIntegrateNo(), message.getIdentifier(), result.get(0), callback);
            } else {
                log.warn("Call service converter failed, {} {}", message.getIntegrateNo(), message.getIdentifier());
            }
        } catch (Exception e) {
            log.warn("Failed to process downLink message", e);
        }
    }

    private void doProcessDownLinkMessage(String integrateNo, String identifier,
                                          DownLinkData downLink, WaitCallResponseCallback callback) throws Exception {
        long start = System.currentTimeMillis();
        log.debug("Send a remote call, integrationNo = {}, identifier = {}", integrateNo, identifier);
        if (!downLink.isEmpty()) {
            if ("manualControl".equals(identifier)) {
                Map<String, String> metadata = downLink.getMetadata();

                if (!metadata.containsKey("url")) {
                    throw new RuntimeException("url is missing in the down link metadata!");
                }
                if (!metadata.containsKey("username")) {
                    throw new RuntimeException("username is missing in the down link metadata!");
                }
                if (!metadata.containsKey("password")) {
                    throw new RuntimeException("password is missing in the down link metadata!");
                }

                String url = metadata.get("url");
                String username = metadata.get("username");
                String password = metadata.get("password");
                String command = metadata.get(integrateNo);

                String data = new String(downLink.getData(), StandardCharsets.UTF_8);
                log.info("Call request integrationNo  = {}, identifier = {}, payload = \n{}",
                        integrateNo, identifier, data);
                HttpClient httpClient = this.getOrCreateClient(integrateNo, username, password);
                //如果是关闸并且锁闸缓存有数据则需要执行解锁
                if (LockDeviceCache.getLockDeviceCache(integrateNo) != null && "0".equals(command)) {
                    StringBuilder sb = new StringBuilder();
                    sb.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
                    sb.append("<BarrierGate xmlns=\"http://www.isapi.org/ver20/XMLSchema\" version=\"2.0\">");
                    sb.append("<ctrlMode opt=\"open,close,lock,unlock\">unlock</ctrlMode>");
                    sb.append("</BarrierGate>");
                    String dataUnlock = sb.toString();
                    byte[] result = this.sendMessage(httpClient, url, dataUnlock);
                    if (result != null) {
                        LockDeviceCache.remove(integrateNo);
                        log.debug("锁闸解锁执行完：{} {} {}", integrateNo, dataUnlock, (System.currentTimeMillis() - start) + "ms");
                    }
                }
                byte[] response = this.sendMessage(httpClient, url, data);
                if (response == null) {
                    log.warn("Call service failed,  integrationNo  = {}, identifier = {}", integrateNo, identifier);
                } else {
                    if (log.isDebugEnabled()) {
                        log.debug("Call response integrationNo  = {}, identifier = {}, payload = \n{}",
                                integrateNo, identifier, xmlMapper.readTree(response).toPrettyString());
                    }
                    Map<String, String> headers = new HashMap<>();
                    headers.put("integrateNo", integrateNo);
                    headers.put("identifier", identifier);
                    List<UpLinkData> upLinkDataList = convert(new JsonHttpConnectorMessage(headers,
                            xmlMapper.readTree(response), null));
                    if (upLinkDataList != null && !upLinkDataList.isEmpty()) {
                        UpLinkData upLinkData = upLinkDataList.get(0);
                        if (upLinkData.hasCallResponse() && callback != null) {
                            callback.responseNow(upLinkData.getCallResponse());
                        }
                    } else {
                        log.warn("Response convert failed, integrateNo = {}, identifier = {}, payload =  '{}'",
                                integrateNo, identifier, xmlMapper.readTree(response).asText());
                    }
                }
                //异步处理响应告警信息
                buildAsyncUploadEvent(integrateNo, response);
            } else if ("pushLed".equals(identifier) || "pushVoice".equals(identifier)) {
                Map<String, String> metadata = downLink.getMetadata();
                String ipAddress = metadata.get("ledIpAddress");
                int port = Integer.parseInt(metadata.get("ledPort"));
                Socket socket = null;
                OutputStream outputStream = null;
                InputStream inputStream = null;
                try {
                    socket = new Socket();
                    socket.setSoTimeout(10000);
                    socket.connect(new InetSocketAddress(ipAddress, port), 2000);
                    outputStream = socket.getOutputStream();
                    inputStream = socket.getInputStream();
                    outputStream.write(downLink.getData());
                    outputStream.flush();
                    byte[] serverContent = new byte[512];
                    //read data exception may ignore,because the server has already received the data,only device connection close
                    while (inputStream.read(serverContent) != -1) {
                        byte[] dataLenArray = new byte[4];
                        System.arraycopy(serverContent, 10, dataLenArray, 0, dataLenArray.length);
                        //response code
                        int code = byteArrayToInt(dataLenArray);
                        log.debug("The response is '{}', integrationNo  = {}, identifier = {}", code, integrateNo,
                                identifier);
                        if (callback != null) {
                            Map<String, String> callResponse = new HashMap<>();
                            if (code == 1) {
                                callResponse.put("state", "1");
                            } else {
                                callResponse.put("state", "0");
                            }
                            callback.responseNow(callResponse);
                        }
                    }
                } catch (Exception e) {
                    log.warn("push led or push voice exception : {}", e.getMessage());
                } finally {
                    if (outputStream != null) {
                        outputStream.close();
                    }
                    if (inputStream != null) {
                        inputStream.close();
                    }
                    if (socket != null) {
                        socket.close();
                    }
                }
            } else if ("post".equals(identifier)) {
                JsonNode data = this.mapper.readTree(downLink.getData());
                if (data != null && data.has("workMode")) {
                    // do nothing
                }
            }
            if (log.isDebugEnabled()) {
                log.debug("The remote call finished, integrationNo  = {}, identifier = {}, ts = {}", integrateNo,
                        identifier, (System.currentTimeMillis() - start) + "ms");
            }
        }
    }

    private void buildAsyncUploadEvent(String integrateNo, byte[] response) {
        deviceAlarmEventExecutor.submit(() -> {
            try {
                String statusCode = "1";
                String subStatusCode = "";
                if (response == null) {
                    statusCode = BarrierGateResCode.GATE_STATUS_8.getStatus();
                } else {
                    JsonNode jsonNode = xmlMapper.readTree(response);
                    statusCode = jsonNode.get("statusCode").asText();
                    subStatusCode = jsonNode.get("subStatusCode").asText();
                    log.debug("道闸状态code: {}, 子状态code：{}", statusCode, subStatusCode);
                }
                buildGateAlarm(integrateNo, statusCode, subStatusCode);
            } catch (Exception e) {
                log.warn("道闸告警事件上报异常============");
            }
        });
    }

    private void buildGateAlarm(String integrateNo, String status, String subStatusCode) {
        try {
            String cacheKey = HttpConnectorConstant.DEV_BUSINESS_ALARM.concat(integrateNo).concat("hk");
            if (!BarrierGateResCode.GATE_STATUS_1.getStatus().equals(status) &&
                    !BarrierGateResCode.GATE_STATUS_0.getStatus().equals(status)) {
                Event event = new Event();
                event.setIdentifier(HttpConnectorConstant.DEV_BUSINESS_ALARM);
                EventValue eventValue = new EventValue();
                eventValue.put("message", "海康道闸告警");
                eventValue.put("content", BarrierGateResCode.findGateStatus(status).getDescription().concat("-").concat(subStatusCode));
                eventValue.put("type", "0");
                eventValue.put("event_identifier", BusinessUtil.buildEventIdentifier(event.getIdentifier()));
                event.setValue(eventValue);
                event.setTime(System.currentTimeMillis() / 1000L);
                UpLinkData upLinkData = new UpLinkData(integrateNo, event, null, null, null);
                Event eventCache = HkWarnEventCache.getEventInfo(cacheKey);
                if (eventCache == null) {
                    processUpLinkData(upLinkData);
                    HkWarnEventCache.put(cacheKey, event);
                }
            } else {
                Event eventCache = HkWarnEventCache.getEventInfo(cacheKey);
                if (eventCache != null) {
                    eventCache.setTime(System.currentTimeMillis() / 1000L);
                    eventCache.getValue().put("type", "1");
                    eventCache.getValue().put("content", BarrierGateResCode.findGateStatus(status).getDescription().concat("-").concat(subStatusCode));
                    eventCache.getValue().put("message", "海康道闸恢复正常");
                    UpLinkData upLinkData = new UpLinkData(integrateNo, eventCache, null, null, null);
                    processUpLinkData(upLinkData);
                    //移除掉缓存
                    HkWarnEventCache.remove(cacheKey);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            log.debug("海康道闸告警上报异常, integrateNo: {}", integrateNo, ex);
        }
    }

    /**
     * byte[]转int
     *
     * @param bytes 需要转换成int的数组
     * @return int值
     */
    public static int byteArrayToInt(byte[] bytes) {
        int value = 0;
        for (int i = 0; i < 4; i++) {
            int shift = (3 - i) * 8;
            value += (bytes[i] & 0xFF) << shift;
        }
        return value;
    }

    private byte[] sendMessage(HttpClient client, String url, String payloadHex) {
        try {
            PutMethod method = new PutMethod(url);
            method.setDoAuthentication(true);
            method.setRequestEntity(new StringRequestEntity(payloadHex, "application/xml", "UTF-8"));
            synchronized (client) {
                try {
                    int statusCode = client.executeMethod(method);
                    if (statusCode == 200) {
                        log.debug("ResponseEntity is2xxSuccessful");
                    } else {
                        log.warn("[{}] ResponseEntity is!2xxSuccessful", statusCode);
                    }
                    return method.getResponseBodyAsString().getBytes(method.getResponseCharSet());
                } catch (Exception e) {
                    e.printStackTrace();
                    return null;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.warn("Failed to process sendRestHttp message, Bad request.", e);
            return null;
        }
    }
}
