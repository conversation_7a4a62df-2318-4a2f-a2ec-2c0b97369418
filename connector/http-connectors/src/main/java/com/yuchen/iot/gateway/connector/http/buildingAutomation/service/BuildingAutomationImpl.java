package com.yuchen.iot.gateway.connector.http.buildingAutomation.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yuchen.iot.gateway.connector.api.data.DownLinkData;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.ParseException;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import com.yuchen.iot.gateway.connector.http.buildingAutomation.service.BuildingAutomationImpl;

import javax.crypto.Cipher;
import java.io.IOException;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import org.apache.http.client.methods.HttpGet;
import com.yuchen.iot.gateway.connector.http.buildingAutomation.entity.DeviceDetailEntity;
import java.util.List;

@Slf4j
public class BuildingAutomationImpl {

    protected final ObjectMapper mapper = new ObjectMapper();
    private ScheduledExecutorService scheduler;
    private String defaultResourceId = "3207"; // 默认资源ID
    private String defaultDeviceTypes = "DSFJ"; // 默认设备类型
    private final OkHttpClient client= new OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();


    /**
     * 用户登录
     *
     * @param username 用户名
     * @param password 密码(未加密的原始密码)
     * @param code 验证码
     * @return 登录结果JSON字符串
     */

    public String loginUser(
            String username,
            String password,
            String code
    ) {
        try {
            log.info("开始调用用户登录接口，用户名: {}", username);

            // 先获取公钥
            String publicKeyResponse = getPublicKey();
            JSONObject publicKeyJson = JSON.parseObject(publicKeyResponse);
            if (!publicKeyJson.containsKey("publicKey")) {
                log.error("获取公钥失败，无法加密密码");
                JSONObject errorResponse = new JSONObject();
                errorResponse.put("success", false);
                errorResponse.put("msg", "获取公钥失败，无法登录");
                return errorResponse.toString();
            }

            String publicKey = publicKeyJson.getString("publicKey");

            // 使用公钥加密密码
            String encryptedPassword;
            try {
                encryptedPassword = encryptByPublicKey(publicKey, password);
            } catch (Exception e) {
                log.error("密码加密失败", e);
                JSONObject errorResponse = new JSONObject();
                errorResponse.put("success", false);
                errorResponse.put("msg", "密码加密失败: " + e.getMessage());
                return errorResponse.toString();
            }

            // 构建multipart/form-data请求体
            MultipartBody.Builder builder = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("username", username)
                    .addFormDataPart("password", encryptedPassword)
                    .addFormDataPart("code", code)
                    .addFormDataPart("uuid", code); // uuid和code相同

            RequestBody body = builder.build();

            // 构建HTTP请求
            Request request = new Request.Builder()
                    .url("http://**************:8060/api/apiLogin")
                    .post(body)
                    .addHeader("Content-Type", "multipart/form-data")
                    .build();

            // 发送请求并获取响应
            try (Response response = client.newCall(request).execute()) {
                String responseBody = response.body() != null ? response.body().string() : "";
                if (!response.isSuccessful()) {
                    log.error("用户登录接口调用失败，状态码: {}", response.code());

                    // 尝试解析错误响应
                    JSONObject errorObj = new JSONObject();
                    try {
                        JSONObject respObj = JSON.parseObject(responseBody);
                        String message = respObj.getString("msg");
                        errorObj.put("success", false);
                        errorObj.put("msg", "登录失败: " + (message != null ? message : responseBody));
                    } catch (Exception ex) {
                        errorObj.put("success", false);

                        errorObj.put("msg", "用户登录接口调用失败，HTTP状态码: " + response.code());
                    }
                    return errorObj.toString();
                }

                // 请求成功，解析响应
                log.info("用户登录接口调用成功");
                return responseBody;
            }
        } catch (Exception e) {
            log.error("用户登录接口调用失败", e);
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("success", false);
            errorResponse.put("msg", "用户登录接口调用失败: " + e.getMessage());
            return errorResponse.toString();
        }
    }

    /**
     * 使用公钥加密文本
     *
     * @param publicKeyString 公钥字符串
     * @param text 待加密文本
     * @return 加密后的Base64字符串
     */
    private String encryptByPublicKey(String publicKeyString, String text) throws Exception {
        X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(Base64.decodeBase64(publicKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicKey = keyFactory.generatePublic(x509EncodedKeySpec);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        byte[] result = cipher.doFinal(text.getBytes());
        return Base64.encodeBase64String(result);
    }

    /**
     * 获取密钥
     *
     * @return 密钥JSON字符串
     */
    public String getPublicKey() {
        try {
            log.info("开始调用获取密钥接口");

            // 构建HTTP请求
            Request request = new Request.Builder()
                    .url("http://**************:8060/api/publicKey")
                    .get()
                    .addHeader("Content-Type", "application/json")
                    .build();

            // 发送请求并获取响应
            try (Response response = client.newCall(request).execute()) {
                String responseBody = response.body() != null ? response.body().string() : "";
                if (!response.isSuccessful()) {
                    log.error("获取密钥接口调用失败，状态码: {}", response.code());
                    JSONObject errorResponse = new JSONObject();
                    errorResponse.put("success", false);
                    errorResponse.put("msg", "获取密钥接口调用失败，HTTP状态码: " + response.code());
                    return errorResponse.toString();
                }

                // 请求成功，返回响应
                log.info("获取密钥接口调用成功");
                return responseBody;
            }
        } catch (Exception e) {
            log.error("获取密钥接口调用失败", e);
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("success", false);
            errorResponse.put("msg", "获取密钥接口调用失败: " + e.getMessage());
            return errorResponse.toString();
        }
    }
    /**
     * 获取设备列表
     * 
     * @return 设备详情实体类列表
     */
    public List<DeviceDetailEntity> getResourceDeviceList(String resourceId, String deviceTypes) {
        try {
            log.info("开始获取设备列表，资源ID: {}, 设备类型: {}", resourceId, deviceTypes);

            String authToken = loginUser("admin", "lanxing121!", "123");
            JSONObject tokenObj = JSON.parseObject(authToken);
            String token = tokenObj.getString("token");

            String url = String.format("http://**************:8060/api/device/api/resourceDeviceList?resourceId=%s&deviceTypes=%s",
                    resourceId, deviceTypes);

            String authHeader = token;
            if (token != null && !token.trim().isEmpty()) {
                if (!token.startsWith("Bearer ") && !token.startsWith("bearer ")) {
                    authHeader = "Bearer " + token;
                }
            }

            Request request = new Request.Builder()
                    .url(url)
                    .get()
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Authorization", authHeader)
                    .build();

            try (Response response = client.newCall(request).execute()) {
                String responseBody = response.body() != null ? response.body().string() : "";

                if (!response.isSuccessful()) {
                    log.error("获取设备列表接口调用失败，状态码: {}, 响应: {}", response.code(), responseBody);
                    return null;
                }

                log.info("获取设备列表接口调用成功");

                // 解析JSON响应为DeviceDetailEntity对象列表
                JSONObject jsonResponse = JSON.parseObject(responseBody);
                if (jsonResponse != null && jsonResponse.containsKey("data")) {
                    List<DeviceDetailEntity> deviceDetailList = JSON.parseArray(
                        jsonResponse.getString("data"),
                        DeviceDetailEntity.class
                    );
                    return deviceDetailList;
                } else {
                    log.error("响应数据格式不正确，缺少data字段");
                    return null;
                }
            }
        } catch (Exception e) {
            log.error("获取设备列表接口调用失败", e);
            return null;
        }
    }

    /**
     * 设备开关操作
     *
     * @param downLink 包含设备操作数据的下行数据对象
     * @param integrateNo 设备集成号
     * @return 返回一个包含操作结果的Map对象，其中"code"表示操作结果代码（0成功，1失败）
     */
    public Map<String, String> open(DownLinkData downLink, String integrateNo) {
        Map<String, String> result = new HashMap<>();
        try {
            log.info("开始执行设备开关操作，设备集成号: {}", integrateNo);

            // 解析下行数据中的doAction值
            JsonNode data = this.mapper.readTree(downLink.getData());
            String doAction = data.path("doAction").asText();

            log.info("设备[{}]操作参数: doAction={}", integrateNo, doAction);

            // 获取认证token
            String authToken = loginUser("admin", "lanxing121!", "123");
            JSONObject tokenObj = JSON.parseObject(authToken);
            String token = tokenObj.getString("token");

            if (token == null || token.trim().isEmpty()) {
                log.error("获取认证token失败");
                result.put("code", "1");
                result.put("msg", "获取认证token失败");
                return result;
            }

            // 构建请求URL和参数
            String dataListParam = String.format("[{\"id\":\"%s\",\"val\":\"%s\"}]", integrateNo, doAction);
            String url = "http://**************:8060/api/device/api/updateDeviceDatas";

            log.info("设备操作请求URL: {}", url);
            log.info("设备操作请求参数: dataList={}", dataListParam);

            // 构建认证头
            String authHeader = token;
            if (!token.startsWith("Bearer ") && !token.startsWith("bearer ")) {
                authHeader = "Bearer " + token;
            }

            // 构建POST请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("dataList", JSON.parseArray(dataListParam));

            RequestBody body = RequestBody.create(
                    MediaType.parse("application/json; charset=utf-8"),
                    requestBody.toString()
            );

            // 构建HTTP POST请求
            Request request = new Request.Builder()
                    .url(url)
                    .post(body)
                    .addHeader("Authorization", authHeader)
                    .addHeader("Content-Type", "application/json")
                    .build();

            // 发送请求并获取响应
            try (Response response = client.newCall(request).execute()) {
                String responseBody = response.body() != null ? response.body().string() : "";

                if (!response.isSuccessful()) {
                    log.error("设备操作接口调用失败，状态码: {}, 响应: {}", response.code(), responseBody);
                    result.put("code", "1");
                    result.put("msg", "设备操作接口调用失败，HTTP状态码: " + response.code());
                    return result;
                }

                log.info("设备操作接口调用成功，响应: {}", responseBody);

                // 解析响应判断操作结果
                try {
                    JSONObject jsonResponse = JSON.parseObject(responseBody);
                    if (jsonResponse != null) {
                        // 根据实际API响应格式判断成功或失败
                        // 假设成功时返回success字段为true或者code为200等
                        boolean success = jsonResponse.getBooleanValue("success");
                        if (success) {
                            result.put("code", "0");
                            result.put("msg", "设备操作成功");
                        } else {
                            result.put("code", "1");
                            result.put("msg", "设备操作失败: " + jsonResponse.getString("msg"));
                        }
                    } else {
                        result.put("code", "0");
                        result.put("msg", "设备操作完成");
                    }
                } catch (Exception parseEx) {
                    log.warn("解析响应JSON失败，默认认为操作成功: {}", parseEx.getMessage());
                    result.put("code", "0");
                    result.put("msg", "设备操作完成");
                }

            }

        } catch (Exception e) {
            log.error("设备操作异常，设备集成号: {}", integrateNo, e);
            result.put("code", "1");
            result.put("msg", "设备操作异常: " + e.getMessage());
        }

        return result;
    }

}
