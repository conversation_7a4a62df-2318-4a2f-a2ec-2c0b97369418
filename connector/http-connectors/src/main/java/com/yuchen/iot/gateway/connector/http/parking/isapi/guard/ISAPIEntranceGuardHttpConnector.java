package com.yuchen.iot.gateway.connector.http.parking.isapi.guard;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.yuchen.iot.gateway.connector.api.WaitCallResponseCallback;
import com.yuchen.iot.gateway.connector.api.ConnectorInitParams;
import com.yuchen.iot.gateway.connector.api.FileObjectService;
import com.yuchen.iot.gateway.connector.api.controller.JsonHttpConnectorMessage;
import com.yuchen.iot.gateway.connector.api.data.DownLinkData;
import com.yuchen.iot.gateway.connector.api.data.UpLinkData;
import com.yuchen.iot.gateway.connector.api.message.ThingMessage;
import com.yuchen.iot.gateway.connector.http.parking.basic.BasicHttpConnector;
import com.yuchen.iot.gateway.connector.http.parking.isapi.guard.acs.ISAPIACSListener;
import com.yuchen.iot.gateway.connector.http.parking.isapi.guard.acs.ISAPIEventExtractor;
import com.yuchen.iot.gateway.connector.http.parking.isapi.guard.acs.ISAPILongPollingClient;
import com.yuchen.iot.gateway.util.DateUtils;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.UsernamePasswordCredentials;
import org.apache.commons.httpclient.auth.AuthScope;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.pf4j.Extension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.async.DeferredResult;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.Executors;

/**
 * 海康 ISAPI 门禁设备连接器实现
 *
 * <AUTHOR>
 * @version 2.0.0
 * @date 2022/11/23 下午12:02
 */
@Extension
public class ISAPIEntranceGuardHttpConnector extends BasicHttpConnector<JsonHttpConnectorMessage> {

	private static final Logger log = LoggerFactory.getLogger(ISAPIEntranceGuardHttpConnector.class);

	private HttpClient client;

	private Map<String, String> endpointMeta;

	public final FileObjectService fileObjectService;


	public ISAPIEntranceGuardHttpConnector(FileObjectService fileObjectService) {
		this.fileObjectService = fileObjectService;
	}

	@Override
	public void init(ConnectorInitParams params) {
		super.init(params);
		this.client = new HttpClient();
	}


	/**
	 * 注册端点监听
	 *
	 * @param deviceKey    设备唯一KEY
	 * @param endpointMeta 设备配置数据，包含设备IP、PORT等信息
	 */
	@Override
	protected void doListenOnEndpoint(String deviceKey, Map<String, String> endpointMeta) {

		this.endpointMeta = endpointMeta;

		// 建立 HTTP 长连接
		ListeningExecutorService pool = MoreExecutors.listeningDecorator(Executors.newFixedThreadPool(4));
		ListenableFuture<Boolean> future = pool.submit(this::connect);
		future.addListener(this::connect, pool);

		// 端点回调，告知设备已经连接上线
		super.doListenOnEndpoint(deviceKey, endpointMeta);

	}

	/**
	 * 注册端点监听
	 *
	 * @param deviceKey    设备唯一KEY
	 * @param endpointMeta 设备配置数据，包含设备IP、PORT等信息
	 */
	@Override
	protected void doListenOffEndpoint(String deviceKey, Map<String, String> endpointMeta) {
		super.doListenOffEndpoint(deviceKey, endpointMeta);
	}


	/**
	 * 连接设备，设备为服务端。
	 *
	 * @return
	 */
	public boolean connect() {

		String username = endpointMeta.get("username");
		String password = endpointMeta.get("password");
		String ip = endpointMeta.get("ipAddress");
		String port = endpointMeta.get("port");
		String api = endpointMeta.get("stream-api");
		if (username == null || username.isEmpty() ||
				password == null || password.isEmpty() ||
				ip == null || ip.isEmpty() ||
				port == null || port.isEmpty()) {
			log.error("error parameter, connect acs device failed");
			return false;
		}


		String url = String.format("http://%s:%s/%s", ip, port, api);
		CredentialsProvider provider = new BasicCredentialsProvider();
		org.apache.http.auth.UsernamePasswordCredentials credentials =
				new org.apache.http.auth.UsernamePasswordCredentials(username, password);
		provider.setCredentials(org.apache.http.auth.AuthScope.ANY, credentials);

		//connect
		ISAPILongPollingClient acsLongPollingClient = new ISAPILongPollingClient(ip, port, provider);
		acsLongPollingClient.connect(url, request -> {

			System.out.println("Send Request Successfully");

		}, new ISAPIEventExtractor(new ISAPIACSListener() {

			@Override
			public String onReceiveImage(byte[] image) {

				String fileUrl = "";
				try {
					String ossKey = "acs".concat(File.separator)
							.concat(DateUtils.getFormatDate(new Date()))
							.concat(File.separator).concat(UUID.randomUUID().toString()).concat("_")
							.concat("picture");
					if (fileObjectService.putObject(image, ossKey)) {
						fileUrl = fileObjectService.getNetUrl(ossKey);
					}
				} catch (Exception e) {
					throw new RuntimeException(e);
				}
				return fileUrl;
			}

			@Override
			public void onMessage(JsonHttpConnectorMessage message) {
				process(message);
			}

		}));

		return true;
	}

	@Override
	public void onDownLinkMessage(ThingMessage message, WaitCallResponseCallback callback) {
		if (this.downLinkConverter != null) {
			this.processDownLinkMessage(message, callback);
		}
	}


	private void processDownLinkMessage(ThingMessage message, WaitCallResponseCallback callback) {
		Map<String, String> mdMap = new HashMap<>(this.metadataTemplate.getKvMap());
		try {
			List<DownLinkData> result = this.downLinkConverter
					.convertDownLink(Collections.singletonList(message), mdMap);
			if (!result.isEmpty()) {
				DownLinkData downLink = result.get(0); // 设计上只支持单个指令下发
				if (!downLink.isEmpty()) {
					Map<String, String> metadata = downLink.getMetadata();

					if (!metadata.containsKey("url")) {
						throw new RuntimeException("url is missing in the down link metadata!");
					}
					if (!metadata.containsKey("username")) {
						throw new RuntimeException("username is missing in the down link metadata!");
					}
					if (!metadata.containsKey("password")) {
						throw new RuntimeException("password is missing in the down link metadata!");
					}

					String url = metadata.get("url");
					String username = metadata.get("username");
					String password = metadata.get("password");


					ObjectMapper objectMapper = new ObjectMapper();
					JsonNode jsonNode = objectMapper.readTree(downLink.getData());


					String data = new String(downLink.getData(), StandardCharsets.UTF_8);
					System.out.println(data);

					byte[] response = this.sendMessage(username, password, url, data);
					Map<String, String> headers = new HashMap<>();
					//headers.put("productKey", message.getProductKey());
					//headers.put("deviceName", message.getDeviceName());
					headers.put("identifier", message.getIdentifier());
					headers.put("integrateNo", message.getIntegrateNo());

					ObjectMapper mapper = new ObjectMapper();


					JsonNode eventContent = mapper.readTree(response);
					System.out.println(eventContent.toString());
					List<UpLinkData> upLinkDataList = convert(new JsonHttpConnectorMessage(headers,
							eventContent, new DeferredResult<>()));
					if (upLinkDataList != null && !upLinkDataList.isEmpty()) {
						UpLinkData upLinkData = upLinkDataList.get(0);
						if (upLinkData.hasCallResponse()) {
							callback.responseNow(upLinkData.getCallResponse());
						}
					}
				}
			}
		} catch (Exception e) {
			log.warn("Failed to process downLink message", e);
		}
	}

	private byte[] sendMessage(String username, String password, String url, String payloadHex) {
		try {
			UsernamePasswordCredentials credentials = new UsernamePasswordCredentials(username, password);
			this.client.getState().setCredentials(AuthScope.ANY, credentials);
			PostMethod method = new PostMethod(url);
			method.setDoAuthentication(true);
			method.setRequestEntity(new StringRequestEntity(payloadHex, "application/json", "UTF-8"));
			int statusCode = client.executeMethod(method);
			if (statusCode == 200) {
				log.info("ResponseEntity is2xxSuccessful");
			} else {
				log.info("[{}] ResponseEntity is!2xxSuccessful", statusCode);
			}
			return method.getResponseBodyAsString().getBytes(method.getResponseCharSet());
		} catch (Exception e) {
			log.warn("Failed to process sendRestHttp message, Bad request.", e);
			return null;
		}
	}

}
