package com.yuchen.iot.gateway.connector.http.parking.basic;

import com.fasterxml.jackson.databind.JsonNode;
import com.yuchen.iot.gateway.connector.api.ConnectorInitParams;
import com.yuchen.iot.gateway.connector.api.controller.HttpConnectorMessage;
import com.yuchen.iot.gateway.connector.api.data.UpLinkData;
import com.yuchen.iot.gateway.connector.api.data.UpLinkMetaData;
import com.yuchen.iot.gateway.connector.http.AbstractHttpConnector;
import com.yuchen.iot.gateway.util.JacksonUtil;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.UsernamePasswordCredentials;
import org.apache.commons.httpclient.auth.AuthScope;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * HTTP协议基础连接器
 *
 * <AUTHOR>
 * @version 2.0.0
 * @date 2022/10/26 下午5:20
 */
public class BasicHttpConnector<T extends HttpConnectorMessage<?>> extends AbstractHttpConnector<T> {

    private static final Logger log = LoggerFactory.getLogger(BasicHttpConnector.class);
    private boolean securityEnabled = false;
    private Map<String, String> headersFilter = new HashMap<>();

    @Override
    public void init(ConnectorInitParams params) {
        super.init(params);
        JsonNode json = JacksonUtil.valueToTree(this.connector.getConfiguration());
        this.securityEnabled = json.has("enableSecurity") && json.get("enableSecurity").asBoolean();
        if (this.securityEnabled && json.has("headersFilter")) {
            JsonNode headersFilterNode = json.get("headersFilter");
            Iterator<Map.Entry<String, JsonNode>> it = headersFilterNode.fields();
            while (it.hasNext()) {
                Map.Entry<String, JsonNode> headerFilter = it.next();
                this.headersFilter.put(headerFilter.getKey(), headerFilter.getValue().asText());
            }
        }
    }

    protected ResponseEntity doProcess(T message) throws Exception {
        if (this.checkSecurity(message)) {
            this.processUpLinkData(message);
            return ResponseEntity.ok().build();
        }
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
    }

    protected boolean checkSecurity(T msg) {
        if (securityEnabled) {
            Map<String, String> requestHeaders = msg.getHeaders();
            log.trace("Validating request using the following request headers: {}", requestHeaders);
            for (Map.Entry<String, String> entry : this.headersFilter.entrySet()) {
                String value = requestHeaders.get((entry.getKey()).toLowerCase());
                if (value == null || !value.equals(entry.getValue())) {
                    return false;
                }
            }
        }
        return true;
    }

    protected void processUpLinkData(T message) throws Exception {
        List<UpLinkData> upLinkDataList = this.convert(message);
        if (upLinkDataList != null && !upLinkDataList.isEmpty()) {
            for (UpLinkData upLinkData : upLinkDataList) {
                this.processUpLinkData(upLinkData);
                log.trace("[{}] Processing up link data", upLinkData);
            }
        }
    }

    protected List<UpLinkData> convert(T message) throws Exception {
        byte[] data = message.getMessageInBytes();
        Map<String, String> mdMap = new HashMap<>(this.metadataTemplate.getKvMap());
        message.getHeaders().forEach((header, value) -> mdMap.put("Header:" + header, value));
        return this.convertToUpLinkDataList(this.connectorContext.getCallbackExecutor(), data,
                new UpLinkMetaData(message.getContentType(), mdMap));
    }
}
