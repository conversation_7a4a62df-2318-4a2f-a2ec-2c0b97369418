package com.yuchen.iot.gateway.connector.http.parking.util;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.CharsetUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * @author: 肖祥
 * @description: led工具类
 * @date: 2023/4/20 14:58
 * @version: 1.0
 */
@Slf4j
public class Rs485Util{
    /**
     * 包头：固定为：0xAA,0X55
     * 流水号：上位机确定的业务流水号，下位机返回应答时，流水号原数返回,可全为 0
     * 地址：下位机 485 地址，出厂默认 100 = 0X64
     * 保留：保留
     * 命令：CMD 参考命令集
     * 长度：2 字节，指定数据内容的长度，高字节在前，低字节在后，比如 255 个字节的长度应
     * 表述为 0x00 0xff
     * 数据内容：N 个字节
     * 校验：CRC16，2 字节,具体算法请参考附件
     * 结束：固定 0xAF
     */
    public static final String FIX_PACK_HEADER_HEX = "AA55";//固定包头 长度2
    public static final String FIX_ADDRESS_HEX = "64";//固定地址 长度1
    public static final String FIX_BUSINESS_HEX = "00";// 固定业务类型
    public static final String FIX_LED_CMD_HEX = "27";// 临显命令值

    public static final String FIX_LED_CMD_HEX_COLOR = "37";// 彩屏临显命令值
    public static final String FIX_VOICE_CMD_HEX = "22";// 语音播报
    public static final String END_MARK = "AF";//固定结束符 长度1


    public static void main(String[] args) {
        //Map<String, String> dataMap = Rs485Util.getRs485Data("您好您好", "pushLed", 1);
        //System.out.println(JSON.toJSONString(dataMap));
        //String data = new String("您好您好".getBytes( StandardCharsets.UTF_8), StandardCharsets.UTF_8);
        //System.out.println(buildLedRS485("12345",data,"pushLed"));
        //System.out.println(getDataContentToHex("您好您好"));
        String hex = Convert.toHex("您好您好", CharsetUtil.CHARSET_GBK);
        System.out.println(hex);
    }



    public static Map<String, String> buildLedRS485(String deviceId, String data, String cmd) {
        Map<String, String> ledData = new HashMap<>();
        log.debug("buildLedRS485 data: {}", data);
        String line1 = "";
        String line2 = "";
        Integer dataSize = 0;
        if (org.springframework.util.StringUtils.hasLength(data)) {
            String[] lines = data.split("<br>");
            if (lines.length == 2) {
                line1 = lines[0];
                line2 = lines[1];
            } else {
                line1 = lines[0];
            }
        }
        JSONArray jsonArrayCh1 = new JSONArray();
        if (org.springframework.util.StringUtils.hasLength(line1)) {
            JSONObject data1 = new JSONObject();
            Map<String, String> dataMap = Rs485Util.convertLedRs485Data(line1, false, 1);
            data1.put("data", dataMap.get("data"));
            dataSize += dataMap.get("size") != null ? Integer.valueOf(dataMap.get("size")) : 0;
            jsonArrayCh1.add(data1);
        }
        if (org.springframework.util.StringUtils.hasLength(line2)) {
            JSONObject data2 = new JSONObject();
            Map<String, String> dataMap = Rs485Util.convertLedRs485Data(line2, false, 2);
            data2.put("data", dataMap.get("data"));
            dataSize += dataMap.get("size") != null ? Integer.valueOf(dataMap.get("size")) : 0;
            jsonArrayCh1.add(data2);
        }
        ledData.put("data", jsonArrayCh1.toString());
        ledData.put("dataSize", String.valueOf(dataSize));
        return ledData;
    }

    /**
     * @param crc16Str:16进制crc16字符串
     * @return String
     * <AUTHOR>
     * @description 获取16进制的校验码
     * @date 2023/4/20 17:11
     */
    public static String getCrcCode(String crc16Str) {
        return StringUtils.leftPad(CRC16Util.getCRC(HexStringUtil.hexStringToBytes(crc16Str)), 4, '0');
    }

    /**
     * @param data: 平台下发的数据
     * @return String
     * <AUTHOR>
     * @description 获取16进制的crc16字符串
     * @date 2023/4/20 17:12
     */
    public static Map<String,String> getCrc16StrContent(String data,int lineNo, Boolean isColor) {
        Map<String, String> dataMap = getDataContentToHex(data,lineNo);
        String sizeHex = dataMap.get("sizeHex");
        String contentHex = dataMap.get("contentHex");
        String length = dataMap.get("length");
        String crc16StrContent = "";
        if (isColor){
            crc16StrContent = String.format("%s%s%s%s%s%s%s", getRandomNumberInRange(16, 255),
                    FIX_ADDRESS_HEX, FIX_BUSINESS_HEX, FIX_LED_CMD_HEX_COLOR,
                    sizeHex, contentHex, "0000");
        }else {
            crc16StrContent = String.format("%s%s%s%s%s%s%s", getRandomNumberInRange(16, 255),
                    FIX_ADDRESS_HEX, FIX_BUSINESS_HEX, FIX_LED_CMD_HEX,
                    sizeHex, contentHex, "0000");
        }

        Map<String,String> crc16StrMap = new HashMap<>();
        crc16StrMap.put("length",length);
        crc16StrMap.put("crc16StrContent",crc16StrContent);
        return crc16StrMap;
    }


    /**
     * @param data: 平台下发的数据
     * @return String
     * <AUTHOR>
     * @description 获取16进制的crc16字符串
     * @date 2023/4/20 17:12
     */
    public static Map<String,String> getCrc16StrVoiceContent(String data) {
        Map<String, String> dataMap = getDataContentVoiceHex(data);
        String sizeHex = dataMap.get("sizeHex");
        String contentHex = dataMap.get("contentHex");
        String length = dataMap.get("length");
        String crc16StrContent = String.format("%s%s%s%s%s%s%s", getRandomNumberInRange(16, 255), FIX_ADDRESS_HEX, FIX_BUSINESS_HEX, FIX_VOICE_CMD_HEX,
                sizeHex, contentHex, "0000");
        Map<String,String> crc16StrMap = new HashMap<>();
        crc16StrMap.put("length",length);
        crc16StrMap.put("crc16StrContent",crc16StrContent);
        return crc16StrMap;
    }


/** 该指令用于下发临显内容
 控制字 1：定义下发内容显示的行号，行号只能是 1~4，其他参数无效
 控制字 2：定义该临显内容显示的时长，单位 秒，该参数为 0 时，表示长期显示，掉
 电或者收到“取消临显指令”才恢复广告内容。
 控制字 3：定义临显的显示颜色，1-3 有效，1=红色，2=绿色，3=黄色，其他默认为 1
 控制字 4：保留。
 */
    /**
     * @param data: 平台下发的led数据
     * @return Map<String, String>
     * <AUTHOR>
     * @description Map<String, String>led内容数据:16进制长度 16进制内容
     * @date 2023/4/20 15:37
     */
    public static Map<String, String> getDataContentToHex(String data,int lineNo) {
        StringBuilder dataContent = new StringBuilder();
        int dataLength = 0;
        try {
            dataContent.append(getLineDataContent("0" + lineNo, "10", "0" + lineNo, data));
            dataLength = data.getBytes("GBK").length + 4;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("getDataContentHex {}", e.getMessage());
        }
        if (dataLength > 255) {
            throw new RuntimeException("单个数据包的长度不能超过255");
        }
        String dataSizeHex = StringUtils.leftPad(Integer.toHexString(dataLength).toUpperCase(), 4, '0');
        Map<String, String> mapData = new HashMap<>(2);
        mapData.put("sizeHex", dataSizeHex);
        mapData.put("contentHex", dataContent.toString());
        mapData.put("length", dataLength+"");
        return mapData;
    }

    /**
     * @param data: 平台下发的VOICE数据
     * @return Map<String, String>
     * <AUTHOR>
     * @description Map<String, String>led内容数据:16进制长度 16进制内容
     * @date 2023/4/20 15:37
     */
    public static Map<String, String> getDataContentVoiceHex(String data) {
        int dataLength = 0;
        String dataContent = "";
        try {
            //欢迎光临
            if(data.contains(VoiceInfoEnum.VOICE_01.getContent()) && data.length() > 4){
                String plateNo = data.substring(0,data.length()-4);
                dataLength = plateNo.getBytes("GBK").length + VoiceInfoEnum.VOICE_01.getLength();
                dataContent = getLineDataVoiceContent(plateNo)+VoiceInfoEnum.VOICE_01.getHex();
            }else if(data.contains(VoiceInfoEnum.VOICE_5F.getContent()) && data.length() > 4){ //一路顺风
                String plateNo = data.substring(0,data.length()-4);
                dataLength = plateNo.getBytes("GBK").length + VoiceInfoEnum.VOICE_5F.getLength();
                dataContent = getLineDataVoiceContent(plateNo)+VoiceInfoEnum.VOICE_5F.getHex();
                //未缴费
            }else if(data.contains(VoiceInfoEnum.VOICE_0B.getContent())
                    || data.contains(VoiceInfoEnum.VOICE_0B_1.getContent())){
                dataLength = VoiceInfoEnum.VOICE_2B.getLength()+VoiceInfoEnum.VOICE_0B.getLength();
                dataContent = VoiceInfoEnum.VOICE_2B.getHex()+VoiceInfoEnum.VOICE_0B.getHex();
            }else if(data.contains(VoiceInfoEnum.VOICE_7F19_1.getContent())){
                dataLength = VoiceInfoEnum.VOICE_7F19_1.getLength()+VoiceInfoEnum.VOICE_2B.getLength()+VoiceInfoEnum.VOICE_7F1B.getLength();
                dataContent = VoiceInfoEnum.VOICE_7F19_1.getHex()+VoiceInfoEnum.VOICE_2B.getHex()+VoiceInfoEnum.VOICE_7F1B.getHex();
            }else if(data.contains(VoiceInfoEnum.VOICE_28_1.getContent())){
                dataLength = VoiceInfoEnum.VOICE_28_1.getLength();
                dataContent = VoiceInfoEnum.VOICE_28_1.getHex();
            } else if(data.contains(VoiceInfoEnum.VOICE_2B_1.getContent())){
                dataLength = VoiceInfoEnum.VOICE_2B.getLength()+VoiceInfoEnum.VOICE_03.getLength();
                dataContent = VoiceInfoEnum.VOICE_2B.getHex()+VoiceInfoEnum.VOICE_03.getHex();
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("getDataContentHex {}", e.getMessage());
        }
        if (dataLength > 255) {
            throw new RuntimeException("单个数据包的长度不能超过255");
        }
        String dataSizeHex = StringUtils.leftPad(Integer.toHexString(dataLength).toUpperCase(), 4, '0');
        Map<String, String> mapData = new HashMap<>(2);
        mapData.put("sizeHex", dataSizeHex);
        mapData.put("contentHex", dataContent);
        mapData.put("length", dataLength+"");
        return mapData;
    }

    /**
     * @param lineNo:    行号
     * @param timeLen:   播放时长
     * @param lineColor: 行颜色
     * @param data:      行数据
     * @return String
     * <AUTHOR>
     * @description 获取一行数据
     * @date 2023/4/20 15:27
     */
    public static String getLineDataContent(String lineNo, String timeLen, String lineColor, String data) {
        return String.format("%s%s%s%s%s", lineNo, timeLen, lineColor, "00", getDataContentToHex(data));
    }




    /**
     * @param data:      行数据
     * @return String
     * <AUTHOR>
     * @description 获取一行数据
     * @date 2023/4/20 15:27
     */
    public static String getLineDataVoiceContent(String data) {
        return String.format("%s", getDataContentToHex(data));
    }

    /**
     * @param data: 构建平台下发的LED数据，获取RS485数据
     * @param isColor: 是否是全彩LED
     * @param lineNo: 行号
     * @return String
     * <AUTHOR>
     * @description 需要透传的报文数据
     * @date 2023/4/20 15:12
     */
    public static Map<String,String> convertLedRs485Data(String data, boolean isColor, int lineNo) {
        Map<String,String> dataMap = new HashMap<>();
        String crc16Str = "";
        String crc16Code = "";
        String size = "";
        Map<String,String> ledMap =  getCrc16StrContent(data,lineNo, isColor);
        crc16Str = ledMap.get("crc16StrContent");
        size = ledMap.get("length");
        crc16Code = getCrcCode(crc16Str);
        String result = String.format("%s%s%s%s", FIX_PACK_HEADER_HEX, crc16Str.substring(0, crc16Str.length() - 4), crc16Code, END_MARK);
        dataMap.put("data",result);
        dataMap.put("size",size);
        return dataMap;
    }


    /**
     * @param data: 构建平台下发的语音数据，获取RS485数据
     * @param cmd: 无意义
     * @param lineNo: 行号
     * @return String
     * <AUTHOR>
     * @description 需要透传的报文数据
     * @date 2023/4/20 15:12
     */
    public static Map<String,String> covertVoiceRs485Data(String data,String cmd,int lineNo) {
        Map<String,String> dataMap = new HashMap<>();
        String crc16Str = "";
        String crc16Code = "";
        String size = "";
        Map<String,String> voiceMap =  getCrc16StrVoiceContent(data);
        crc16Str = voiceMap.get("crc16StrContent");
        size = voiceMap.get("length");
        crc16Code = getCrcCode(crc16Str);
        String result = String.format("%s%s%s%s", FIX_PACK_HEADER_HEX, crc16Str.substring(0, crc16Str.length() - 4), crc16Code, END_MARK);
        dataMap.put("data",result);
        dataMap.put("size",size);
        return dataMap;
    }

    /**
     * @param data: 16进制的数据内容
     * @return String
     * <AUTHOR>
     * @description 获取16进制的数据内容
     * @date 2023/4/20 17:13
     */
    public static String getDataContentToHex(String data) {
        return Convert.toHex(data, CharsetUtil.CHARSET_GBK).toUpperCase();
    }


    /**
     * @param min: 16
     * @param max: 255
     * @return int
     * <AUTHOR>
     * @description 获取流水号, 返回16进制
     * @date 2023/4/20 15:02
     */
    public static String getRandomNumberInRange(int min, int max) {
        if (min >= max) {
            throw new IllegalArgumentException("max must be greater than min");
        }
        Random r = new Random();
        return Integer.toHexString(r.nextInt((max - min) + 1) + min).toUpperCase();
    }
}
