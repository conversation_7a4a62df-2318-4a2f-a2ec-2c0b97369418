package com.yuchen.iot.gateway.connector.http.parking.util;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/04/21 11:18
 * @description 语音播报枚举
 */
@Getter
public enum VoiceInfoEnum {
     VOICE_01("01", "欢迎光临",1),
     VOICE_02("02", "一路平安",1),
     VOICE_03("03", "请等待人工确认",1),
     VOICE_04("04", "余额不足",1),
     VOICE_05("05", "此车已进场",1),
     VOICE_06("06", "此车已出场",1),
     VOICE_07("07", "此车无权限",1),
     VOICE_08("08", "此车已过期",1),
     VOICE_09("09", "此车黑名单",1),
     VOICE_0A("0A", "车位已满",1),
     VOICE_0B("0B", "未缴费",1),//临停未缴费不放行
     VOICE_0B_1("0B", "占位费",1),//临停未缴费不放行
     VOICE_0C("0C", "有效期",1),
     VOICE_0D("0D", "音量",1),
     VOICE_0E("0E", "此卡",1),
     VOICE_0F("0F", "已过期",1),
     VOICE_10("10","无效",1),
     VOICE_11("11","有效",1),
     VOICE_12("12","有效期",1),
     VOICE_13("13","此车",1),
     VOICE_14("14","请入场停车",1),
     VOICE_15("15","黑名单",1),
     VOICE_16("16","记录",1),
     VOICE_17("17","下载",1),
     VOICE_18("18","成功",1),
     VOICE_19("19","失败",1),
     VOICE_1A("1A","已进场",1),
     VOICE_1B("1B","已出场",1),
     VOICE_1C("1C","无权限",1),
     VOICE_1D("1D","删除",1),
     VOICE_1E("1E","请等待",1),
     VOICE_1F("1F","人工确认",1),
     VOICE_20("20","亲情车",1),
     VOICE_21("21","临时车",1),
     VOICE_22("22","月租车",1),
     VOICE_23("23","储值车",1),
     VOICE_24("24","免费车",1),
     VOICE_25("25","未派车",1),
     VOICE_26("26","谢谢",1),
     VOICE_27("27","欢迎回家",1),
     VOICE_28("28","请通行",1),
     VOICE_28_1("28","免费时间",1),//临停免费时间
     VOICE_29("29","未授权",1),
     VOICE_2A("2A","已挂失",1),
     VOICE_2B("2B","禁止通行",1),
     VOICE_2B_1("2B1","无入场时间",1),
     VOICE_2C("2C","扣款",1),
     VOICE_2D("2D","金额",1),
     VOICE_2E("2E","停车",1),
     VOICE_2F("2F","小时",1),
     VOICE_5F("5F","一路顺风",1),
     VOICE_7F19("7F19","无车牌",2),
     VOICE_7F19_1("7F19","请在公众号",2),//请在公众号出园放行
     VOICE_7F1B("7F1B","扫码",2)
    ;

    private String hex;

    private String content;

    private int length;

    VoiceInfoEnum(String hex, String content, int length){
        this.hex = hex;
        this.content = content;
        this.length = length;
    }
    public static VoiceInfoEnum findConverterMode(String content) {
        for (VoiceInfoEnum item : VoiceInfoEnum.values()) {
            if (item.content.equals(content)) {
                return item;
            }
        }
        return null;
    }
}
