package com.yuchen.iot.gateway.connector.http.parking.isapi.guard.acs;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;



@Data
public class ISAPIEvent {

    private Map<String, String> header = new HashMap<>();

    private JsonNode content;

    private byte[] image;

    public ISAPIEvent(Map<String, String> header, JsonNode content, byte[] image ){
        this.header = header;
        this.content = content;
        this.image = image;
    }
}
