package com.yuchen.iot.gateway.connector.http.parking.isapi.guard.acs;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yuchen.iot.gateway.connector.api.controller.JsonHttpConnectorMessage;
import org.apache.commons.fileupload.MultipartStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.web.client.ResponseExtractor;
import org.springframework.web.context.request.async.DeferredResult;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 海康门禁设备数据提取与解析
 */
public class ISAPIEventExtractor implements ResponseExtractor<Void> {

    private static final Logger log = LoggerFactory.getLogger(ISAPIEventExtractor.class);
    final String boundary = "MIME_boundary";
    final String boundaryEnd = "--MIME_boundary--";
    final String contentType = "Content-Type";
    final String jsonType = "json";
    final String xmlType = "xml";
    final String imageType = "image";

    final ISAPIACSListener acsListener;

    public ISAPIEventExtractor(ISAPIACSListener ACSListener){
        this.acsListener = ACSListener;
    }

    /**
     * extract data from connection
     * @param response the HTTP response
     * @return
     * @throws IOException
     */
    @Override
    public Void extractData(ClientHttpResponse response) throws IOException {

        StringBuilder out = new StringBuilder(4096);
        InputStreamReader reader = new InputStreamReader(response.getBody(), StandardCharsets.ISO_8859_1);
        char[] buffer = new char[256];

        int charsRead;
        while ((charsRead = reader.read(buffer)) != -1) {

            try {
                out.append(buffer, 0, charsRead);
                int last = out.lastIndexOf(boundaryEnd);
                if (last != -1) {
                    String s = out.substring(0, last + boundaryEnd.length());
                    out.delete(0, last + boundaryEnd.length());
                    ByteArrayInputStream content = new ByteArrayInputStream(s.getBytes(StandardCharsets.ISO_8859_1));
                    parseSingleEvent(content);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }


    /**
     * parse acs event message
     * @param content
     */
    private void parseSingleEvent(ByteArrayInputStream content) {

        try {
            Map<String, String> eventHeader = new HashMap<>();
            ObjectMapper mapper = new ObjectMapper();
            JsonNode eventContent = mapper.readTree("");
            byte[] eventImage = new byte[0];

            MultipartStream multipartStream = new MultipartStream(content, boundary.getBytes());

            boolean nextPart = multipartStream.skipPreamble();
            while (nextPart) {
                String headerString = multipartStream.readHeaders();
                String delimeterHeader = "\r\n";
                Map<String, String> responseHeaders = new HashMap<>();
                ArrayList<String> headers = new ArrayList<String>(Arrays.asList(headerString.split(delimeterHeader)));
                for (String header : headers) {
                    String[] split = header.split(": ");
                    responseHeaders.put(split[0], split[1]);
                }

                String type = responseHeaders.get(contentType);
                if (type.contains(jsonType)) {

                    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                    multipartStream.readBodyData(byteArrayOutputStream);
                    byte[] bytes = byteArrayOutputStream.toByteArray();

                    responseHeaders.put("identifier", "AccessControllerEvent");
                    eventHeader = responseHeaders;
                    eventContent = mapper.readTree(bytes);

                } else if (type.contains(imageType)) {

                    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                    multipartStream.readBodyData(byteArrayOutputStream);
                    eventImage = byteArrayOutputStream.toByteArray();
                } else {
                    log.error("Acs event type is unsupported.");
                }

                nextPart = multipartStream.readBoundary();
            }

            if (eventHeader.size() != 0 && !eventContent.isEmpty()) {
                if (acsListener != null) {
                    acsListener.onMessage(new JsonHttpConnectorMessage(eventHeader,eventContent,new DeferredResult<>()));
                }
            }


        } catch (Exception e) {
            e.printStackTrace();

        }

    }

}
