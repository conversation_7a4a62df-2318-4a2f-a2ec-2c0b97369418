package com.yuchen.iot.gateway.connector.http.buildingAutomation;

import cn.hutool.core.util.ClassUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.yuchen.iot.gateway.connector.api.ConnectorInitParams;
import com.yuchen.iot.gateway.connector.api.WaitCallResponseCallback;
import com.yuchen.iot.gateway.connector.api.controller.JsonHttpConnectorMessage;
import com.yuchen.iot.gateway.connector.api.data.DownLinkData;
import com.yuchen.iot.gateway.connector.api.data.UpLinkData;
import com.yuchen.iot.gateway.connector.api.message.ThingMessage;
import com.yuchen.iot.gateway.connector.api.util.SpecialBeanUtil;
import com.yuchen.iot.gateway.connector.http.buildingAutomation.entity.DeviceDetailEntity;
import com.yuchen.iot.gateway.connector.http.buildingAutomation.service.BuildingAutomationImpl;
import com.yuchen.iot.gateway.connector.http.parking.basic.BasicHttpConnector;
import com.yuchen.iot.gateway.connector.http.tiejunDoor.controller.TiejunDoorConnectorController;
import com.yuchen.iot.gateway.connector.http.tiejunDoor.service.TiejunDoorServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.pf4j.Extension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.async.DeferredResult;

import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.*;


/**
 * 铁军门禁连接器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2024/04/13 下午1:04
 */
@Slf4j
@Extension
public class BuildingAutomationHttpConnector extends BasicHttpConnector<JsonHttpConnectorMessage> {

    //private static final Logger log = LoggerFactory.getLogger(BuildingAutomationHttpConnector.class);


    private ExecutorService executorService;

    private Map<String, Method> services = new HashMap<>();
    private Map<String, Object> serviceInstances = new HashMap<>();
    private BuildingAutomationImpl buildingAutomation = new BuildingAutomationImpl();
    private ScheduledExecutorService scheduler;
    private String defaultResourceId = "2"; // 默认资源ID
    private String defaultDeviceTypes = "TFJ20,FJ"; // 默认设备类型
    private String defaultResourceIdTwo = "3209"; // 默认资源ID
    private String defaultDeviceTypesTwo = "TFJ20,FJ"; // 默认设备类型
    private String waterPumpResourceId = "3208"; // 默认资源ID
    private String waterPumpDeviceTypes = "GPS11"; // 默认设备类型
    private String waterPumpResourceIdTwo = "3210"; // 默认资源ID
    private String waterPumpDeviceTypesTwo = "GPS11"; // 默认设备类型
    private ScheduledExecutorService schedulerGps11; // 新增

    @Override
    public void init(ConnectorInitParams params) {
        super.init(params);
        this.executorService = new ThreadPoolExecutor(0, Integer.MAX_VALUE, 60L,
                TimeUnit.SECONDS, new SynchronousQueue());

        startScheduledTask();
        startScheduledTaskGps11(); // 新增

        try {
         //   SpecialBeanUtil.registerController(TiejunDoorConnectorController.class);

            List<Class<?>> classes = Arrays.asList(BuildingAutomationImpl.class);
            for (Class<?> clazz : classes) {
                try {
                    Constructor<?> con = clazz.getConstructor();
                    Object o = con.newInstance();
                    Method[] methods = ClassUtil.getDeclaredMethods(clazz);
                    for (Method method : methods) {
                        serviceInstances.put(method.getName(), o);
                        services.put(method.getName(), method);
                    }
                } catch (Exception e) {
                    log.error("init hik door connect fail: {}", e.getMessage());
                    throw new RuntimeException(e);
                }
            }
        } catch (Exception e) {
            log.error("注入controller 接口失败, 原因:{}", e.getMessage());
            throw new RuntimeException(e);
        }




    }
    // 停止定时任务
    public void stopScheduledTask() {
        if (scheduler != null && !scheduler.isShutdown()) {
            try {
                log.info("停止定时任务");
                scheduler.shutdown();
                if (!scheduler.awaitTermination(10, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            } finally {
                scheduler = null;
            }
        }
    }

    public void startScheduledTask() {
        if (scheduler != null && !scheduler.isShutdown()) {
            log.info("已有定时任务在运行，先停止它");
            stopScheduledTask();
        }
        
        log.info("启动定时任务，5分钟后开始执行，每10分钟查询设备列表一次，resourceId: {}, deviceTypes: {}", defaultResourceId, defaultDeviceTypes);
        scheduler = Executors.newSingleThreadScheduledExecutor();
        scheduler.scheduleAtFixedRate(() -> {
            try {
                log.info("执行定时任务：查询设备列表");
                List<DeviceDetailEntity> result = buildingAutomation.getResourceDeviceList(defaultResourceId, defaultDeviceTypes);
                log.info("风机设备列表1 (resourceId: {}, deviceTypes: {}): 获取到 {} 个设备",
                        defaultResourceId, defaultDeviceTypes, result != null ? result.size() : 0);
                if (result != null && !result.isEmpty()) {
                    for (int i = 0; i < result.size(); i++) {
                        DeviceDetailEntity device = result.get(i);
                        log.info("风机设备1[{}]: ID={}, 名称={}, 状态={}, 类型={}",
                                i+1, device.getId(), device.getName(), device.getCommunicateStatus(), device.getDeviceType());
                    }
                }

                List<DeviceDetailEntity> resultTwo = buildingAutomation.getResourceDeviceList(defaultResourceIdTwo, defaultDeviceTypesTwo);
                log.info("风机设备列表2 (resourceId: {}, deviceTypes: {}): 获取到 {} 个设备",
                        defaultResourceIdTwo, defaultDeviceTypesTwo, resultTwo != null ? resultTwo.size() : 0);
                if (resultTwo != null && !resultTwo.isEmpty()) {
                    for (int i = 0; i < resultTwo.size(); i++) {
                        DeviceDetailEntity device = resultTwo.get(i);
                        log.info("风机设备2[{}]: ID={}, 名称={}, 状态={}, 类型={}",
                                i+1, device.getId(), device.getName(), device.getCommunicateStatus(), device.getDeviceType());
                    }
                    result.addAll(resultTwo);
                }

                log.info("风机设备合并后总数: {} 个设备", result != null ? result.size() : 0);
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

                // 遍历设备列表，对每个设备单独处理
                if (result != null && !result.isEmpty()) {
                    int onlineCount = 0;
                    int offlineCount = 0;
                    
                    for (DeviceDetailEntity device : result) {
                        // 新增：检查设备在线状态并通知
                        String deviceId = String.valueOf(device.getId());
                        boolean isOnline = "在线".equals(device.getCommunicateStatus());
                        
                        if (isOnline) {
                            onlineCount++;
                            log.info("风机设备 [{}] 在线", deviceId);
                            
                            // 通知设备上线
                            Optional.ofNullable(this.getUpLinkDataListener(deviceId))
                                    .ifPresent(listener -> {
                                        try {
                                            listener.onSouthConnect();
                                        } catch (Exception e) {
                                            log.info("风机设备[{}]上线处理异常: {}", deviceId, e.getMessage(), e);
                                        }
                                    });
                            Thread.sleep(5000);
                            // 保留原有处理逻辑
                            ObjectNode rootNode = objectMapper.createObjectNode();
                            rootNode.set("data", objectMapper.valueToTree(device));
                            doProcess(new JsonHttpConnectorMessage(new HashMap<>(), rootNode,
                                    new DeferredResult<>()));
                            log.info("处理风机设备数据: {}", device.getId());
                        } else {
                            offlineCount++;
                            log.info("风机设备 [{}] 离线", deviceId);
                            
                            // 通知设备离线
                            Optional.ofNullable(this.getUpLinkDataListener(deviceId))
                                    .ifPresent(listener -> {
                                        try {
                                            listener.onSouthDisconnect();
                                        } catch (Exception e) {
                                            log.info("风机设备[{}]离线处理异常: {}", deviceId, e.getMessage(), e);
                                        }
                                    });
                        }
                        

                    }
                    
                    log.info("风机设备状态检查完成: 共 {} 个设备, {} 个在线, {} 个离线",
                            result.size(), onlineCount, offlineCount);
                } else {
                    log.warn("未获取到设备数据");
                }

            } catch (Exception e) {
                log.info("风机定时任务执行异常", e);
            }
        }, 5, 10, TimeUnit.MINUTES);
    }

    // 修改GPS11设备任务
    public void startScheduledTaskGps11() {
        if (schedulerGps11 != null && !schedulerGps11.isShutdown()) {
            log.info("已有GPS11定时任务在运行，先停止它");
            schedulerGps11.shutdown();
        }
        log.info("启动GPS11定时任务，5分钟后开始执行，每10分钟查询设备列表一次，resourceId: {}, deviceTypes: {}", waterPumpResourceId, waterPumpDeviceTypes);
        schedulerGps11 = Executors.newSingleThreadScheduledExecutor();
        schedulerGps11.scheduleAtFixedRate(() -> {
            try {
                log.info("执行GPS11定时任务：查询设备列表");
                List<DeviceDetailEntity> result = buildingAutomation.getResourceDeviceList(waterPumpResourceId, waterPumpDeviceTypes);
                log.info("水泵设备列表1 (resourceId: {}, deviceTypes: {}): 获取到 {} 个设备",
                        waterPumpResourceId, waterPumpDeviceTypes, result != null ? result.size() : 0);
                if (result != null && !result.isEmpty()) {
                    for (int i = 0; i < result.size(); i++) {
                        DeviceDetailEntity device = result.get(i);
                        log.info("水泵设备1[{}]: ID={}, 名称={}, 状态={}, 类型={}",
                                i+1, device.getId(), device.getName(), device.getCommunicateStatus(), device.getDeviceType());
                    }
                }

                List<DeviceDetailEntity> resultTwo = buildingAutomation.getResourceDeviceList(waterPumpResourceIdTwo, waterPumpDeviceTypesTwo);
                log.info("水泵设备列表2 (resourceId: {}, deviceTypes: {}): 获取到 {} 个设备",
                        waterPumpResourceIdTwo, waterPumpDeviceTypesTwo, resultTwo != null ? resultTwo.size() : 0);
                if (resultTwo != null && !resultTwo.isEmpty()) {
                    for (int i = 0; i < resultTwo.size(); i++) {
                        DeviceDetailEntity device = resultTwo.get(i);
                        log.info("水泵设备2[{}]: ID={}, 名称={}, 状态={}, 类型={}",
                                i+1, device.getId(), device.getName(), device.getCommunicateStatus(), device.getDeviceType());
                    }
                    result.addAll(resultTwo);
                }

                log.info("水泵设备合并后总数: {} 个设备", result != null ? result.size() : 0);
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

                if (result != null && !result.isEmpty()) {
                    int onlineCount = 0;
                    int offlineCount = 0;
                    
                    for (DeviceDetailEntity device : result) {
                        // 新增：检查设备在线状态并通知
                        String deviceId = String.valueOf(device.getId());
                        boolean isOnline = "在线".equals(device.getCommunicateStatus());
                        
                        if (isOnline) {
                            onlineCount++;
                            log.info("水泵设备 [{}] 在线", deviceId);
                            
                            // 通知设备上线
                            Optional.ofNullable(this.getUpLinkDataListener(deviceId))
                                    .ifPresent(listener -> {
                                        try {
                                            listener.onSouthConnect();
                                        } catch (Exception e) {
                                            log.info("水泵设备[{}]上线处理异常: {}", deviceId, e.getMessage(), e);
                                        }
                                    });
                            // 保留原有处理逻辑
                            Thread.sleep(2000);
                            ObjectNode rootNode = objectMapper.createObjectNode();
                            rootNode.set("data", objectMapper.valueToTree(device));
                            doProcess(new JsonHttpConnectorMessage(new HashMap<>(), rootNode, new DeferredResult<>()));
                            log.info("处理水泵设备数据: {}", device.getId());
                        } else {
                            offlineCount++;
                            log.info("水泵设备 [{}] 离线", deviceId);
                            
                            // 通知设备离线
                            Optional.ofNullable(this.getUpLinkDataListener(deviceId))
                                    .ifPresent(listener -> {
                                        try {
                                            listener.onSouthDisconnect();
                                        } catch (Exception e) {
                                            log.info("水泵设备[{}]离线处理异常: {}", deviceId, e.getMessage(), e);
                                        }
                                    });
                        }
                        

                    }
                    
                    log.info("水泵设备状态检查完成: 共 {} 个设备, {} 个在线, {} 个离线",
                            result.size(), onlineCount, offlineCount);
                } else {
                    log.warn("未获取到水泵设备数据");
                }
            } catch (Exception e) {
                log.info("水泵定时任务执行异常", e);
            }
        }, 5, 10, TimeUnit.MINUTES);
    }


    @Override
    protected ResponseEntity doProcess(JsonHttpConnectorMessage message) throws Exception {
        this.processUpLinkData(message);
        JSONObject jsonObject = new JSONObject();
        return ResponseEntity.ok().body(jsonObject);
    }

    @Override
    protected void doListenOnEndpoint(String deviceKey, Map<String, String> endpointMeta) {
       // this.getUpLinkDataListener(deviceKey).onSouthConnect();
    }

    @Override
    protected void doListenOffEndpoint(String deviceKey, Map<String, String> endpointMeta) {
       // this.getUpLinkDataListener(deviceKey).onSouthDisconnect();
    }


    protected void processUpLinkData(JsonHttpConnectorMessage message) throws Exception {
        List<UpLinkData> upLinkDataList = this.convert(message);
        if (upLinkDataList != null && !upLinkDataList.isEmpty()) {
            for (UpLinkData upLinkData : upLinkDataList) {
                this.processUpLinkData(upLinkData);
                log.debug("[{}] Processing up link data", upLinkData);
            }
        }
    }

    @Override
    public void onDownLinkMessage(ThingMessage message, WaitCallResponseCallback callback) {
        if (this.downLinkConverter != null) {
            this.executorService.execute(() -> processDownLinkMessage(message, callback));
        }
    }

    private void processDownLinkMessage(ThingMessage message, WaitCallResponseCallback callback) {
        Map<String, String> mdMap = new HashMap<>(this.metadataTemplate.getKvMap());
        try {
            List<DownLinkData> result = this.downLinkConverter
                    .convertDownLink(Collections.singletonList(message), mdMap);
            if (!result.isEmpty()) {
                this.doProcessDownLinkMessage(message.getIntegrateNo(), message.getIdentifier(), result.get(0), callback);
            } else {
                log.warn("Call service converter failed, {} {}", message.getIntegrateNo(), message.getIdentifier());
            }
        } catch (Exception e) {
            log.warn("Failed to process downLink message", e);
        }
    }

    private void doProcessDownLinkMessage(String integrateNo, String identifier,
                                          DownLinkData downLink, WaitCallResponseCallback callback) throws Exception {
        Map<String, String> mdMap = new HashMap<>(this.metadataTemplate.getKvMap());
        String url=mdMap.get("webUrl");
        log.debug("Send a remote call, integrationNo = {}, identifier = {}", integrateNo, identifier);
        if (integrateNo == null || integrateNo.isEmpty()){
            return;
        }
        Object o = serviceInstances.get(identifier);
        Method method = services.get(identifier);
        if (o == null || method == null) {
            log.error("========command not found=======");
            return;
        }
        Map<String, String> response = (Map<String, String>) method.invoke(o, downLink,url);
        callback.responseNow(response);
    }

}
