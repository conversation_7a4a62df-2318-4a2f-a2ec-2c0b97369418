package com.yuchen.iot.gateway.netty;

import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.timeout.IdleStateHandler;

import java.util.concurrent.TimeUnit;


/**
 * netty服务channel初始化服务
 * <AUTHOR>
 * @date 2022/12/07 15:29
 */
public class ServerHikChannelInitializer extends ChannelInitializer<SocketChannel> {

	@Override
	protected void initChannel(SocketChannel ch) throws Exception {
		ChannelPipeline pipeline = ch.pipeline();
		//发送消息格式
		pipeline.addLast("encoder", new HexEncoder());
		//接收消息格式
		pipeline.addLast("decoder", new HexDecoder());
		pipeline.addLast(new IdleStateHandler(180, 0, 0, TimeUnit.SECONDS));
		pipeline.addLast("handler", new BusinessHikChannelHandler());
	}
}
