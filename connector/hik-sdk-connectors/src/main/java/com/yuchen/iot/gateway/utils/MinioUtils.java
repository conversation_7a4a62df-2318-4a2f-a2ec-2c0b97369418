package com.yuchen.iot.gateway.utils;

import io.minio.*;
import io.minio.http.Method;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @date 2022/7/4 17:26
 * @Description
 */
public class MinioUtils {

    private static String ak = "cm9vdA==";

    private static String sk = "dGhpbmt1bmlvbg==";

    private static String endPoint = "http://*************:9000/";

    private static String prefix = endPoint + "torch/";

    private static String bucketName = "torch";

    public static MinioClient getMinioClient() {
        return minioClient;
    }

    private static MinioClient minioClient;

    static {
        minioClient = MinioClient.builder()
                .endpoint(endPoint)
                .credentials(cn.hutool.core.codec.Base64.decodeStr(ak),
                        cn.hutool.core.codec.Base64.decodeStr(sk))
                .build();
        try {
            if (!bucketExists(bucketName)) {
                makeBucket();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 检查存储桶是否存在
     *
     * @return
     */
    public static boolean bucketExists(String bucketName) throws Exception {
        boolean flag = false;
        flag = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
        if (flag) {
            return true;
        }
        return false;
    }

    /**
     * 创建存储桶
     */
    public static boolean makeBucket() throws Exception {
        boolean flag = bucketExists(bucketName);
        if (!flag) {
            minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
            return true;
        } else {
            return false;
        }
    }

    public static boolean putObject(InputStream inputStream, String objectName) throws Exception {
        minioClient.putObject(PutObjectArgs.builder().bucket(bucketName).object(objectName).stream(inputStream,
                inputStream.available(), -1).contentType("image/jpg").build());
        return true;
    }

    public static String getObjectUrl(String objectName) {
        return prefix + objectName;
    }

    public static String getDownloadUrl(String objectName) throws Exception {
        return minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                .bucket(bucketName).object(objectName).method(Method.GET).build());
    }

    /**
     * 解析二进制格式图片上传至minio
     * @param objectName
     * @param pictureBuffer
     * @return
     */
    public static String uploadPictureBuffer(String objectName, byte[] pictureBuffer) {
        InputStream inputStream = null;
        String picUrl = "";
        try {
            inputStream = new ByteArrayInputStream(pictureBuffer);
            MinioUtils.putObject(inputStream, objectName);
            picUrl = getObjectUrl(objectName);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return picUrl;
    }

    public static void main(String[] args) throws Exception {
        //image 66.jpg
        // image 78.jpg
        // people_comparison_20220718162307.jpg
        System.out.println(getDownloadUrl("people_comparison_20220718162307.jpg"));
    }

}
