package com.yuchen.iot.gateway.netty;

import com.yuchen.iot.gateway.utils.HexStringUtil;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * netty服务业务处理类
 * <AUTHOR>
 * @date 2022/12/07 15:29
 */
public class BusinessHikChannelHandler extends SimpleChannelInboundHandler {
	private Logger logger = LoggerFactory.getLogger(this.getClass());

	@Override
	protected void channelRead0(ChannelHandlerContext ctx,Object msg) throws Exception {
		Channel channel = ctx.channel();
       String msgIp = HexStringUtil.hexStrToStr(msg.toString());
		logger.debug("LED设备IP：{} ,channel: {}", msgIp, channel);
		LedChannelMap.put(msgIp,channel);
	}

	@Override
	public void handlerAdded(ChannelHandlerContext ctx) throws Exception {
		super.handlerAdded(ctx);
		Channel incoming = ctx.channel();
		logger.debug("add connect:" + incoming.remoteAddress());
	}

	@Override
	public void handlerRemoved(ChannelHandlerContext ctx) throws Exception {
		super.handlerRemoved(ctx);
		Channel incoming = ctx.channel();
		logger.debug("remove connect:" + incoming.remoteAddress());
	}


	/**
	 * 当Channel变成活跃状态时被调用；Channel是连接/绑定、就绪的
	 */
	@Override
	public void channelActive(ChannelHandlerContext ctx) throws Exception {
		super.channelActive(ctx);
		Channel incoming = ctx.channel();
		logger.debug("channelActive:" + incoming.remoteAddress() + "在线");
	}

	/**
	 * Channel未连接到远端
	 */
	@Override
	public void channelInactive(ChannelHandlerContext ctx) throws Exception { // (6)
		Channel incoming = ctx.channel();
		logger.debug("channelInactive:" + incoming.remoteAddress() + "掉线");
		incoming.close();
		ctx.close();
	}

	@Override
	public void channelReadComplete(ChannelHandlerContext ctx) throws Exception {
		super.channelReadComplete(ctx);
	}

	@Override
	public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
		logger.error("exceptioncaught," + ctx.channel().remoteAddress(), cause);
	}
}
