package com.yuchen.iot.gateway.common;


import com.yuchen.iot.gateway.entity.DeviceInfoData;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/7/15 18:24
 * @Description
 */
public class DeviceUserIdManage {

    public static Map<String, Integer> map = new HashMap<>();

    public static Map<String, Integer> deviceSnMap = new HashMap<>();
    /**
     * 用于撤防
     */
    public static Map<String, Integer> alarmHandleMap = new HashMap<>();

    public static void addUserId(String productKey, String deviceName, int lUserId) {

        map.put(String.format("%s&%s", productKey, deviceName), lUserId);
    }

    public static Integer getUserId(String productKey, String deviceName) {
        return map.get(String.format("%s&%s", productKey, deviceName));
    }

    public static void putDeviceSn(String deviceSn, Integer lUserId) {
        deviceSnMap.put(deviceSn, lUserId);
    }

    public static Integer getLUserId(String deviceSn) {
        Integer integer = deviceSnMap.get(deviceSn);
        return integer;
    }

    public static DeviceInfoData getByUserId(Integer userId) {
        Set<Map.Entry<String, Integer>> set = map.entrySet();
        for (Map.Entry<String, Integer> entry : set) {
            if (userId.equals(entry.getValue())) {
                String pkAndDn = entry.getKey();
                DeviceInfoData deviceInfoData = new DeviceInfoData();
                deviceInfoData.productKey = pkAndDn.split("&")[0];
                deviceInfoData.deviceName = pkAndDn.split("&")[1];
                return deviceInfoData;
            }
        }
        return null;
    }

    public static void addAlarmHandle(String productKey, String deviceName, int lUserId) {

        alarmHandleMap.put(String.format("%s&%s", productKey, deviceName), lUserId);
    }

    public static Integer getAlarmHandle(String productKey, String deviceName) {
        return alarmHandleMap.get(String.format("%s&%s", productKey, deviceName));
    }
}
