package com.yuchen.iot.gateway.common;

import cn.hutool.core.util.StrUtil;
import com.yuchen.iot.gateway.connector.api.controller.BaseConnectorController;
import com.yuchen.iot.gateway.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.UUID;

/**
 * @Author: 肖润飞
 * @CreateTime: 2023/05/04 16:07
 * @Description: 海康异步上传车辆图片工具类
 * @Version: 1.0
 */
@Slf4j
@Component
public class HikUploadPic extends BaseConnectorController {

    /**
     * 上传车辆全景图
     *
     * @param strIP        设备ip
     * @param platePic     车牌号
     * @param platePicByte 车牌图片
     */
    public String buildUploadFullPic(String strIP, String platePic, byte[] platePicByte) {
        return this.uploadPlatePic(strIP, platePic, platePicByte, "detectionPicture.jpg");
    }

    /**
     * 上传车辆牌照图
     *
     * @param strIP        设备ip
     * @param platePic     车牌号
     * @param platePicByte 车牌图片
     */
    public String buildUploadPlatePic(String strIP, String platePic, byte[] platePicByte) {
        return this.uploadPlatePic(strIP, platePic, platePicByte, "licensePlatePicture.jpg");
    }

    /**
     * 上传图片
     *
     * @param strIP
     * @param platePic
     * @param platePicByte
     */
    private String uploadPlatePic(String strIP, String platePic, byte[] platePicByte, String type) {

        if (!StrUtil.isBlank(platePic) && platePicByte.length > 0) {
            try {
                String ossKey = "/".concat(strIP).concat("/")
                        .concat(DateUtils.getFormatDate(new Date()))
                        .concat("/").concat(UUID.randomUUID().toString())
                        .concat("_")
                        .concat(type);
                //异步上传车牌文件
                buildAsyncUpload(platePicByte, ossKey);
                return ossKey;
            } catch (Exception ex) {
                log.error("上传文件异常", ex);
            }
        }
        return "";
    }

    private void buildAsyncUpload(byte[] decode, String ossKey) {
        uploadPicExecutor.submit(() -> {
            try {
                this.fileObjectService.putObject(decode, ossKey);
            } catch (Exception ex) {
                ex.printStackTrace();
                log.error("上传全景图片失败: {}", ex.getMessage());
            }
        });
    }
}