package com.yuchen.iot.gateway.netty;

import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @description 管理led的Channel
 * @date 2023/5/17 14:03
 */
public class LedChannelMap {
    private static final Logger logger = LoggerFactory.getLogger(LedChannelMap.class);

    private static Map<String, Channel> ledChannelMap = new ConcurrentHashMap<>(30);

    public static void put(String sn, Channel channel){
        logger.debug("LED设备channel存储 {} {}", sn, channel);
        ledChannelMap.put(sn,channel);
    }

    public static  Channel getLedChannel(String sn){
        if(StringUtils.isEmpty(sn)){
            return null;
        }
        logger.debug("获取LED设备信息 {}", sn);
        return ledChannelMap.get(sn);
    }

    public static void remove(String sn){
        logger.debug("移除LED设备信息 {}", sn);
        ledChannelMap.remove(sn);
    }

    public static Map<String, Channel> getAllLedMap(){
         return ledChannelMap;
    }

}
