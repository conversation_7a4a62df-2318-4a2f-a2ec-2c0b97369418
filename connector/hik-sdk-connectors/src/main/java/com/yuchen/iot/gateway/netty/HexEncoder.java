package com.yuchen.iot.gateway.netty;

import com.yuchen.iot.gateway.utils.HexStringUtil;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;

/**
 * @author: 肖祥
 * @description: 16进制的编码
 * @date: 2023/5/18 11:53
 * @version: 1.0
 */
public class HexEncoder extends MessageToByteEncoder<String> {

    @Override
    protected void encode(ChannelHandlerContext channelHandlerContext, String s, ByteBuf byteBuf) throws Exception {
        //将16进制字符串转为数组
        byteBuf.writeBytes(HexStringUtil.hexStringToBytes(s));
    }
}
