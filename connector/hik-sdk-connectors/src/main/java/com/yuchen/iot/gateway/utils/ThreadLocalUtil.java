package com.yuchen.iot.gateway.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;


@Slf4j
@SuppressWarnings("unchecked")
public class ThreadLocalUtil {
    private static final ThreadLocal<Map<String, Object>> LOCAL = ThreadLocal.withInitial(HashMap::new);


    public static Map<String, Object> getAll() {
        return new HashMap<>(LOCAL.get());
    }


    public static <T> T put(String key, T value) {
        LOCAL.get().put(key, value);
        log.info("设置值 ==> {} {}", key, value);
        return value;
    }


    public static void put(Map<String, Object> map) {
        LOCAL.get().putAll(map);
    }


    public static void remove(String key) {
        LOCAL.get().remove(key);
    }


    public static void clear() {
        log.info("清空值 == >{}", LOCAL);
        LOCAL.remove();
    }

    @Nullable
    public static <T> T get(String key) {
        T obj = (T) LOCAL.get().get(key);
        log.trace("获取值 ==> {} {}", key, obj);
        return obj;
    }


    @Nullable
    public static <T> T getIfAbsent(String key, Supplier<T> supplierOnNull) {
        return ((T) LOCAL.get().computeIfAbsent(key, k -> supplierOnNull.get()));
    }


    public static <T> T getAndRemove(String key) {
        try {
            return get(key);
        } finally {
            remove(key);
        }
    }

}
