package com.yuchen.iot.gateway.common;

import com.sun.jna.Native;
import com.sun.jna.ptr.IntByReference;
import com.yuchen.iot.gateway.connect.connector.HikSdkConnector;
import com.yuchen.iot.gateway.dao.integration.Device;
import com.yuchen.iot.gateway.entity.DeviceLoginInfo;
import com.yuchen.iot.gateway.sdk.FMSGCallBack_V31;
import com.yuchen.iot.gateway.sdk.HCNetSDK;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/4 13:45
 * @Description
 */
public class HCNetSDKManage {

    private static final Logger logger = LoggerFactory.getLogger(HCNetSDKManage.class);
    private static HCNetSDK hcNetSDK = null;
    static int lUserID = -1;//用户句柄
    static int lAlarmHandle = -1; //布防句柄
    static int lListenHandle = -1; //监听句柄


    public static HCNetSDK getHCNetSDKInstance() {
        return hcNetSDK;
    }

    /**
     * 初始化海康设备sdk
     *
     * @return
     */
    public static boolean initSdk() {
        if (HCNetSDKManage.getHCNetSDKInstance() == null) {
            if (!CreateSDKInstance()) {
                System.out.println("Load SDK fail");
                return false;
            }
        }
        //linux系统建议调用以下接口加载组件库
        if (OsSelect.isLinux()) {
            HCNetSDK.BYTE_ARRAY ptrByteArray1 = new HCNetSDK.BYTE_ARRAY(256);
            HCNetSDK.BYTE_ARRAY ptrByteArray2 = new HCNetSDK.BYTE_ARRAY(256);
            //这里是库的绝对路径，请根据实际情况修改，注意改路径必须有访问权限
            String strPath1 = System.getProperty("user.dir") + "/lib/linux/libcrypto.so.1.1";
            String strPath2 = System.getProperty("user.dir") + "/lib/linux/libssl.so.1.1";

            System.arraycopy(strPath1.getBytes(), 0, ptrByteArray1.byValue, 0, strPath1.length());
            ptrByteArray1.write();
            HCNetSDKManage.getHCNetSDKInstance().NET_DVR_SetSDKInitCfg(3, ptrByteArray1.getPointer());

            System.arraycopy(strPath2.getBytes(), 0, ptrByteArray2.byValue, 0, strPath2.length());
            ptrByteArray2.write();
            hcNetSDK.NET_DVR_SetSDKInitCfg(4, ptrByteArray2.getPointer());

            String strPathCom = System.getProperty("user.dir") + "/lib/linux";
            HCNetSDK.NET_DVR_LOCAL_SDK_PATH struComPath = new HCNetSDK.NET_DVR_LOCAL_SDK_PATH();
            System.arraycopy(strPathCom.getBytes(), 0, struComPath.sPath, 0, strPathCom.length());
            struComPath.write();
            hcNetSDK.NET_DVR_SetSDKInitCfg(2, struComPath.getPointer());
        }
        /*初始化*/
        if (!hcNetSDK.NET_DVR_Init()) {
            logger.info("----------SDK初始化失败--------------");
            return false;
        }
        logger.info("----------SDK初始化成功--------------");
        return true;
    }

    /**
     * 根据不同操作系统选择不同的库文件和库路径
     *
     * @return
     */
    private static boolean CreateSDKInstance() {
        if (hcNetSDK == null) {
            synchronized (HCNetSDK.class) {
                String strDllPath = "";
                try {
                    //System.setProperty("jna.debug_load", "true");
                    if (OsSelect.isWindows()) {
                        //win系统加载库路径
                        strDllPath = System.getProperty("user.dir") + "\\lib\\windows\\HCNetSDK.dll";
                    } else if (OsSelect.isLinux()) {
                        //Linux系统加载库路径
                        strDllPath = System.getProperty("user.dir") + "/lib/linux/libhcnetsdk.so";
                    }
                    logger.info("系统加载路径 strDllPath  {}", strDllPath);
                    hcNetSDK = (HCNetSDK) Native.loadLibrary(strDllPath, HCNetSDK.class);
                } catch (Exception ex) {
                    logger.error("loadLibrary: {}  Error: {}", strDllPath, ex.getMessage());
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 设备登录
     *
     * @param deviceLoginInfo
     * @return
     * @throws UnsupportedEncodingException
     */
    public static int login(DeviceLoginInfo deviceLoginInfo) throws UnsupportedEncodingException {//todo 网关
        //注册
        //设备登录信息
        HCNetSDK.NET_DVR_USER_LOGIN_INFO m_strLoginInfo = new HCNetSDK.NET_DVR_USER_LOGIN_INFO();
        //设备ip地址
        String m_sDeviceIP = deviceLoginInfo.ip;
        m_strLoginInfo.sDeviceAddress = new byte[HCNetSDK.NET_DVR_DEV_ADDRESS_MAX_LEN];
        System.arraycopy(m_sDeviceIP.getBytes(), 0, m_strLoginInfo.sDeviceAddress, 0, m_sDeviceIP.length());
        //设备用户名
        String m_sUsername = deviceLoginInfo.username;
        m_strLoginInfo.sUserName = new byte[HCNetSDK.NET_DVR_LOGIN_USERNAME_MAX_LEN];
        System.arraycopy(m_sUsername.getBytes(), 0, m_strLoginInfo.sUserName, 0, m_sUsername.length());
        //设备密码
        String m_sPassword = deviceLoginInfo.password;
        m_strLoginInfo.sPassword = new byte[HCNetSDK.NET_DVR_LOGIN_PASSWD_MAX_LEN];
        System.arraycopy(m_sPassword.getBytes(), 0, m_strLoginInfo.sPassword, 0, m_sPassword.length());
        //sdk端口
        m_strLoginInfo.wPort = deviceLoginInfo.port;
        //是否异步登录：0- 否，1- 是
        m_strLoginInfo.bUseAsynLogin = false;
        //登录模式：0- SDK私有协议，1- ISAPI协议
        m_strLoginInfo.byLoginMode = 0;  //SDK登录
        m_strLoginInfo.write();
        //设备信息
        HCNetSDK.NET_DVR_DEVICEINFO_V40 m_strDeviceInfo = new HCNetSDK.NET_DVR_DEVICEINFO_V40();
        int lUserID = hcNetSDK.NET_DVR_Login_V40(m_strLoginInfo, m_strDeviceInfo);
        if (lUserID == -1) {
            logger.info("[{}] 登录失败，错误码为 {}", m_sDeviceIP, hcNetSDK.NET_DVR_GetLastError());
            return -1;
        } else {
            logger.info("[{}]: 设备登录成功, lUserId: {}, 设备序列号: {}", m_sDeviceIP, lUserID, new String(
                    m_strDeviceInfo.struDeviceV30.sSerialNumber)
                    .trim());
            m_strDeviceInfo.read();
            logger.info("iCharEncodeType： {}", m_strDeviceInfo.byCharEncodeType);
            DeviceUserIdManage.addUserId(deviceLoginInfo.productKey, deviceLoginInfo.deviceName, lUserID);

            // 从连接器中获得deviceSn 设置缓存
            List<Device> devices = HikSdkConnector.getInstance().getConfiguration().getDevices();
            if (devices != null && devices.size() > 0) {
                for (Device device : devices) {
                    if (deviceLoginInfo.deviceName.equals(device.getDeviceName())) {
                        DeviceUserIdManage.putDeviceSn(device.getIntegrateNo(), lUserID);
                    }
                }
            }
            return lUserID;

        }
    }

    /**
     * 布防
     *
     * @param lUserId
     * @param deviceLoginInfo
     */
    public static boolean setupAlarmChan(int lUserId, DeviceLoginInfo deviceLoginInfo) {
        // 设置布防参数
        //报警布防参数设置
        HCNetSDK.NET_DVR_SETUPALARM_PARAM m_strAlarmInfo = new HCNetSDK.NET_DVR_SETUPALARM_PARAM();
        m_strAlarmInfo.dwSize = m_strAlarmInfo.size();
        m_strAlarmInfo.byLevel = 1;  //布防等级
        m_strAlarmInfo.byAlarmInfoType = 1;   // 智能交通报警信息上传类型：0- 老报警信息（NET_DVR_PLATE_RESULT），1- 新报警信息(NET_ITS_PLATE_RESULT)
        m_strAlarmInfo.byDeployType = 1;   //布防类型：0 - 客户端布防(会断网续传),1 - 实时布防(只上传实时数据)
        m_strAlarmInfo.write();
        lAlarmHandle = hcNetSDK.NET_DVR_SetupAlarmChan_V41(lUserId, m_strAlarmInfo);
        if (lAlarmHandle == -1) {
            logger.error("--------------[{}]: 布防失败,错误码: [{}]--------------", deviceLoginInfo.ip, hcNetSDK.NET_DVR_GetLastError());
            logout(lUserId);
            return false;
        } else {
            DeviceUserIdManage.addAlarmHandle(deviceLoginInfo.productKey, deviceLoginInfo.deviceName, lAlarmHandle);
            deviceLoginInfo.setLUserId(lUserId);
            logger.info("--------------[{}]: 布防成功--------------", deviceLoginInfo.ip);
            return true;
        }
    }

    /**
     * 注销
     *
     * @param lUserId
     */
    public static void logout(int lUserId) {
        //注销
        logger.info("注销设备登陆信息 lUserId {}", lUserId);
        hcNetSDK.NET_DVR_Logout(lUserId);
    }

    /**
     * 释放SDK所有资源
     */
    public static void cleanup() {
        //释放SDK资源
        logger.info("程序关闭,释放SDK资源");
        hcNetSDK.NET_DVR_Cleanup();
    }


    /**
     * 远程操控关闸
     *
     * @param deviceId   设备ID
     * @param timeoutSec 超时时间（秒）
     * @return CodeEnum.java
     */
    public int remoteOperateGateClose(String deviceId, int timeoutSec) {
        //int code = CodeEnum.MESSAGE_46.getCode();
        try {
            // code = JS_LIB.RemoteOperateGateClose(deviceId, timeoutSec);


        } catch (Throwable e) {
            logger.warn("【JS.LOG - 远程操控关闸】 设备={} 失败", deviceId, e);
        }
        return 1;
    }

}
