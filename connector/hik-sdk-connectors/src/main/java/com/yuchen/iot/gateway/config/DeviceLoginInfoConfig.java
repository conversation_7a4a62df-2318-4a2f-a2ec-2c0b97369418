package com.yuchen.iot.gateway.config;

import com.yuchen.iot.gateway.entity.DeviceLoginInfo;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * @Author: 肖润飞
 * @CreateTime: 2023/05/11 16:47
 * @Description: TODO
 * @Version: 1.0
 */
@Configuration
@ConfigurationProperties(prefix = "hikvision")
public class DeviceLoginInfoConfig {

    private List<DeviceLoginInfo> deviceList;

    public List<DeviceLoginInfo> getDeviceList() {
        return deviceList;
    }

    public void setDeviceList(List<DeviceLoginInfo> deviceList) {
        this.deviceList = deviceList;
    }
}