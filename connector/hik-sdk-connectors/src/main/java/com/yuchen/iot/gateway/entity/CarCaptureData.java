package com.yuchen.iot.gateway.entity;

import com.yuchen.iot.gateway.sdk.HCNetSDK;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/8/5 13:53
 * @Description
 */
@Data
public class CarCaptureData {
    /**
     * 车牌号码
     */
    private byte[] sLicense;

    /**
     * 车牌字符个数
     */
    private byte byLicenseLen;

    /**
     * 车牌类型
     */
    private byte byVehicleType;

    /**
     * 车牌类型
     */
    private byte byPlateType;

    /**
     * 车牌颜色
     */
    private byte byColor;

    /**
     * 整个车牌的置信度
     */
    private byte byEntireBelieve;

    /**
     * 车牌图片二进制数据大小
     */
    private int platePicDataLen;

    /**
     * 车牌图片二进制数据缓冲
     */
    private byte[] platePictureBuffer;

    /**
     * 车辆图片二进制数据大小
     */
    private int vehiclePicDataLen;

    /**
     * 车辆图片二进制数据缓冲
     */
    private byte[] vehiclePictureBuffer;

    /**
     * 牌识区域坐标，根据位置自己从车辆图片截取车牌特写图
     */
    public HCNetSDK.NET_VCA_RECT struPlateRect;
}
