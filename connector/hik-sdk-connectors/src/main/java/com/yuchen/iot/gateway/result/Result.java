package com.yuchen.iot.gateway.result;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yuchen.iot.gateway.constant.ErrorConstant;
import com.yuchen.iot.gateway.constant.NestConstant;
import com.yuchen.iot.gateway.error.Code;
import com.yuchen.iot.gateway.error.CommCodeEnum;
import com.yuchen.iot.gateway.utils.ObjectUtil;
import com.yuchen.iot.gateway.utils.ThreadLocalUtil;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.lang.Nullable;
import org.springframework.web.util.UriUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.util.Optional;


@ToString
@NoArgsConstructor
public class Result<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    private String RequestId;

    private String SubCode;

    private boolean Success;

    private T Data;

    private String SubMessage;

    @JsonProperty("RequestId")
    public String getRequestId() {
        return RequestId;
    }

    public void setRequestId(String RequestId) {
        RequestId = RequestId;
    }

    @JsonProperty("Code")
    public String getSubCode() {
        return SubCode;
    }

    public void setSubCode(String subCode) {
        SubCode = subCode;
    }

    @JsonProperty("Success")
    public boolean getSuccess() {
        return isSuccess();
    }

    public boolean isSuccess() {
        return ErrorConstant.SUCCESS.equals(SubCode);
    }

    public void setSuccess(boolean success) {
        Success = success;
    }

    @JsonProperty("Data")
    public T getData() {
        return Data;
    }

    public void setData(T data) {
        Data = data;
    }

    @JsonProperty("Message")
    public String getSubMessage() {
        return SubMessage;
    }

    public void setSubMessage(String subMessage) {
        SubMessage = subMessage;
    }

    private Result(T data, String code, String message) {
        this.SubCode = code;
        this.Data = data;
        this.SubMessage = message;
        this.Success = ErrorConstant.SUCCESS.equals(code);
    }

    private static <T> Result<T> build(T data, String code, String message) {
        return new Result<>(data, code, message);
    }

    /**
     * 返回Result 默认 Message “操作成功”
     *
     * @param <T> T 泛型标记
     * @return Result
     */
    public static <T> Result<T> buildSuccess() {
        return buildSuccess(CommCodeEnum.ISV_OPERATE_SUCCESS.getErrorMeta().getServiceCode());
    }

    /**
     * 返回Result 默认 Message “操作成功”
     *
     * @param <T> T 泛型标记
     * @return Result
     */
    public static <T> Result<T> buildSuccess(Code serviceCode) {
        return build(null, ErrorConstant.SUCCESS, serviceCode.getMessage());
    }

    /**
     * 返回Result
     *
     * @param <T>  T 泛型标记
     * @return Result
     */
    public static <T> Result<T> buildStatus(boolean status) {
        return status ? buildSuccess() : buildFailure(CommCodeEnum.ISV_OPERATE_FAILURE.getErrorMeta().getServiceCode());
    }

    /**
     * 返回Result
     *
     * @param data 数据
     * @param <T>  T 泛型标记
     * @return Result
     */
    public static <T> Result<T> buildData(T data) {
        return buildData(data, CommCodeEnum.ISV_OPERATE_SUCCESS.getErrorMeta().getServiceCode());
    }

    /**
     * 返回Result
     *
     * @param data 数据
     * @param serviceCode  消息
     * @param <T>  T 泛型标记
     * @return Result
     */
    public static <T> Result<T> buildData(T data, Code serviceCode) {
        return build(data, ErrorConstant.SUCCESS, serviceCode.getMessage());
    }

    /**
     * 返回Result
     *
     * @param <T>        T 泛型标记
     * @return Result
     */
    public static <T> Result<T> buildFailure() {
        return buildFailure(CommCodeEnum.ISV_OPERATE_FAILURE.getErrorMeta().getServiceCode());
    }

    /**
     * 返回Result
     *
     * @param serviceCode 业务代码
     * @param <T>        T 泛型标记
     * @return Result
     */
    public static <T> Result<T> buildFailure(Code serviceCode) {
        return buildFailure(null, serviceCode.getCode(), serviceCode.getMessage());
    }

    /**
     * 返回Result
     *
     * @param code 业务代码
     * @param message 业务消息
     * @param <T>        T 泛型标记
     * @return Result
     */
    private static <T> Result<T> buildFailure(T data, String code, String message) {
        HttpServletResponse response = ThreadLocalUtil.get(NestConstant.RESPONSE_KEY);
        if (null != response && response.getHeader(ErrorConstant.X_SERVICE_ERROR_HEADER_NAME) == null) {
            response.setHeader(ErrorConstant.X_SERVICE_ERROR_HEADER_NAME,
                    String.valueOf(ErrorConstant.BIZ_ERROR_CODE));
        }
        return build(data, code, message);
    }

    /**
     * 系统发生未知错误或不可恢复错误
     *
     * @param errorMessage
     * @param <T>
     * @return
     */
    public static <T> Result<T> buildError(String errorMessage) {
        return buildError(CommCodeEnum.ISV_UNLIMITED_ERROR.getErrorMeta().getServiceCode(), errorMessage);
    }

    /**
     * 系统发生未知错误或不可恢复错误
     *
     * @param errorMessage
     * @param <T>
     * @return
     */
    public static <T> Result<T> buildError(Code serviceCode, String errorMessage) {
        HttpServletResponse response = ThreadLocalUtil.get(NestConstant.RESPONSE_KEY);
        if (null != response && response.getHeader(ErrorConstant.X_SERVICE_ERROR_HEADER_NAME) == null) {
            response.setHeader(ErrorConstant.X_SERVICE_ERROR_HEADER_NAME,
                    String.valueOf(ErrorConstant.SYSTEM_ERROR_CODE));
            response.setHeader(ErrorConstant.X_SERVICE_ERROR_MESSAGE, UriUtils.encode(errorMessage,
                    StandardCharsets.UTF_8));
        }
        return build(null, serviceCode.getCode(), serviceCode.getMessage());
    }


    /**
     * 判断返回是否为成功
     *
     * @param result Result
     * @return 是否成功
     */
    public static boolean isSuccess(@Nullable Result<?> result) {
        return Optional.ofNullable(result)
                .map(res -> ObjectUtil.nullSafeEquals(SuccessResult.SUCCESS.code, res.SubCode))
                .orElse(Boolean.FALSE);
    }

    /**
     * 判断返回是否为失败
     *
     * @param result Result
     * @return 是否失败
     */
    public static boolean isFailure(@Nullable Result<?> result) {
        return !Result.isSuccess(result);
    }
}
