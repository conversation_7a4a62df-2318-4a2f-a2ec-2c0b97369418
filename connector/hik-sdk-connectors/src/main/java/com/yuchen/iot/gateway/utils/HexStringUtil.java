package com.yuchen.iot.gateway.utils;

/**
 * @author: 肖祥
 * @description: 16进制字符串
 * @date: 2023/4/20 18:02
 * @version: 1.0
 */
public class HexStringUtil {
    public static final String CONVERT_HEX = "0123456789ABCDEF";

    /**
     * @param hexString: 16进制字符串
     * @return byte
     * <AUTHOR>
     * @description 16进制字符串转换成字节
     * @date 2023/4/20 10:32
     */
    public static byte[] hexStringToBytes(String hexString) {
        if (hexString == null || hexString.equals("")) {
            return null;
        }
        // toUpperCase将字符串中的所有字符转换为大写
        hexString = hexString.toUpperCase();
        int length = hexString.length() / 2;
        // toCharArray将此字符串转换为一个新的字符数组。
        char[] hexChars = hexString.toCharArray();
        byte[] d = new byte[length];
        for (int i = 0; i < length; i++) {
            int pos = i * 2;
            d[i] = (byte) (charToByte(hexChars[pos]) << 4
                    | charToByte(hexChars[pos + 1]));
        }
        return d;
    }

    // charToByte返回在指定字符的第一个发生的字符串中的索引，即返回匹配字符
    private static byte charToByte(char c) {
        return (byte) "0123456789ABCDEF".indexOf(c);
    }

    /**
     * @param str: 普通字符串
     * @return String
     * <AUTHOR>
     * @description 字符串转16进制字符串
     * @date 2023/4/13 18:48
     */
    public static String strToHexStr(String str) {
        char[] chars = CONVERT_HEX.toCharArray();
        StringBuilder sb = new StringBuilder("");
        byte[] bs = str.getBytes();
        int bit;
        for (int i = 0; i < bs.length; i++) {
            bit = (bs[i] & 0x0f0) >> 4;
            sb.append(chars[bit]);
            bit = bs[i] & 0x0f;
            sb.append(chars[bit]);
        }
        return sb.toString().trim();
    }


    /**
     * @param hexStr: 16进制字符串
     * @return String
     * <AUTHOR>
     * @description 16进制字符串转普通字符串
     * @date 2023/4/13 18:48
     */
    public static String hexStrToStr(String hexStr) {
        char[] hexs = hexStr.toCharArray();
        byte[] bytes = new byte[hexStr.length() / 2];
        int n;
        for (int i = 0; i < bytes.length; i++) {
            n = CONVERT_HEX.indexOf(hexs[2 * i]) * 16;
            n += CONVERT_HEX.indexOf(hexs[2 * i + 1]);
            bytes[i] = (byte) (n & 0xff);
        }
        return new String(bytes);
    }

  public static void main(String[] args) {
      //byte[] data = hexString2Bytes("30313233343536373839");
      //System.out.println(JSON.toJSONString(data));
        //System.out.println(strToHexStr("无牌车"));
      String test  = strToHexStr("*************");
      String test1 = hexStrToStr(test);
      System.out.println(test);
      System.out.println(test1);
    }
}
