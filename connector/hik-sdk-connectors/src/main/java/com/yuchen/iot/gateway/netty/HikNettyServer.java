package com.yuchen.iot.gateway.netty;

import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * netty服务
 * <AUTHOR>
 * @date 2022/12/07 15:29
 */
public class HikNettyServer{
	private Logger logger = LoggerFactory.getLogger(getClass());
	private EventLoopGroup bossGroup = null;
	private EventLoopGroup workerGroup = null;

	public void start(int port) {
		bossGroup = new NioEventLoopGroup(1);
		workerGroup = new NioEventLoopGroup();
		ServerBootstrap b = new ServerBootstrap();
		b.group(bossGroup, workerGroup)
				.channel(NioServerSocketChannel.class)
				.childHandler(new ServerHikChannelInitializer())
				.option(ChannelOption.SO_BACKLOG, 128)
				.childOption(ChannelOption.SO_KEEPALIVE, true);
		try {
			logger.info("========================》start netty server:{}", port);
			b.bind(port).sync();
		} catch (Exception e) {
			logger.error("start netty server exception.", e);
		}
		//项目关闭执行释放资源
		Runtime.getRuntime().addShutdownHook(new Thread(){
			@Override
			public void run() {
				logger.debug("netty关闭释放资源");
				// 释放资源
				if (bossGroup != null) {
					bossGroup.shutdownGracefully();
				}

				if (workerGroup != null) {
					workerGroup.shutdownGracefully();
				}
			}
		});
	}
}
