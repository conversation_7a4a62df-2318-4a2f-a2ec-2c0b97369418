package com.yuchen.iot.gateway.test.ws;

import com.yuchen.iot.gateway.connector.ws.hxzx.util.BusinessRequestUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: 肖祥
 * @description: 业务请求工具测试类
 * @date: 2023/4/13 18:27
 * @version: 1.0
 */
@Slf4j
public class BusinessRequestUtilTest {

    public static void main(String[] args) {
        testBuildRS485();
    }

    public static void testBuildRS485(){
        String deviceId = "09H5202201808273";
        String data = "欢迎观临";
        String rs485 = BusinessRequestUtil.buildLedRS485(deviceId,data,null);
        log.info("testBuildRS485：{}",rs485);
    }
}
