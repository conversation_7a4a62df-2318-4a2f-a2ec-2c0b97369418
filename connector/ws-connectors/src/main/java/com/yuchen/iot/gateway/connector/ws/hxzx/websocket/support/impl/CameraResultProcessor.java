package com.yuchen.iot.gateway.connector.ws.hxzx.websocket.support.impl;

import com.alibaba.fastjson.JSONObject;
import com.yuchen.iot.gateway.connector.api.controller.BaseConnectorController;
import com.yuchen.iot.gateway.connector.ws.GBKTextConnectorMessage;
import com.yuchen.iot.gateway.connector.ws.hxzx.HXZXWsConnector;
import com.yuchen.iot.gateway.connector.ws.hxzx.util.BusinessResponseUtil;
import com.yuchen.iot.gateway.connector.ws.hxzx.websocket.WebSocket;
import com.yuchen.iot.gateway.connector.ws.hxzx.websocket.support.RequestProcessor;
import com.yuchen.iot.gateway.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * @author: 肖祥
 * @description: 相机识别结果处理
 * @date: 2023/4/15 10:29
 * @version: 1.0
 */
@Slf4j
@Component
public class CameraResultProcessor extends BaseConnectorController implements RequestProcessor {
    @Resource
    private WebSocket webSocket;

    @Override
    public void processorRequest(JSONObject jsonObject, String deviceId) {
        String fullPic =  jsonObject.getString("full_pic");
        String platePic =  jsonObject.getString("plate_pic");
        String msgId =  jsonObject.getString("msg_id");
        Map<String,String> picsMap =  uploadPic(deviceId,fullPic,platePic);
        jsonObject.put("detectionPicture.jpg", picsMap.get("detectionPicture.jpg"));
        jsonObject.put("licensePlatePicture.jpg", picsMap.get("licensePlatePicture.jpg"));
        jsonObject.put("deviceId", deviceId);
        //上传数据到IOT平台
        HXZXWsConnector.getInstance().process(new GBKTextConnectorMessage(jsonObject.toString()));
        //结果响应
        webSocket.sendMessage(deviceId, BusinessResponseUtil.buildResultRsp(msgId));
    }

    @Override
    public String getType() {
        return "result";
    }

    /**
     * @param fullPic: 车辆全景图
     * @param platePic:  车牌图
     * @return void
     * <AUTHOR>
     * @description 上传车辆全景图和车牌图
     * @date 2023/4/13 11:17
     */
    public Map<String,String> uploadPic(String deviceId, String fullPic, String platePic){
        Map<String, String> picMap = new HashMap<>(2);
        //构建上传全景图图片
        buildUploadFullPic(deviceId, fullPic, picMap);
        //构建上传车牌图片
        buildUploadPlatePic(deviceId, platePic, picMap);
        return picMap;
    }

    private void buildUploadPlatePic(String deviceId, String platePic, Map<String, String> picMap) {
        if (StringUtils.hasLength(platePic)) {
            // 解码
            byte[] decodePlate = buildPicBytes(platePic);
            try {
                String ossKey = deviceId.concat("/")
                        .concat(DateUtils.getFormatDate(new Date()))
                        .concat("/").concat(UUID.randomUUID().toString())
                        .concat("_")
                        .concat("licensePlatePicture.jpg");
                picMap.put("licensePlatePicture.jpg", ossKey);
                //异步上传车牌文件
                buildAsyncUpload(decodePlate, ossKey);
            } catch (Exception ex) {
                log.error("上传文件异常", ex);
            }
        }
    }

    private void buildUploadFullPic(String deviceId, String fullPic, Map<String, String> picMap) {
        if (StringUtils.hasLength(fullPic)) {
            // 解码
            byte[] decode = buildPicBytes(fullPic);
            try {
                String ossKey = deviceId.concat("/")
                        .concat(DateUtils.getFormatDate(new Date()))
                        .concat("/").concat(UUID.randomUUID().toString())
                        .concat("_")
                        .concat("detectionPicture.jpg");
                picMap.put("detectionPicture.jpg", ossKey);
                //异步上传车辆文件
                buildAsyncUpload(decode, ossKey);
            } catch (Exception ex) {
                log.error("上传文件异常", ex);
            }
        }
    }

    /**
     * @param decode: 图片解码后的字节
     * @param ossKey: 图片存储路径
     * @return void
     * <AUTHOR>
     * @description 构建异步上传图片
     * @date 2023/4/14 16:34
     */
    private void buildAsyncUpload(byte[] decode, String ossKey) {
        uploadPicExecutor.submit(() -> {
            try {
                this.fileObjectService.putObject(decode, ossKey);
            } catch (Exception ex) {
                ex.printStackTrace();
                log.error("上传全景图片失败: {}", ex.getMessage());
            }
        });
    }

    /**
     * @param pic: 图片BASE64
     * @return byte
     * <AUTHOR>
     * @description 构建图片解码后的字节
     * @date 2023/4/14 16:31
     */
    private byte[] buildPicBytes(String pic) {
        Base64.Decoder decoderFull = Base64.getDecoder();
        byte[] decode = decoderFull.decode(pic);
        // 处理数据
        for (int i = 0; i < decode.length; ++i) {
            if (decode[i] < 0) {
                decode[i] += 256;
            }
        }
        return decode;
    }

}
