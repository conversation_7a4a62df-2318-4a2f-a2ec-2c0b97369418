package com.yuchen.iot.gateway.connector.ws.hxzx.websocket.support;

import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: 肖祥
 * @description: 处理持有者
 * @date: 2023/4/15 10:43
 * @version: 1.0
 */
@Component
public class ProcessorHolder {

    @Resource
    private List<RequestProcessor> requestProcessorList;

    private Map<String,RequestProcessor> processorMap = new HashMap<>();

    public RequestProcessor getProcessorHolder(String cmd){
        return processorMap.get(cmd);
    }

    /**
     * @return void
     * <AUTHOR>
     * @description 初始化持有者
     * @date 2023/4/15 10:48
     */
    @PostConstruct
    public void initProcessorHolder(){
        requestProcessorList.forEach(requestProcessor -> processorMap.put(requestProcessor.getType(),requestProcessor));
    }
}
