package com.yuchen.iot.gateway.connector.ws;

import com.fasterxml.jackson.databind.JsonNode;
import com.yuchen.iot.gateway.connector.api.data.UpLinkContentType;
import com.yuchen.iot.gateway.util.JacksonUtil;

/**
 * J<PERSON><PERSON>文本连接器消息
 * <AUTHOR>
 * @date 2023/4/12 11:29
 * @version 2.0.0
 */
public class JsonWsConnectorMessage extends WsConnectorMessage<JsonNode> {
    /**
     * channelId
     */
    private String channelId;

    private String packageId;

    public JsonWsConnectorMessage(JsonNode message) {
        super(message);
    }

    @Override
    public UpLinkContentType getContentType() {
        return UpLinkContentType.JSON;
    }

    @Override
    public byte[] getMessageInBytes() {
        return JacksonUtil.writeValueAsBytes(this.message);
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getPackageId() {
        return packageId;
    }

    public void setPackageId(String packageId) {
        this.packageId = packageId;
    }
}
