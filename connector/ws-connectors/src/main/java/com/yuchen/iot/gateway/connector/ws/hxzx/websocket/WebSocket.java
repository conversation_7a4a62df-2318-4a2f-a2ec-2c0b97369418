package com.yuchen.iot.gateway.connector.ws.hxzx.websocket;

import com.alibaba.fastjson.JSONObject;
import com.yuchen.iot.gateway.connector.ws.hxzx.cache.WebSocketSessionCache;
import com.yuchen.iot.gateway.connector.ws.hxzx.cache.WebSocketTokenCache;
import com.yuchen.iot.gateway.connector.ws.hxzx.constant.WebSocketConstant;
import com.yuchen.iot.gateway.connector.ws.hxzx.util.BusinessUtil;
import com.yuchen.iot.gateway.connector.ws.hxzx.websocket.support.ProcessorHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.websocket.OnClose;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;

/**
 * @author: 肖祥
 * @description: websocket的核心处理类
 * @date: 2023/4/12 11:29
 * @version: 1.0
 */
@Slf4j
@Component
@ServerEndpoint("/websocket/{deviceId}")
public class WebSocket {

    /**
     *  与某个客户端的连接对话，需要通过它来给客户端发送消息
     */
    private Session session;

    /**
     * 标识当前连接客户端的设备ID
     */
    private String deviceId;

    private static ProcessorHolder processorHolder;

    @Autowired
    public void setApplicationContext(ProcessorHolder processorHolder){
        WebSocket.processorHolder = processorHolder;
    }

    @OnOpen
    public void OnOpen(Session session, @PathParam(value = "deviceId") String deviceId){
        //设置websocket的缓存
        session.setMaxBinaryMessageBufferSize(WebSocketConstant.CACHE_SIZE);
        session.setMaxTextMessageBufferSize(WebSocketConstant.CACHE_SIZE);
        this.session = session;
        this.deviceId = deviceId;
        // name是用来表示唯一客户端，如果需要指定发送，需要指定发送通过name来区分
        if(WebSocketSessionCache.getDeviceSession(deviceId) != null){
            WebSocketSessionCache.remove(deviceId);
            WebSocketTokenCache.remove(deviceId);
        }
        WebSocketSessionCache.put(deviceId,this);
        WebSocketTokenCache.put(deviceId, BusinessUtil.buildUUID());
        log.info("[WebSocket] 连接成功，当前连接设备数为：={}", WebSocketSessionCache.getDeviceOnline());
    }


    @OnClose
    public void OnClose(){
        WebSocketSessionCache.remove(this.deviceId);
        WebSocketTokenCache.remove(this.deviceId);
        log.info("[WebSocket] 退出成功，当前连接设备为：={}", WebSocketSessionCache.getDeviceOnline());
    }

    @OnMessage
    public void OnMessage(String message){
        log.info("[WebSocket] 收到来自设备的消息：{}", message);
        JSONObject jsonObject = JSONObject.parseObject(message);
        String cmd = jsonObject.getString("cmd");
        //设备请求处理
        if(processorHolder.getProcessorHolder(cmd) != null){
            processorHolder.getProcessorHolder(cmd).processorRequest(jsonObject,deviceId);
        }
    }

    /**
     * 主动发送消息给设备
     * @param deviceId
     * @param message
     */
    public void sendMessage(String deviceId, String message){
        try {
            log.info("[网关] 请求发送给设备的数据为: {} {}",deviceId, message);
            if(WebSocketSessionCache.getDeviceSession(deviceId) != null){
                WebSocketSessionCache.getDeviceSession(deviceId).session.getBasicRemote().sendText(message);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}
