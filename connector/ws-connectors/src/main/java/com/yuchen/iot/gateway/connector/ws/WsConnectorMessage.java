package com.yuchen.iot.gateway.connector.ws;

import com.yuchen.iot.gateway.connector.api.data.UpLinkContentType;

import java.io.UnsupportedEncodingException;

/**
 * @author: 肖祥
 * @description: ws连接器消息基类
 * @date: 2023/4/12 11:29
 * @version: 1.0
 */
public abstract class WsConnectorMessage<T> {

    protected final T message;

    public abstract UpLinkContentType getContentType();

    public abstract byte[] getMessageInBytes() throws UnsupportedEncodingException;

    public T getMessage() {
        return this.message;
    }

    public WsConnectorMessage(T message) {
        this.message = message;
    }
}
