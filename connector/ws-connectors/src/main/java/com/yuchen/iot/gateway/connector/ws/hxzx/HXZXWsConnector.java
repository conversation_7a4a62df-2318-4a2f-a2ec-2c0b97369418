package com.yuchen.iot.gateway.connector.ws.hxzx;

import com.alibaba.fastjson.JSON;
import com.yuchen.iot.gateway.connector.api.AbstractConnector;
import com.yuchen.iot.gateway.connector.api.ConnectorInitParams;
import com.yuchen.iot.gateway.connector.api.WaitCallResponseCallback;
import com.yuchen.iot.gateway.connector.api.data.DownLinkData;
import com.yuchen.iot.gateway.connector.api.data.UpLinkData;
import com.yuchen.iot.gateway.connector.api.data.UpLinkMetaData;
import com.yuchen.iot.gateway.connector.api.message.ThingMessage;
import com.yuchen.iot.gateway.connector.ws.GBKTextConnectorMessage;
import com.yuchen.iot.gateway.connector.ws.hxzx.cache.WebSocketSessionCache;
import com.yuchen.iot.gateway.connector.ws.hxzx.cache.WsLockDeviceCache;
import com.yuchen.iot.gateway.connector.ws.hxzx.util.BusinessRequestUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 华夏智信ws连接器,主要初始化连接器,处理上行数据,处理下行数据
 * <AUTHOR>
 * @date 2023/4/12 11:29
 * @version 2.0.0
 */
public class HXZXWsConnector extends AbstractConnector<GBKTextConnectorMessage> {

    private static final HXZXWsConnector INSTANCE = new HXZXWsConnector();

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private HXZXWsConnector() {
    }

    public static HXZXWsConnector getInstance() {
        return INSTANCE;
    }

    @Override
    public void init(ConnectorInitParams params) {
        log.debug("hua xia zhi xin ConnectorInitParams ==========> {}", JSON.toJSONString(params));
        super.init(params);
    }

    @Override
    protected void doListenOnEndpoint(String deviceKey, Map<String, String> endpointMeta) {
        this.getUpLinkDataListener(deviceKey).onSouthConnect();
    }

    @Override
    protected void doListenOffEndpoint(String deviceKey, Map<String, String> endpointMeta) {
        this.getUpLinkDataListener(deviceKey).onSouthDisconnect();
    }

    @Override
    public void process(GBKTextConnectorMessage message) {
        try {
            this.processUpLinkData(message);
            this.connectorStatistics.incMessagesProcessed();
        } catch (Exception e) {
            this.connectorStatistics.incMessagesProcessed();
            log.debug("Failed to apply data converter function: {}", e.getMessage(), e);
        }
    }

    protected void processUpLinkData(GBKTextConnectorMessage message) throws Exception {
        List<UpLinkData> upLinkDataList = this.convert(message);
        if (upLinkDataList != null && !upLinkDataList.isEmpty()) {
            for (UpLinkData upLinkData : upLinkDataList) {
                this.processUpLinkData(upLinkData);
                log.trace("[{}] Processing up link data", upLinkData);
            }
        }
    }

    protected List<UpLinkData> convert(GBKTextConnectorMessage message) throws Exception {
        byte[] data = message.getMessageInBytes();
        Map<String, String> mdMap = new HashMap<>(this.metadataTemplate.getKvMap());
        return this.convertToUpLinkDataList(this.connectorContext.getCallbackExecutor(), data,
                new UpLinkMetaData(message.getContentType(), mdMap));
    }

    @Override
    public void onDownLinkMessage(ThingMessage message, WaitCallResponseCallback callback) {
        this.processDownLinkMessage(message, callback);
    }

    private void processDownLinkMessage(ThingMessage message, WaitCallResponseCallback callback) {
        Map<String, String> mdMap = new HashMap<>(this.metadataTemplate.getKvMap());
        try {
            List<DownLinkData> result = this.downLinkConverter.convertDownLink(Collections.singletonList(message),
                    mdMap);
            if (!result.isEmpty()) {
                this.doProcessDownLinkMessage(message.getIntegrateNo(), message.getIdentifier(), result.get(0),
                        callback);
            } else {
                log.warn("Call service converter failed, {} {}", message.getIntegrateNo(), message.getIdentifier());
            }
        } catch (Exception e) {
            log.warn("Failed to process downLink message", e);
        }
    }

    private void doProcessDownLinkMessage(String integrateNo, String identifier,
                                          DownLinkData downLink, WaitCallResponseCallback callback) throws Exception {
        long start = System.currentTimeMillis();
        log.debug("Send a remote call, integrationNo = {}, identifier = {}", integrateNo, identifier);
        if (!downLink.isEmpty()) {
            String data = new String(downLink.getData(), StandardCharsets.UTF_8);
            log.debug("华夏智信处理之后的下行数据 {}", data);
            String payload = "";
            if ("manualControl".equals(identifier)) {
                switch (data){
                    case "0":
                        //关闭闸门
                        payload = buildCloseGate(integrateNo, start);
                        break;
                    case "1":
                        //打开闸门
                        payload = buildOpenGate(integrateNo);
                      break;
                    case "2":
                        //构建锁闸门
                        payload = buildLock(integrateNo, data);
                        break;
                    default: break;
                }
                if (log.isDebugEnabled()) {
                    log.debug("Call response integrationNo  = {}, identifier = {}, payload = \n{}",
                            integrateNo, identifier, payload);
                }

            } else if ("pushLed".equals(identifier)) {
                log.debug("华夏智信-LED开始 integrateNo = {}", integrateNo);
                payload = data;
                //todo 网关
                WebSocketSessionCache.getDeviceSession(integrateNo).sendMessage(integrateNo, BusinessRequestUtil.buildLedRS485(integrateNo,payload,identifier));
                //WebSocketSessionCache.getDeviceSession(integrateNo).sendMessage(integrateNo,BusinessRequestUtil.buildLed(integrateNo,data));
                //WebSocketSessionCache.getDeviceSession(integrateNo).sendMessage(integrateNo,BusinessRequestUtil.buildLcd(integrateNo,data));
                log.debug("华夏智信-LED结束 integrateNo = {} payload = {}", integrateNo, payload);

            } else if ("pushVoice".equals(identifier)) {
                log.debug("华夏智信-Voice开始 integrateNo = {}", integrateNo);
                payload = data;
                //todo 网关
                WebSocketSessionCache.getDeviceSession(integrateNo).sendMessage(integrateNo,BusinessRequestUtil.buildVoiceRS485(integrateNo,payload,identifier));
                //WebSocketSessionCache.getDeviceSession(integrateNo).sendMessage(integrateNo,BusinessRequestUtil.buildLedVoice(integrateNo,data));
                //WebSocketSessionCache.getDeviceSession(integrateNo).sendMessage(integrateNo,BusinessRequestUtil.buildLcdVoice(integrateNo,data));
                log.debug("华夏智信-Voice结束 integrateNo = {} payload = {}", integrateNo, payload);
            }
            if (log.isDebugEnabled()) {
                log.debug("The remote call finished, integrationNo  = {}, identifier = {}, ts = {}", integrateNo,
                        identifier, (System.currentTimeMillis() - start) + "ms");
            }
        }
    }

    /**
     * @param integrateNo: 设备号
     * @param data: 平台下发的数据
     * @return String
     * <AUTHOR>
     * @description 构建锁闸门
     * @date 2023/4/14 15:59
     */
    private String buildLock(String integrateNo, String data) {
        String payload;
        payload = BusinessRequestUtil.buildMotorcade(integrateNo,"on");
        log.debug("华夏智信锁闸开始 integrateNo = {}", integrateNo);
        WebSocketSessionCache.getDeviceSession(integrateNo).sendMessage(integrateNo,payload);
        //设置锁闸缓存
        WsLockDeviceCache.put(integrateNo, data);
        log.debug("华夏智信锁闸结束 integrateNo = {}", integrateNo);
        return payload;
    }

    /**
     * @param integrateNo: 设备号
     * @return String
     * <AUTHOR>
     * @description 构建打开闸门
     * @date 2023/4/14 15:57
     */
    private String buildOpenGate(String integrateNo) {
        String payload;
        payload = BusinessRequestUtil.buildBarrierGate(1, integrateNo);
        log.debug("华夏智信开闸开始 integrateNo = {}", integrateNo);
        WebSocketSessionCache.getDeviceSession(integrateNo).sendMessage(integrateNo,payload);
        log.debug("华夏智信开闸结束 integrateNo = {}", integrateNo);
        return payload;
    }

    /**
     * @param integrateNo: 设备号
     * @param start: 执行开始时间
     * @return String
     * <AUTHOR>
     * @description 构建关闭闸门
     * @date 2023/4/14 15:56
     */
    private String buildCloseGate(String integrateNo, long start) {
        String payload;
        log.debug("华夏智信关闸开始 integrateNo = {}", integrateNo);
        if(WsLockDeviceCache.getLockDeviceCache(integrateNo) != null){
            //锁闸解锁
            payload = BusinessRequestUtil.buildMotorcade(integrateNo,"off");
            WebSocketSessionCache.getDeviceSession(integrateNo).sendMessage(integrateNo,payload);
            WsLockDeviceCache.remove(integrateNo);
            log.debug("华夏智信锁闸解锁执行完：{} {} {}", integrateNo, payload, (System.currentTimeMillis() - start) + "ms");
        }
        payload = BusinessRequestUtil.buildBarrierGate(2, integrateNo);
        WebSocketSessionCache.getDeviceSession(integrateNo).sendMessage(integrateNo,payload);
        log.debug("华夏智信关闸结束 integrateNo = {}", integrateNo);
        return payload;
    }

    @Override
    public void destroy() {
    }
}
