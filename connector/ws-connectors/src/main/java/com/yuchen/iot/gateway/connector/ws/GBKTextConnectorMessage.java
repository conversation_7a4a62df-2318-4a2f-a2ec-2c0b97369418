package com.yuchen.iot.gateway.connector.ws;

import com.yuchen.iot.gateway.connector.api.data.UpLinkContentType;

import java.io.UnsupportedEncodingException;

/**
 * GBK文本连接器消息
 * <AUTHOR>
 * @date 2023/4/12 11:29
 * @version 2.0.0
 */
public class GBKTextConnectorMessage extends WsConnectorMessage<String> {

    public GBKTextConnectorMessage(String message) {
        super(message);
    }

    public UpLinkContentType getContentType() {
        return UpLinkContentType.TEXT;
    }

    public byte[] getMessageInBytes() throws UnsupportedEncodingException {
        return this.message.getBytes();
    }
}
