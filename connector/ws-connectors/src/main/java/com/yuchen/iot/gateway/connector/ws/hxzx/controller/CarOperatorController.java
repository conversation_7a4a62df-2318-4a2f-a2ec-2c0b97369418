package com.yuchen.iot.gateway.connector.ws.hxzx.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yuchen.iot.gateway.connector.ws.hxzx.cache.WebSocketSessionCache;
import com.yuchen.iot.gateway.connector.ws.hxzx.cache.WebSocketTokenCache;
import com.yuchen.iot.gateway.connector.ws.hxzx.util.BusinessRequestUtil;
import com.yuchen.iot.gateway.connector.ws.hxzx.util.BusinessUtil;
import com.yuchen.iot.gateway.connector.ws.hxzx.websocket.support.impl.CameraResultProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;


/**
 * @author: 肖祥
 * @description: websocket控制器
 * @date: 2023/4/12 17:28
 * @version: 1.0
 */
@Slf4j
@RestController
public class CarOperatorController {

    @Resource
    private CameraResultProcessor cameraResultProcessor;

    @PostMapping("/car/open/gate")
    public String carIn(String deviceId) throws Exception{
        WebSocketSessionCache.getDeviceSession(deviceId).sendMessage(deviceId,buildBarrierGate(1,deviceId));
        return "true";
    }

    @PostMapping("/car/close/gate")
    public String carOut(String deviceId)  throws Exception{
        WebSocketSessionCache.getDeviceSession(deviceId).sendMessage(deviceId,buildBarrierGate(2,deviceId));
        return "true";
    }

    @PostMapping("/car/rs485")
    public String rs485(String deviceId)  throws Exception{
        WebSocketSessionCache.getDeviceSession(deviceId).sendMessage(deviceId,buildRS485(deviceId));
        return "true";
    }

    @PostMapping("/car/soft/trigger")
    public String softTrigger(String deviceId)  throws Exception{
        WebSocketSessionCache.getDeviceSession(deviceId).sendMessage(deviceId, BusinessRequestUtil.buildSoftTrigger(deviceId));
        return "true";
    }

    @PostMapping("/car/upload/data")
    public String uploadData(String deviceId)  throws Exception{
        cameraResultProcessor.processorRequest(buildDeviceData(deviceId),deviceId);
        return "true";
    }

    public JSONObject buildDeviceData(String deviceId){
        log.debug("华夏智信推车的设备为: {}",deviceId);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("deviceId",deviceId);
        jsonObject.put("cmd","result");
        jsonObject.put("msg_id",BusinessUtil.buildMessageId());
        jsonObject.put("token",WebSocketTokenCache.getDeviceToken(deviceId));
        jsonObject.put("type","online");
        jsonObject.put("plate_num","京N8P8F8");
        jsonObject.put("plate_color","蓝色");
        jsonObject.put("plate_val","true");
        jsonObject.put("confidence",27);
        jsonObject.put("car_logo","未知");
        jsonObject.put("car_color","其它");
        jsonObject.put("vehicle_type","轿车");
        jsonObject.put("vehicle_dir","unknown");
        jsonObject.put("utc_ts",System.currentTimeMillis());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        jsonObject.put("local_time",sdf.format(new Date()));
        jsonObject.put("inout","out");
        jsonObject.put("is_whitelist","false");
        jsonObject.put("trigger_type","swtriger");
        jsonObject.put("full_pic_len",21023);
        jsonObject.put("plate_pic_len",9205);
        String base64Full = "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";
        jsonObject.put("full_pic",base64Full);
        String base64Plate = "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";
        jsonObject.put("plate_pic",base64Plate);
        return jsonObject;
    }



/**
 * @param ioNum:
 * @param deviceId:
 * @return String
 * <AUTHOR>
 * @description 一体机 lcd
 * @date 2023/4/12 16:00
 */
    public String buildBarrierGate(Integer ioNum,String deviceId){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cmd","iooutput");
        jsonObject.put("msg_id", BusinessUtil.buildMessageId());
        jsonObject.put("token", WebSocketTokenCache.getDeviceToken(deviceId));
        jsonObject.put("ionum",ioNum);
        jsonObject.put("action","on");
        jsonObject.put("utc_ts",new Date().getTime());
        JSONObject jsonShowInfo = new JSONObject();
        jsonShowInfo.put("text_type","plateline");
        //车牌信息
        JSONObject jsonPlateInfo = new JSONObject();
        jsonPlateInfo.put("plate_num","京 A12345");
        jsonPlateInfo.put("text_color","FF00F0");
        jsonShowInfo.put("plate_info",jsonPlateInfo);
        //行信息
        JSONArray lineArray = new JSONArray();
        JSONObject line1 = new JSONObject();
        line1.put("line_text","月租车");
        line1.put("font_size","large");
        line1.put("text_color","FF00F0");
        lineArray.add(line1);
        JSONObject line2 = new JSONObject();
        line2.put("line_text","一路顺风");
        line2.put("font_size","large");
        line2.put("text_color","FF00F0");
        lineArray.add(line2);
        jsonShowInfo.put("line_info",lineArray);
        //语音播报信息
        JSONObject jsonVoiceInfo = new JSONObject();
        jsonVoiceInfo.put("voice_text","<T1>京 A12345+一路顺风");
        jsonObject.put("show_info",jsonShowInfo);
        jsonObject.put("voice_info",jsonVoiceInfo);
        return jsonObject.toString();
    }


    public String buildRS485(String deviceId){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("cmd","rs485");
        jsonObject.put("msg_id", BusinessUtil.buildMessageId());
        jsonObject.put("token", WebSocketTokenCache.getDeviceToken(deviceId));
        jsonObject.put("encode_type","hex2string");
        JSONArray jsonArrayCh1 = new JSONArray();
        JSONObject data1 = new JSONObject();
        data1.put("data","AA553097BEEF");
        JSONObject data2 = new JSONObject();
        data2.put("data","AA5509307208BEEF");
        jsonArrayCh1.add(data1);
        jsonArrayCh1.add(data2);
        jsonObject.put("rs485ch1_data",jsonArrayCh1);
        JSONArray jsonArrayCh2 = new JSONArray();
        JSONObject data3 = new JSONObject();
        data3.put("data","AA550102056607BEEF");
        jsonArrayCh2.add(data3);
        jsonObject.put("rs485ch2_data",jsonArrayCh2);
        return jsonObject.toString();
    }
}
