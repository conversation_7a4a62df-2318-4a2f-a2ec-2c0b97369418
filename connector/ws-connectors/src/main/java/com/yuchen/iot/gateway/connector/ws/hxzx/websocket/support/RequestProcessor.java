package com.yuchen.iot.gateway.connector.ws.hxzx.websocket.support;

import com.alibaba.fastjson.JSONObject;

/**
 * @author: 肖祥
 * @description: 请求处理类
 * @date: 2023/4/15 10:10
 * @version: 1.0
 */
public interface RequestProcessor {

    /**
     * @param message:请求的消息
     * @param deviceId:请求的设备id
     * @return void
     * <AUTHOR>
     * @description 请求消息处理
     * @date 2023/4/15 10:13
     */
    void processorRequest(JSONObject jsonObject, String deviceId);

    /**
     * @param :
     * @return String
     * <AUTHOR>
     * @description 处理类型
     * @date 2023/4/15 10:13
     */
    String getType();
}
