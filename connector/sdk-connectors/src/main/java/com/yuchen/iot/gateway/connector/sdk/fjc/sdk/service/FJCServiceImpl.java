package com.yuchen.iot.gateway.connector.sdk.fjc.sdk.service;


import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yuchen.iot.gateway.connector.api.data.DownLinkData;
import com.yuchen.iot.gateway.connector.sdk.fjc.sdk.FJCManage;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;
@Slf4j
public class FJCServiceImpl {


    protected final ObjectMapper mapper = new ObjectMapper();

    /**
     * 控制开关闸
     * 该函数根据传入的下行数据和返回码执行特定的控制逻辑，并返回执行状态。
     *
     * @param downLink 包含设备下发数据的DownLinkData对象
     * @param returnCode 格式为"xx:xx"，相机句柄
     * @return 返回一个Map，包含执行状态码。"state"为"1"表示执行成功，为"0"表示执行失败。
     */
    public Map<String, String> manualControl(DownLinkData downLink, String returnCode) {

        int code= Integer.parseInt(returnCode.split(":")[1]);
        try {
            // 将下行数据解析为JsonNode
            JsonNode jsonNode = this.mapper.readTree(downLink.getData());
            // 从JsonNode中获取命令字符串
            String command = jsonNode.asText();
            int result;

            // 根据命令执行不同的控制逻辑
            if ("1".equals(command)) {
                result = FJCManage.fjcparkApi.FJC_Park_EquipmentOutput(code, 5, 1);
            } else if ("0".equals(command)) {
                result = FJCManage.fjcparkApi.FJC_Park_EquipmentOutput(code, 6, 1);
            } else {
                result = FJCManage.fjcparkApi.FJC_Park_EquipmentOutput(code, 7, 1);
            }

            // 根据执行结果准备返回数据
            Map<String, String> respon = new HashMap<>();
            if (result == 0) {
                log.info("调用开闸指令成功==========");
                respon.put("state", "1");
            } else {
                respon.put("state", "0");
            }
            return respon;
        } catch (IOException e) {
            // 将IO异常转换为运行时异常抛出
            throw new RuntimeException(e);
        }
    }
    /**
     * 向LED设备推送信息并获取推送状态。
     *
     * @param downLink 包含要推送的数据的下链数据对象。
     * @param returnCode 包含推送操作返回码的字符串，返回相机句柄。
     * @return 一个包含推送状态的Map，其中"state"键的值为"1"表示推送成功，为"0"表示推送失败。
     * @throws RuntimeException 如果在解析JSON数据时发生异常。
     */
    public Map<String, String> pushLed(DownLinkData downLink, String returnCode) {
        int code= Integer.parseInt(returnCode.split(":")[1]);
        try {
            // 将下链数据解析为JSON节点
            JsonNode jsonNode = this.mapper.readTree(downLink.getData());

            // 调用API播放声音并构建指令
            int status = FJCManage.fjcparkApi.FJC_Park_PlayVoice(code, (byte) 0xFE, build(jsonNode.get("line1").toString(), jsonNode.get("line2").toString(), null));

            // 根据API返回的状态构建响应
            Map<String, String> respon = new HashMap<>();
            if (status == 0) {
                respon.put("state", "1");
            } else {
                respon.put("state", "0");
            }
            return respon;
        } catch (IOException e) {
            // 如果解析异常，则抛出运行时异常
            throw new RuntimeException(e);
        }
    }

    /**
     * 推送语音服务
     *
     * @param downLink 包含下链数据的对象，其中含有需要解析的数据
     * @param returnCode 返回码字符串，用于指示具体的操作行为
     * @return 返回一个包含操作状态的Map，"state"键的值为"1"表示成功，"0"表示失败
     */
    public Map<String, String> pushVoice(DownLinkData downLink, String returnCode) {
        // 解析返回码中的状态码
        int code= Integer.parseInt(returnCode.split(":")[1]);
        try {
            // 将下链数据解析为Map格式
            Map<String, Object> data = mapper.readValue(downLink.getData(), new TypeReference<Map<String, Object>>() {
            });
            // 调用API播放语音，传入操作码、预留字节和语音内容
            int status = FJCManage.fjcparkApi.FJC_Park_PlayVoice(code, (byte) 0xFE, build(null, null, "[n1]"+data.get("content").toString()+"[d]"));
            Map<String, String> respon = new HashMap<>();
            log.info("调用停车道闸设备接口AIP：FJC_Park_PlayVoice,句柄code："+code+" 返回值为："+status+"  内容为："+data.get("content").toString());
            // 根据API返回的状态设置响应状态
            if (status == 0) {
                respon.put("state", "1");
            } else {
                respon.put("state", "0");
            }
            return respon;
        } catch (IOException e) {
            // 将解析异常转换为运行时异常
            throw new RuntimeException(e);
        }
    }



    public static String build(String line1, String line2, String voice) {
        String ledInfo = "";
        String voiceInfo = "";

        // 自定义语音命令
        byte[] buf;

        // 第一行
        try {
            if (line1 != null) {
                buf = line1.trim().getBytes("GBK");

                if (buf.length > 0) {
                    // 转为16进制字符
                    for (byte c : buf) {
                        ledInfo += String.format("%02X", c);
                    }
                    // 结束换行符
                    ledInfo += "0D0A";
                }
            }
            if (line1 != null) {
                // 第二行
                buf = line2.trim().getBytes("GBK");
                if (buf.length > 0) {
                    // 转为16进制字符
                    for (byte c : buf) {
                        ledInfo += String.format("%02X", c);
                    }
                    // 结束换行符
                    ledInfo += "0D0A";
                }
            }
            if (voice != null) {
                // 语音
                buf = voice.trim().getBytes("GBK");
                // 转为16进制字符
                for (byte c : buf) {
                    voiceInfo += String.format("%02X", c);
                }
          //      voiceInfo+= "0D0A";
            }
            // 末尾 00 结束标识符号 很重要，很重要
            voiceInfo += "00";
       //     voiceInfo.length();
            // 数据长度
            String ledDataLen = String.format("%04X", ledInfo.length() / 2);
            String voiceDataLen = String.format("%04X", voiceInfo.length() / 2);

            // 组装数据 语音长度+语音数据+显示屏长度+显示屏数据
            String cmd = voiceDataLen + voiceInfo + ledDataLen + ledInfo;
          //  cmd+= "00";
            log.info("自定义发送数据十六进制格式:" + cmd);
            return cmd;
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }

    }



}
