package com.yuchen.iot.gateway.connector.sdk.fjc.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum PlateTypeEnum {

    EP_PLATE_TYPE_NULL(0, "未知"),
    EP_PLATE_TYPE_BLUE(1, "普通蓝牌"),
    EP_PLATE_TYPE_BLACK(2, "普通黑牌"),
    EP_PLATE_TYPE_YELL(3, "普通黄牌"),
    EP_PLATE_TYPE_YELL2(4, "双层黄牌"),
    EP_PLATE_TYPE_POL(5, "警察车牌"),
    EP_PLATE_TYPE_APOL(6, "武警车牌"),
    EP_PLATE_TYPE_APOL2(7, "双层武警"),
    EP_PLATE_TYPE_ARM(8, "单层军牌"),
    EP_PLATE_TYPE_ARM2(9, "双层军牌"),
    EP_PLATE_TYPE_INDI(10, "个性车牌"),
    EP_PLATE_TYPE_NEWN(11, "新能源小车牌"),
    EP_PLATE_TYPE_NEWN1(12, "新能源大车牌"),
    EP_PLATE_TYPE_EMB(13, "大使馆车牌"),
    EP_PLATE_TYPE_CON(14, "领事馆车牌"),
    EP_PLATE_TYPE_MIN(15, "民航车牌")
    ;

    private Integer code;
    private String desc;

    public static PlateTypeEnum getEnumByCode(Integer code) {
      return   Arrays.stream(PlateTypeEnum.values()).filter(
                s -> s.code.equals(code)
        ).findFirst().orElse(null);
    }

    @Override
    public String toString() {
        return "ColorTypeEnum{" +
                "状态码=" + code +
                ", 描述='" + desc + '\'' +
                '}';
    }
}
