package com.yuchen.iot.gateway.connector.sdk.fjc.sdk;

import com.sun.jna.Memory;
import com.sun.jna.Native;
import com.sun.jna.Pointer;
import com.yuchen.iot.gateway.connector.api.util.SpecialBeanUtil;
import com.yuchen.iot.gateway.connector.sdk.fjc.sdk.api.FJCPARKApi;
import com.yuchen.iot.gateway.connector.sdk.fjc.sdk.api.FJCSDKApi;
import com.yuchen.iot.gateway.connector.sdk.fjc.sdk.callback.impl.FGetImageCB2CallbackImpl;
import org.apache.commons.lang3.StringUtils;
import org.pf4j.Extension;
import org.pf4j.ExtensionPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Component
@Order(value = 9)
@Extension
public class FJCManage implements ExtensionPoint {


    public static FJCSDKApi fjcsdkApi = null;

    public static FJCPARKApi fjcparkApi = null;


    private HashMap<String, String> deviceReturnCode = new HashMap<>();

    private ScheduledExecutorService statisticsExecutorService;

    private final Logger log = LoggerFactory.getLogger(this.getClass());



    /**
     * 初始化SDK函数
     * 本函数用于加载FjcNetSDK和FJC_PARKSDK的动态链接库，并初始化SDK。
     * 注意：此函数不接受任何参数，也不返回任何值，但会通过返回码判断初始化是否成功。
     */
    public void initSdk() {
        // 获取当前用户目录，并拼接动态链接库文件路径
        try {
           String strDllPath = System.getProperty("user.dir") + "\\lib\\windows\\FjcNetSDK.dll";
         //   String strDllPath = "D:\\ideaProjects\\mqtt-gateway\\lib\\windows\\FjcNetSDK.dll" ;
            String strParkDllPath = System.getProperty("user.dir") + "\\lib\\windows\\FJC_PARKSDK.dll";
        //    String strDllPath= System.loadLibrary("your-dll-file");
             log.info("strDllPath:======="+strDllPath);
            log.info("strParkDllPath:======="+strParkDllPath);
            // 加载FjcNetSDK和FJC_PARKSDK动态库
            fjcsdkApi = Native.loadLibrary(strDllPath, FJCSDKApi.class);
            fjcparkApi = Native.loadLibrary(strParkDllPath, FJCPARKApi.class);
            log.info("3:=======");
            // 初始化SDK，返回码非0表示初始化失败
            int returnCode = fjcsdkApi.FJC_Init();
            log.info("returnCode:======="+returnCode);
            if (returnCode != 0)
                throw new RuntimeException("设备初始化失败");// 初始化失败，抛出异常
        }catch (Exception e){
            log.info("initSdk:=====",e);
        }


    }

    /**
     * 初始化设备连接。
     * 该方法首先尝试添加摄像头设备，然后连接设备，并注册图像接收回调。
     * 如果设备添加或连接失败，会删除已添加的设备并返回空字符串。
     * 成功则返回设备ID与打开设备的返回码的字符串表示。
     *
     * @param deviceKey 设备的唯一标识符。
     * @return 返回格式为"设备ID:打开设备返回码"的字符串，失败则返回空字符串。
     */
    public String initDevice(String deviceKey) {
        // 添加摄像头设备，返回设备ID或-1表示失败
        int nCamId = fjcsdkApi.FJC_AddCamera(deviceKey);
        if (nCamId == -1) {
            return "";
        }

        // 尝试连接设备，返回0表示成功，非0表示失败
        int iRet = fjcsdkApi.FJC_ConnCamera(nCamId, 30000, 10);
        if (iRet != 0) {
            // 连接失败，删除设备
            fjcsdkApi.FJC_DelCamera(nCamId);
            return "";
        }

        // 注册图像接收回调
        fjcsdkApi.FJC_RegImageRecv2(SpecialBeanUtil.applicationContext.getBean(FGetImageCB2CallbackImpl.class));

        // 打开设备（TCP方式），返回码表示操作结果
        int code = fjcparkApi.FJC_Park_OpenDevice_TCP(deviceKey, (short) 8000);
     //   StringBuilder buffer = new StringBuilder(128);



        Executors.newSingleThreadScheduledExecutor().scheduleAtFixedRate(() -> {
            try {
                int bufferSize = 1024; // 假设我们需要1024字节的缓冲区
                byte[] buffer = new byte[bufferSize];
                Pointer pOutBuffer = new Memory(buffer.length);
                pOutBuffer.write(0, buffer, 0, buffer.length);
                int result= fjcparkApi.FJC_Park_ReadRecord(code, pOutBuffer, buffer.length);
                log.info("定时发送心跳FJC_Park_ReadRecord:"+code+"result:"+result);
            }catch (Exception e){
                e.printStackTrace();
            }
        }, 0, 2, TimeUnit.SECONDS);


        log.info("FJC_AddCamera设备连接成功，设备ID为：" + nCamId + "，FJC_RegImageRecv2打开设备返回码为：" + code);
        // 返回设备ID和打开设备的返回码
        return nCamId + ":" + code;
    }



    @PreDestroy
    public void destroy() {
        fjcsdkApi.FJC_UNinit();
        for (Map.Entry<String, String> entry : deviceReturnCode.entrySet()) {
            System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
            String[] strs = StringUtils.split(entry.getValue(), ":");
            fjcparkApi.FJC_Park_CloseDevice(Integer.parseInt(strs[1]));
        }
        log.info("富士SDK 库反初始化，释放 SDK 管理资源");
    }

}
