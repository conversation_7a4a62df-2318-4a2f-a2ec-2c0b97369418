package com.yuchen.iot.gateway.connector.sdk.fjc.sdk.dto;

import com.sun.jna.Pointer;
import com.sun.jna.Structure;

import java.util.ArrayList;
import java.util.List;

public class TPicInfo extends Structure {
    public int uiPanoramaPicLen;  /*全景图片大小*/
    public int uiVehiclePicLen;      /*车牌图片大小*/
    public Pointer ptPanoramaPicBuff;   /*全景图片缓冲区*/
    public Pointer ptVehiclePicBuff;  /*车牌图片缓冲区*/

    @Override
    protected List<String> getFieldOrder() {
        List<String> fieldOrderList = new ArrayList<String>();
        fieldOrderList.add("uiPanoramaPicLen");
        fieldOrderList.add("uiVehiclePicLen");
        fieldOrderList.add("ptPanoramaPicBuff");
        fieldOrderList.add("ptVehiclePicBuff");
        return fieldOrderList;
    }
}