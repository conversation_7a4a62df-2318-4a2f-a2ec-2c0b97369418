package com.yuchen.iot.gateway.connector.sdk.fjc.sdk.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum ColorTypeEnum {

    COLOR_TYPE_BLUE(0, "蓝色"),
    COLOR_TYPE_YELLO(1, "黄色"),
    COLOR_TYPE_WHITE(2, "白色"),
    COLOR_TYPE_BLACK(3, "黑色"),
    COLOR_TYPE_OTHER(4, "其他颜色"),
    COLOR_TYPE_GREEN(5, "绿色"),
    COLOR_TYPE_YELLOW_GREEN(6, "黄绿")
    ;

    private Integer code;
    private String desc;

    public static ColorTypeEnum getEnumByCode(Integer code) {
      return   Arrays.stream(ColorTypeEnum.values()).filter(
                s -> s.code.equals(code)
        ).findFirst().orElse(null);
    }

    @Override
    public String toString() {
        return "ColorTypeEnum{" +
                "状态码=" + code +
                ", 描述='" + desc + '\'' +
                '}';
    }
}
