package com.yuchen.iot.gateway.connector.sdk.fjc.sdk.dto;

import com.sun.jna.Structure;

import java.util.ArrayList;
import java.util.List;

public class TImageUserInfo2 extends Structure {
    public short usWidth; //图片的宽度，单位:像素
    public short usHeight; //图片的高度，单位:像素
    public byte ucVehicleColor; //车身颜色，E_ColorType
    public byte ucVehicleBrand; //车标，E_VehicleFlag
    public byte ucVehicleSize; //车型(1大2中3小)，ITS_Tb_Vt,目前根据车牌颜色
    public byte ucPlateColor; //车牌颜色，E_ColorType
    public byte[] szLprResult= new byte[16]; //车牌，若为'\0'，表示无效GB2312编码
    public byte[] usLpBox= new byte[4]; //车牌位置，左上角(0, 1), 右下角(2,3)
    public byte ucLprType; //车牌类型, ITS_Ep_Pt
    public short usSpeed; //单位km/h
    public byte ucSnapType; //抓拍模式, E_SnapType
    public byte ucHaveVehicle; // 车牌防伪 0未知1异常2正常
    public byte[] acSnapTime= new byte[18]; //图片抓拍时间:格式YYYYMMDDHHMMSSmmm(年月日时分秒毫秒)
    public byte ucViolateCode; //违法代码E_ViolationCode
    public byte ucLaneNo; //车道号,从0开始编码
    public int uiVehicleId; //检测到的车辆id，若为同一辆车，则id相同
    public byte ucScore; //车牌识别可行度
    public byte ucDirection; //行车方向E_Direction
    public byte ucTotalNum; //该车辆抓拍总张数
    public byte ucSnapshotIndex; //当前抓拍第几张，从0开始编号



    @Override
    protected List<String> getFieldOrder() {
        List<String> fieldOrderList = new ArrayList<String>();
        fieldOrderList.add("usWidth");
        fieldOrderList.add("usHeight");
        fieldOrderList.add("ucVehicleColor");
        fieldOrderList.add("ucVehicleBrand");
        fieldOrderList.add("ucVehicleSize");
        fieldOrderList.add("ucPlateColor");
        fieldOrderList.add("szLprResult");
        fieldOrderList.add("usLpBox");
        fieldOrderList.add("ucLprType");
        fieldOrderList.add("usSpeed");
        fieldOrderList.add("ucSnapType");
        fieldOrderList.add("ucHaveVehicle");
        fieldOrderList.add("acSnapTime");
        fieldOrderList.add("ucViolateCode");
        fieldOrderList.add("ucLaneNo");
        fieldOrderList.add("uiVehicleId");
        fieldOrderList.add("ucScore");
        fieldOrderList.add("ucDirection");
        fieldOrderList.add("ucTotalNum");
        fieldOrderList.add("ucSnapshotIndex");

        return fieldOrderList;
    }
}
