package com.yuchen.iot.gateway.connector.sdk.fjc.sdk.callback;

import com.sun.jna.win32.StdCallLibrary;
import com.yuchen.iot.gateway.connector.sdk.fjc.sdk.dto.TImageUserInfo2;
import com.yuchen.iot.gateway.connector.sdk.fjc.sdk.dto.TPicInfo;

public interface FGetImageCB2 extends StdCallLibrary.StdCallCallback {


    public int FGetImageCB2(int tHandle, int uiImageId, TImageUserInfo2 tImageInfo, TPicInfo tPicInfo);

}
