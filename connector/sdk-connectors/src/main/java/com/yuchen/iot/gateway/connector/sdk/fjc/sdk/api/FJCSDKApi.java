package com.yuchen.iot.gateway.connector.sdk.fjc.sdk.api;

import com.sun.jna.Library;
import com.yuchen.iot.gateway.connector.sdk.fjc.sdk.callback.FGetImageCB2;
import com.yuchen.iot.gateway.connector.sdk.fjc.sdk.dto.TControlGate;


/**
 * @author: llc
 * @description: 富士sdkAPI
 * @date: 2024/04/11 11:03
 * @version: 1.0
 */
public interface FJCSDKApi extends Library {

    /**
     *  @brief  全局初始化
     *  @return int
     */
    int FJC_Init();
    /**
     *  @brief  全局初始化取消
     *  @return void
     */
    void FJC_UNinit();
    /**
     *  @brief  添加相机
     *  @return int
     */
    int FJC_AddCamera(String ptIp);
    /**
     *  @brief  删除相机
     *  @return int
     */
    int FJC_DelCamera(int tHandle);
    /**
     *  @brief  连接相机
     *  @return int
     */
    int FJC_ConnCamera(int tHandle,  int usPort,  int usTimeout);
    /**
     *  @brief  取消连接相机
     *  @return int
     */
    int FJC_DisConnCamera(int tHandle);

    /**
     *  @brief  图片抓拍
     *  @return int
     */
    int FJC_RegImageRecv2(FGetImageCB2 fCb);

    /**
     * 控制开关闸
     * @param tHandle
     * @param ptControlGate
     * @return
     */
    int FJC_GateSetup(int tHandle,  TControlGate ptControlGate);



   // int FJC_StartVideo(int tHandle, int niStreamType, IntPtr hWnd);




}
