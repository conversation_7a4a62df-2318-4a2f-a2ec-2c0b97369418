package com.yuchen.iot.gateway.connector.sdk.fjc.sdk;

import cn.hutool.core.util.ClassUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Function;
import com.yuchen.iot.gateway.connector.api.ConnectorInitParams;
import com.yuchen.iot.gateway.connector.api.WaitCallResponseCallback;
import com.yuchen.iot.gateway.connector.api.data.DownLinkData;
import com.yuchen.iot.gateway.connector.api.data.UpLinkData;
import com.yuchen.iot.gateway.connector.api.data.UpLinkMetaData;
import com.yuchen.iot.gateway.connector.api.data.message.CallResponse;
import com.yuchen.iot.gateway.connector.api.data.message.Event;
import com.yuchen.iot.gateway.connector.api.data.message.EventValue;
import com.yuchen.iot.gateway.connector.api.message.ThingMessage;
import com.yuchen.iot.gateway.connector.api.util.SpecialBeanUtil;
import com.yuchen.iot.gateway.connector.sdk.AbstractSdkConnector;
import com.yuchen.iot.gateway.connector.sdk.GBKTextConnectorMessage;
import com.yuchen.iot.gateway.connector.sdk.fjc.sdk.service.FJCServiceImpl;
import com.yuchen.iot.gateway.connector.sdk.jieshun.sdk.JsComponent;
import com.yuchen.iot.gateway.connector.sdk.jieshun.sdk.cache.JieShunWarnEventCache;
import com.yuchen.iot.gateway.connector.sdk.jieshun.sdk.constant.JieShunSdkConstant;
import com.yuchen.iot.gateway.connector.sdk.jieshun.sdk.enums.CodeEnum;
import com.yuchen.iot.gateway.connector.sdk.jieshun.sdk.factory.JSParkingThreadFactory;
import com.yuchen.iot.gateway.util.BusinessUtil;
import org.apache.commons.lang3.StringUtils;
import org.pf4j.Extension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 富士sdk连接器,主要初始化连接器,处理上行数据,处理下行数据
 *
 * <AUTHOR>
 * @version 2.0.0
 * @date 2024/04/6 下午2:04
 */
@Extension
public class FJCSdkConnector extends AbstractSdkConnector<GBKTextConnectorMessage> {
    private static ExecutorService deviceAlarmEventExecutor;

    static {
        deviceAlarmEventExecutor = Executors.newFixedThreadPool(20, JSParkingThreadFactory.forName("js-device-alarm-event"));
    }

    private static final FJCSdkConnector INSTANCE = new FJCSdkConnector();

    private final Logger log = LoggerFactory.getLogger(this.getClass());


    private Map<String, Method> services = new HashMap<>();
    private Map<String, Object> serviceInstances = new HashMap<>();


    public static HashMap<String, String> deviceReturnCode = new HashMap<>();


    private FJCManage fjcManage;

    private FJCSdkConnector() {
    }

    public static FJCSdkConnector getInstance() {
        return INSTANCE;
    }


    @Override
    public void init(ConnectorInitParams params) {
        log.debug("jie shun ConnectorInitParams ==========> {}", JSON.toJSONString(params));
        super.init(params);

        fjcManage = SpecialBeanUtil.applicationContext.getBean(FJCManage.class);
        fjcManage.initSdk();

        List<Class<?>> classes = Arrays.asList(FJCServiceImpl.class);
        for (Class<?> clazz : classes) {
            try {
                Constructor<?> con = clazz.getConstructor();
                Object o = con.newInstance();
                Method[] methods = ClassUtil.getDeclaredMethods(clazz);
                for (Method method : methods) {
                    serviceInstances.put(method.getName(), o);
                    services.put(method.getName(), method);
                }
            } catch (Exception e) {
                log.error("init hik door connect fail: {}", e.getMessage());
                throw new RuntimeException(e);
            }
        }
    }

    @Override
    protected void doListenOnEndpoint(String deviceKey, Map<String, String> endpointMeta) {
        this.getUpLinkDataListener(deviceKey).onSouthConnect();
        String returnCode = fjcManage.initDevice(deviceKey);
        if (StringUtils.isNotBlank(returnCode)) {
            deviceReturnCode.put(deviceKey, returnCode);
        }
    }

    @Override
    protected void doListenOffEndpoint(String deviceKey, Map<String, String> endpointMeta) {
        this.getUpLinkDataListener(deviceKey).onSouthDisconnect();
    }

    @Override
    public void process(GBKTextConnectorMessage message) {
        try {
            this.processUpLinkData(message);
            this.connectorStatistics.incMessagesProcessed();
        } catch (Exception e) {
            log.debug("Failed to apply data converter function: {}", e.getMessage(), e);
            this.connectorStatistics.incErrorsOccurred();
        }
    }


    protected void processUpLinkData(GBKTextConnectorMessage message) throws Exception {
        List<UpLinkData> upLinkDataList = this.convert(message);
        if (upLinkDataList != null && !upLinkDataList.isEmpty()) {
            for (UpLinkData upLinkData : upLinkDataList) {
                this.processUpLinkData(upLinkData);
                log.trace("[{}] Processing up link data", upLinkData);
            }
        }
    }

    protected List<UpLinkData> convert(GBKTextConnectorMessage message) throws Exception {
        byte[] data = message.getMessageInBytes();
        Map<String, String> mdMap = new HashMap<>(this.metadataTemplate.getKvMap());
        return this.convertToUpLinkDataList(this.connectorContext.getCallbackExecutor(), data,
                new UpLinkMetaData(message.getContentType(), mdMap));
    }

    @Override
    public void onDownLinkMessage(ThingMessage message, WaitCallResponseCallback callback) {
        this.processDownLinkMessage(message, callback);
    }

    private void processDownLinkMessage(ThingMessage message, WaitCallResponseCallback callback) {
        Map<String, String> mdMap = new HashMap<>(this.metadataTemplate.getKvMap());
        try {
            List<DownLinkData> result = this.downLinkConverter.convertDownLink(Collections.singletonList(message),
                    mdMap);
            if (!result.isEmpty()) {
                this.doProcessDownLinkMessage(message.getIntegrateNo(), message.getIdentifier(), result.get(0),
                        callback);
            } else {
                log.warn("Call service converter failed, {} {}", message.getIntegrateNo(), message.getIdentifier());
            }
        } catch (Exception e) {
            log.warn("Failed to process downLink message", e);
        }
    }

    private void doProcessDownLinkMessage(String integrateNo, String identifier,
                                          DownLinkData downLink, WaitCallResponseCallback callback) throws Exception {
        log.debug("Send a remote call, integrationNo = {}, identifier = {}", integrateNo, identifier);
        Object o = serviceInstances.get(identifier);
        Method method = services.get(identifier);
        if (o == null || method == null) {
            log.error("========command not found=======");
            return;
        }
        String returnCode = deviceReturnCode.get(integrateNo);
        Map<String, String> response = (Map<String, String>) method.invoke(o, downLink, returnCode);
        callback.responseNow(response);
    }



    @Override
    public void destroy() {
        log.info("销毁停车SDK=================");
    }
}
