package com.yuchen.iot.gateway.coverter.isapi;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.yuchen.iot.gateway.connector.api.converter.AbstractKeyEvaluator;
import com.yuchen.iot.gateway.connector.api.converter.evaluator.UpLinkEvaluator;
import com.yuchen.iot.gateway.connector.api.converter.internal.InternalEvaluatorSet;
import com.yuchen.iot.gateway.connector.api.data.UpLinkData;
import com.yuchen.iot.gateway.connector.api.data.UpLinkMetaData;
import com.yuchen.iot.gateway.connector.api.data.message.CallResponse;
import com.yuchen.iot.gateway.connector.api.data.message.Event;
import com.yuchen.iot.gateway.connector.api.data.message.EventValue;
import com.yuchen.iot.gateway.connector.api.parking.cache.DeviceGroupCache;
import com.yuchen.iot.gateway.connector.api.parking.cache.DeviceVehicleCache;
import com.yuchen.iot.gateway.connector.api.parking.dto.DeviceOneInfo;
import com.yuchen.iot.gateway.connector.api.parking.dto.DeviceTwoInfo;
import com.yuchen.iot.gateway.connector.api.parking.dto.LinkDeviceInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 海康 ISAPI 停车场设备上行转换器,led显示和抓拍不是一体机,型号DS-TCG205-A
 *
 * <AUTHOR>
 * @date 2022/11/17 下午3:00
 * @version 2.0.0
 */
@Component
public class ParkingUpLinkEvaluator extends AbstractKeyEvaluator implements UpLinkEvaluator {
    @Resource
    @Lazy
    private InternalEvaluatorSet internalEvaluatorSet;

    private final Logger logger = LoggerFactory.getLogger(ParkingUpLinkEvaluator.class);

    @Override
    public ListenableFuture<Object> execute(byte[] data, UpLinkMetaData upLinkMetaData) {
        List<UpLinkData> upLinkDataList = new ArrayList<>();
        try {
            String integrateNo = null;
            JsonNode jsonNode = this.objectMapper.readTree(data);
            if (jsonNode.has("anpr.xml")) {
                JsonNode anprFile = jsonNode.get("anpr.xml");

                Event event = new Event();
                event.setIdentifier("passDetect");

                EventValue eventValue = new EventValue();
                event.setValue(eventValue);

                // 设备名
                String ipAddress = anprFile.get("ipAddress").asText();
                eventValue.put("integrateNoAct",ipAddress);
                // 查找主相机//todo 网关
                if (upLinkMetaData.getKvMap().containsKey("masterCameraIp") && StringUtils
                        .isNotBlank(upLinkMetaData.getKvMap().get("masterCameraIp"))) {
                    logger.debug("上行元数据配置信息: {}", JSON.toJSONString(upLinkMetaData.getKvMap()));
                    JsonNode masterIp = this.objectMapper.readTree(upLinkMetaData.getKvMap().get("masterCameraIp"));
                    if (masterIp.has(ipAddress)) {
                        eventValue.put("merge", "true");
                        integrateNo = ipAddress;//todo 合并
                    } else {
                        Iterator<Map.Entry<String, JsonNode>> iterator = masterIp.fields();
                        flag:
                        while (iterator.hasNext()) {
                            Map.Entry<String, JsonNode> groupNode = iterator.next();
                            JsonNode salvesNode = groupNode.getValue();
                            if (salvesNode.isArray()) {
                                Iterator<JsonNode> salvesValue = salvesNode.elements();
                                while (salvesValue.hasNext()) {
                                    if (ipAddress.equals(salvesValue.next().asText())) {
                                        eventValue.put("merge", "true");
                                        integrateNo = groupNode.getKey();
                                        break flag;
                                    }
                                }
                            }
                        }
                    }
                }

                //根据ip地址获取主IP地址  业务剥离主程，框架代码回迁主程
                String groupId = DeviceGroupCache.getDeviceGroup(ipAddress);
                if (integrateNo == null && StringUtils.isNotBlank(groupId)) {
                    LinkDeviceInfo linkDeviceInfo = DeviceVehicleCache.getDeviceVehicle(groupId);
                    DeviceOneInfo deviceOneInfo = linkDeviceInfo.getDeviceOneInfo();
                    DeviceTwoInfo deviceTwoInfo = linkDeviceInfo.getDeviceTwoInfo();
                    if (deviceOneInfo != null && deviceOneInfo.getDeviceType() == 1) {
                        integrateNo = deviceOneInfo.getDeviceId();
                    } else if (deviceTwoInfo != null && deviceTwoInfo.getDeviceType() == 1) {
                        integrateNo = deviceTwoInfo.getDeviceId();
                    }
                }

                if (integrateNo == null) {
                    integrateNo = ipAddress;
                }

                JsonNode anpr = anprFile.get("ANPR");
                // 车牌号
                if (anpr.has("licensePlate")) {
                    String plateNo = anpr.get("licensePlate").asText();
                    if (StringUtils.isEmpty(plateNo) || "无车牌".equals(plateNo)) {
                        eventValue.put("plateNo", "无车牌");
                    } else {
                        if(isTwoChinese(plateNo)){
                            eventValue.put("plateNo", plateNo.substring(1));
                        }else{
                            eventValue.put("plateNo", plateNo);
                        }
                    }
                } else {
                    eventValue.put("plateNo", "无车牌");
                }
                logger.debug("passDetect 车牌识别信息及设备信息: {} {}", eventValue.get("plateNo"), ipAddress);
                // 置信度 confidenceLevel
                if (anpr.has("confidenceLevel")) {
                    eventValue.put("confidenceLevel", anpr.get("confidenceLevel").asText());
                }

                // 车牌类型
                if (anpr.has("plateType")) {
                    String plateType = anpr.get("plateType").asText();
                    if (StringUtils.isEmpty(plateType) || "unknown".equals(plateType)) {
                        eventValue.put("plateType", "9");
                    } else {
                        String plateTypeNo = null;
                        switch (plateType) {
                            case "92TypeCivil":
                                plateTypeNo = "0";
                                break;
                            case "arm":
                                plateTypeNo = "2";
                                break;
                            case "92TypeArm":
                            case "oneLineArm":
                            case "twoLineArm":
                            case "oneLineArmHeadquarters":
                            case "twoLineArmHeadquarters":
                                plateTypeNo = "3";
                                break;
                            case "02TypePersonalized":
                                plateTypeNo = "1";
                                break;
                            case "yellowTwoLine":
                                break;
                            case "04NewMilitay":
                            case "upDownMilitay":
                            case "leftRightMilitay":
                                plateTypeNo = "13";
                                break;
                            case "embassy":
                            case "consulate":
                                plateTypeNo = "5";
                                break;
                            case "yellow1225FarmVehicle":
                            case "green1325FarmVehicle":
                            case "yellow1325FarmVehicle":
                                plateTypeNo = "6";
                                break;
                            case "motorola":
                            case "twoWheelVehicle":
                                plateTypeNo = "7";
                                break;
                            case "coach":
                                plateTypeNo = "10";
                                break;
                            case "tempTravl":
                                plateTypeNo = "11";
                                break;
                            case "trailer":
                                plateTypeNo = "12";
                                break;
                            case "hongKongMacao":
                                plateTypeNo = "14";
                                break;
                            case "tempEntry":
                                break;
                            case "civilAviation":
                                plateTypeNo = "15";
                                break;
                            case "newEnergy":
                                plateTypeNo = "8";
                                break;
                            case "92FarmVehicle":
                                plateTypeNo = "4";
                                break;
                            case "emergency":
                                plateTypeNo = "16";
                                break;
                            default:
                                plateTypeNo = "9";
                        }
                        eventValue.put("plateType", plateTypeNo);
                    }
                }

                // 车牌颜色
                if (anpr.has("plateColor")) {
                    String plateColor = anpr.get("plateColor").asText();
                    if (StringUtils.isEmpty(plateColor) || "unknown".equals(plateColor)) {
                        eventValue.put("plateColor", "unknown");
                    } else {
                        eventValue.put("plateColor", plateColor);
                    }
                } else {
                    eventValue.put("plateColor", "unknown");
                }

                // 车辆类型
                if (anpr.has("vehicleInfo") && anpr.get("vehicleInfo").has("vehicleType")) {
                    String vehicleType = anpr.get("vehicleInfo").get("vehicleType").asText();
                    if (StringUtils.isEmpty(vehicleType)) {
                        eventValue.put("vehicleType", "0");
                    } else {
                        String vehicleTypeNo;
                        switch (vehicleType) {
                            case "1":
                                vehicleTypeNo = "1";
                                break;
                            case "2":
                                vehicleTypeNo = "2";
                                break;
                            case "4":
                            case "5":
                                vehicleTypeNo = "3";
                                break;
                            default:
                                vehicleTypeNo = "0";
                        }
                        eventValue.put("vehicleType", vehicleTypeNo);
                    }
                } else {
                    eventValue.put("vehicleType", "unknown");
                }

                // 车辆颜色
                if (anpr.has("vehicleInfo") && anpr.get("vehicleInfo").has("color")) {
                    String vehicleColor = anpr.get("vehicleInfo").get("color").asText();
                    if (StringUtils.isEmpty(vehicleColor) || "unknown".equals(vehicleColor)) {
                        eventValue.put("vehicleColor", "unknown");
                    } else {
                        eventValue.put("vehicleColor", vehicleColor);
                    }
                } else {
                    eventValue.put("vehicleColor", "unknown");
                }

                // 车牌图片
                if (jsonNode.has("licensePlatePicture.jpg")) {
                    eventValue.put("platePicUrl", jsonNode.get("licensePlatePicture.jpg").asText());
                } else {
                    eventValue.put("platePicUrl", "");
                }

                // 送检图片
                if (jsonNode.has("detectionPicture.jpg")) {
                    eventValue.put("vehiclePicUrl", jsonNode.get("detectionPicture.jpg").asText());
                } else {
                    eventValue.put("vehiclePicUrl", "");
                }

                // 检测时间
                JsonNode dateTime = anprFile.get("dateTime");
                if (dateTime == null) {
                    event.setTime(System.currentTimeMillis() / 1000L);
                } else {
                    String date = dateTime.asText();
                    if (StringUtils.isEmpty(date)) {
                        event.setTime(System.currentTimeMillis() / 1000L);
                    } else {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss+08:00");
                        event.setTime(sdf.parse(date).getTime() / 1000L);
                    }
                }
                UpLinkData upLinkData = new UpLinkData(integrateNo, event, null, null, null);
                upLinkDataList.add(upLinkData);
            } else {
                if (upLinkMetaData.getKvMap().containsKey("Header:identifier")) {
                    String identifier = upLinkMetaData.getKvMap().get("Header:identifier");
                    integrateNo = upLinkMetaData.getKvMap().get("Header:integrateNo");
                    if (("manualControl").equals(identifier)) {
                        CallResponse callResponse = new CallResponse();
                        String statusCode = jsonNode.get("statusCode").asText();
                        if ("1".equals(statusCode)) {
                            callResponse.put("state", "1");
                        } else {
                            callResponse.put("state", "0");
                        }
                        UpLinkData upLinkData = new UpLinkData(integrateNo, null, null, callResponse, null);
                        upLinkDataList.add(upLinkData);
                    }
                }
            }
        } catch (IOException | ParseException e) {
            e.printStackTrace();
            logger.warn("Evaluator execute failed {}", e.getMessage());
        }
        return Futures.immediateFuture(this.gson.toJson(upLinkDataList));
    }

    @Override
    public String getKey() {
        return "old-parking-up-link";
    }

    @Override
    public void destroy() {
        internalEvaluatorSet.removeUpLinkEvaluator(this.getKey());
    }

    /**
     * 判断是否是中文或者中文符号
     *
     * @param c
     * @return
     */
    public static boolean isChinese(char c) {
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
        if (ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B
                || ub == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION || ub == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS
                || ub == Character.UnicodeBlock.GENERAL_PUNCTUATION) {
            return true;
        }
        return false;
    }

    /**
     * 判断是否包含两个汉字
     *
     * @param plateNo
     * @return
     */
    public static boolean isTwoChinese(String plateNo) {
        char plateChar[] = plateNo.toCharArray();
        int hasChCount = 0;
        for (char c : plateChar) {
            if(isChinese(c)){
                hasChCount++;
            }
            if(hasChCount == 2){
                return true;
            }
        }
        return false;
    }
}
