package com.yuchen.iot.gateway.coverter.hikled;

import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.yuchen.iot.gateway.connector.api.converter.AbstractKeyEvaluator;
import com.yuchen.iot.gateway.connector.api.converter.evaluator.UpLinkEvaluator;
import com.yuchen.iot.gateway.connector.api.data.UpLinkData;
import com.yuchen.iot.gateway.connector.api.data.UpLinkMetaData;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 海康 ISAPI 停车场设备上行转换器
 *
 * <AUTHOR>
 * @date 2022/11/17 下午3:00
 * @version 2.0.0
 */
@Component
public class HiLedUpLinkEvaluator extends AbstractKeyEvaluator implements UpLinkEvaluator {

    @Override
    public ListenableFuture<Object> execute(byte[] data, UpLinkMetaData upLinkMetaData) {
        List<UpLinkData> upLinkDataList = new ArrayList<>();
        return Futures.immediateFuture(this.gson.toJson(upLinkDataList));
    }

    @Override
    public String getKey() {
        return "hik-parking-led-up-link";
    }

    @Override
    public void destroy() {
        // do nothing
    }
}
