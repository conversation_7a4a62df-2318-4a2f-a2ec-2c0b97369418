package com.yuchen.iot.gateway.coverter.kehua;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.yuchen.iot.gateway.connector.api.converter.AbstractKeyEvaluator;
import com.yuchen.iot.gateway.connector.api.converter.evaluator.DownLinkEvaluator;
import com.yuchen.iot.gateway.connector.api.message.ThingMessage;
import org.springframework.stereotype.Component;

import javax.script.ScriptException;
import java.util.Map;

/**
 * 科华充电桩下行转换器
 *
 * <AUTHOR>
 * @date 2022/12/28
 * @version 1.0
 */
@Component
public class KhChargingDownLinkEvaluator extends AbstractKeyEvaluator implements DownLinkEvaluator {

    @Override
    public String getKey() {
        return "kehua-charging-down-link";
    }

    @Override
    public JsonNode execute(ThingMessage message, Map<String, String> metadata) throws ScriptException {

        ObjectNode resultData = this.objectMapper.createObjectNode();

        Map<String, String> deviceMeta = message.getDeviceMetaData();
        String identifier = message.getIdentifier();
        if (StrUtil.isEmpty(identifier)) {
            throw new ScriptException("param cmd can not be null");
        }

        ObjectNode resultMeta = this.objectMapper.createObjectNode();
        resultMeta.put("identifier",message.getIdentifier());
        String data = null;
        Map<String, Object> params = (Map<String, Object>) message.getData();
        System.out.println(params.toString());
        try {
            data = this.objectMapper.writeValueAsString(params);
            System.out.println(data);
        }catch (Exception e){
            e.printStackTrace();
        }
        resultData.put("contentType", "JSON");


        if (StrUtil.isEmpty(data)) {
            throw new ScriptException("execute failed");
        } else {
            resultData.put("metadata", resultMeta);
            resultData.put("data", data);
            return resultData;
        }
    }



    @Override
    public void destroy() {
        // do nothing
    }
}
