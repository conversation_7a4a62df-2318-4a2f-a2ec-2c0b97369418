package com.yuchen.iot.gateway.coverter.isapi;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.yuchen.iot.gateway.connector.api.converter.AbstractKeyEvaluator;
import com.yuchen.iot.gateway.connector.api.converter.evaluator.DownLinkEvaluator;
import com.yuchen.iot.gateway.connector.api.converter.internal.InternalEvaluatorSet;
import com.yuchen.iot.gateway.connector.api.message.ThingMessage;
import com.yuchen.iot.gateway.connector.api.parking.cache.LockDeviceCache;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.script.ScriptException;
import java.io.UnsupportedEncodingException;
import java.util.Base64;
import java.util.Map;

/**
 * 海康 ISAPI 停车场设备下行 转换器,led显示和抓拍不是一体机,型号DS-TCG205-A
 *
 * <AUTHOR>
 * @date 2022/11/17 下午3:00
 * @version 2.0.0
 */
@Component
public class ParkingDownLinkEvaluator extends AbstractKeyEvaluator implements DownLinkEvaluator {
    @Resource
    @Lazy
    private InternalEvaluatorSet internalEvaluatorSet;

    public static final int FRAME_HEAD_BYTE1 = 0;
    public static final int FRAME_HEAD_BYTE2 = 1;
    public static final int FRAME_HEAD_BYTE3 = 2;
    public static final int FRAME_HEAD_BYTE4 = 3;

    public static final int ADDRESS = 4;
    public static final int KEEP_ONE_BYTE1 = 5;
    public static final int KEEP_ONE_BYTE2 = 6;
    public static final int OPERATE_CODE = 7;

    public static final int KEEP2_BYTE1 = 14;
    public static final int KEEP2_BYTE2 = 15;

    @Override
    public String getKey() {
        return "old-parking-down-link";
    }

    @Override
    public JsonNode execute(ThingMessage message, Map<String, String> metadata) throws ScriptException {

        ObjectNode resultData = this.objectMapper.createObjectNode();
        ObjectNode resultMeta = this.objectMapper.createObjectNode();

        Map<String, String> deviceMeta = message.getDeviceMetaData();
        String ip = deviceMeta.get("ipAddress");
        String port = deviceMeta.get("port");
        String channel = deviceMeta.get("channel");
        String laneNo = deviceMeta.get("laneNo");
        String username = deviceMeta.get("username");
        String password = deviceMeta.get("password");
        String ledIpAddress = deviceMeta.get("ledIpAddress");
        String ledPort = deviceMeta.get("ledPort");

        String identifier = message.getIdentifier();
        resultMeta.put("identifier", identifier);

        if (StrUtil.isEmpty(identifier)) {
            throw new ScriptException("param cmd can not be null");
        }

        String returnData = null;
        if ("pushLed".equals(identifier) || "pushVoice".equals(identifier)) {

            resultMeta.put("ledIpAddress", ledIpAddress);
            resultMeta.put("ledPort", ledPort);
            resultData.put("contentType", "BINARY");

            Map<String, Object> params = (Map<String, Object>) message.getData();

            StringBuilder sb = new StringBuilder();

            try {
                byte[] tmpRequestFrame = new byte[1024];

                getFramePrefix(tmpRequestFrame);

                String line1 = params.containsKey("line1") ? (String) params.get("line1") : "";
                String line2 = params.containsKey("line2") ? (String) params.get("line2") : "";
                String line3 = params.containsKey("line3") ? (String) params.get("line3") : "";
                String line4 = params.containsKey("line4") ? (String) params.get("line4") : "";
                String voiceContent = params.containsKey("content") ? (String) params.get("content") : "";

                byte[] data = getLedAndVoiceBytes(line1, line2, line3, line4, voiceContent);

                System.arraycopy(data, 0, tmpRequestFrame, 20, data.length);

                //数据总长 32位整形 低字节优先 4个字节
                int dataTotalLen = data.length;

                byte[] dataTotalLenBytes = intToByteArray(dataTotalLen);
                System.arraycopy(dataTotalLenBytes, 0, tmpRequestFrame, 10, dataTotalLenBytes.length);

                //数据长度 32位整形 低字节优先 4个字节
                byte[] dataLenBytes = intToByteArray(dataTotalLen);
                System.arraycopy(dataLenBytes, 0, tmpRequestFrame, 16, dataLenBytes.length);

                // 帧尾
                byte[] frameTailBytes = getFrameTailBytes();
                System.arraycopy(frameTailBytes, 0, tmpRequestFrame, 20 + dataTotalLen, frameTailBytes.length);

                byte[] requestFrame = new byte[dataTotalLen + 24];
                System.arraycopy(tmpRequestFrame, 0, requestFrame, 0, dataTotalLen + 24);

                // Base64
                sb.append(Base64.getEncoder().encodeToString(requestFrame));

                returnData = sb.toString();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if ("manualControl".equals(identifier)) {
            resultMeta.put("username", username);
            resultMeta.put("password", password);
            resultData.put("contentType", "XML");
            Map<String, Object> params = (Map<String, Object>) message.getData();
            if (!params.containsKey("command")) {
                throw new ScriptException("bad content");
            } else {
                String mode = (String) params.get("command");
                //元数据增加集成号对应的命令
                resultMeta.put(ip,mode);
                String modeStr = null;
                switch (mode) {
                    case "0":
                        modeStr = "<ctrlMode opt=\"open,close,lock,unlock\">close</ctrlMode>";
                        break;
                    case "1":
                        modeStr = "<ctrlMode opt=\"open,close,lock,unlock\">open</ctrlMode>";
                        break;
                    case "2":
                        modeStr = "<ctrlMode opt=\"open,close,lock,unlock\">lock</ctrlMode>";
                        //存储锁闸设备信息
                        //业务剥离主程，框架代码回迁主程
                        LockDeviceCache.put(ip,mode);
                        break;
                    case "3":
                        modeStr = "<ctrlMode opt=\"open,close,lock,unlock\">unlock</ctrlMode>";
                        break;
                    default:
                        break;
                }

                if (StrUtil.isEmpty(modeStr)) {
                    throw new ScriptException("bad content");
                } else {
                    StringBuilder sb = new StringBuilder();
                    sb.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
                    sb.append("<BarrierGate xmlns=\"http://www.isapi.org/ver20/XMLSchema\" version=\"2.0\">");
                    sb.append(modeStr);
                    sb.append("</BarrierGate>");
                    returnData = sb.toString();

                    String url = String.format("http://%s:%s/ISAPI/Parking/channels/%s/barrierGate", ip, port, channel);
                    resultMeta.put("url", url);
                }
            }
        }

        if (StrUtil.isEmpty(returnData)) {
            throw new ScriptException("bad content");
        } else {
            resultData.put("metadata", resultMeta);
            resultData.put("data", returnData);
            return resultData;
        }
    }

    /**
     * 获取led 和 文字的 字节数组信息
     *
     * @return byte[]
     * @throws Exception 编码异常
     */
    private byte[] getLedAndVoiceBytes(String line1, String line2, String line3, String line4,
                                       String voiceContent) throws Exception {

        // 开始构建节目数据块
        byte[] programDataBlock = new byte[1];
        // 节目总数量
        programDataBlock[0] = (byte) 0x1;

        //区域号，从1开始
        int areaNumber = 1;
        byte[] tempCharacterArray = new byte[1024];
        // 主要用来标记上一区域的字节长度信息，作为下个区域的起始复制小标
        int lastAreaLength = 0;
        int programAreaNumbers = 0;

        // 第一行
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(line1)) {
            byte[] oneAreaBytes = getCharacterAreaData((byte) areaNumber, (byte) 0x00, (byte) 0x0f, (byte) 0x02,
                    (byte) 0x01, (byte) 0x1e, line1);
            System.arraycopy(oneAreaBytes, 0, tempCharacterArray, 0, oneAreaBytes.length);
            lastAreaLength = lastAreaLength + oneAreaBytes.length;
            areaNumber = areaNumber + 1;
            programAreaNumbers = programAreaNumbers + 1;
        }

        // 第二行
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(line2)) {
            byte[] twoAreaBytes = getCharacterAreaData((byte) areaNumber, (byte) 0x10, (byte) 0x1f, (byte) 0x04,
                    (byte) 0x01, (byte) 0x1e, line2);
            System.arraycopy(twoAreaBytes, 0, tempCharacterArray, lastAreaLength, twoAreaBytes.length);
            lastAreaLength = lastAreaLength + twoAreaBytes.length;
            areaNumber = areaNumber + 1;
            programAreaNumbers = programAreaNumbers + 1;
        }

        //第三行
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(line3)) {
            byte[] threeAreaBytes = getCharacterAreaData((byte) areaNumber, (byte) 0x20, (byte) 0x2f, (byte) 0x01,
                    (byte) 0x20, (byte) 0x1e, line3);
            System.arraycopy(threeAreaBytes, 0, tempCharacterArray, lastAreaLength, threeAreaBytes.length);
            lastAreaLength = lastAreaLength + threeAreaBytes.length;
            areaNumber = areaNumber + 1;
            programAreaNumbers = programAreaNumbers + 1;
        }
        //第四行
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(line4)) {
            byte[] fourAreaBytes = getCharacterAreaData((byte) areaNumber, (byte) 0x30, (byte) 0x3f, (byte) 0x04,
                    (byte) 0x20, (byte) 0x1e, line4);
            System.arraycopy(fourAreaBytes, 0, tempCharacterArray, lastAreaLength, fourAreaBytes.length);
            lastAreaLength = lastAreaLength + fourAreaBytes.length;
            areaNumber = areaNumber + 1;
            programAreaNumbers = programAreaNumbers + 1;
        }

        byte[] totalCharacterBytes;
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(voiceContent)) {
            programAreaNumbers = 1;
            totalCharacterBytes = getVoiceAreaData(voiceContent);
        } else {
            totalCharacterBytes = new byte[lastAreaLength];
            System.arraycopy(tempCharacterArray, 0, totalCharacterBytes, 0, lastAreaLength);
        }

        byte[] programBytes = getProgramProperties(totalCharacterBytes.length,
                getProgramAreaNumbersByte(programAreaNumbers));

        byte[] data = new byte[1 + totalCharacterBytes.length + programBytes.length];

        System.arraycopy(programDataBlock, 0, data, 0, programDataBlock.length);
        System.arraycopy(programBytes, 0, data, 1, programBytes.length);
        System.arraycopy(totalCharacterBytes, 0, data, programBytes.length + 1, totalCharacterBytes.length);

        return data;
    }

    /**
     * int 转byte 低字节在前（低字节序）
     *
     * @param n int 参数
     * @return 字节数组
     */
    private static byte[] intToByteArray(int n) {
        byte[] b = new byte[4];
        b[0] = (byte) (n & 0xff);
        b[1] = (byte) (n >> 8 & 0xff);
        b[2] = (byte) (n >> 16 & 0xff);
        b[3] = (byte) (n >> 24 & 0xff);
        return b;
    }

    /**
     * 获取帧尾的字节数据
     *
     * @return byte[]
     */
    private byte[] getFrameTailBytes() {
        byte[] frameTailBytes = new byte[4];
        frameTailBytes[0] = (byte) 0x00;
        frameTailBytes[1] = (byte) 0x00;
        frameTailBytes[2] = (byte) 0x0d;
        frameTailBytes[3] = (byte) 0x0a;
        return frameTailBytes;
    }

    /**
     * 获取节目属性
     * 节目区域数量 最大值为 5 其中：1.显示类型的区域最大共为 4；2.语音类型最多为 1；
     *
     * @param areaProgramDataLength 节目数据大小
     * @param programAreaNumbers    节目区域数量
     * @return byte[]
     */
    private byte[] getProgramProperties(int areaProgramDataLength, byte programAreaNumbers) {
        //节目属性，应该独立出一个方法
        byte[] programProperties = new byte[512];

        programProperties[0] = (byte) 0x1;

        int programDataLength = areaProgramDataLength + 1 + 4 + 1 + 18;
        //节目数据大小
        byte[] programDataLengthBytes = intToByteArray(programDataLength);
        System.arraycopy(programDataLengthBytes, 0, programProperties, 1, programDataLengthBytes.length);

        //节目区域数量
        programProperties[5] = programAreaNumbers;

        //保留域 18 字节，设置为0
        byte[] programKeepArea = new byte[18];
        System.arraycopy(programKeepArea, 0, programProperties, 6, programKeepArea.length);
        // 数据域的长度 = 节目数据块 的 1个字节 + （节目属性 + 区域数据库）/ 2
        byte[] programPropertiesBytes = new byte[24];
        System.arraycopy(programProperties, 0, programPropertiesBytes, 0, 24);

        return programPropertiesBytes;
    }

    /**
     * 获取一行文字区域数据 的字节数组
     * 字符内码区域属性
     *
     * @param areaNumber           区域号
     * @param areaCoordinate       区域坐标
     * @param areaCoordinateSuffix 区域坐标后缀
     * @param fontColor            字体颜色
     * @param actionType           动作方式
     * @param speed                速度
     * @param lineContent          led文字字符串
     */
    private byte[] getCharacterAreaData(byte areaNumber, byte areaCoordinate,
                                        byte areaCoordinateSuffix, byte fontColor, byte actionType, byte speed,
                                        String lineContent) {
        byte[] characterProperties = new byte[512];

        //区域号 从 1 开始
        characterProperties[0] = areaNumber;

        //区域数据类型， 文字固定为0x0E：字符内码
        characterProperties[5] = (byte) 0x0E;

        //区域坐标6-13
        characterProperties[6] = (byte) 0x00;
        characterProperties[7] = (byte) 0x00;
        characterProperties[8] = areaCoordinate;
        characterProperties[9] = (byte) 0x00;
        characterProperties[10] = (byte) 0x3f;

        characterProperties[11] = (byte) 0x00;
        characterProperties[12] = areaCoordinateSuffix;
        characterProperties[13] = (byte) 0x00;

        //区域字体颜色 0x01:红;0x02:绿;0x04:黄(红+绿)
        characterProperties[14] = fontColor;

        //15-16 保留域，没有用处
        characterProperties[15] = (byte) 0x00;
        characterProperties[16] = (byte) 0x00;

        //D17动作方式
        characterProperties[17] = actionType;

        //D18保留域
        characterProperties[18] = (byte) 0x00;

        //D19 速度
        characterProperties[19] = speed;

        //D20 每页停留时间
        characterProperties[20] = (byte) 0x01;

        //D21字体大小  0x10:16x16 字体;0x20:32x32 字体
        characterProperties[21] = (byte) 0x10;

        try {
            //D22--D25  字符串长度
            byte[] stringLength = intToByteArray(lineContent.getBytes("GBK").length);
            System.arraycopy(stringLength, 0, characterProperties, 22, stringLength.length);

            //D26----Dn  显示字符串 GBK 编码
            byte[] lineContentBytes = lineContent.getBytes("GBK");
            System.arraycopy(lineContentBytes, 0, characterProperties, 26, lineContentBytes.length);

            //26 是其他所有字段加起来的字节总长度;  计算方式：1+4+1+8+1+2+1+1+1+1+1+4;具体查看对接文档中的《字符内码区域属性》
            int areaDataLength = 26 + lineContentBytes.length;

            byte[] areaDataLengthArray = intToByteArray(areaDataLength);

            System.arraycopy(areaDataLengthArray, 0, characterProperties, 1, areaDataLengthArray.length);

            // 在最后计算出总的数据长度，然后再赋值，最后，再拷贝一个新数组，返回真正的字节数组
            byte[] oneAreaDataBytes = new byte[areaDataLength];
            System.arraycopy(characterProperties, 0, oneAreaDataBytes, 0, areaDataLength);
            return oneAreaDataBytes;
        } catch (UnsupportedEncodingException unsupportedEncodingException) {
            unsupportedEncodingException.printStackTrace();
        }
        return null;
    }

    /**
     * 获取语音播报的字节数组
     *
     * @param voiceContent 语音播报内容
     * @return byte[]
     */
    private byte[] getVoiceAreaData(String voiceContent) {
        byte[] voiceProperties = new byte[512];
        //语音播报的区域号，固定为05. 如果是其他区域，会被覆盖，或者文字不会再移动
        voiceProperties[0] = (byte) 0x01;
        //区域数据类型 0x2D：语音播报数据
        voiceProperties[5] = (byte) 0x2D;

        //保留域  D6-D20
        byte[] keepArea = new byte[15];
        System.arraycopy(keepArea, 0, voiceProperties, 6, keepArea.length);

        // 循环播放次数
        voiceProperties[21] = (byte) 0x01;

        try {
            byte[] voiceContentBytes = voiceContent.getBytes("GBK");
            //D22-D25字符串长度
            byte[] voiceContentLength = intToByteArray(voiceContentBytes.length);
            System.arraycopy(voiceContentLength, 0, voiceProperties, 22, voiceContentLength.length);

            //D26-----Dn 显示字符串
            System.arraycopy(voiceContentBytes, 0, voiceProperties, 26, voiceContentBytes.length);

            //区域数据大小 D1--D4
            //26 是其他所有字段加起来的字节总长度;  计算方式：1+4+1+15+1+4;具体查看对接文档中的《语音播报数据属性》
            int areaDataLength = voiceContentBytes.length + 26;
            byte[] areaDataLengthArray = intToByteArray(areaDataLength);
            System.arraycopy(areaDataLengthArray, 0, voiceProperties, 1, areaDataLengthArray.length);

            // 在最后计算出总的数据长度，然后再赋值，最后，再拷贝一个新数组，返回真正的字节数组
            byte[] voiceAreaDataBytes = new byte[areaDataLength];
            System.arraycopy(voiceProperties, 0, voiceAreaDataBytes, 0, areaDataLength);
            return voiceAreaDataBytes;

        } catch (UnsupportedEncodingException unsupportedEncodingException) {
            unsupportedEncodingException.printStackTrace();
        }
        return null;
    }


    /**
     * 节目号和byte的转换
     *
     * @param number int类型节目号
     * @return byte
     * @throws Exception
     */
    private byte getProgramAreaNumbersByte(int number) throws Exception {
        int oneProgram = 1;
        int twoProgram = 2;
        int threeProgram = 3;
        int fourProgram = 4;
        int fiveProgram = 5;
        if (number == oneProgram) {
            return (byte) 0x01;
        } else if (number == twoProgram) {
            return (byte) 0x02;
        } else if (number == threeProgram) {
            return (byte) 0x03;
        } else if (number == fourProgram) {
            return (byte) 0x04;
        } else if (number == fiveProgram) {
            return (byte) 0x05;
        } else {
            throw new Exception("Program Area Numbers error!!");
        }

    }

    /**
     * short 转byte 数组
     *
     * @param s short 参数
     * @return byte[]
     */
    private static byte[] short2byte(short s) {
        byte[] b = new byte[2];
        for (int i = 0; i < b.length; i++) {
            //因为byte占4个字节，所以要计算偏移量
            int offset = 16 - (i + 1) * 8;

            //把16位分为2个8位进行分别存储
            b[i] = (byte) ((s >> offset) & 0xff);
        }
        return b;
    }

    /**
     * 获取报文帧的前缀。这部分内容都是固定的
     *
     * @param tmpRequestFrame 字节数组
     */
    private void getFramePrefix(byte[] tmpRequestFrame) {
        //第一：帧头，4个字节
        tmpRequestFrame[FRAME_HEAD_BYTE1] = (byte) 0x55;
        tmpRequestFrame[FRAME_HEAD_BYTE2] = (byte) 0xaa;
        tmpRequestFrame[FRAME_HEAD_BYTE3] = (byte) 0x00;
        tmpRequestFrame[FRAME_HEAD_BYTE4] = (byte) 0x00;

        //第二：地址，1个字节
        tmpRequestFrame[ADDRESS] = (byte) 0x01;

        //第三：保留域 2个字节
        tmpRequestFrame[KEEP_ONE_BYTE1] = (byte) 0x00;
        tmpRequestFrame[KEEP_ONE_BYTE2] = (byte) 0x00;

        //第四：操作码 1个字节
        tmpRequestFrame[OPERATE_CODE] = (byte) 0xdb;

        //第五：帧序号 16位整形 低字节优先 2个字节
        short frameSequence = 0;
        byte[] frameSequenceByte = short2byte(frameSequence);
        System.arraycopy(frameSequenceByte, 0, tmpRequestFrame, 8, frameSequenceByte.length);

        //第六：数据总长

        //第七：保留域 2个字节
        tmpRequestFrame[KEEP2_BYTE1] = (byte) 0x00;
        tmpRequestFrame[KEEP2_BYTE2] = (byte) 0x00;
    }

    /**
     * byte[]转int
     *
     * @param bytes 需要转换成int的数组
     * @return int值
     */
    private static int byteArrayToInt(byte[] bytes) {
        int value = 0;
        for (int i = 0; i < 4; i++) {
            int shift = (3 - i) * 8;
            value += (bytes[i] & 0xFF) << shift;
        }
        return value;
    }

    @Override
    public void destroy() {
        internalEvaluatorSet.removeDownLinkEvaluator(this.getKey());
    }
}
