package com.yuchen.iot.gateway.coverter.jieshun;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.yuchen.iot.gateway.connector.api.converter.AbstractKeyEvaluator;
import com.yuchen.iot.gateway.connector.api.converter.evaluator.DownLinkEvaluator;
import com.yuchen.iot.gateway.connector.api.converter.internal.InternalEvaluatorSet;
import com.yuchen.iot.gateway.connector.api.message.ThingMessage;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.script.ScriptException;
import java.util.Map;

/**
 * 捷顺 停车场设备下行 转换器
 *
 * <AUTHOR>
 * @date 2022/11/17 下午3:00
 * @version 2.0.0
 */
@Component
public class JieShunParkingDownLinkEvaluator extends AbstractKeyEvaluator implements DownLinkEvaluator {
    @Resource
    @Lazy
    private InternalEvaluatorSet internalEvaluatorSet;

    @Override
    public String getKey() {
        return "jieshun-sdk-parking-down-link";
    }

    @Override
    public JsonNode execute(ThingMessage message, Map<String, String> metadata) throws ScriptException {
        ObjectNode resultData = this.objectMapper.createObjectNode();
        ObjectNode resultMeta = this.objectMapper.createObjectNode();
        String identifier = message.getIdentifier();
        resultMeta.put("identifier", identifier);
        if (StrUtil.isEmpty(identifier)) {
            throw new ScriptException("param cmd can not be null");
        }
        Map<String, Object> params = (Map<String, Object>) message.getData();
        String returnData = null;
        if ("pushLed".equals(identifier)) {
            resultData.put("contentType", "TEXT");
            try {
                String line1 = params.containsKey("line1") ? (String) params.get("line1") : "";
                String line2 = params.containsKey("line2") ? (String) params.get("line2") : "";
                String line3 = params.containsKey("line3") ? (String) params.get("line3") : "";
                String line4 = params.containsKey("line4") ? (String) params.get("line4") : "";
                String ledContent;
                if (ObjectUtils.isNotEmpty(line4) && line4.contains("费")) {
                    ledContent = line2.concat("<br>").concat(line4);
                } else if (ObjectUtils.isEmpty(line2)) {
                    ledContent = line1;
                } else if (ObjectUtils.isEmpty(line3)) {
                    ledContent = line1.concat("<br>").concat(line2);
                } else {
                    ledContent = line2.concat("<br>").concat(line3);
                }
                returnData = ledContent;
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if ("pushVoice".equals(identifier)) {
            resultData.put("contentType", "TEXT");
            returnData = params.containsKey("content") ? (String) params.get("content") : "";
        }

        if ("manualControl".equals(identifier)) {
            resultData.put("contentType", "TEXT");
            if (!params.containsKey("command")) {
                throw new ScriptException("bad content");
            } else {
                String mode = (String) params.get("command");
                if (StrUtil.isEmpty(mode)) {
                    throw new ScriptException("bad content");
                } else {
                    returnData = mode;
                }
            }
        }

        if (StrUtil.isEmpty(returnData)) {
            throw new ScriptException("bad content");
        } else {
            resultData.put("metadata", resultMeta);
            resultData.put("data", returnData);
            return resultData;
        }
    }

    @Override
    public void destroy() {
        internalEvaluatorSet.removeDownLinkEvaluator(this.getKey());
    }
}
