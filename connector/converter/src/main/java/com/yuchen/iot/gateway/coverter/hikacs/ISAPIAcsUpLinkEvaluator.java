package com.yuchen.iot.gateway.coverter.hikacs;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.yuchen.iot.gateway.connector.api.converter.AbstractKeyEvaluator;
import com.yuchen.iot.gateway.connector.api.converter.evaluator.UpLinkEvaluator;
import com.yuchen.iot.gateway.connector.api.data.UpLinkData;
import com.yuchen.iot.gateway.connector.api.data.UpLinkMetaData;
import com.yuchen.iot.gateway.connector.api.data.message.CallResponse;
import com.yuchen.iot.gateway.connector.api.data.message.Event;
import com.yuchen.iot.gateway.connector.api.data.message.EventValue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class ISAPIAcsUpLinkEvaluator extends AbstractKeyEvaluator implements UpLinkEvaluator {

    private final Logger logger = LoggerFactory.getLogger(ISAPIAcsUpLinkEvaluator.class);

    @Override
    public String getKey() {
        return "hik-acs-up-link";
    }

    @Override
    public void destroy() {

    }

    @Override
    public ListenableFuture<Object> execute(byte[] data, UpLinkMetaData upLinkMetaData) {


        List<UpLinkData> upLinkDataList = new ArrayList<>();
        Map<String, String>metaMap = upLinkMetaData.getKvMap();
        String identifier = metaMap.get("Header:identifier");

        if (identifier.equals("addUser") ){
            try {
                //{"requestURL":"/ISAPI/AccessControl/UserInfo/Record?format=json","statusCode":4,"statusString":"Invalid Operation","subStatusCode":"methodNotAllowed","errorCode":1073741828,"errorMsg":"unknow"}
                JsonNode jsonNode = this.objectMapper.readTree(data);
                //String requestURL = jsonNode.get("requestURL");
                String status = jsonNode.get("statusCode").toString();
                String statusString = jsonNode.get("statusString").toString();

                CallResponse callResponse = new CallResponse();
                callResponse.put("status",status);
                callResponse.put("statusString",statusString);
                //String productKey = upLinkMetaData.getKvMap().get("Header:productKey");
                //String deviceName = upLinkMetaData.getKvMap().get("Header:deviceName");
                String integrateNo = upLinkMetaData.getKvMap().get("Header:integrateNo");

                UpLinkData upLinkData = new UpLinkData(integrateNo, null, null, callResponse, null);
                upLinkDataList.add(upLinkData);

            }catch (Exception e){
                e.printStackTrace();
            }
        }else {
            try {
                JsonNode jsonNode = this.objectMapper.readTree(data);
                JsonNode eventNode = jsonNode.get("AccessControllerEvent");
                String productKey = "6llA3zhq8jRK6HA6";
                String deviceName = "testACS001";

                Event event = new Event();
                event.setIdentifier("AccessControllerEvent");

                EventValue eventValue = new EventValue();
                event.setValue(eventValue);
                event.setTime(System.currentTimeMillis() / 1000L);

                String mask = eventNode.get("mask").toString();
                if (!mask.isEmpty()){
                    eventValue.put("mask",mask);
                }

                String statusValue = eventNode.get("statusValue").toString();
                if (statusValue != null && !statusValue.isEmpty()){
                    eventValue.put("statusValue",statusValue);
                }

                String majorEventType = eventNode.get("majorEventType").toString();
                if (majorEventType != null && !majorEventType.isEmpty()){
                    eventValue.put("majorEventType",majorEventType);
                }

                String subEventType = eventNode.get("subEventType").toString();
                if (subEventType != null && !subEventType.isEmpty()){
                    eventValue.put("subEventType",subEventType);
                }
                //TODO Get IntegrateNo from Device
                UpLinkData upLinkData = new UpLinkData("E93616368", event, null, null, null);
                upLinkDataList.add(upLinkData);
                System.out.println(jsonNode.toString());
            } catch (IOException  e) {
                e.printStackTrace();
                logger.warn("Evaluator execute failed {}", e.getMessage());
            }
        }
        return Futures.immediateFuture(this.gson.toJson(upLinkDataList));
    }
}
