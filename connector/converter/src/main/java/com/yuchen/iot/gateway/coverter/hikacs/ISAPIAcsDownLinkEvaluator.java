package com.yuchen.iot.gateway.coverter.hikacs;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.yuchen.iot.gateway.connector.api.converter.AbstractKeyEvaluator;
import com.yuchen.iot.gateway.connector.api.converter.evaluator.DownLinkEvaluator;
import com.yuchen.iot.gateway.connector.api.message.ThingMessage;
import org.springframework.stereotype.Component;

import javax.script.ScriptException;
import java.util.Map;


@Component
public class ISAPIAcsDownLinkEvaluator  extends AbstractKeyEvaluator implements DownLinkEvaluator {

    @Override
    public String getKey() {
        return "hik-acs-down-link";
    }

    @Override
    public JsonNode execute(ThingMessage message, Map<String, String> metadata) throws ScriptException {

        ObjectNode resultData = this.objectMapper.createObjectNode();

        Map<String, String> deviceMeta = message.getDeviceMetaData();
        String ip = deviceMeta.get("ipAddress");
        String port = deviceMeta.get("port");
        String channel = deviceMeta.get("channel");
        String laneNo = deviceMeta.get("laneNo");
        String username = deviceMeta.get("username");
        String password = deviceMeta.get("password");

        String identifier = message.getIdentifier();
        if (StrUtil.isEmpty(identifier)) {
            throw new ScriptException("param cmd can not be null");
        }

        ObjectNode resultMeta = this.objectMapper.createObjectNode();

        resultMeta.put("username", username);
        resultMeta.put("password", password);

        String data = null;

        if ("addUser".equals(identifier)) {

            String url = String.format("http://%s:%s/ISAPI/AccessControl/UserInfo/Record?format=json", ip, port, channel);
            resultMeta.put("url", url);

            ObjectNode evtInfo = this.objectMapper.createObjectNode();
            Map<String, Object> params = (Map<String, Object>) message.getData();
            System.out.println(params.toString());
            try {
                String json = this.objectMapper.writeValueAsString(params);
                evtInfo.put("UserInfo",json);
                System.out.println(evtInfo);
            }catch (Exception e){
                e.printStackTrace();
            }

            //evtInfo.put("UserInfo", params);
            data = evtInfo.toString();

            resultData.put("contentType", "JSON");
        }


        if (StrUtil.isEmpty(data)) {
            throw new ScriptException("execute failed");
        } else {
            resultData.put("metadata", resultMeta);
            resultData.put("data", data);
            return resultData;
        }
    }

    @Override
    public void destroy() {

    }
}
