package com.yuchen.iot.gateway.coverter.hxzx;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.yuchen.iot.gateway.connector.api.converter.AbstractKeyEvaluator;
import com.yuchen.iot.gateway.connector.api.converter.evaluator.UpLinkEvaluator;
import com.yuchen.iot.gateway.connector.api.converter.internal.InternalEvaluatorSet;
import com.yuchen.iot.gateway.connector.api.data.UpLinkData;
import com.yuchen.iot.gateway.connector.api.data.UpLinkMetaData;
import com.yuchen.iot.gateway.connector.api.data.message.Event;
import com.yuchen.iot.gateway.connector.api.data.message.EventValue;
import com.yuchen.iot.gateway.dao.exception.DataValidationException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 华夏智信 停车场设备上行转换器
 *
 * <AUTHOR>
 * @version 2.0.0
 * @date 2023/04/13
 */
@Component
public class HXZXParkingUpLinkEvaluator extends AbstractKeyEvaluator implements UpLinkEvaluator {

	private final Logger logger = LoggerFactory.getLogger(HXZXParkingUpLinkEvaluator.class);

	@Resource
	@Lazy
	private InternalEvaluatorSet internalEvaluatorSet;

	@Override
	public ListenableFuture<Object> execute(byte[] data, UpLinkMetaData upLinkMetaData) {
		List<UpLinkData> upLinkDataList = new ArrayList<>();
		try {
			String text = new String(data, "UTF-8");
			JsonNode jsonNode = this.objectMapper.readTree(text);
			Event event = new Event();
			event.setIdentifier("passDetect");
			EventValue eventValue = new EventValue();
			event.setValue(eventValue);

            // 设备名
            String integrateNo = jsonNode.get("deviceId").asText();//获取监听的时候会用到
			String ipAddressAct = jsonNode.get("integrateNoAct") != null ? jsonNode.get("integrateNoAct").asText() : integrateNo;
			eventValue.put("integrateNoAct", ipAddressAct);//副抓拍获取组的时候需要用到

			// 车牌号
			if (jsonNode.has("plate_num")) {
				String plateNo = jsonNode.get("plate_num").asText().trim();
				if (StringUtils.isNotEmpty(plateNo) && StringUtils.isBlank(plateNo)) {
					throw new DataValidationException("plateNo 不应该空字符串!");
				}
				if (StringUtils.isEmpty(plateNo) || "无车牌".equals(plateNo) || "null".equals(plateNo)
						|| "无牌车".equals(plateNo)) {
					eventValue.put("plateNo", "无车牌");
				} else {
					eventValue.put("plateNo", plateNo);
				}
			} else {
				eventValue.put("plateNo", "无车牌");
			}
			logger.debug("passDetect 车牌识别信息及设备信息: {} {}", eventValue.get("plateNo"), integrateNo);

			// 车牌类型 & 车牌颜色
			if (jsonNode.has("plate_color")) {
				//车牌颜色
				String plateColor = jsonNode.get("plate_color").asText();
				String vehicleType = jsonNode.get("vehicle_type").asText();
				String carColor = jsonNode.get("car_color").asText();
				eventValue.put("plateColor", plateColor);
				eventValue.put("vehicleType", "1");
				eventValue.put("plateType", "9");
				// 车辆颜色
				if ("其它".equals(carColor)) {
					eventValue.put("vehicleColor", "unknown");
				} else {
					eventValue.put("vehicleColor", carColor);
				}
			} else {
				eventValue.put("plateColor", "unknown");
				eventValue.put("vehicleType", "0");
				eventValue.put("plateType", "0");
				// 车辆颜色
				eventValue.put("vehicleColor", "unknown");
			}

			// 车牌图片 & 车辆图片
			if (jsonNode.has("detectionPicture.jpg")) {
				if ("无车牌".equals(eventValue.get("plateNo"))) {
					String prefix = upLinkMetaData.getKvMap().get("ossPrefixKey");
					String detectionPicture = jsonNode.get("detectionPicture.jpg").asText();
					eventValue.put("platePicUrl", "");
					eventValue.put("vehiclePicUrl", prefix.concat("/") + detectionPicture);
				} else {
					String prefix = upLinkMetaData.getKvMap().get("ossPrefixKey");
					String detectionPicture = jsonNode.get("detectionPicture.jpg").asText();
					if (jsonNode.get("licensePlatePicture.jpg") != null) {
						String licensePlatePicture = jsonNode.get("licensePlatePicture.jpg").asText();
						eventValue.put("platePicUrl", prefix.concat("/") + licensePlatePicture);
					} else {
						eventValue.put("platePicUrl", "");
					}
					eventValue.put("vehiclePicUrl", prefix.concat("/") + detectionPicture);
				}
			}

			// 检测时间
			String dateTime = jsonNode.get("utc_ts").asText();
			if (dateTime == null) {
				event.setTime(System.currentTimeMillis() / 1000L);
			} else {
				if (StringUtils.isEmpty(dateTime)) {
					event.setTime(System.currentTimeMillis() / 1000L);
				} else {
					event.setTime(Long.valueOf(dateTime) / 1000L);
				}
			}
			UpLinkData upLinkData = new UpLinkData(integrateNo, event, null, null, null);
			upLinkDataList.add(upLinkData);
		} catch (IOException | DataValidationException e) {
			e.printStackTrace();
			logger.warn("Evaluator execute failed {}", e.getMessage());
		}
		return Futures.immediateFuture(this.gson.toJson(upLinkDataList));
	}

	@Override
	public String getKey() {
		return "hxzx-ws-parking-up-link";
	}

	@Override
	public void destroy() {
		internalEvaluatorSet.removeUpLinkEvaluator(this.getKey());
	}

}
