package com.yuchen.iot.gateway.coverter.jieshun;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.yuchen.iot.gateway.connector.api.converter.AbstractKeyEvaluator;
import com.yuchen.iot.gateway.connector.api.converter.evaluator.UpLinkEvaluator;
import com.yuchen.iot.gateway.connector.api.converter.internal.InternalEvaluatorSet;
import com.yuchen.iot.gateway.connector.api.data.UpLinkData;
import com.yuchen.iot.gateway.connector.api.data.UpLinkMetaData;
import com.yuchen.iot.gateway.connector.api.data.message.Event;
import com.yuchen.iot.gateway.connector.api.data.message.EventValue;
import com.yuchen.iot.gateway.dao.exception.DataValidationException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * 捷顺 停车场设备上行转换器
 *
 * <AUTHOR>
 * @date 2022/11/17 下午3:00
 * @version 2.0.0
 */
@Component
public class JieShunParkingUpLinkEvaluator extends AbstractKeyEvaluator implements UpLinkEvaluator {

    private final Logger logger = LoggerFactory.getLogger(JieShunParkingUpLinkEvaluator.class);

    @Resource
    @Lazy
    private InternalEvaluatorSet internalEvaluatorSet;

    @Override
    public ListenableFuture<Object> execute(byte[] data, UpLinkMetaData upLinkMetaData) {
        List<UpLinkData> upLinkDataList = new ArrayList<>();
        try {
            String text = new String(data, "UTF-8");
            JsonNode jsonNode = this.objectMapper.readTree(text);
            if (jsonNode.isArray()) {
                for (JsonNode eventNode : jsonNode) {
                    Event event = new Event();
                    event.setIdentifier("passDetect");
                    EventValue eventValue = new EventValue();
                    event.setValue(eventValue);

                    // 设备名
                    String integrateNo = eventNode.get("deviceId").asText();
                    eventValue.put("integrateNoAct",integrateNo);//这个地方不区分主副抓拍

                    // 车牌号
                    if (eventNode.has("carNo")) {
                        String plateNo = eventNode.get("carNo").asText();
                        if(StringUtils.isNotEmpty(plateNo) && StringUtils.isBlank(plateNo)){
                            throw new DataValidationException("plateNo 不应该空字符串!");
                        }
                        if (StringUtils.isEmpty(plateNo) || "无车牌".equals(plateNo)) {
                            eventValue.put("plateNo", "无车牌");
                        } else {
                            eventValue.put("plateNo", plateNo);
                        }
                    } else {
                        eventValue.put("plateNo", "无车牌");
                    }
                    logger.debug("passDetect 车牌识别信息及设备信息: {} {}", eventValue.get("plateNo"), integrateNo);

                    // 车牌类型 & 车牌颜色
                    if (eventNode.has("carNumColor")) {
                        int plateColor = eventNode.get("carNumColor").asInt();
                        if (plateColor == 5) {
                            eventValue.put("plateColor", "green"); // 绿
                            eventValue.put("vehicleType", "4"); // 新能源车
                            eventValue.put("plateType", "8"); // 绿牌车
                        } else if (plateColor == 2) {
                            eventValue.put("plateColor", "yellow"); // 黄
                            eventValue.put("vehicleType", "2"); // 大型车
                            eventValue.put("plateType", "17"); // 黄牌车
                        } else {
                            eventValue.put("plateColor", "blue");
                            eventValue.put("vehicleType", "1");
                            eventValue.put("plateType", "0");
                        }
                    } else {
                        eventValue.put("plateColor", "unknown");
                        eventValue.put("vehicleType", "1");
                        eventValue.put("plateType", "9");
                    }

                    // 车辆颜色
                    eventValue.put("vehicleColor", "unknown");

                    // 车牌图片 & 车辆图片
                    if (eventNode.has("pictureFilePath")) {
                        if ("无车牌".equals(eventValue.get("plateNo"))) {
                            String prefix = upLinkMetaData.getKvMap().get("ossPrefixKey");
                            JsonNode xtNode = eventNode.get("pictureFilePath").get(1);
                            eventValue.put("platePicUrl", "");
                            eventValue.put("vehiclePicUrl", prefix + xtNode.asText().replace("down/pic", ""));
                        } else {
                            String prefix = upLinkMetaData.getKvMap().get("ossPrefixKey");
                            JsonNode plateNoPicNode = eventNode.get("pictureFilePath").get(1);
                            JsonNode xtNode = eventNode.get("pictureFilePath").get(0);
                            eventValue.put("platePicUrl", prefix + plateNoPicNode.asText().replace("down/pic", ""));
                            eventValue.put("vehiclePicUrl", prefix + xtNode.asText().replace("down/pic", ""));
                        }
                    }

                    // 检测时间
                    JsonNode dateTime = eventNode.get("time");
                    if (dateTime == null) {
                        event.setTime(System.currentTimeMillis() / 1000L);
                    } else {
                        String date = dateTime.get("time").asText();
                        if (StringUtils.isEmpty(date)) {
                            event.setTime(System.currentTimeMillis() / 1000L);
                        } else {
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            event.setTime(sdf.parse(date).getTime() / 1000L);
                        }
                    }
                    UpLinkData upLinkData = new UpLinkData(integrateNo, event, null, null, null);
                    upLinkDataList.add(upLinkData);
                }
            }
        } catch (IOException | DataValidationException | ParseException e) {
            e.printStackTrace();
            logger.warn("Evaluator execute failed {}", e.getMessage());
        }
        return Futures.immediateFuture(this.gson.toJson(upLinkDataList));
    }

    @Override
    public String getKey() {
        return "jieshun-sdk-parking-up-link";
    }

    @Override
    public void destroy() {
        internalEvaluatorSet.removeUpLinkEvaluator(this.getKey());
    }

}
