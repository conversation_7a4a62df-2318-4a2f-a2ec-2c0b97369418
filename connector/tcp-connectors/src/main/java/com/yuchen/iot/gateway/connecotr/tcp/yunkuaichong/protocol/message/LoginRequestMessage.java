package com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.message;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.BCDUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import io.netty.util.ReferenceCountUtil;
import lombok.Data;


/**
 * @description: 登录请求
 * @author: zhong
 * @create: 2022-12-08 16:55
 **/

@JsonIgnoreProperties(value = { "handler" })
@Data
public class LoginRequestMessage implements MessageDecoder {


    public String devNo;



    @Override
    public void decode(ByteBuf in) {
        ByteBuf byteBuf=  in.readBytes(7);

        byte[] bytes = ByteBufUtil.getBytes(byteBuf);
        // TODO:排查内存泄漏的问题
     //   ReferenceCountUtil.release(byteBuf);
        //    bytes = BytesUtil.bytesReverse(bytes);
         devNo = BCDUtil.bytesToHex(bytes);
    }


}
