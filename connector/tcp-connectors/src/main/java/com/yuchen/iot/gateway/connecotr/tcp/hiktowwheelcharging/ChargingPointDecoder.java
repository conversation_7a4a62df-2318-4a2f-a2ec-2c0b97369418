package com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yuchen.iot.gateway.connecotr.tcp.JsonTcpConnectorMessage;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.util.ExecutorUtils;
import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.comm.Constant;
import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.ChargingPointProtocol;
import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.MsgHeader;
import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.MsgType;
import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.ProtocolConstants;
import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.message.*;
import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.request.*;
import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.util.BytesUtil;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.util.HexStringUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import io.netty.util.ReferenceCountUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * 处理海康充电桩 字节流 解码
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023年5月25日 20:03:54
 */
public class ChargingPointDecoder extends ByteToMessageDecoder {

    private static final Logger log = LoggerFactory.getLogger(ChargingPointDecoder.class);

    private static ExecutorService chargingEventExecutor;

    static {
        chargingEventExecutor = new ExecutorUtils(50, 100, 60000L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(100000), "hik-charging-iot-executor");
    }

    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) throws Exception {
      //  log.info("ChargingPointDecoder---" + HexStringUtil.Bytes2hexStr(ByteBufUtil.getBytes(in)));
        // 如果有粘包情况，则可以多次读取

        log.info("ChargingPointDecoder---" + BytesUtil.bytesToHexString(ByteBufUtil.getBytes(in)));


        log.info("ChargingPointDecoder---while---begin ---{}", System.currentTimeMillis());
        in.markReaderIndex();
        byte[] inArray = new byte[in.readableBytes()];
        in.getBytes(in.readerIndex(), inArray);

        byte[] newInArray = inDecode(inArray);


        ByteBuf newIn=  Unpooled.wrappedBuffer(newInArray);

        byte preamble = newIn.readByte();
        if (preamble != ProtocolConstants.PREAMBLE) {
            log.info("preamble number is illegal, " + preamble);
            log.info("ChargingPointDecoder---preamble---{}", HexStringUtil.Bytes2hexStr(ByteBufUtil.getBytes(newIn)));
            newIn.clear();
            return;
        }

        byte msgId = newIn.readByte();

        ByteBuf byteBuf = newIn.readBytes(6);
        byte[] bytes = ByteBufUtil.getBytes(byteBuf);

        ByteBuf messageBody = newIn.readBytes(newIn.readableBytes() - 2);


        // 组装消息
        MsgHeader header = new MsgHeader();
        header.setPreamble(preamble);
        header.setMsgId(msgId);
        header.setDeviceNo(BCDUtil.bcdToStr(bytes));

        chargingEventExecutor.submit(() -> {
            //构建消费发送
            processMessageBody(ctx, header, messageBody);
            ReferenceCountUtil.release(messageBody);
        });

        log.info("ChargingPointDecoder---while---end---{}", System.currentTimeMillis());

    }


    // 将十六进制字符串转换为字节数组
    public static byte[] hexStringToByteArray1(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
                    + Character.digit(s.charAt(i + 1), 16));
        }
        return data;
    }

    // 计算字节数组的异或校验
    public static byte calculateXorChecksum(byte[] bytes) {
        byte checksum = 0;
        for (byte b : bytes) {
            checksum ^= b;
        }
        return checksum;
    }

    public static byte[] hexStringToByteArray(String hexString) {
        int len = hexString.length();
        byte[] byteArray = new byte[len / 2];
        for (int i = 0; i < len - 1; i += 2) {
            byteArray[i / 2] = (byte) ((Character.digit(hexString.charAt(i), 16) << 4)
                    + Character.digit(hexString.charAt(i + 1), 16));
        }
        return byteArray;
    }


    public byte[] inDecode(byte[] input) {
        // Calculate the length of the output array
        int outputLength = 0;
        for (int i = 0; i < input.length; i++) {
            if (input[i] == (byte) 0x7d && i + 1 < input.length) {
                outputLength++;
                i++; // Skip the next byte as it is part of the escape sequence
            } else {
                outputLength++;
            }
        }

        byte[] output = new byte[outputLength];
        int j = 0;
        for (int i = 0; i < input.length; i++) {
            if (input[i] == (byte) 0x7d && i + 1 < input.length) {
                switch (input[i + 1]) {
                    case 0x02:
                        output[j++] = 0x7e;
                        break;
                    case 0x01:
                        output[j++] = 0x7d;
                        break;
                    default:
                        // Handle unexpected escape sequences by copying them as-is
                        output[j++] = input[i];
                        output[j++] = input[i + 1];
                }
                i++; // Skip the next byte as it is part of the escape sequence
            } else {
                output[j++] = input[i];
            }
        }

        return output;
    }

    private void processMessageBody(ChannelHandlerContext ctx, MsgHeader header, ByteBuf data) {
        // 根据不同的messageId解析消息体
        MsgType msgTypeEnum = MsgType.findByType(header.getMsgId());
        if (msgTypeEnum == null) {
            log.info("ChargingPointDecoder---msgTypeEnum---{}", JSON.toJSONString(header));
            return;
        }
        ChargingPointProtocol<?> chargingPointProtocol = null;
        ChargingChannelListener listener = ctx.channel().attr(Constant.eventListenerKey).get();
        switch (msgTypeEnum) {
            case deviceState: {
                chargingPointProtocol = packet(DeviceStateRequestMessage.class, header, data);

                // 如果充电桩的channel不存在则不进行回应

                Channel channel = ChargingPointChannelManager.getChannelByName(header.deviceNo);
                log.info("海康充电桩进行心跳回应1:{}", header.deviceNo);
                if (null == channel) {
                    log.info("通道不存在重新建立通道", header.deviceNo);
                    ChargingPointChannelManager.addChannel(header.deviceNo, ctx.channel());
                }else{
                    log.info("通道状态isActive:", channel.isActive());
                    log.info("通道状态isOpen:", channel.isOpen());
                }


                DeviceStateResponeMessage deviceStateResponeMessage = new DeviceStateResponeMessage();
                ChargingPointProtocol<DeviceStateResponeMessage> response = new ChargingPointProtocol<>();
                response.setBody(deviceStateResponeMessage);
                response.setHeader(header);
                ctx.channel().writeAndFlush(response);
                log.info("海康充电桩进行心跳回应:{}", header.deviceNo);
                break;
            }
            case DeviceAlarm:
                chargingPointProtocol = packet(DeviceAlarmRequestMessage.class, header, data);
                break;
            case BeginChargingInfo:
                chargingPointProtocol = packet(BeginChargingRequestMessage.class, header, data);
                break;
            case DeviceRegistr:
                chargingPointProtocol = packet(DeviceRegistrRequestMessage.class, header, data);

                Channel oldChannel = ChargingPointChannelManager.getChannelByName(header.deviceNo);
                if (oldChannel != null) {
                    log.info("存在旧的channel，清理旧的channel配置信息，避免后期旧channel关闭时把新的channel关闭了");
                    oldChannel.attr(Constant.integrateNoKey).remove();
                }

                // 存储新channel
                ChargingPointChannelManager.addChannel(header.deviceNo, ctx.channel());


                log.info("海康登陆:{}", header.deviceNo);

                // 将客户端ID作为自定义属性加入到channel中，方便随时channel中获取用户ID
                ctx.channel().attr(Constant.integrateNoKey).setIfAbsent(header.deviceNo);

                // iot设备上线
                if (listener != null) {
                    listener.onConnect(header.deviceNo);
                }


                break;
            case FreeModeChannle:
                chargingPointProtocol = packet(FreeModeChannleRequestMessage.class, header, data);
                break;
            case MeterReading:
                chargingPointProtocol = packet(MeterReadingRequestMessage.class, header, data);
                break;
            case UserSwipeCard:
                chargingPointProtocol = packet(UserSwipeCardRequestMessage.class, header, data);
                break;
            case SIMInfo:
                chargingPointProtocol = packet(SIMInfoRequestMessage.class, header, data);
                break;


        }


        if (listener != null) {
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            JsonNode node = objectMapper.valueToTree(chargingPointProtocol);
            listener.onMessage(new JsonTcpConnectorMessage(node));
        }
    }

    private void validateChecksum(byte checksum) {
        // 实现校验码的验证
    }


    private static <T extends MessageDecoder> ChargingPointProtocol<T> packet(Class<T> clazz, MsgHeader msgHeader,
                                                                              ByteBuf data) {

        try {
            T t = clazz.getDeclaredConstructor().newInstance();
            t.decode(data);
            ChargingPointProtocol<T> protocol = new ChargingPointProtocol<>();
            protocol.setHeader(msgHeader);
            protocol.setBody(t);
            return protocol;
        } catch (InstantiationException | IllegalAccessException | InvocationTargetException |
                 NoSuchMethodException e) {
            throw new RuntimeException(e);
        }

    }

    private static String bytesToHex(final byte[] bytes) {
        final int numBytes = bytes.length;
        final char[] container = new char[numBytes * 2];

        for (int i = 0; i < numBytes; i++) {
            final int b = bytes[i] & 0xFF;
            container[i * 2] = Character.forDigit(b >>> 4, 0x10);
            container[i * 2 + 1] = Character.forDigit(b & 0xF, 0x10);
        }

        return new String(container);
    }

}
