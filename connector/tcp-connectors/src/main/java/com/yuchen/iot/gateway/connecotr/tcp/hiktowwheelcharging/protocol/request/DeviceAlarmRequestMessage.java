package com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.request;

import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.message.MessageDecoder;
import io.netty.buffer.ByteBuf;
import lombok.Data;

@Data
public class DeviceAlarmRequestMessage implements MessageDecoder {

    private int alarmType;


    private int alarmChannel ;

    private int alarmValue;


    @Override
    public void decode(ByteBuf in) {

         alarmType = in.readByte();


        // 读取报警通道号
         alarmChannel = in.readByte();



        alarmValue = in.readByte();

    }
}
