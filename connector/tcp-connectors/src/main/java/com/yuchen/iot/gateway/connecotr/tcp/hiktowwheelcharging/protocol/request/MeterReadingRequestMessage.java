package com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.request;

import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.message.MessageDecoder;
import io.netty.buffer.ByteBuf;
import lombok.Data;

@Data
public class MeterReadingRequestMessage implements MessageDecoder {


    private double actualMeterValue ;




    @Override
    public void decode(ByteBuf in) {
        // 读取电表值（4个字节，BCD编码）
        byte[] meterValueBytes = new byte[4];
        in.readBytes(meterValueBytes);

        // 转换BCD编码为实际数值
        long meterValue = bcdToLong(meterValueBytes);

        // 计算实际电表值（单位度）
         actualMeterValue = meterValue / 100.0;


    }

    // BCD编码转换为长整型数值
    private static long bcdToLong(byte[] bcdBytes) {
        long result = 0;
        for (byte b : bcdBytes) {
            int highNibble = (b & 0xF0) >> 4;
            int lowNibble = b & 0x0F;
            result = result * 100 + highNibble * 10 + lowNibble;
        }
        return result;
    }


}
