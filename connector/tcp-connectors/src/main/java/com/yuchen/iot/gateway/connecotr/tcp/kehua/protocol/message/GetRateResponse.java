package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import com.alibaba.fastjson.JSON;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.Data;

import java.util.ArrayList;

/**
 * @description: 充电桩应答平台计费规则查询
 * @author: zhonghx
 * @create: 2023-02-15 15:39
 **/
@Data
public class GetRateResponse implements MessageDecoder{


    /*长度10字节 ASCII码，计费规则版本号*/
    private String version;

    /*时段数量，至少1条，最多10条。时段必须按顺序排列，覆盖00：00 - 24：00*/
    private byte timeIntervalCount;

    private ArrayList<TimeIntervalInfo> array = new ArrayList<>();

    /*尖时段单价*/
    private short jianRate;
    /*峰时段单价*/
    private short fengRate;
    /*平时段单价*/
    private short pingRate;
    /*谷时段单价*/
    private short guRate;

    /*尖时段服务费*/
    private short jianServiceRate;

    /*峰时段服务费*/
    private short fengServiceRate;

    /*平时段服务费*/
    private short pingServiceRate;

    /*谷时段服务费*/
    private short guServiceRate;

    /**
     * 时间段 JSON
     */
    private String arrayJson;

    @Override
    public void decode(ByteBuf in) {

        byte[] versionBytes = ByteBufUtil.getBytes(in.readBytes(10));
        version = new String(versionBytes);
        timeIntervalCount = in.readByte();
        for (int i = 0;i < timeIntervalCount; i++){
            short time = in.readShort();
            byte index = in.readByte();
            array.add(new TimeIntervalInfo(time,index));
        }
        arrayJson = JSON.toJSONString(array);
        jianRate = in.readShort();
        fengRate = in.readShort();
        pingRate = in.readShort();
        guRate = in.readShort();
        jianServiceRate = in.readShort();
        fengServiceRate = in.readShort();
        pingServiceRate = in.readShort();
        guServiceRate = in.readShort();
    }
}
