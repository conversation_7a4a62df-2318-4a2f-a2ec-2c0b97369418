package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * @description: 充电金额补发，在充电阶段，检测到卡金额变化时，立即通过数据报文 0x1C 功能码 下发给终端。
 * @author: zhonghx
 * @create: 2023-02-15 14:34
 **/
@Data
@AllArgsConstructor
public class ChargingAmountChangedRequest implements MessageEncoder{


    /*用户卡号 20字节 ASCII码 卡号或手机号，不足20补\0*/
    private String cardNumber;

    /*充电金额 */
    private int amount;
    @Override
    public ByteBuf encode() {

        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeBytes(cardNumber.getBytes(StandardCharsets.US_ASCII));
        buf.writeInt(amount);
        return buf;
    }
}
