package com.yuchen.iot.gateway.connecotr.tcp.firefighting.entity;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 组件状态实体类
 * 按照RN6020报警传输网络通讯协议中的部件状态定义
 */
@Data
@Slf4j
public class ComponentStatus {
    
    // 第一个字节
    // bit0: 0-正常运行, 1-测试运行
    private int testRunning;
    
    // bit1: 0-无, 1-火警
    private int fireAlarm;
    
    // bit2: 0-无, 1-故障
    private int fault;
    
    // bit3: 0-无, 1-屏蔽
    private int shielded;
    
    // bit4: 0-无, 1-监管
    private int supervised;
    
    // bit5: 0-停止, 1-启动
    private int started;
    
    // bit6: 0-无, 1-反馈
    private int feedback;
    
    // bit7: 0-无, 1-延时
    private int delayed;
    
    // 第二个字节
    // bit0: 0-电源正常, 1-电源故障
    private int powerFault;
    
    /**
     * 从十六进制状态值解析状态位，直接解析二进制位
     * 采用LSB 0编号模式解析
     * @param statusHex 十六进制状态值
     * @return 解析后的ComponentStatus对象
     */
    public static ComponentStatus parseFromHex(String statusHex) {
        ComponentStatus status = new ComponentStatus();
        
        try {
            // 确保十六进制字符串长度为4（两个字节）
            if (statusHex.length() < 4) {
                statusHex = String.format("%4s", statusHex).replace(' ', '0');
            }
            
            // 分别提取两个字节
            String byte1Hex = statusHex.substring(0, 2); // 低位字节
            String byte2Hex = statusHex.substring(2, 4); // 高位字节
            
            // 转换为整数
            int byte1 = Integer.parseInt(byte1Hex, 16);
            int byte2 = Integer.parseInt(byte2Hex, 16);
            
            // 解析第一个字节的8个位（采用LSB 0编号模式）
            status.setTestRunning((byte1 & 0x01) == 0x01 ? 1 : 0);  // bit0
            status.setFireAlarm((byte1 & 0x02) == 0x02 ? 1 : 0);    // bit1
            status.setFault((byte1 & 0x04) == 0x04 ? 1 : 0);        // bit2
            status.setShielded((byte1 & 0x08) == 0x08 ? 1 : 0);     // bit3
            status.setSupervised((byte1 & 0x10) == 0x10 ? 1 : 0);   // bit4
            status.setStarted((byte1 & 0x20) == 0x20 ? 1 : 0);      // bit5
            status.setFeedback((byte1 & 0x40) == 0x40 ? 1 : 0);     // bit6
            status.setDelayed((byte1 & 0x80) == 0x80 ? 1 : 0);      // bit7
            
            // 解析第二个字节的bit0（电源故障）
            status.setPowerFault((byte2 & 0x01) == 0x01 ? 1 : 0);   // 第二个字节的bit0
            
            log.debug("状态十六进制: {}, 第一字节: {}, 第二字节: {}", 
                    statusHex, byte1Hex, byte2Hex);
            
        } catch (Exception e) {
            log.error("解析部件状态异常: {}", e.getMessage());
        }
        
        return status;
    }
    
    /**
     * 获取状态描述
     * @return 状态描述字符串
     */
    public String getStatusDescription() {
        StringBuilder description = new StringBuilder();
        
        if (fireAlarm == 1) {
            description.append("火警 ");
        }
        if (fault == 1) {
            description.append("故障 ");
        }
        if (shielded == 1) {
            description.append("屏蔽 ");
        }
        if (supervised == 1) {
            description.append("监管 ");
        }
        if (started == 1) {
            description.append("启动 ");
        } else {
            description.append("停止 ");
        }
        if (feedback == 1) {
            description.append("有反馈 ");
        }
        if (delayed == 1) {
            description.append("延时 ");
        }
        if (powerFault == 1) {
            description.append("电源故障 ");
        }
        if (testRunning == 1) {
            description.append("测试运行");
        } else {
            description.append("正常运行");
        }
        
        return description.toString().trim();
    }
}