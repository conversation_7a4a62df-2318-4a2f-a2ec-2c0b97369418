package com.yuchen.iot.gateway.connecotr.tcp.firefighting.entity;

import lombok.Data;

/**
 * 用户信息传输装置运行状态消息实体类
 * 类型标志为21
 */
@Data
public class DeviceStatusMessage {
    // 原始数据
  //  private String rawData;
    
    // 类型标志
  //  private int typeFlag;
    
    // 信息对象数目
   // private int objectCount;
    
    // 设备状态
    private DeviceStatus deviceStatus;
    
    // 时间
    private String eventTime;
    
    /**
     * 设备状态内部类
     */
    @Data
    public static class DeviceStatus {
        // bit0: 0-测试, 1-正常
        private int normal;
        
        // bit1: 0-无, 1-火警
        private int fireAlarm;
        
        // bit2: 0-无, 1-故障
        private int fault;
        
        // bit3: 0-无, 1-主电故障
        private int mainPowerFault;
        
        // bit4: 0-无, 1-备电故障
        private int backupPowerFault;
        
        // bit5: 0-无, 1-监控中心通信信道故障
        private int communicationFault;
        
        // bit6: 0-无, 1-监测连接线路故障
        private int connectionFault;
        
        // bit7: 0-无, 1-延时
        private int delayed;
    }
}