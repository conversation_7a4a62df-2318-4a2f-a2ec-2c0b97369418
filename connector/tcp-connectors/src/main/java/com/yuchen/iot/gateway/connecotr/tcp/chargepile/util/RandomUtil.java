package com.yuchen.iot.gateway.connecotr.tcp.chargepile.util;

/**
 * 随机码工具
 */
public class RandomUtil {

    public static String createKey() {
        StringBuilder sb = new StringBuilder();
        java.util.Random rand = new java.util.Random();//随机用以下三个随机生成器
        java.util.Random randdata = new java.util.Random();
        int data = 0;
        for (int i = 0; i < 16; i++) {
            int index = rand.nextInt(3);
            //目的是随机选择生成数字，大小写字母
            switch (index) {
                case 0:
                    data = randdata.nextInt(10);//仅仅会生成0~9
                    sb.append(data);
                    break;
                case 1:
                    data = randdata.nextInt(26) + 65;//保证只会产生65~90之间的整数
                    sb.append((char) data);
                    break;
                case 2:
                    data = randdata.nextInt(26) + 97;//保证只会产生97~122之间的整数
                    sb.append((char) data);
                    break;
            }
        }
        return sb.toString();
    }

    public static String createSecret() {
        StringBuilder sb = new StringBuilder();
        java.util.Random rand = new java.util.Random();//随机用以下三个随机生成器
        java.util.Random randdata = new java.util.Random();
        int data = 0;
        for (int i = 0; i < 16; i++) {
            int index = rand.nextInt(3);
            //目的是随机选择生成数字，大小写字母
            switch (index) {
                case 0:
                    data = randdata.nextInt(10);//仅仅会生成0~9
                    sb.append(data);
                    break;
                case 1:
                    data = randdata.nextInt(26) + 65;//保证只会产生65~90之间的整数
                    sb.append((char) data);
                    break;
                case 2:
                    data = randdata.nextInt(26) + 97;//保证只会产生97~122之间的整数
                    sb.append((char) data);
                    break;
            }
        }
        return sb.toString();
    }

    public static String createByLength(int length) {
        StringBuilder sb = new StringBuilder();
        java.util.Random rand = new java.util.Random();//随机用以下三个随机生成器
        java.util.Random randdata = new java.util.Random();
        int data = 0;
        for (int i = 0; i < length; i++) {
            int index = rand.nextInt(3);
            //目的是随机选择生成数字，大小写字母
            switch (index) {
                case 0:
                    data = randdata.nextInt(10);//仅仅会生成0~9
                    sb.append(data);
                    break;
                case 1:
                    data = randdata.nextInt(26) + 65;//保证只会产生65~90之间的整数
                    sb.append((char) data);
                    break;
                case 2:
                    data = randdata.nextInt(26) + 97;//保证只会产生97~122之间的整数
                    sb.append((char) data);
                    break;
            }
        }
        return sb.toString();
    }

    /**
     * 随机生成一个四位整数
     * @return
     */
  public static int random4Int(){
      int randomInt = (int) (Math.random()*9000+1000);
      return randomInt;
  }

    public static void main(String[] args) {
        System.out.println(createByLength(4));
    }
}
