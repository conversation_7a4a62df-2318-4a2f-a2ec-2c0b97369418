package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @description: 服务器响应上传升级结果,网关直接应答！！
 * @author: zhonghx
 * @create: 2023-02-14 15:11
 * 互联双方远程升级指令发送，在 30S 钟之内未收到应答，则重发，超时生
 * 重发最大次数 10 次
 **/
@Data
@AllArgsConstructor
public class TcpOTAResultResponse implements MessageEncoder{

    /*应答*/
    private byte ack;

    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(ack);
        return buf;
    }
}
