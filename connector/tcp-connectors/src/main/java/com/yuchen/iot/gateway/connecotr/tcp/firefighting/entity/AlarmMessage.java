package com.yuchen.iot.gateway.connecotr.tcp.firefighting.entity;

import lombok.Data;

/**
 * 报警消息实体类
 */
@Data
public class AlarmMessage {
    // 类型值
   // private Integer typeValue;
    // 系统类型
    private int systemType;
    // 系统地址
    private String machineNo;
    // 部件类型说明
    private Integer componentType;
    // 部件状态
 //   private String status;
    // 位号
    private Integer position;
    // 区号
    private Integer area;
    // 部件说明
    private String description;
    // 时间
    private String eventTime;
    // 原始16进制数据
   // private String rawData;
    // 部件状态对象(按位解析)
    private ComponentStatus componentStatus;
} 