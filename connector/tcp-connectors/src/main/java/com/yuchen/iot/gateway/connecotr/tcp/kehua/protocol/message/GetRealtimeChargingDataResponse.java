package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.DataType;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.TerminalData;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.util.BCDUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.Data;

import java.util.HashMap;

/**
 * @description: 终端响应云平台获取实时数据
 * @author: zhonghx
 * @create: 2023-02-17 10:31
 **/
@Data
public class GetRealtimeChargingDataResponse implements MessageDecoder{


    /*数据单元个数*/
    private byte dataCount;

    /*
     * name--value
     * */
    private HashMap<String, Object> dataMap = new HashMap<>();

    @Override
    public void decode(ByteBuf in) {
        dataCount = in.readByte();

        for (int i = 0; i < dataCount; i++) {

            int id = in.readShort();
            int len = in.readByte();

            TerminalData data = TerminalData.idOf(id);
            if (data == null){
                throw new IllegalArgumentException("dataType is illegal" );
            }
            DataType dataType = data.getType();
            if (dataType == DataType.UINT_8 ){
                Integer value = (int) in.readByte();
                dataMap.put(data.getKey(),value);
            }else if (dataType == DataType.UINT_16 ){
                Integer value = (int) in.readShort();
                dataMap.put(data.getKey(),value);
            }else if (dataType == DataType.UINT_32 ){
                Integer value = (int) in.readInt();
                dataMap.put(data.getKey(),value);
            }else if (dataType == DataType.BCD){
                byte[] timeBytes = ByteBufUtil.getBytes(in.readBytes(len));
                String time = BCDUtil.bcdToStr(timeBytes);
                dataMap.put(data.getKey(),time);
            }else if(dataType == DataType.ASCII){
                byte[] contentBytes = ByteBufUtil.getBytes(in.readBytes(len));
                String content = new String(contentBytes);
                dataMap.put(data.getKey(),content);
            }else if(dataType == DataType.RESERVED){
                byte[] b = ByteBufUtil.getBytes(in.readBytes(len));
            }else {
                throw new IllegalArgumentException("dataType is illegal" );
            }
        }
    }

}
