package com.yuchen.iot.gateway.connecotr.tcp.chargepile.enums;

/**
 * 充电桩功能码
 */
public enum ChargeCommandCode {

    PILE_AUTH_OA(0x0A, "接入OA认证"),//0x0A->10
    PILE_AUTH_OB(0x0B, "接入OB认证"),//0x0B->11
    PILE_HEART(0x01, "心跳"),//0x01->1
    PLATFORM_START(0x03, "平台充电启动请求"),//0x03->1
    PILE_START(0x04, "桩充电启动请求"),//0x04->4
    PILE_UPLOAD(0x06, "充电数据上传"),//0x06->6
    PLATFORM_STOP(0x08, "平台充电停止请求"),//0x08->8
    PILE_STOP(0x05, "桩充电停止请求"),//0x05->5
    CARD_START(0x09, "刷卡启动请求"),//0x09->9
    CARD_STOP(0x12, "刷卡停止请求"),//0x12->18
    ;
    private ChargeCommandCode(int code, String value) {
        this.code = code;
        this.value = value;
    }

    private int code;
    private String value;

    public int getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static ChargeCommandCode findCode(int code) {
        for (ChargeCommandCode item : ChargeCommandCode.values()) {
            if (item.getCode() == code) {
                return item;
            }
        }
        return null;
    }
}
