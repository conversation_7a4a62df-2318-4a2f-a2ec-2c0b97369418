package com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.protocol.message;

import com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.BCDUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class TimeIntervalInfo implements MessageEncoder{
    private byte index;

    /*BCD格式，高字节表示小时 ，低字节表示分钟，结束时间为下一段时间的起始时间，最后一个
     时段的结束时间为24:00*/
    private String startTime;

    /**
     *
     * @param index      费率编号
     * @param startTime  bcd格式时间
     */
    TimeIntervalInfo(byte index, short startTime){
        this.index = index;
        this.startTime = BCDUtil.bcdToStr(short2byte(startTime));
    }


    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(index);
        buf.writeShortLE(byte2short(BCDUtil.strToBcd(startTime)));
        return buf;
    }

    public static byte[] short2byte(short s){
        byte[] b = new byte[2];
        for(int i = 0; i < 2; i++){
            int offset = 16 - (i+1)*8; //因为byte占4个字节，所以要计算偏移量
            b[i] = (byte)((s >> offset)&0xff); //把16位分为2个8位进行分别存储
        }
        return b;
    }

    public static short byte2short(byte[] b){
        short l = 0;
        for (int i = 0; i < 2; i++) {
            l<<=8; //<<=和我们的 +=是一样的，意思就是 l = l << 8
            l |= (b[i] & 0xff); //和上面也是一样的  l = l | (b[i]&0xff)
        }
        return l;
    }
}
