package com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.down;


import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.message.MessageEncoder;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class SwipeCardBeginChargingDownMessage implements MessageEncoder {




    private int type ;
    private int cardType ;
    private int money ;
    private int channel ;



    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(type);
        buf.writeByte(cardType);
        buf.writeBytes( doubleTo3Bytes(money));
        buf.writeByte(channel);
        return buf;
    }



    public  byte[] doubleTo3Bytes(double value) {
        // 定义缩放因子，将 double 缩放到 3 个字节可以表示的范围
        double scale = Math.pow(2, 24) - 1;

        // 将 double 缩放并转换为 int
        int scaledValue = (int) Math.round(value * scale);

        // 创建一个 3 字节数组
        byte[] result = new byte[3];

        // 将 int 值拆分到 3 个字节
        result[0] = (byte) ((scaledValue >> 16) & 0xFF);
        result[1] = (byte) ((scaledValue >> 8) & 0xFF);
        result[2] = (byte) (scaledValue & 0xFF);

        return result;
    }
}
