package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @description: 服务器下发限制功率命令
 * @author: zhonghx
 * @create: 2023-02-16 10:30
 **/
@Data
@AllArgsConstructor
public class LimitPowerRequest implements MessageEncoder{

    /*限制控制
    * 0解除限制
    * 1执行限制
    * */
    private byte controlType;

    /*最大输出功率
    * 为1时有效 单位W
    * */
    private int maxPower;

    @Override
    public ByteBuf encode() {

        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(controlType);
        buf.writeInt(maxPower);
        return buf;
    }
}
