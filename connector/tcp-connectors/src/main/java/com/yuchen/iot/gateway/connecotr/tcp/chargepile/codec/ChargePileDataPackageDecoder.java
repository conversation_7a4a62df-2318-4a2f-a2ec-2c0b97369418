package com.yuchen.iot.gateway.connecotr.tcp.chargepile.codec;

import com.yuchen.iot.gateway.connecotr.tcp.chargepile.constants.ChargePileConstants;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.message.request.DataPackRequest;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.enums.ChargeCommandCode;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.message.request.PackHeaderRequest;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.util.DESUtil;
import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class ChargePileDataPackageDecoder extends ByteToMessageDecoder {
    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) throws Exception {
        // 可读的数据域
        int readable = in.readableBytes();
        if (readable < ChargePileConstants.MESSAGE_LENGTH) {
            return;
        }
        //头域信息
        //起始域
        byte start = in.readByte();
        if (start != ChargePileConstants.START) {
            throw new IllegalArgumentException("start number is illegal, " + start);
        }
        //版本
        byte version = in.readByte();
        //功能码
        byte cmd = in.readByte();
        //充电桩编号
        int pileNo = in.readInt();
        //充电桩抢号
        byte gunNo = in.readByte();
        //加密标志
        byte encryptFlag = in.readByte();
        //校验码
        byte verifyCode = in.readByte();
        //补位长度
        byte complementLength = in.readByte();
        //消息长度
        byte msgLength = in.readByte();
        ChargeCommandCode msgTypeEnum = ChargeCommandCode.findCode(cmd);
        if (msgTypeEnum == null) {
            return;
        }

        DataPackRequest dataPack = new DataPackRequest();
        //****************数据头****************
        PackHeaderRequest packHeader = new PackHeaderRequest();
        packHeader.setStart(start);
        packHeader.setVersion(version);
        packHeader.setCmd(cmd);
        Channel channel = ctx.channel();
        channel.attr(ChargePileConstants.DEVICE_ATTRIBUTE_KEY).set(pileNo +"");
        packHeader.setPileNo(pileNo);
        packHeader.setGunNo(gunNo);
        packHeader.setEncryptFlag(encryptFlag);
        packHeader.setVerifyCode(verifyCode);
        packHeader.setComplementLength(complementLength);
        packHeader.setMsgLength(msgLength);
        dataPack.setPackHeader(packHeader);
        channel.attr(ChargePileConstants.PACK_HEADER_ATTRIBUTE_KEY).set(packHeader.toString());

        //****************数据域****************
        readable = in.readableBytes();
        if (readable + ChargePileConstants.MESSAGE_LENGTH - 1 < msgLength) {
            log.debug("数据包未接收完整:{}/{}，{}", readable, msgLength, ctx.channel());
            in.resetReaderIndex();
            return;
        }
        byte[] dataBytes = new byte[readable];
        in.readBytes(dataBytes);
        byte[] result = DESUtil.decrypt3DES(dataBytes, DESUtil.key);
        dataPack.setData(result);
        out.add(dataPack);
    }
}
