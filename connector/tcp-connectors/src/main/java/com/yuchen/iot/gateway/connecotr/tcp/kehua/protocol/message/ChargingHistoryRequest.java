package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import com.yuchen.iot.gateway.connecotr.tcp.kehua.util.BCDUtil;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.util.HexStringUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.Data;

import java.nio.charset.StandardCharsets;

/**
 * @description: 历史充电记录主动上传, 当交易订单完成后，由充电桩主动发送充电记录给平台。充电桩上报的充
 * 电记录云平台必须回复，否则充电桩会一直发送同一条充电记录（回复确认帧
 * 需注意充电流水号的一致性
 * @author: zhonghx
 * @create: 2023-02-13 11:16
 **/
@Data
public class ChargingHistoryRequest implements MessageDecoder{

    /**
     * 充电方式
     */
    private byte chargingType;

    /**
     * 充电模式
     */
    private byte chargingMode;

    /**
     * 充电卡类型
     */
    private byte chargingCardType;

    /**
     * 充电卡号 20字节ASCII 码
     */
    private String cardNumber;

    /**
     * 车辆VIN 17字节
     */
    private String vin;

    /**
     * 充电之前余额 两位小数
     */
    private int balanceBeforeCharging;

    /**
     * 充电最高电压 1位小数
     */
    private short maxVoltage;

    /**
     * 充电最大电流 2位小数
     */
    private short maxCurrent;

    /**
     * 充电时间 秒
     */
    private int chargingTime;

    /**
     * 充电费用，非总费用，4位小数
     */
    private int chargingFee;

    /**
     * 充电电能
     */
    private int chargingElectricityQuantity;

    /**
     * 开始充电电能
     */
    private int startElectricityQuantity;

    /**
     * 结束充电电能
     */
    private int endElectricityQuantity;

    /**
     * 开始SOC
     */
    private byte startSoc;

    /**
     * 结束SOC
     */
    private byte endSoc;

    /**
     * 是否付费
     */
    private byte isPay;

    /**
     * 充电停止原因低字节，充电终止代码，参考附录4.2
     */
    private int stopReason;

    /**
     * 充电开始时间 BCD 7字节
     */
    private String startTime;

    /**
     * 充电结束时间 BCD 7字节
     */
    private String endTime;

    /**
     * 单体最高电压 两位小数
     */
    private short singleMaxVoltage;

    /**
     * 单体最高温度
     */
    private short singleMaxTemperature;

    /**
     * 充电流水号，由平台生成，在充电桩保存后上传
     */
    private int chargingSN;

    /**
     * 本地存储流水号
     */
    private int localChargingSN;

    /**
     * 服务费 4位小数
     */
    private int serviceFee;

    /**
     * 总费用 充电电费+充电服务费，4 位小数
     */
    private int totalCharge;

    /**
     * 计费版本 10字节
     */
    private String billingVersion;

    /**
     * 尖时段电量
     */
    private int jianElectricityQuantity;

    /**
     * 峰时段电量
     */
    private int fengElectricityQuantity;

    /**
     * 平时段电量
     */
    private int pingElectricityQuantity;

    /**
     * 谷时段电量
     */
    private int guElectricityQuantity;

    /**
     * 尖时段电费
     */
    private int jianElectricityCharge;

    /**
     * 峰时段电费
     */
    private int fengElectricityCharge;

    /**
     * 平时段电费
     */
    private int pingElectricityCharge;

    /**
     * 谷时段电费
     */
    private int guElectricityCharge;

    /**
     * 尖时段服务费
     */
    private int jianServiceCharge;

    /**
     * 峰时段服务费
     */
    private int fengServiceCharge;

    /**
     * 平时段服务费
     */
    private int pingServiceCharge;

    /**
     * 谷时段服务费
     */
    private int guServiceCharge;

    @Override
    public void decode(ByteBuf in) {
        chargingType = in.readByte();
        chargingMode = in.readByte();
        chargingCardType = in.readByte();
        byte[] cardNumberBytes = ByteBufUtil.getBytes(in.readBytes(20));
        cardNumber = new String(cardNumberBytes, StandardCharsets.US_ASCII);
        byte[] vinBytes = ByteBufUtil.getBytes(in.readBytes(17));
        vin = new String(vinBytes, StandardCharsets.US_ASCII);
        balanceBeforeCharging = in.readInt();
        maxVoltage = in.readShort();
        maxCurrent = in.readShort();
        chargingTime = in.readInt();
        chargingFee =  (in.readInt());
        chargingElectricityQuantity = in.readInt();
        startElectricityQuantity = in.readInt();
        endElectricityQuantity = in.readInt();
        startSoc = in.readByte();
        endSoc = in.readByte();
        isPay = in.readByte();
        stopReason = in.readByte();
        ByteBuf discard = in.readBytes(15);
        byte[] startTimeBytes = ByteBufUtil.getBytes(in.readBytes(7));
        startTime = BCDUtil.bcdToStr(startTimeBytes);
        byte[] endTimeBytes = ByteBufUtil.getBytes(in.readBytes(7));
        endTime = BCDUtil.bcdToStr(endTimeBytes);
        singleMaxVoltage = in.readShort();
        singleMaxTemperature = in.readShort();
        chargingSN = in.readInt();
        localChargingSN = in.readInt();
        serviceFee = in.readInt();
        totalCharge = in.readInt();

        byte[] billingVersionBytes = ByteBufUtil.getBytes(in.readBytes(10));
        billingVersion = new String(billingVersionBytes, StandardCharsets.US_ASCII);
        jianElectricityQuantity = in.readInt();
        fengElectricityQuantity = in.readInt();
        pingElectricityQuantity = in.readInt();
        guElectricityQuantity = in.readInt();

        jianElectricityCharge = in.readInt();
        fengElectricityCharge = in.readInt();
        pingElectricityCharge = in.readInt();
        guElectricityCharge = in.readInt();

        jianServiceCharge = in.readInt();
        fengServiceCharge = in.readInt();
        pingServiceCharge = in.readInt();
        guServiceCharge = in.readInt();
    }
}
