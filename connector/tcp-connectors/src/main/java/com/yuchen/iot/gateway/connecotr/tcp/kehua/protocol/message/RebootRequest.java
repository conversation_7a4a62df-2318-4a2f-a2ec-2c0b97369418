package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * @description: 系统重启
 * @author: zhonghx
 * @create: 2023-02-16 10:45
 **/
@Data
@AllArgsConstructor
public class RebootRequest implements MessageEncoder{

    /*复位
    * 1.系统复位，其他无效
    * */
    private byte type;

    /*账户 20 平台账户，记录操作账户*/
    private String account;

    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(type);
        buf.writeBytes(account.getBytes(StandardCharsets.US_ASCII));
        return buf;
    }
}
