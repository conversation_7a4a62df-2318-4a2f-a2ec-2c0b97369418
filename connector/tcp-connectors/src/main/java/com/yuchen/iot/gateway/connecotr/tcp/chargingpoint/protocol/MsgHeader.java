package com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.protocol;

import lombok.Data;


@Data
public class MsgHeader {

    /*起始符，0x68*/
    public int preamble;

    /*帧长度，协议版本至数据，总字节长度*/
    public int frameLength;

    /*版本号，0x20*/
    public int version;

    /*方向，0=终端到主站，1=主站到终端*/
    public int direction;
    /*启动标志，0=报文来自从动站，1=报文来自启动站  响应消息为0 发起消息为1*/
    public int subDirection;

    /*时间戳，unix时间戳，时间戳的时间比本地时间晚60秒以上，则认为本命令已经超时失效，直接丢弃*/
    public int time;

    /*设备类型*/
    public int devType;

    /*地址，根据设备类型，选择集中器地址或者充电桩编号*/
    public String address;

    /*命令*/
    public int cmd;

    /*数据*/
    //public ByteBuf data;

    /*验证码*/
    public int verifyCode;

    /*结束符*/
    public int terminator;
}
