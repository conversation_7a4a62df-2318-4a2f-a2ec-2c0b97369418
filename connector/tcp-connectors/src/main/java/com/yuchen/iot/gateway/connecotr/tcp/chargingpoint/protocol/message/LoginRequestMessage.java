package com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.protocol.message;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.netty.buffer.ByteBuf;
import lombok.Data;


/**
 * @description: 登录请求
 * @author: zhong
 * @create: 2022-12-08 16:55
 **/

@JsonIgnoreProperties(value = { "handler" })
@Data
public class LoginRequestMessage implements MessageDecoder {

    @Override
    public void decode(ByteBuf in) {

    }
}
