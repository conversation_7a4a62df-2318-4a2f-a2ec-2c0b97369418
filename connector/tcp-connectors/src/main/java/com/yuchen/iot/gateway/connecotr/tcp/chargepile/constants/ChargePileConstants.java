package com.yuchen.iot.gateway.connecotr.tcp.chargepile.constants;

import io.netty.util.AttributeKey;

/**
 * 充电桩常量
 */
public class ChargePileConstants {

    public final static int START = 50;//起始域开始

    public static final int MESSAGE_LENGTH = 12;// 报文总12位数据

    public static final String CHARGE_LISTENER_KEY = "chargePile:connector-listener";

    public static final AttributeKey<String> DEVICE_ATTRIBUTE_KEY = AttributeKey
            .valueOf("chargePile:device-key");

    public static final String CHARGE_DEVICE_KEY = "chargePile:device-key";

    public static final AttributeKey<String> PACK_HEADER_ATTRIBUTE_KEY = AttributeKey
            .valueOf("chargePile:pack-header");

    public static final String BUSINESS_TYPE = "business-type";// 报文总12位数据

}
