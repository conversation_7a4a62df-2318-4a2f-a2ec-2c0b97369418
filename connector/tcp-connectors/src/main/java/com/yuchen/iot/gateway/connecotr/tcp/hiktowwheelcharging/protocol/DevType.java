package com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol;

public enum DevType {

    /* 交流 */
    CAC_CHARGING(0x10),
    /* 直流 */
    DC_CHARGING(0x20),
    /* 交直流 */
    AC_DC_MIXED_CHARGING(0x30),
    /* 集中器 */
    CONCENTRATOR(0x40),
    /* 保留 */
    RETAIN(0x50);

    private int code;
    DevType(int code)
    {
        this.code = code;
    }
    public int getCode()
    {
        return code;
    }

    public static DevType codeOf(int code) {
        for (DevType fdType : values()) {
            if (fdType.getCode() == code) {
                return fdType;
            }
        }
        return CAC_CHARGING;
    }
}
