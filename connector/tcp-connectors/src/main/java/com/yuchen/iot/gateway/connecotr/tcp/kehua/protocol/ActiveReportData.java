package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol;

import lombok.Getter;

/*主动上传数据表*/
public enum ActiveReportData {


    /*
    * 枪状态
    * 登录时立即上传
    * 变化上传
    * 传送本帧必传
    *
    * 0-空闲
    1-插枪
    2-充电等待
    3-启动中
    4-充电中
    5-重连
    6-等待接收 BSD
    7-结算状态
    8-充电停机（A 类故障）
    9-放电中
    10-预留
    11-预约状态
    12-平台预约状态
    13-未支付卡状态
    14-充电完成状态
    15-APP 预约状态
    16-试用期到，停止服务状态
    * */
    GUN_STATE(0x1000, 1, "gunState", DataType.UINT_8),
    CARD_LINK(0x1001, 1, "cardLink", DataType.UINT_8),                                                  /*车辆链接状态 0-未链接 1-已链接*/
    ALARM_INFO_LOW(0x1002, 1, "alarmInfoLow", DataType.UINT_8),                                         /*产生和恢复时上传，在多个告警产生时，需按产生顺序，逐个上传告警信息;与0x1006 字段组合成2个字节解析*/
    ALARM_STATE(0x1003, 1,"alarmState", DataType.UINT_8),                                               /*告警状态 0发生 1恢复*/
    CHARGING_SERIALNUMBER(0x1004, 4, "chargingSerialNumber", DataType.UINT_32),                                             /*充电流水号 */
    FAULT_DUTY(0x1005, 2, "faultDuty", DataType.UINT_16),                                               /*故障责任划分*/
    ALARM_INFO_HIGH(0x1006, 1, "alarmInfoHigh", DataType.UINT_8),                                       /*产生和恢复时上传，在多个告警产生时，需按产生顺序，逐个上传告警信息;与0x1006 字段组合成2个字节解析*/

    CARD_NUMBER(0x1030, 20, "cardNumber", DataType.ASCII),                                              /*充电卡号*/
    CARD_TYPE(0x1031, 1, "chargingCardType", DataType.UINT_8),                                                  /*充电卡类型*/
    BALANCE(0x1032, 4, "balance", DataType.UINT_32),                                                    /*充电前余额*/
    CARD_VIN(0x1033, 17, "cardVin", DataType.ASCII),                                                             /*车辆VIN*/
    NUMBERPLATE(0x1034, 8, "numberplate", DataType.ASCII),                                              /*车牌号码*/
    START_TIME(0x1035, 7, "startTime", DataType.BCD),                                                   /*充电开始时间*/
    METER_NUMBER(0x1036, 4, "meterNumber", DataType.UINT_32),                                           /*充电前电表读书*/
    CHARGING_START_TYPE(0x1037, 1, "chargingStartType", DataType.UINT_8),                               /*充电启动方式*/
    CHARGING_MODE(0x1038, 1, "chargingMode", DataType.UINT_8),                                          /*充电模式*/
    CHARGING_MODE_PARAMS(0x1039, 4, "chargingModeParams", DataType.UINT_32),                            /*充电模式参数*/
    PARKING_SPACE_STATE(0x103A, 1, "parkingSpaceState", DataType.UINT_8),                               /*车位占用状态 0空闲 1占用*/
    TERMINAL_ONLINE_STATE(0x103B, 1, "terminalOnlineState", DataType.UINT_8);                           /*终端在线状态 0离线 1在线*/





    /*数据单元标识ID*/
    @Getter
    private final int id;

    /*字节形态的长度*/
    @Getter
    private final int len;

    /*名称*/
    @Getter
    private final String key;

    /*数据类型*/
    @Getter
    private final DataType type;

    ActiveReportData(int id, int len, String key, DataType type) {
        this.id = id;
        this.len = len;
        this.key = key;
        this.type = type;
    }

    public static ActiveReportData idOf(int id) {
        for (ActiveReportData data : ActiveReportData.values()) {
            if (data.getId() == id) {
                return data;
            }
        }
        return null;
    }

    public static ActiveReportData keyOf(String key) {
        for (ActiveReportData data : ActiveReportData.values()) {
            if (data.key.equals(key)) {
                return data;
            }
        }
        return null;
    }
}
