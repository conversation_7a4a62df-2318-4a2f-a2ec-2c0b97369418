package com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.request;

import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.message.MessageDecoder;
import io.netty.buffer.ByteBuf;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Data
public class DeviceRegistrRequestMessage implements MessageDecoder {

    private List<Map<String, Object>> channels = new ArrayList<>();

    private int deviceMode ;


  /*  private int softwareVersion     ;

    private int hardwareVersion  ;*/
    private String stm32DeviceId   ;

    @Override
    public void decode(ByteBuf in) {


        int[] all = mergeBitArrays(changeByteTOBit(in.readByte()), changeByteTOBit(in.readByte()));
        for (int i = 0; i < all.length; i++) {
            Map<String, Object> channel = new HashMap<>();
            channel.put("number",i+1);
            channel.put("status", all[i]);
            channels.add(channel);
        }

        // 读取设备模式
        deviceMode = in.readByte();


        // 读取软件版本
       in.readByte();


        // 读取硬件版本
       in.readByte();


        // 读取STM32设备ID
        byte[] stm32DeviceIds = new byte[6];
        in.readBytes(stm32DeviceIds);
        stm32DeviceId=bytesToHex(stm32DeviceIds);

    }

    private static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X", b));
        }
        return sb.toString();
    }

    public static int[] mergeBitArrays(int[] first, int[] second) {
        int[] merged = new int[first.length + second.length];
        System.arraycopy(first, 0, merged, 0, first.length);
        System.arraycopy(second, 0, merged, first.length, second.length);
        return merged;
    }

    public static int[] changeByteTOBit(byte bt) {

        String str = String.format("%8s", Integer.toBinaryString(bt & 0xFF)).replace(' ', '0');

        int[] list = new int[8];
        list[0] = Character.getNumericValue(str.charAt(7));
        list[1] = Character.getNumericValue(str.charAt(6));
        list[2] = Character.getNumericValue(str.charAt(5));
        list[3] = Character.getNumericValue(str.charAt(4));
        list[4] = Character.getNumericValue(str.charAt(3));
        list[5] = Character.getNumericValue(str.charAt(2));
        list[6] = Character.getNumericValue(str.charAt(1));
        list[7] = Character.getNumericValue(str.charAt(0));

        return list;
    }
}
