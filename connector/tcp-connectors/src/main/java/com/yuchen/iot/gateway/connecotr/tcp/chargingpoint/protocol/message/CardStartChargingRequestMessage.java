package com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.protocol.message;

import com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.BCDUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.Data;

/**
 * @description: 终端启动充电的请求，需要主站应答
 * @author: zhonghx
 * @create: 2022-12-09 17:27
 **/
@Data
public class CardStartChargingRequestMessage implements MessageDecoder{

    /*枪号 1-n*/
    private byte gunNo;

    /*卡号， 16字节BCD*/
    private String cardNo;

    /*普通用户  2集团用户*/
    private byte cardType;

    /*用户来源*/
    private byte from;

    /*充电方式， 0x01按金额（元）， 0x02按电量（度），0x03按时间（分钟）*/
    private byte chargingType;

    /*充电方式附带值， 按金额：充电金额，单位元，2位小数
    *               按电量，充电电量，单位kw/h
    *               按时间，充电时间，单位分*/
    private short chargingValue;


    @Override
    public void decode(ByteBuf in) {
        gunNo = in.readByte();
        byte[] bytes = ByteBufUtil.getBytes(in.readBytes(16));
        cardNo = BCDUtil.bcdToStr(bytes);
        cardType = in.readByte();
        from = in.readByte();
        chargingType = in.readByte();
        chargingValue = in.readShortLE();
    }
}
