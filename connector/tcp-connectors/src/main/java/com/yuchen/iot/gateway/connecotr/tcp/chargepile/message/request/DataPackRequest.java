package com.yuchen.iot.gateway.connecotr.tcp.chargepile.message.request;

import com.yuchen.iot.gateway.connecotr.tcp.chargepile.enums.ChargeCommandCode;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.util.BytesUtil;
import lombok.Data;

/**
 * 数据包头
 */
@Data
public class DataPackRequest {
 /**
  * 数据头
  */
 private PackHeaderRequest packHeader;

 /**
  * 消息数据
  */
 private byte[] data;

 /**
  * 解码实体类
  */
 public Object decode() {
  byte cmd = packHeader.getCmd();
  ChargeCommandCode msgTypeEnum = ChargeCommandCode.findCode(cmd);
  Object request = null;

  switch (msgTypeEnum) {
   case PILE_AUTH_OA: request = getOaRequest(data);break;
   case PILE_AUTH_OB: request = getObRequest(data);break;
   case PILE_HEART: request = getHeartRequest(data);break;
   default: break;
  }
  return request;
 }

 /**
  * 解码OA认证请求
  * @param data
  * @return
  */
 public Object getOaRequest(byte[] data) {
  AuthOARequest authRequest = new AuthOARequest();
  byte content = data[0];
  authRequest.setContent(content);
  return authRequest;
 }

 /**
  * 解码OB认证请求
  * @param data
  * @return
  */
 public Object getObRequest(byte[] data) {
  AuthOBRequest authRequest = new AuthOBRequest();
  authRequest.setContent(BytesUtil.copyBytes(data,0,15).toString());
  authRequest.setVersionType(BytesUtil.bytesToShort(BytesUtil.copyBytes(data,16,17)));
  authRequest.setVersionNo(BytesUtil.bytesToShort(BytesUtil.copyBytes(data,18,19)));
  authRequest.setSimCardNo(BytesUtil.copyBytes(data,20,33).toString());
  authRequest.setConfigVersionNo(BytesUtil.copyBytes(data,34,66).toString());
  return authRequest;
 }

 /**
  * 解码心跳认证请求
  * @param data
  * @return
  */
 public Object getHeartRequest(byte[] data) {
  HeartRequest heartRequest = new HeartRequest();
  heartRequest.setStatusCode(data[0]);
  heartRequest.setFaultState(BytesUtil.bytesToShort(BytesUtil.copyBytes(data,1,2)));
  return heartRequest;
 }


}
