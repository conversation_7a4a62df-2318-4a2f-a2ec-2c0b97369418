package com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.message;

import com.alibaba.fastjson.JSON;
import io.netty.buffer.ByteBuf;
import lombok.Data;

import java.util.ArrayList;


/**
 * @description: 终端应答平台的读取终端费率请求
 * @author: zhong
 * @create: 2022-12-08 16:55
 **/
@Data
public class ServerGetRateResponseMessage implements MessageDecoder {

    /*服务费单价，单位：元/kWh, 4位小数*/
    private int serviceChargePerkWh;
    /*费率1单价*/
    private int rate1;
    /*费率2单价*/
    private int rate2;
    /*费率3单价*/
    private int rate3;
    /*费率4单价*/
    private int rate4;
    /*时段数量，至少1条，最多12条。时段必须按顺序排列，覆盖00：00 - 24：00*/
    private byte timeIntervalCount;

    /**
     * 时间段对象
     */
    private ArrayList<TimeIntervalInfo> array = new ArrayList<>();

    /**
     * 时间段 JSON
     */
    private String arrayJson;

    @Override
    public void decode(ByteBuf in) {

        serviceChargePerkWh = in.readIntLE();
        rate1 = in.readIntLE();
        rate2 = in.readIntLE();
        rate3 = in.readIntLE();
        rate4 = in.readIntLE();
        timeIntervalCount = in.readByte();

        for (int i = 0;i < timeIntervalCount; i++){
            byte index = in.readByte();
            short time = in.readShortLE();
            array.add(new TimeIntervalInfo(index,time));
        }
        arrayJson = JSON.toJSONString(array);
    }
}
