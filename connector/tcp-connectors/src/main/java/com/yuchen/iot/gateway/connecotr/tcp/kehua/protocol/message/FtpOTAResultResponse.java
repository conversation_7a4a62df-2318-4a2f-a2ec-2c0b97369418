package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * @description: 远程升级结果上传应答!!,由网关应答
 * @author: zhonghx
 * @create: 2023-02-13 18:56
 **/

@Data
@AllArgsConstructor
public class FtpOTAResultResponse implements MessageEncoder{

    /*确认标识*/
    private byte ack;

    /*软件版本*/
    private String version;

    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(ack);
        buf.writeBytes(Arrays.copyOf(version.getBytes(StandardCharsets.US_ASCII),20));
        return buf;
    }
}
