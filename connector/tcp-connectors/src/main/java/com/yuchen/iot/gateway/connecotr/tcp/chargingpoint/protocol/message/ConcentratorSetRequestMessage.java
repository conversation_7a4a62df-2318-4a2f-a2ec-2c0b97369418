package com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;

/**
 * @description: 主站发起，设置集中器参数的指令,用于配置集中器下连接充电桩的数量及参数
 * 路由分配设置需要在终端登录成功后立即发起，如果没有收到终端的应答，则需要反复重发，直到
 * 收到终端应答。
 * @author: zhonghx
 * @create: 2022-12-09 19:36
 **/

@Data
@AllArgsConstructor
public class ConcentratorSetRequestMessage implements MessageEncoder{

    /*子终端数量（通常为集中器下充电桩的数量）*/
    private byte count;

    private ArrayList<PointInfo> array = new ArrayList<>();
    @Override
    public ByteBuf encode() {

        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(count);
        for (PointInfo t: array) {
            buf.writeBytes(t.encode());
        }
        return buf;
    }

}
