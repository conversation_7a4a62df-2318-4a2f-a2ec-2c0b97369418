package com.yuchen.iot.gateway.connecotr.tcp.firefighting;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.yuchen.iot.gateway.connecotr.tcp.AbstractTcpServerConnector;
import com.yuchen.iot.gateway.connecotr.tcp.JsonTcpConnectorMessage;
import com.yuchen.iot.gateway.connecotr.tcp.firefighting.service.impl.CommandServiceImpl;
import com.yuchen.iot.gateway.connector.api.ConnectorInitParams;
import com.yuchen.iot.gateway.connector.api.WaitCallResponseCallback;
import com.yuchen.iot.gateway.connector.api.data.DownLinkData;
import com.yuchen.iot.gateway.connector.api.data.UpLinkData;
import com.yuchen.iot.gateway.connector.api.data.UpLinkMetaData;
import com.yuchen.iot.gateway.connector.api.message.ThingMessage;
import com.yuchen.iot.gateway.connecotr.tcp.firefighting.service.CommandService;
import com.yuchen.iot.gateway.dao.integration.MetaConfig;
import io.netty.channel.Channel;
import io.netty.channel.ChannelInitializer;
import lombok.extern.slf4j.Slf4j;
import org.pf4j.Extension;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Calendar;

@Extension
@Slf4j
public class FirefightingTcpConnector extends AbstractTcpServerConnector<JsonTcpConnectorMessage> {

    private final Map<String, Channel> channels = new HashMap<>();
    private NettyServer nettyServer;


    private CommandService commandService =new CommandServiceImpl();

    protected final ObjectMapper objectMapper = new ObjectMapper();

    // 流水号生成器
    private final AtomicInteger flowIdGenerator = new AtomicInteger(1);
    private String currentFlowId = "0100";

    /**
     * 定义默认端口号
     */
    private int PORT = 7799;

    @Override
    public void init(ConnectorInitParams params) {
        super.init(params);
        ListeningExecutorService pool = MoreExecutors.listeningDecorator(Executors.newFixedThreadPool(2));
        // 获取自定义配置
        MetaConfig configuration = params.getConfiguration().getConfiguration();
        // 如果端口号不为空则继续执行
        if (configuration.containsKey("port")) {
            // 获取端口号
            PORT = (int) configuration.get("port");
        }
        nettyServer = new NettyServer();
        ListenableFuture<Boolean> future = pool.submit(this::startServer);
        future.addListener(this::startServer, pool);
    }

    @Override
    protected ChannelInitializer<?> getChannelInitializer() {
        return null;
    }

    protected UserSmartIotByKeAnHandler getUserSmartIotByKeAnHandler() {
        return new UserSmartIotByKeAnHandler(new ChargingChannelListener<JsonTcpConnectorMessage>() {
            @Override
            public void onDisconnect(String deviceId) {
                getUpLinkDataListener(deviceId).onSouthDisconnect();
            }

            @Override
            public void onConnect(String deviceId) {
                getUpLinkDataListener(deviceId).onSouthConnect();
            }

            @Override
            public void onMessage(JsonTcpConnectorMessage event) {
                process(event);
            }
        });
    }

    @Override
    protected void doProcess(JsonTcpConnectorMessage message) throws Exception {
        JsonNode json = mapper.readTree(message.getMessageInBytes());
        List<UpLinkData> upLinkDataList = this.convert(message);
        if (upLinkDataList != null && !upLinkDataList.isEmpty()) {
            for (UpLinkData upLinkData : upLinkDataList) {
                this.processUpLinkData(upLinkData);
                log.trace("[{}] Processing up link data", upLinkData);
            }
        }
    }


    protected List<UpLinkData> convert(JsonTcpConnectorMessage message) throws Exception {
        byte[] data = message.getMessageInBytes();
        Map<String, String> mdMap = new HashMap<>(this.metadataTemplate.getKvMap());
        return this.convertToUpLinkDataList(this.connectorContext.getCallbackExecutor(), data,
                new UpLinkMetaData(message.getContentType(), mdMap));
    }

    private Boolean startServer() {
        try {
            log.info("启动消防设备服务器，端口: {}", PORT);
            nettyServer.userDataByKeAn(PORT,getUserSmartIotByKeAnHandler());
            log.info("启动消防设备服务器成功，端口: {}", PORT);
            return true;
        } catch (Exception e) {
            log.error("启动消防设备服务器失败", e);
            return false;
        }
    }

    @Override
    protected void doListenOffEndpoint(String deviceKey, Map<String, String> endpointMeta) {
        if (this.channels.containsKey(deviceKey)) {
            Channel channel = this.channels.get(deviceKey);
            if (channel != null) {
                channel.close().addListener(future -> {
                    if (future.isSuccess()) {
                        this.getUpLinkDataListener(deviceKey).onSouthDisconnect();
                    } else {
                        future.cause().printStackTrace();
                    }
                });
            }
        }
    }

    @Override
    public void onDownLinkMessage(ThingMessage message, WaitCallResponseCallback callback) {
        if (this.downLinkConverter != null) {
            this.processDownLinkMessage(message, callback);
        }
    }

    @Override
    protected void doListenOnEndpoint(String deviceKey, Map<String, String> endpointMeta) {
        // 不需要实现
    }

    /**
     * 生成流水号
     * @return 流水号
     */
    private String generateFlowId() {
        // 生成0-65535之间的数
        int id = flowIdGenerator.getAndIncrement() % 65536; 
        
        // 将数值拆分为高字节和低字节
        int highByte = (id >> 8) & 0xFF;  // 高字节
        int lowByte = id & 0xFF;          // 低字节
        
        // 低字节在前，高字节在后
        return String.format("%02X%02X", lowByte, highByte);
    }

    private void processDownLinkMessage(ThingMessage message, WaitCallResponseCallback callback) {
        Map<String, String> mdMap = new HashMap<>(this.metadataTemplate.getKvMap());
        try {
            List<DownLinkData> result = this.downLinkConverter
                    .convertDownLink(Collections.singletonList(message), mdMap);
            if (!result.isEmpty()) {
                DownLinkData downLink = result.get(0);
                if (!downLink.isEmpty()) {
                    Map<String, String> metadata = downLink.getMetadata();
                    String identifier = metadata.get("identifier");
                    String deviceId = message.getIntegrateNo();
                    
                    // 从元数据获取流水号，如果没有则生成新的
              //      String flowId = metadata.containsKey("flowId") ?
                   //         metadata.get("flowId") : generateFlowId();
                    String flowId="0100";
                    log.info("收到下行消息: deviceId={}, identifier={}, flowId={}", 
                            deviceId, identifier, flowId);
                    
                    // 根据identifier调用对应的CommandService方法，并传入流水号
                    boolean success = executeCommand(identifier, flowId, downLink);
                    
                    if (callback != null && success) {
                        // 使用flowId作为key
                        callback.waitForResponse(flowId, downLink);
                        log.info("消防设备--平台下发指令---写入回调---key:{}", flowId);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("下行消息处理失败", e);
        }
    }
    
    /**
     * 根据identifier执行相应的命令
     * 
     * @param identifier 命令标识符
     * @param flowId 流水号
     * @param downLink 下行数据
     * @return 执行结果
     */
    private boolean executeCommand(String identifier, String flowId, DownLinkData downLink) {
        try {
            JsonNode params = this.objectMapper.readTree(downLink.getData());
            
            switch (identifier) {
                // 系统命令
                case "readSystemStatus":
                    return commandService.sendReadSystemStatus(
                            flowId,
                            params.get("systemType").asInt(),
                            params.get("systemAddress").asInt());
                            
                case "readComponentStatus":
                    return commandService.sendReadComponentStatus(
                            flowId,
                            params.get("systemType").asInt(),
                            params.get("systemAddress").asInt(),
                            params.get("componentAddress").asText());
                            
                case "readAnalogValue":
                    return commandService.sendReadAnalogValue(
                            flowId,
                            params.get("systemType").asInt(),
                            params.get("systemAddress").asInt(),
                            params.get("componentAddress").asText());
                            
                case "readOperationRecord":
                    return commandService.sendReadOperationRecord(
                            flowId,
                            params.get("systemType").asInt(),
                            params.get("systemAddress").asInt(),
                            params.get("recordCount").asInt(),
                            params.get("startTime").asText());
                            
                case "readSoftwareVersion":
                    return commandService.sendReadSoftwareVersion(
                            flowId,
                            params.get("systemType").asInt(),
                            params.get("systemAddress").asInt());
                            
                case "readSystemConfig":
                    return commandService.sendReadSystemConfig(
                            flowId,
                            params.get("systemType").asInt(),
                            params.get("systemAddress").asInt());
                            
                case "readComponentConfig":
                    return commandService.sendReadComponentConfig(
                            flowId,
                            params.get("systemType").asInt(),
                            params.get("systemAddress").asInt(),
                            params.get("componentAddress").asText());
                            
                case "readSystemTime":
                    return commandService.sendReadSystemTime(
                            flowId,
                            params.get("systemType").asInt(),
                            params.get("systemAddress").asInt());
                
                // 设备命令
                case "readDeviceStatus":
                    return commandService.sendReadDeviceStatus(flowId);
                    
                case "readDeviceOperation":
                    return commandService.sendReadDeviceOperation(
                            flowId,
                            params.get("recordCount").asInt(),
                            convertDateTimeToHex(params.get("startTime").asText()));
                            
                case "readDeviceVersion":
                    return commandService.sendReadDeviceVersion(flowId);
                    
                case "readDeviceConfig":
                    return commandService.sendReadDeviceConfig(flowId);
                    
                case "readDeviceTime":
                    return commandService.sendReadDeviceTime(flowId);
                    
                case "initDevice":
                    return commandService.sendInitDevice(flowId);
                    
                case "syncDeviceTime":
                    return commandService.sendSyncDeviceTime(
                            flowId,
                            params.get("time").asText());
                            
                case "checkPost":
                    return commandService.sendCheckPost(
                            flowId,
                            params.get("timeout").asInt());
                    
                default:
                    log.warn("未知的命令标识符: {}", identifier);
                    return false;
            }
        } catch (Exception e) {
            log.error("执行命令失败: {}", identifier, e);
            return false;
        }
    }

    @Override
    public void destroy() {
        // 释放资源
        if (nettyServer != null) {
            try {
                // 关闭服务器
            } catch (Exception e) {
                log.error("关闭消防设备服务器失败", e);
            }
        }
    }

    /**
     * 将日期时间字符串转换为特定格式的十六进制字符串
     * 输入格式: yyyy-MM-dd HH:mm:ss
     * 输出格式: 秒分时日月年 (每个值用两位十六进制表示)
     * 
     * @param dateTimeStr 日期时间字符串，如 "2012-09-26 09:58:02"
     * @return 十六进制字符串，如 "023A091A090C"
     */
    public static String convertDateTimeToHex(String dateTimeStr) {
        try {
            // 解析日期时间字符串
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = sdf.parse(dateTimeStr);
            
            // 获取日历实例并设置时间
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            
            // 获取各个时间组件
            int year = calendar.get(Calendar.YEAR) % 100; // 只取年份的后两位
            int month = calendar.get(Calendar.MONTH) + 1; // 月份从0开始，需要+1
            int day = calendar.get(Calendar.DAY_OF_MONTH);
            int hour = calendar.get(Calendar.HOUR_OF_DAY);
            int minute = calendar.get(Calendar.MINUTE);
            int second = calendar.get(Calendar.SECOND);
            
            // 按照"秒分时日月年"的顺序组合成十六进制字符串
            return String.format("%02X%02X%02X%02X%02X%02X", 
                    second, minute, hour, day, month, year);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}