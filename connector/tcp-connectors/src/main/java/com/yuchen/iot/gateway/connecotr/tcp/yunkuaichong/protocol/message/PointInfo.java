package com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @description: 集中器的终端信息
 * @author: zhonghx
 * @create: 2022-12-09 19:40
 **/

@Data
@AllArgsConstructor
public class PointInfo implements MessageEncoder {

    /*子终端序号*/
    private byte index;
    /*CAN地址*/
    private byte canAddress;
    /*子终端编号*/
    private String number;

    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(index);
        buf.writeByte(canAddress);
        buf.writeBytes(number.getBytes());
        return buf;
    }

}
