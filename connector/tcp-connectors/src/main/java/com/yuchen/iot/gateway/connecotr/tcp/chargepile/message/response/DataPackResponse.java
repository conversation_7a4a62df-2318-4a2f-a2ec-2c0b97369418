package com.yuchen.iot.gateway.connecotr.tcp.chargepile.message.response;

import com.alibaba.fastjson.JSON;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.enums.ChargeCommandCode;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.util.BytesUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
public class DataPackResponse {

    /**
     * 下发的命令
     */
    private int cmd;
    /**
     * 下发的数据域
     */
    private Object data;

    /**
     * 解码实体类
     */
    public byte[] encode() throws Exception{
        ChargeCommandCode msgTypeEnum = ChargeCommandCode.findCode(cmd);
        byte[] response = null;
        switch (msgTypeEnum) {
            case PILE_AUTH_OA: response = getOaResponse(data);break;
            default: break;
        }
        return response;
    }

    /**
     * 编码OA认证请求
     * @param data
     * @return
     */
    public byte[] getOaResponse(Object data) throws Exception{
        AuthOAResponse authResponse = JSON.parseObject(data.toString(), AuthOAResponse.class);
        log.info("authResponse :{}",JSON.toJSONString(authResponse));
        String hexCode = authResponse.getContent();
        //todo 到时候测试需要看下这个地方
        return BytesUtil.hexStringToBytes(completeOaZero(hexCode));
    }

    /**
     * OA认证补零
     * @param content
     * @return
     */
    public String completeOaZero(String content){
        if(content.isEmpty()){
            return "";
        }
        if(content.length() % 16 != 0 && content.length() == 26){
            content +="0000000";
        }
        return content;
    }

}
