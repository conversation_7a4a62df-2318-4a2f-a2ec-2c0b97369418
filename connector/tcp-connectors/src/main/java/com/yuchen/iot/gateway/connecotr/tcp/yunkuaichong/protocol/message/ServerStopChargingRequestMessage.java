package com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.message;

import com.yuchen.iot.gateway.connecotr.tcp.chargepile.util.BytesUtil;
import com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.BCDUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @description: 主站停止充电请求，用户从APP微信操作停止充电，此时的应答数据并不是最终可结算的数据，等充电完全结束后，终端会重新发起终端充电结束，作为最终的结算依据
 * @author: zhonghx
 * @create: 2022-12-09 19:05
 **/
@Data
@AllArgsConstructor
public class ServerStopChargingRequestMessage implements MessageEncoder {

    /*枪号 1-n*/
    private byte gunNo;

    /*停止原因 0x01，人为原因， 0x02 其他原因*/
    private byte stopReason;

    /*充电流水号BCD*/
    private String serialNumber;

    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(gunNo);
        buf.writeByte(stopReason);
        byte[] bytes = BCDUtil.strToBcd(serialNumber);
        bytes = BytesUtil.bytesReverse(bytes);
        buf.writeBytes(bytes);
        return buf;
    }
}
