package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import lombok.Data;

/**
 * @description: 充电桩应答服务器下发升级参数指令
 * @author: zhonghx
 * @create: 2023-02-14 13:42
 **/

@Data
public class TcpOTAResponse implements MessageDecoder {

    /*确认
    * 0.准备就绪
    * 1.准备失败
    * 2.下发文件有误
    * */
    private byte ack;

    /*单包长度*/
    private short singlePackLen;

    /*文件总报数*/
    private short totalPacks;
    @Override
    public void decode(ByteBuf in) {
        ack = in.readByte();
        singlePackLen = in.readShort();
        totalPacks = in.readShort();
    }
}
