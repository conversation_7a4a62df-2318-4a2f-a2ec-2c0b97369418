package com.yuchen.iot.gateway.connecotr.tcp.isapi;

import com.fasterxml.jackson.databind.JsonNode;
import com.yuchen.iot.gateway.connecotr.tcp.AbstractTcpClientConnector;
import com.yuchen.iot.gateway.connecotr.tcp.JsonTcpConnectorMessage;
import com.yuchen.iot.gateway.connecotr.tcp.isapi.comm.Constant;
import com.yuchen.iot.gateway.connecotr.tcp.isapi.netty.*;
import com.yuchen.iot.gateway.connector.api.data.UpLinkData;
import com.yuchen.iot.gateway.connector.api.data.UpLinkMetaData;
import io.netty.channel.Channel;
import io.netty.channel.ChannelInitializer;
import io.netty.handler.codec.http.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 基础TCP客户端实现
 *
 * 不作粘包处理
 *
 * <AUTHOR>
 * @date 2022/11/23 下午3:41
 * @version 2.0.0
 */
public class ISAPITcpClientConnector extends AbstractTcpClientConnector<JsonTcpConnectorMessage> {

    private static final Logger log = LoggerFactory.getLogger(ISAPITcpClientConnector.class);

    private final Map<String, Channel> channels = new HashMap<>();

    @Override
    protected ChannelInitializer<?> getChannelInitializer() {
        return new ISAPIChannelInitializer(new ISAPIDigestHandler(), new ISAPIClientHandler(), new ISAPIIOHandler());
    }

    /**
     * @see ISAPIEvent
     */
    protected void doProcess(JsonTcpConnectorMessage message) throws Exception {
        // TODO 需要过滤一遍消息，文件单独上传
        JsonNode json = mapper.readTree(message.getMessageInBytes());
        // TODO
        List<UpLinkData> upLinkDataList = this.convert(message);
        if (upLinkDataList != null && !upLinkDataList.isEmpty()) {
            for (UpLinkData upLinkData : upLinkDataList) {
                this.processUpLinkData(upLinkData);
                log.trace("[{}] Processing up link data", upLinkData);
            }
        }
    }

    protected List<UpLinkData> convert(JsonTcpConnectorMessage message) throws Exception {
        byte[] data = message.getMessageInBytes();
        Map<String, String> mdMap = new HashMap<>(this.metadataTemplate.getKvMap());
        return this.convertToUpLinkDataList(this.connectorContext.getCallbackExecutor(), data,
                new UpLinkMetaData(message.getContentType(), mdMap));
    }

    @Override
    protected void doListenOnEndpoint(String deviceKey, Map<String, String> endpointMeta) {
        try {
            String host = endpointMeta.get("host");
            int port = Integer.parseInt(endpointMeta.get("port"));
            String api = endpointMeta.get("stream-api");

            String username = endpointMeta.get("username");
            String password = endpointMeta.get("password");

            // Make the connection attempt.
            Channel channel = this.bootstrap.connect(host, port).addListener(future -> {
                if (future.isSuccess()) {
                    // connect success
                } else {
                    future.cause().printStackTrace();
                }
            }).sync().channel();

            // cache
            this.channels.put(deviceKey, channel);

            // Prepare the HTTP request.
            HttpRequest request = new DefaultFullHttpRequest(HttpVersion.HTTP_1_1, HttpMethod.GET, api);
            request.headers().set(HttpHeaderNames.HOST, host);
            request.headers().set(HttpHeaderNames.ACCEPT_ENCODING, HttpHeaderValues.GZIP);
            request.headers().set(Constant.HEADER_USERNAME, username);
            request.headers().set(Constant.HEADER_PASSWORD, password);
            request.headers().set(Constant.HEADER_DEVICE_KEY, password);

            channel.attr(Constant.LISTENER_ATTRIBUTE_KEY).set(new ISAPIChannelListener<JsonTcpConnectorMessage>() {
                @Override
                public void onDisconnect() {
                    getUpLinkDataListener(deviceKey).onSouthDisconnect();
                }

                @Override
                public void onConnect() {
                    getUpLinkDataListener(deviceKey).onSouthConnect();
                }

                @Override
                public void onMessage(JsonTcpConnectorMessage event) {
                    process(event);
                }
            });

            // Send the HTTP request.
            channel.writeAndFlush(request);

            // Wait for the server to close the connection.
            channel.closeFuture().sync();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void doListenOffEndpoint(String deviceKey, Map<String, String> endpointMeta) {
        if (this.channels.containsKey(deviceKey)) {
            Channel channel = this.channels.get(deviceKey);
            if (channel != null) {
                channel.close().addListener(future -> {
                    if (future.isSuccess()) {
                        this.getUpLinkDataListener(deviceKey).onSouthDisconnect();
                    } else {
                        future.cause().printStackTrace();
                    }
                });
            }
        }
    }

    @Override
    public void destroy() {

    }
}
