package com.yuchen.iot.gateway.connecotr.tcp.kehua;

import com.yuchen.iot.gateway.connecotr.tcp.JsonTcpConnectorMessage;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.timeout.IdleStateHandler;

import java.util.concurrent.TimeUnit;

public class KhChargingPointChannelInitializer extends ChannelInitializer<SocketChannel> {

	KhChargingChannelListener<JsonTcpConnectorMessage> khChargingChannelListener;

	public KhChargingPointChannelInitializer(KhChargingChannelListener<JsonTcpConnectorMessage> khChargingChannelListener) {
		this.khChargingChannelListener = khChargingChannelListener;
	}

	protected void initChannel(SocketChannel socketChannel) {
		ChannelPipeline p = socketChannel.pipeline();
		p.addLast(new IdleStateHandler(180, 0, 0, TimeUnit.SECONDS));
		p.addLast(new KhChargingPointHandler(khChargingChannelListener));
		p.addLast(new KhChargingPointEncoder());
		p.addLast(new KhChargingPointDecoder());
	}
}
