package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.Data;

/**
 * @description: 终端数据单元封装
 * @author: zhonghx
 * @create: 2023-02-16 14:34
 **/
@Data
public class TerminalDataPack implements MessageEncoder{
    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        return buf;
    }
}
