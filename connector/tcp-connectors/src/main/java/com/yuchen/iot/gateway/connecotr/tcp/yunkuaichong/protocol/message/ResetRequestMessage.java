package com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @description: 主站发起终端复位指令，终端应答
 * @author: zhonghx
 * @create: 2022-12-09 19:15
 **/
@Data
@AllArgsConstructor
public class ResetRequestMessage implements MessageEncoder {

    /*方式， 1常规复位（充电状态不复位），9强制复位*/
    private byte type;

    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(type);
        return buf;
    }
}
