package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;

/**
 * @description: 离线规则命令
 * @author: zhonghx
 * @create: 2023-02-16 10:34
 **/
public class OfflineRulesRequest implements MessageEncoder{
    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        return buf;
    }
}
