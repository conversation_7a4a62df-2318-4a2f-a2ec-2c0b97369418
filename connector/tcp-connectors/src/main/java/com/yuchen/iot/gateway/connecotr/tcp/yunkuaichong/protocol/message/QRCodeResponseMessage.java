package com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.message;

import io.netty.buffer.ByteBuf;
import lombok.Data;


/**
 * @description: 终端应答平台设置二维码的请求
 * @author: zhong
 * @create: 2022-12-08 16:55
 **/
@Data
public class QRCodeResponseMessage implements MessageDecoder {

    /**
     * 设置结果
     * 6表示成功，0表示失败
     */
    private int setResult;

    @Override
    public void decode(ByteBuf in) {
        setResult = in.readByte();
    }
}
