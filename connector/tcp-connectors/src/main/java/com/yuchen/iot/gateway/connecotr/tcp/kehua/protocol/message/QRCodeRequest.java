package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;

import java.nio.charset.StandardCharsets;

/**
 * @description: 二维码设置请求
 * @author: zhonghx
 * @create: 2023-02-16 10:38
 **/

@AllArgsConstructor
public class QRCodeRequest implements MessageEncoder{

    /*规则长度
    * 本次下发二维码规则的长度
    * */
    private byte len;

    /*二维码规则 《 100字节*/
    private String code;

    @Override
    public ByteBuf encode() {

        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(len);
        buf.writeBytes(code.getBytes(StandardCharsets.US_ASCII));
        return buf;
    }
}
