package com.yuchen.iot.gateway.connecotr.tcp.kehua;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yuchen.iot.gateway.connecotr.tcp.JsonTcpConnectorMessage;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.util.ExecutorUtils;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.comm.Constant;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.ChargingPointProtocol;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.MsgHeader;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.MsgType;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.ProtocolConstants;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message.*;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.util.HexStringUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import io.netty.util.ReferenceCountUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

import static com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.MsgType.*;

/**
 * 处理科华充电桩 字节流 解码
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023年5月25日 20:03:54
 */
public class KhChargingPointDecoder extends ByteToMessageDecoder {

    private static final Logger log = LoggerFactory.getLogger(KhChargingPointDecoder.class);

    private static ExecutorService chargingEventExecutor;

    static  {
        chargingEventExecutor = new ExecutorUtils(50, 100, 60000L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(100000), "kehua-charging-iot-executor");
    }

    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) throws Exception {
        log.info("KhChargingPointDecoder---" + HexStringUtil.Bytes2hexStr(ByteBufUtil.getBytes(in)));
        // 如果有粘包情况，则可以多次读取
        while (in.readableBytes() > ProtocolConstants.HEADER_TOTAL_LEN) {
            log.info("KhChargingPointDecoder---while--- begin ---{}",System.currentTimeMillis());
            in.markReaderIndex();
            short preamble = in.readShort();
            if (preamble != ProtocolConstants.PREAMBLE) {
                log.info("preamble number is illegal, " + preamble);
                log.info("KhChargingPointDecoder---preamble---" + HexStringUtil.Bytes2hexStr(ByteBufUtil.getBytes(in)));
                in.clear();
                return;
            }

            short frameLength = in.readShort();
            if (in.readableBytes() < frameLength - 4) {
                in.resetReaderIndex();
                return;
            }

            short version = in.readShort();
            short msgId = in.readShort();
            byte devType = in.readByte();

            ByteBuf byteBuf = in.readBytes(ProtocolConstants.DEV_SN_LENGTH);
            byte[] bytes = ByteBufUtil.getBytes(byteBuf);
            // TODO:排查内存泄漏的问题
            ReferenceCountUtil.release(byteBuf);
            String devSN = new String(bytes);

            byte secretType = in.readByte();
            short cmd = in.readUnsignedByte();
            short devId = in.readShort();
            byte gunId = in.readByte();
            ByteBuf data = in.readBytes(frameLength - ProtocolConstants.HEADER_TOTAL_LEN);
            short verifyCode = in.readShort();
            byte terminator = in.readByte();

            MsgHeader header = new MsgHeader();
            header.setPreamble(preamble);
            header.setFrameLength(frameLength);
            header.setVersion(version);
            header.setMsgId(msgId);
            header.setDevType(devType);
            header.setDevNumber(devSN);
            header.setSecretType(secretType);
            header.setCmd(cmd);
            header.setDevId(devId);
            header.setGunNumber(gunId);
            header.setVerifyCode(verifyCode);
            header.setTerminator(terminator);
            chargingEventExecutor.submit(()->{
                // 处理消息
                header(ctx, header, data);
                // TODO:排查内存泄漏的问题
                ReferenceCountUtil.release(data);
            });
            log.info("KhChargingPointDecoder---while--- end ---{}",System.currentTimeMillis());
        }


    }

    /**
     * 处理消息
     * @param ctx 通道处理上下文
     * @param header 消息体
     * @param data 数据内容
     */
    public void header(ChannelHandlerContext ctx, MsgHeader header, ByteBuf data) {
        log.info("KhChargingPointDecoder---header---{}",JSON.toJSONString(header));
        // 获取消息类别
        MsgType msgTypeEnum = MsgType.findByType(header.getCmd());
        if (msgTypeEnum == null) {
            log.info("KhChargingPointDecoder---msgTypeEnum---{}",JSON.toJSONString(header));
            return;
        }

        ChargingPointProtocol<?> chargingPointProtocol = null;
        KhChargingChannelListener listener = ctx.channel().attr(Constant.eventListenerKey).get();

        //保存上行的消息流水号，应答时使用
        ctx.channel().attr(Constant.reqMsgId).set(header.msgId);

        //保存上行的枪号
        ctx.channel().attr(Constant.gunNumber).set(header.gunNumber);

        switch (msgTypeEnum) {
            case LOGIN: {

                chargingPointProtocol = packet(LoginRequest.class, header, data);

                // 如果设备极短时间内发起两次登录请求，需要清理旧的channel，把旧的channel中设备ID信息抹除，
                // 以防旧的channel关闭时根据设备ID把新的channel也关闭了
                Channel oldChannel = KhChargingPointChannelManager.getChannelByName(header.devNumber);
                if (oldChannel != null){
                    log.info("存在旧的channel，清理旧的channel配置信息，避免后期旧channel关闭时把新的channel关闭了");
                    oldChannel.attr(Constant.integrateNoKey).remove();
                }

                // 存储channel
                KhChargingPointChannelManager.addChannel(header.devNumber, ctx.channel());

                log.info("科华登陆:{}", header.devNumber);

                // 将客户端ID作为自定义属性加入到channel中，方便随时channel中获取用户ID
                ctx.channel().attr(Constant.integrateNoKey).setIfAbsent(header.devNumber);

                //保存devType
                ctx.channel().attr(Constant.devTypeKey).set(header.devType);

                //保存devVer
                ctx.channel().attr(Constant.devVerKey).set(header.version);

                //保存上行的设备ID，但是下行也用设备ID
                ctx.channel().attr(Constant.devId).set(header.devId);

                // iot设备上线
                if (listener != null) {
                    listener.onConnect(header.devNumber);
                }

                MsgHeader headerAck = JSON.parseObject(JSON.toJSONString(header), MsgHeader.class);
                LoginResponse loginResponse = new LoginResponse((byte) 1, (int) (System.currentTimeMillis() / 1000));
                ChargingPointProtocol<LoginResponse> response = new ChargingPointProtocol<>();
                response.setBody(loginResponse);

                headerAck.setCmd(LOGIN_ACK.getType());
                response.setHeader(headerAck);
                ctx.channel().writeAndFlush(response);
                break;
            }
            case HEARTBEAT: {
                chargingPointProtocol = packet(HeartbeatRequest.class, header, data);

                // 如果充电桩的channel不存在则不进行回应
                Channel channel = KhChargingPointChannelManager.getChannelByName(header.devNumber);
                log.info("科华充电桩进行心跳回应:{}",header.devNumber);
                if (null == channel){
                    log.info("科华充电桩的channel不存在则不进行心跳回应");
                    break;
                }

                //response
                MsgHeader headerAck = JSON.parseObject(JSON.toJSONString(header), MsgHeader.class);
                HeartbeatResponse heartbeatResponseMessage = new HeartbeatResponse((byte) 1);
                ChargingPointProtocol<HeartbeatResponse> response = new ChargingPointProtocol<>();
                response.setBody(heartbeatResponseMessage);
                headerAck.setCmd(HEARTBEAT_ACK.getType());
                response.setHeader(headerAck);
                ctx.channel().writeAndFlush(response);
                break;
            }
            case GET_DATA_ACK: {
                log.info("科华-充电桩上传终端数据");
                chargingPointProtocol = packet(GetTerminalDataResponse.class, header, data);
                break;
            }
            case SET_DATA_ACK: {
                log.info("科华-充电桩上传写终端数据确认帧");
                chargingPointProtocol = packet(SetTerminalDataResponse.class, header, data);
                break;
            }
            case START_STOP_CHARGING_ACK: {
                log.info("科华-充电桩上传启停充电通知确认帧");
                chargingPointProtocol = packet(ChargingResponse.class, header, data);
                break;
            }
            case START_STOP_RESULT: {
                log.info("科华-充电桩上传启停充电结果帧");
                chargingPointProtocol = packet(ChargingResultRequest.class, header, data);
                break;
            }
            case CARD_AUTH: {
                log.info("科华-充电桩上传刷卡请求帧");
                chargingPointProtocol = packet(CardAuthRequest.class, header, data);
                break;
            }
            case REAL_TIME_DATA: {
                log.info("科华-充电桩上传实时数据");
                chargingPointProtocol = packet(RealtimeChargingDataRequest.class, header, data);
                break;
            }
            case CHARGING_HISTORY_RECORD: {
                log.info("科华-充电桩上传历史充电记录");
                chargingPointProtocol = packet(ChargingHistoryRequest.class, header, data);
                break;
            }
            case ALARM_HISTORY_RECORD: {
                log.info("科华-充电桩上传历史告警数据");
                chargingPointProtocol = packet(AlarmHistoryRequest.class, header, data);

                // 做出应答
                MsgHeader headerAck = JSON.parseObject(JSON.toJSONString(header), MsgHeader.class);
                AlarmHistoryRequest alarmHistoryRequest = (AlarmHistoryRequest) chargingPointProtocol.getBody();
                AlarmHistoryResponse alarmHistoryResponse = new AlarmHistoryResponse((byte) 1,
                        alarmHistoryRequest.getType(), alarmHistoryRequest.getStartTime());
                ChargingPointProtocol<AlarmHistoryResponse> response = new ChargingPointProtocol<>();
                response.setBody(alarmHistoryResponse);
                headerAck.setCmd(ALARM_HISTORY_RECORD_ACK.getType());
                response.setHeader(headerAck);
                ctx.channel().writeAndFlush(response);
                break;
            }
            case OPT_HISTORY_RECORD: {
                log.info("科华-充电桩上传操作记录数据");
                chargingPointProtocol = packet(OptHistoryRequest.class, header, data);
                break;
            }
            case FTP_OTA_ACK: {
                log.info("科华-充电桩上传远程升级应答信息");
                chargingPointProtocol = packet(FtpOTAResponse.class, header, data);
                break;
            }
            case FTP_OTA_RESULT: {
                log.info("科华-充电桩上传升级结果标识");
                chargingPointProtocol = packet(FtpOTAResultRequest.class, header, data);
                break;
            }
            case TCP_OTA_ACK: {
                log.info("科华-充电桩应答下发升级参数指令");
                chargingPointProtocol = packet(TcpOTAResponse.class, header, data);
                break;
            }
            case TCP_OTA_FILE_ACK: {
                log.info("科华-充电桩应答服务器下发升级文件数据指令");
                chargingPointProtocol = packet(TcpOTATransferFileResponse.class, header, data);
                break;
            }
            case TCP_OTA_RESULT: {
                log.info("科华-充电桩上传升级结果");
                chargingPointProtocol = packet(TcpOTAResultRequest.class, header, data);
                break;
            }
            case REPORT_DATA: {
                log.info("科华-终端主动上传数据");
                chargingPointProtocol = packet(RealTimeGunStateChangedRequest.class, header, data);

                // 做出应答
                MsgHeader headerAck = JSON.parseObject(JSON.toJSONString(header), MsgHeader.class);
                RealTimeGunStateChangedRequest realTimeGunStateChangedRequest =
                        (RealTimeGunStateChangedRequest) chargingPointProtocol
                        .getBody();
                RealTimeGunStateChangedResponse realTimeGunStateChangedResponse =
                        new RealTimeGunStateChangedResponse(realTimeGunStateChangedRequest.getFrameID(), (byte) 1);
                ChargingPointProtocol<RealTimeGunStateChangedResponse> response = new ChargingPointProtocol<>();
                response.setBody(realTimeGunStateChangedResponse);
                headerAck.setCmd(REPORT_DATA_ACK.getType());
                response.setHeader(headerAck);
                ctx.channel().writeAndFlush(response);
                break;
            }
            case TIMING_ACK: {
                log.info("科华-终端上传对时结果");
                chargingPointProtocol = packet(TimingResponse.class, header, data);
                break;
            }
            case CHARGING_AMOUNT_CHANGED_ACK: {
                log.info("科华-终端回复上传结果");
                chargingPointProtocol = packet(ChargingAmountChangedResponse.class, header, data);
                break;
            }
            case VIN_REQ_CHARGING: {
                log.info("科华-VIN 码请求充电");
                chargingPointProtocol = packet(ChargingWithVINRequest.class, header, data);
                break;
            }
            case PWD_REQ_CHARGING: {
                log.info("科华-密码请求充电");
                chargingPointProtocol = packet(ChargingWithAccountRequest.class, header, data);
                break;
            }
            case SET_TIME_INTERVAL_RATE_ACK: {
                log.info("科华-终端回复结果");
                chargingPointProtocol = packet(SetRateResponse.class, header, data);
                break;
            }
            case GET_TIME_INTERVAL_RATE_ACK: {
                log.info("科华-终端回复结果");
                chargingPointProtocol = packet(GetRateResponse.class, header, data);
                break;
            }
            case SET_POWER_OUT_LIMIT_ACK: {
                log.info("科华-终端回复结果");
                chargingPointProtocol = packet(LimitPowerResponse.class, header, data);
                break;
            }
            case SET_QR_FORMAT_ACK: {
                log.info("科华-终端回复二维码规则设置结果");
                chargingPointProtocol = packet(QRCodeResponse.class, header, data);
                break;
            }
            case GET_REAL_DATA_ACK: {
                log.info("科华-终端回复充电实时数据");
                chargingPointProtocol = packet(GetRealtimeChargingDataResponse.class, header, data);
                break;
            }
            case REBOOT_ACK: {
                log.info("科华-终端回复系统重启结果");
                chargingPointProtocol = packet(RebootResponse.class, header, data);
                break;
            }
            case LOGOUT_ACK: {
                log.info("科华-终端回复下线注销指令结果");
                chargingPointProtocol = packet(LogoutResponse.class, header, data);
                break;
            }
        }

        if (listener != null) {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode node = objectMapper.valueToTree(chargingPointProtocol);
            listener.onMessage(new JsonTcpConnectorMessage(node));
        }
    }


    private <T extends MessageDecoder> ChargingPointProtocol<T> packet(Class<T> clazz, MsgHeader msgHeader,
                                                                       ByteBuf data) {

        try {
            T t = clazz.getDeclaredConstructor().newInstance();
            t.decode(data);
            ChargingPointProtocol<T> protocol = new ChargingPointProtocol<>();
            protocol.setHeader(msgHeader);
            protocol.setBody(t);
            return protocol;
        } catch (InstantiationException | IllegalAccessException | InvocationTargetException |
                NoSuchMethodException e) {
            throw new RuntimeException(e);
        }

    }


}
