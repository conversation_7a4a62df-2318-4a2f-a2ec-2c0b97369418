package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import com.yuchen.iot.gateway.connecotr.tcp.kehua.util.BCDUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.Data;

/**
 * @description: 历史告警数据主动上传
 * @author: zhonghx
 * @create: 2023-02-13 14:16
 **/
@Data
public class AlarmHistoryRequest implements MessageDecoder{

    /*告警点*/
    private byte type;

    /*告警开始时间*/
    private String startTime;

    /*告警结束时间*/
    private String endTime;

    /*是否影响充电
    *
    * 0.无影响
    * 1.有影响
    * 交流桩默认传1
    * */
    private byte isAffectCharging;
    @Override
    public void decode(ByteBuf in) {
        type = in.readByte();
        byte[] startTimeBytes = ByteBufUtil.getBytes(in.readBytes(7));
        startTime = BCDUtil.bcdToStr(startTimeBytes);
        byte[] endTimeBytes = ByteBufUtil.getBytes(in.readBytes(7));
        endTime = BCDUtil.bcdToStr(endTimeBytes);
        isAffectCharging = in.readByte();
    }
}
