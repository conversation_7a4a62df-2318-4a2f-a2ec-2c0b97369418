package com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.convertor;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.yuchen.iot.gateway.connector.api.converter.AbstractKeyEvaluator;
import com.yuchen.iot.gateway.connector.api.converter.evaluator.UpLinkEvaluator;
import com.yuchen.iot.gateway.connector.api.data.UpLinkData;
import com.yuchen.iot.gateway.connector.api.data.UpLinkMetaData;
import com.yuchen.iot.gateway.connector.api.data.message.CallResponse;
import com.yuchen.iot.gateway.connector.api.data.message.Event;
import com.yuchen.iot.gateway.connector.api.data.message.EventValue;
import org.pf4j.Extension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 海康奥能充电桩上行转换器
 *
 * <AUTHOR>
 * @date 2022/12/28
 * @version 1.0
 */
@Component
@Extension
public class HikChargingUpLinkEvaluator extends AbstractKeyEvaluator implements UpLinkEvaluator {

    private final Logger logger = LoggerFactory.getLogger(HikChargingUpLinkEvaluator.class);

    @Override
    public ListenableFuture<Object> execute(byte[] data, UpLinkMetaData upLinkMetaData) {
        List<UpLinkData> upLinkDataList = new ArrayList<>();
        try {

            JsonNode jsonNode = this.objectMapper.readTree(data);

            JsonNode header = jsonNode.get("header");
            JsonNode body = jsonNode.get("body");

            MsgType msgType = MsgType.findByType(header.get("cmd").asInt());
            if (msgType == null){
                throw new NullPointerException();
            }
            String integrateNo =  body.get("devNo").asText();/*"AABBCCDDEEFF"*//*header.get("address").asText()*/;
            Event event = new Event();
            EventValue eventValue = new EventValue();

            CallResponse callResponse = new CallResponse();
            String callId = null;

            switch(msgType){
                /*登入*/
                case CLI_LOGIN:
                {
                    event.setIdentifier("login");
                    event.setValue(eventValue);
                    eventValue.put("sn",integrateNo);

                }
                break;
                /*心跳/校时*/
                case CLI_HEARTBEAT:
                {
                    event.setIdentifier("heartbeat");
                    event.setValue(eventValue);

                }
                break;
                /*终端请求费率*/
                case CLI_RATE_REQ:
                {
                    event.setIdentifier("getRateCli");
                    event.setValue(eventValue);
                    eventValue.put("reserved", "nothing");
                }
                break;
                /*实时状态信息*/
                case CLI_REAL_TIME_STATUS:
                {
                    event.setIdentifier("realtimeStatus");
                    event.setValue(eventValue);
                 //   eventValue.put();
/*                    body.fields().forEachRemaining(json -> {
                        eventValue.put(json.getKey(), json.getValue().asText());
                    });*/
                }
                break;
                /*充电枪遥测信息*/
                case CLI_GUN_STATUS:
                {
                    event.setIdentifier("realTimeGunStatus");
                    event.setValue(eventValue);
                    body.fields().forEachRemaining(json -> {
                        eventValue.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                /*充电实时信息*/
                case CLI_CHARGING_INFO:
                case CLI_CHARGING_INFO_NEW:
                {
                    event.setIdentifier("realTimeChargingInfo");
                    event.setValue(eventValue);
                    body.fields().forEachRemaining(json -> {
                        eventValue.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                /*网络信息*/
                case CLI_NETWORK_INFO:
                {
                    event.setIdentifier("realtimeNetworkInfo");
                    event.setValue(eventValue);
                    body.fields().forEachRemaining(json -> {
                        eventValue.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                /*终端遥测信息*/
                case CLI_TELEMETRY:
                {
                    event.setIdentifier("realTimeTelemetryInfo");
                    event.setValue(eventValue);
                    body.fields().forEachRemaining(json -> {
                        eventValue.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                /*刷卡鉴权*/
                case CLI_CARD_AUTH:
                {
                    event.setIdentifier("cartAuth");
                    event.setValue(eventValue);
                    body.fields().forEachRemaining(json -> {
                        eventValue.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                /*终端启动充电*/
                case CLI_START_CHARGING:
                {
                    event.setIdentifier("startChargingCli");
                    event.setValue(eventValue);
                    body.fields().forEachRemaining(json -> {
                        eventValue.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                /*终端充电结束*/
                case CLI_STOP_CHARGING:
                {
                    event.setIdentifier("stopChargingCli");
                    event.setValue(eventValue);
                    body.fields().forEachRemaining(json -> {
                        eventValue.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                /*终端充电结束*/
                case CLI_STOP_CHARGING_NEW:
                {
                    event.setIdentifier("stopChargingNewCli");
                    event.setValue(eventValue);
                    body.fields().forEachRemaining(json -> {
                        eventValue.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                /*车辆VIN鉴权*/
                case VIN_AUTH:
                {
                    event.setIdentifier("VinAuthRequest");
                    event.setValue(eventValue);
                    body.fields().forEachRemaining(json -> {
                        eventValue.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                // VIN码启动充电
                case VIN_START_CHARGING:
                {
                    event.setIdentifier("VinStartChargingRequest");
                    event.setValue(eventValue);
                    body.fields().forEachRemaining(json -> {
                        eventValue.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                /*终端响应--终端软硬件版本*/
                case SER_FIRMWARE:
                {
                    callId = integrateNo.concat("getFirmwareInfo");
                    body.fields().forEachRemaining(json -> {
                        callResponse.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                /*终端响应--二维码格式*/
                case SER_SET_QR_FORMAT:
                {
                    callId = integrateNo.concat("setQRFormat");
                    body.fields().forEachRemaining(json -> {
                        callResponse.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                /*终端响应--费率设置*/
                case SER_SET_RATE:
                {
                    callId = integrateNo.concat("setRate");
                    body.fields().forEachRemaining(json -> {
                        callResponse.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;

                /*终端响应--主站读取终端费率*/
                case SER_GET_RATE:
                {
                    callId = integrateNo.concat("getRate");
                    body.fields().forEachRemaining(json -> {
                        callResponse.put(json.getKey(), json.getValue().asText());
                    });
                    callResponse.remove("array");
                }
                break;
                /*终端响应--开始充电*/
                case SERVER_START_CHARGING:
                {
                    // 获取充电流水号
                    String serialNumber = body.get("chargingSerialNumber").asText();
                    callId = integrateNo.concat("startCharging").concat(serialNumber);
                    body.fields().forEachRemaining(json -> {
                        callResponse.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                /*终端响应--结束充电*/
                case SERVER_STOP_CHARGING:
                {
                    // 获取充电流水号
                    String serialNumber = body.get("chargingSerialNumber").asText();
                    callId = integrateNo.concat("stopCharging").concat(serialNumber);
                    body.fields().forEachRemaining(json -> {
                        callResponse.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                default:{
                    logger.error("Not support message type --- ",msgType.name());
                    break;
                }
            }

            if (null != event.getIdentifier()){
                logger.info("海康--终端主动上报事件信息---{}---{}---{}",integrateNo,msgType.name(), JSONObject.toJSONString(event));
            }
            if (callResponse.size() > 0){
                logger.info("海康--平台下发指令响应信息---{}---{}---{}",integrateNo,msgType.name(),
                        JSONObject.toJSONString(callResponse));
            }

            UpLinkData upLinkData = new UpLinkData(integrateNo, event, null, callResponse, callId, null);
            upLinkDataList.add(upLinkData);

        } catch (IOException | NullPointerException e ) {
            e.printStackTrace();
            logger.warn("Evaluator execute failed {}", e.getMessage());
        }
        return Futures.immediateFuture(this.gson.toJson(upLinkDataList));
    }

    @Override
    public String getKey() {
        return "new-charging-up-link";
    }

    @Override
    public void destroy() {
        // do nothing
    }
}
