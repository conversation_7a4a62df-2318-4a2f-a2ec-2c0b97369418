package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @description: 终端上传补发结果
 * @author: zhonghx
 * @create: 2023-02-15 14:39
 **/
@Data
@AllArgsConstructor
public class ChargingAmountChangedResponse implements MessageDecoder{


    /*用户卡号 20字节 ASCII码 卡号或手机号，不足20补\0*/
    private String number;

    /*更新结果 0失败 1成功  2卡号不匹配 3余额未大于1元 4系统不再充电*/
    private byte ack;

    @Override
    public void decode(ByteBuf in) {
        byte[] numberBytes = ByteBufUtil.getBytes(in.readBytes(20));
        number = new String(numberBytes);
        ack = in.readByte();
    }
}
