package com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.request;

import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.message.MessageDecoder;
import io.netty.buffer.ByteBuf;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
@Data
public class BeginChargingRequestMessage implements MessageDecoder {



    private int channel ;


    private double current ;

    private int power  ;



    @Override
    public void decode(ByteBuf in) {

        channel = in.readByte();


        // 读取报警通道号
        current = (double) in.readByte()/10;;



        power = in.readUnsignedShort();

    }
}
