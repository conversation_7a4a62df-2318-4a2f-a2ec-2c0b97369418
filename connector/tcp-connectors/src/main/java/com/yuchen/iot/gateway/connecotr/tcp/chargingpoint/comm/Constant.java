package com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.comm;

import com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.ChargingChannelListener;
import com.yuchen.iot.gateway.connecotr.tcp.isapi.auth.DigestAuthenticator;
import io.netty.util.AttributeKey;

/**
 * TODO 
 * <AUTHOR>
 * @date 2022/12/29
 * @version 1.0
 */
public class Constant {

    //充电桩编号，南向设备唯一标识
    public static final AttributeKey<String> integrateNoKey = AttributeKey.valueOf("chargingIntegrateNo");

    //channel id
    public static final AttributeKey<String> channelIdKey = AttributeKey.valueOf("chargingChannelId");

    //设备类型
    public static final AttributeKey<Integer> devTypeKey = AttributeKey.valueOf("chargingDevType");

    //设备版本
    public static final AttributeKey<Integer> devVerKey = AttributeKey.valueOf("chargingDevVer");

    //设备版本
    public static final AttributeKey<ChargingChannelListener> eventListenerKey = AttributeKey.valueOf("ChargingChannelListener");
}
