package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * @description: 远程FTP升级指令
 * @author: zhonghx
 * @create: 2023-02-13 14:43
 * 充电桩通过主站进行 FTP 软件升级，首先服务器下发升级指令，终端进行
 * 文件下载，最后进行软件升级。FTP 软件升级具体步骤如下：
 * 1) 充电桩与运营云平台建立基本通信后，运营云平台发送远程升级通知给
 * 充电桩；
 * 2) 充电桩收到报文之后进行解析，充电桩上传确认收到软件升级通知；
 * 3) 充电桩根据自身工作状态，检查软件下载过程，异常时告知系统 FTP 情
 * 况；
 * 4) 充电桩告知运营云平台软件安装情况；
 * 5) 运营云平台确认充电桩软件安装情况
 **/
@Data
@AllArgsConstructor
public class FtpOTARequest implements MessageEncoder{

    /*程序类型
    * 0.一体机程序
    1.模块柜程序
    3.分体桩程序
    4.充电模块程序
    5.环境监控程序
    6.交流桩程序
    * */
    private byte softType;

    /*主机Host*/
    private String ipAddress;

    /*端口*/
    private short port;

    /*用户名*/
    private String userName;

    /*密码*/
    private String password;

    /*文件名称*/
    private String fileName;

    /*软件版本*/
    private String version;

    /*文件总长度*/
    private int totalSize;

    /*校验码*/
    private int verifyCode;

    @Override
    public ByteBuf encode() {

        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(softType);
        buf.writeBytes(Arrays.copyOf(ipAddress.getBytes(StandardCharsets.US_ASCII),128));
        buf.writeShort(port);
        buf.writeBytes(Arrays.copyOf(userName.getBytes(StandardCharsets.US_ASCII),20));
        buf.writeBytes(Arrays.copyOf(password.getBytes(StandardCharsets.US_ASCII),20));
        buf.writeBytes(Arrays.copyOf(fileName.getBytes(StandardCharsets.US_ASCII),32));
        buf.writeBytes(Arrays.copyOf(version.getBytes(StandardCharsets.US_ASCII),20));
        buf.writeInt(totalSize);
        buf.writeInt(verifyCode);
        return buf;
    }
}
