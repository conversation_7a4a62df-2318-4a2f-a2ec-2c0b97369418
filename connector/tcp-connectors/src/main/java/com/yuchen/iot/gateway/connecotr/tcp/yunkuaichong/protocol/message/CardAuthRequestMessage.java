package com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.message;


import com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.BCDUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.Data;

/**
 * @description: 刷卡鉴权，由终端发起，主站应答，如果通过鉴权，则允许用户充电并发起终端启动充电，主站应答
 * @author: zhonghx
 * @create: 2022-12-09 11:01
 **/
@Data
public class CardAuthRequestMessage implements MessageDecoder {

    /*枪号 1-n*/
    private byte gunNo;

    /*卡号， 16字节BCD*/
    private String cardNo;

    /*密码，卡密码，MD5 32位加密，按顺序，不用反过来*/
    private String password;

    /*普通用户  2集团用户*/
    private byte cardType;

    /*用户来源*/
    private byte from;

    @Override
    public void decode(ByteBuf in) {
        gunNo = in.readByte();
        byte[] bytes = ByteBufUtil.getBytes(in.readBytes(16));
        cardNo = BCDUtil.bcdToStr(bytes);
        password = in.readBytes(16).toString();
        cardType = in.readByte();
        from = in.readByte();
    }


}
