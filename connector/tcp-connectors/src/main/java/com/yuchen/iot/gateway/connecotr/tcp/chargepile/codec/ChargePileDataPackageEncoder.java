package com.yuchen.iot.gateway.connecotr.tcp.chargepile.codec;

import com.alibaba.fastjson.JSONObject;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.constants.ChargePileConstants;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.message.response.DataPackResponse;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.message.request.PackHeaderRequest;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.util.BytesUtil;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.util.DESUtil;
import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class ChargePileDataPackageEncoder extends MessageToByteEncoder<DataPackResponse> {

    @Override
    protected void encode(ChannelHandlerContext ctx, DataPackResponse msg, ByteBuf out) throws Exception {
        Channel channel = ctx.channel();
        byte[] data = msg.encode();
        String header = channel.attr(ChargePileConstants.PACK_HEADER_ATTRIBUTE_KEY).get();
        PackHeaderRequest packHeader = JSONObject.parseObject(header, PackHeaderRequest.class);
        //****************起始域的值（1 Byte）*****************/
        out.writeByte(packHeader.getStart());

        //****************版本（1 Byte）*****************/
        out.writeByte(packHeader.getVersion());

        //****************功能码（1 Byte）*****************/
        out.writeByte(packHeader.getCmd());

        //****************充电桩编号(4 Byte)****************/
        out.writeBytes((BytesUtil.intToBytes(packHeader.getPileNo())));

        //****************抢号(1 Byte)****************/
        out.writeByte(packHeader.getGunNo());

        //****************加密标志（1 Byte）*****************/
        out.writeByte(packHeader.getEncryptFlag());

        //****************校验码（1 Byte）*****************/
        out.writeByte(packHeader.getVerifyCode());

        //****************消息长度（1 Byte）*****************/
        out.writeByte(packHeader.getMsgLength());

        //****************数据域的值*****************/
        //对校验码加密
        byte[]  encrypt3DES = DESUtil.encrypt3DES(data, DESUtil.key);
        out.writeBytes(encrypt3DES);
        ctx.writeAndFlush(out);
        out.release();
    }
}
