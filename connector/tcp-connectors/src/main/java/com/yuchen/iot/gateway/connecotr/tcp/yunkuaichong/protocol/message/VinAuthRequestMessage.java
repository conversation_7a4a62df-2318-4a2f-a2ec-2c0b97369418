package com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.Data;

import java.nio.charset.StandardCharsets;

/**
 * @description: 车辆VIN鉴权，车辆具有VIN时，可以使用VIN码鉴权，主站应答，如果通过鉴权，则允许启动充电，否则终端结束充电
 * @author: zhonghx
 * @create: 2022-12-09 17:40
 **/
@Data
public class VinAuthRequestMessage implements MessageDecoder {

    /**
     * 枪号
     */
    private byte gunNo;

    /**
     * VIN码 17个字节 ASCII
     */
    private String VIN;

    @Override
    public void decode(ByteBuf in) {
        gunNo = in.readByte();
        byte[] vinBytes = ByteBufUtil.getBytes(in.readBytes(17));
        VIN = new String(vinBytes, StandardCharsets.US_ASCII);
    }
}
