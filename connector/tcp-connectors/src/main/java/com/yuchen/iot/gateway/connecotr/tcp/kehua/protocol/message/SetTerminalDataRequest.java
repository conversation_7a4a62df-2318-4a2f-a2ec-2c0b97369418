package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.DataType;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.TerminalData;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.util.BCDUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.function.BiConsumer;

/**
 * @description: 设置终端数据
 * @author: zhonghx
 * @create: 2023-02-16 17:07
 **/

@Data
@AllArgsConstructor
public class SetTerminalDataRequest implements MessageEncoder{

    /*数据单元个数*/
    private byte dataCount;

    /*
     * name--value
     * */
    private HashMap<String, Object> dataMap;

    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(dataCount);
        dataMap.forEach((k,v)->{
            TerminalData data = TerminalData.keyOf(k);
            if (data == null){
                return;
            }
            DataType type = data.getType();
            buf.writeShort(data.getId());
            buf.writeByte(data.getLen());
            switch (type){
                case UINT_8:{
                    buf.writeByte((Integer) v);
                    break;
                }
                case UINT_16:{
                    buf.writeShort((Integer) v);
                    break;
                }
                case UINT_32:{
                    buf.writeInt((Integer) v);
                    break;
                }
                case BCD:{
                    buf.writeBytes(BCDUtil.strToBcd((String) v));
                    break;
                }
                case ASCII:{
                    String content = (String)v;
                    buf.writeBytes(Arrays.copyOf(content.getBytes(StandardCharsets.US_ASCII),data.getLen()));
                    break;
                }
                default:{
                    break;
                }
            }
        });
        return buf;
    }
}
