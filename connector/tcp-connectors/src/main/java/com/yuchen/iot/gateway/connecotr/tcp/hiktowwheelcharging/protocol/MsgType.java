package com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol;

import lombok.Getter;

public enum MsgType {


    /*----上行消息---*/
    deviceState(0x61,"deviceState"),              /*设备状态*/
    DeviceAlarm(0x62, "deviceAlarm"),              /*设备故障报警*/
    BeginChargingInfo(0x64, "beginChargingInfo"),              /*设备检测到充电，发送开始充电信息*/
    DeviceRegistr(0x65, "deviceRegistr"),              /*设备注册*/
    FreeModeChannle(0x66, "freeModeChannle"),              /*免费模式下，设备上传点击按钮通道信息*/
    MeterReading(0x6A, "meterReading"),              /*远程抄表，表电量返回*/
    UserSwipeCard(0x73, "userSwipeCard"),              /*用户刷卡，直接返回通道信息*/
    SIMInfo(0x6E, "simInfo"),    /*上报SIM卡信息*/











    /*----下行消息----*/
    StartCharging(0x81, "startCharging"),              /*用户选择充电通道,开始充电*/
    StopCharging(0x82, "stopCharging"),           /*关闭充电*/
    SettingMaxCurrent(0x91, "settingMaxCurrent"),           /*设置电流最大值*/
    DeviceRestart(0x92, "deviceRestart"),           /*设备远程重启*/
    SettingDeviceType(0x93, "settingDeviceType"),           /*设置设备类型*/
    SettingVolume(0x94, "settingVolume"),           /*设置音量*/
    ClosedVolume(0x95, "closedVolume"),           /*关闭声音*/
    OpenSmokeAlarm(0x96, "openSmokeAlarm"),           /*打开烟雾报警检测*/
    ClosedSmokeAlarm(0x97, "closedSmokeAlarm"),           /*关闭烟雾报警检测*/
    OpenPanelLights(0xA4, "openPanelLights"),           /*开面板灯*/
    ClosedPanelLights(0xA5, "closedPanelLights"),           /*关面板灯*/
    OpenUnplugPowerOff(0xA6, "openUnplugPowerOff"),           /*打开插拔断电*/
    ClosedUnplugPowerOff(0xA7, "closedUnplugPowerOff"),           /*关闭插拔断电*/
    OpenSoftwareOverCurrent(0xAB, "openSoftwareOverCurrent"),           /*打开软件过流检测*/
    ClosedSoftwareOverCurrent(0xAC, "closedSoftwareOverCurrent"),           /*关闭软件过流检测*/
    SettingDisconnectEnteringCount(0xAD, "settingDisconnectEnteringCount"),           /*设置断开检测参数*/
    SearchElectricMeter(0xB0, "searchElectricMeter"),           /*查询电表信息*/
    SettingTrickleTime(0xB3, "settingTrickleTime"),          /*设置涓流时间*/
    SettingDeviceTemperatureAlarm(0xB5, "settingDeviceTemperatureAlarm"),           /*设置设备温度报警值*/
    SettingCableTemperatureAlarm(0xB6, "settingCableTemperatureAlarm"),           /*设置电缆温度报警值*/
    OpenFullAutoStop(0xB7, "openFullAutoStop"),           /*打开充满自停*/
    ClosedFullAutoStop(0xB8, "closedFullAutoStop"),           /*关闭充满自停*/
    SettingEnteringTrickleCurrent(0xB9, "settingEnteringTrickleCurrent"),     /*设置进入涓流电流值*/
    SettingDisconnectEntering(0xBA, "settingDisconnectEntering"),          /*设置进入断开电流值*/
    OpenTemperatureAlarm(0xC1, "openTemperatureAlarm"),            /*关闭充满自停*/
    ClosedTemperatureAlarm(0xC2, "closedTemperatureAlarm"),           /*关闭充满自停*/
    GetSIMInfo(0xBD, "getSIMInfo"),              /*获取SIM卡号*/
    SwipeCardBeginCharging(0xC4, "swipeCardBeginCharging"),      /*刷卡回复开始充电*/
    SettingTemperatureAlarmTime(0xC3, "settingTemperatureAlarmTime");        /*关闭充满自停*/




    @Getter
    private final int type;
    @Getter
    private final String value;

    MsgType(int type, String value) {
        this.type = type;
        this.value = value;
    }

    public static MsgType findByType(int type) {
        for (MsgType msgType : MsgType.values()) {
            if (msgType.getType() == type) {
                return msgType;
            }
        }
        return null;
    }

    public static MsgType findByValue(String value) {
        for (MsgType msgType : MsgType.values()) {
            if (msgType.getValue().equals(value)) {
                return msgType;
            }
        }
        return null;
    }
}
