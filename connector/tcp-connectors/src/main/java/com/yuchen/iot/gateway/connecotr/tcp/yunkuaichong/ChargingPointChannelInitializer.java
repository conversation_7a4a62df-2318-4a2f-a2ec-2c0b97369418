package com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong;

import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.timeout.IdleStateHandler;

import java.util.concurrent.TimeUnit;

public class ChargingPointChannelInitializer extends ChannelInitializer<SocketChannel> {

    private ChargingPointHandler clientHandler;
    ChargingChannelListener chargingChannelListener;

    // TODO:需要绑定一个IO线程池来进行处理
    //private final EpollEventLoopGroup businessEventGroup = new EpollEventLoopGroup(10);

    public ChargingPointChannelInitializer(ChargingChannelListener chargingChannelListener) {
        this.chargingChannelListener = chargingChannelListener;
    }

    protected void initChannel(SocketChannel socketChannel) throws Exception {
        ChannelPipeline p = socketChannel.pipeline();
        p.addLast(new IdleStateHandler(180, 0, 0, TimeUnit.SECONDS));
        p.addLast(new ChargingPointHandler(chargingChannelListener));
        p.addLast(new ChargingPointEncoder());
        // TODO:需要绑定一个IO线程池来进行处理
        p.addLast(new ChargingPointDecoder());
    }
}
