package com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong;


import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.yuchen.iot.gateway.connecotr.tcp.AbstractTcpServerConnector;
import com.yuchen.iot.gateway.connecotr.tcp.JsonTcpConnectorMessage;
import com.yuchen.iot.gateway.connecotr.tcp.isapi.netty.ISAPIEvent;
import com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.ChargingPointProtocol;
import com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.MsgHeader;
import com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.MsgType;
import com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.message.*;
import com.yuchen.iot.gateway.connector.api.ConnectorInitParams;
import com.yuchen.iot.gateway.connector.api.WaitCallResponseCallback;
import com.yuchen.iot.gateway.connector.api.data.DownLinkData;
import com.yuchen.iot.gateway.connector.api.data.UpLinkData;
import com.yuchen.iot.gateway.connector.api.data.UpLinkMetaData;
import com.yuchen.iot.gateway.connector.api.message.ThingMessage;
import com.yuchen.iot.gateway.dao.integration.MetaConfig;
import io.netty.channel.Channel;
import io.netty.channel.ChannelInitializer;
import org.pf4j.Extension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.Executors;

@Extension
public class ChargingPointTcpConnector extends AbstractTcpServerConnector<JsonTcpConnectorMessage> {

    private static final Logger log = LoggerFactory.getLogger(ChargingPointTcpConnector.class);

    private final Map<String, Channel> channels = new HashMap<>();

    /**
     * 定义默认端口号
     */
    private int PORT = 2404;

    @Override
    public void init(ConnectorInitParams params) {
        super.init(params);
        ListeningExecutorService pool = MoreExecutors.listeningDecorator(Executors.newFixedThreadPool(2));
        // 获取自定义配置
        MetaConfig configuration = params.getConfiguration().getConfiguration();
        // 如果端口号不为空则继续执行
        if (configuration.containsKey("port")) {
            // 获取端口号
            PORT = (int) configuration.get("port");
            ListenableFuture<Boolean> future = pool.submit(this::startServer);
            future.addListener(this::startServer, pool);

        }
    }

    @Override
    protected ChannelInitializer<?> getChannelInitializer() {
        //return new ChargingPointChannelInitializer(/*new ChargingPointEncoder(), new ChargingPointDecoder()*/);
        return new ChargingPointChannelInitializer(new ChargingChannelListener<JsonTcpConnectorMessage>() {
            @Override
            public void onDisconnect(String chargingPointId) {
                getUpLinkDataListener(chargingPointId).onSouthDisconnect();
            }

            @Override
            public void onConnect(String chargingPointId) {
                getUpLinkDataListener(chargingPointId).onSouthConnect();
            }

            @Override
            public void onMessage(JsonTcpConnectorMessage event) {
                process(event);
            }
        });
    }

    /**
     * @see ISAPIEvent
     */
    protected void doProcess(JsonTcpConnectorMessage message) throws Exception {
        // TODO 需要过滤一遍消息，文件单独上传
        JsonNode json = mapper.readTree(message.getMessageInBytes());
        List<UpLinkData> upLinkDataList = this.convert(message);
        if (upLinkDataList != null && !upLinkDataList.isEmpty()) {
            for (UpLinkData upLinkData : upLinkDataList) {
                this.processUpLinkData(upLinkData);
                log.trace("[{}] Processing up link data", upLinkData);
            }
        }
    }

    protected List<UpLinkData> convert(JsonTcpConnectorMessage message) throws Exception {
        byte[] data = message.getMessageInBytes();
        Map<String, String> mdMap = new HashMap<>(this.metadataTemplate.getKvMap());
        return this.convertToUpLinkDataList(this.connectorContext.getCallbackExecutor(), data,
                new UpLinkMetaData(message.getContentType(), mdMap));
    }

    private Boolean startServer() {
        try {
            log.info("start hik charging server port : {}", PORT);
            // Make the connection attempt.
            this.bootstrap.bind(PORT).addListener(future -> {
                if (future.isSuccess()) {
                    // connect success
                    log.info("start hik charging server successfully port : {}", PORT);
                } else {
                    future.cause().printStackTrace();
                }
            }).sync().channel().closeFuture().sync();

        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

    @Override
    protected void doListenOffEndpoint(String deviceKey, Map<String, String> endpointMeta) {
        if (this.channels.containsKey(deviceKey)) {
            Channel channel = this.channels.get(deviceKey);
            if (channel != null) {
                channel.close().addListener(future -> {
                    if (future.isSuccess()) {
                        this.getUpLinkDataListener(deviceKey).onSouthDisconnect();
                    } else {
                        future.cause().printStackTrace();
                    }
                });
            }
        }
    }


    @Override
    public void onDownLinkMessage(ThingMessage message, WaitCallResponseCallback callback) {
        if (this.downLinkConverter != null) {
            this.processDownLinkMessage(message, callback);
        }
    }

    @Override
    protected void doListenOnEndpoint(String deviceKey, Map<String, String> endpointMeta) {

    }

    /**
     * 处理下行链路消息
     *
     * @param message  物模型消息
     * @param callback 回调
     */
    private void processDownLinkMessage(ThingMessage message, WaitCallResponseCallback callback) {
        Map<String, String> mdMap = new HashMap<>(this.metadataTemplate.getKvMap());
        try {
            List<DownLinkData> result = this.downLinkConverter
                    .convertDownLink(Collections.singletonList(message), mdMap);
            if (!result.isEmpty()) {
                DownLinkData downLink = result.get(0); // 设计上只支持单个指令下发
                if (!downLink.isEmpty()) {
                    Map<String, String> metadata = downLink.getMetadata();

                    String identifier = metadata.get("identifier");
                    JsonNode jsonNode = mapper.readTree(downLink.getData());
                    // 过滤 array 数组的问题。
                    if (null != jsonNode.get("array")) {
                        ObjectNode objectNode = jsonNode.deepCopy();
                        JsonNode arrayNode = mapper.readTree(objectNode.get("array").asText());
                        objectNode.set("array", arrayNode);
                        jsonNode = mapper.readTree(String.valueOf(objectNode));
                    }
                    ChargingPointProtocol obj = packetObjectFromJson(message.getIntegrateNo(), identifier, jsonNode);
                    ChargingPointChannelManager.sendMessageToSN(message.getIntegrateNo(),obj);

                    if (callback != null) {
                        // 存放请求事件的唯一标识，为了下发回调响应( 桩编号+下发命令 ， 根据判断额外会加入其他信息，例如 充电流水号)
                        String key = getCallbackKey(message.getIntegrateNo(), identifier, jsonNode);
                        callback.waitForResponse(key, downLink);
                        log.info("海康--平台下发指令---写入回调---key:{}", key);
                    }
                    log.info("海康--平台下发指令---" + message.getIntegrateNo() + "---" + identifier + "---" + JSONObject
                            .toJSONString(obj));
                }
            }
        } catch (Exception e) {
            log.warn("Failed to process downLink message", e);
        }
    }

    /**
     * 下行充电桩协议转换
     *
     * @param integrateNo 设备唯一编码
     * @param identifier  下行消息类别
     * @param json        下行数据
     * @return
     */
    public ChargingPointProtocol packetObjectFromJson(String integrateNo, String identifier, JsonNode json) {



        if (identifier.equals("getRateCliResponse")) {

                //*服务费单价，单位：元/kWh, 4位小数*//*
            int serviceChargePerkWh = json.get("serviceChargePerkWh").asInt();
                //*费率1单价*//*
            int rate1 = json.get("rate1").asInt();
                //*费率2单价*//*
            int rate2 = json.get("rate2").asInt();
                //*费率3单价*//*
            int rate3 = json.get("rate3").asInt();
                //*费率4单价*//*
            int rate4 = json.get("rate4").asInt();
                //*时段数量，至少1条，最多12条。时段必须按顺序排列，覆盖00：00 - 24：00*//*
            byte timeIntervalCount = (byte) json.get("timeIntervalCount").asInt();


            ArrayList<TimeIntervalInfo> timePeriodList = new ArrayList<>();
            JsonNode arrNode = json.get("array");
            if (arrNode.isArray()) {
                for (JsonNode objNode : arrNode) {
                    int index = objNode.get("index").asInt();
                    String startTime = objNode.get("startTime").asText();
                    TimeIntervalInfo timeIntervalInfo = new TimeIntervalInfo((byte) index, startTime);
                    timePeriodList.add(timeIntervalInfo);
                }
            }


            int[] indexForEachPeriod = new int[48];
            for (int i = 0; i < 48; i++) {
                indexForEachPeriod[i] = -1;
            }
/*            // 遍历时间区间列表，标记每个时段对应的index
            for (TimePeriod period : timePeriodList) {
                int startHour = Integer.parseInt(period.startTime.substring(0, 2));
                int startMinute = Integer.parseInt(period.startTime.substring(2));
                int periodIndex = startHour * 2 + (startMinute > 0? 1 : 0);
                indexForEachPeriod[periodIndex] = period.index;
            }*/
            for (int i = 0; i < timePeriodList.size(); i++) {
                TimeIntervalInfo currentPeriod = timePeriodList.get(i);
                int startHour = Integer.parseInt(currentPeriod.getStartTime().substring(0, 2));
                int startMinute = Integer.parseInt(currentPeriod.getStartTime().substring(2));
                int periodIndex = startHour * 2 + (startMinute > 0? 1 : 0);
                if (i < timePeriodList.size() - 1) {
                    TimeIntervalInfo nextPeriod = timePeriodList.get(i + 1);
                    int endHour = Integer.parseInt(nextPeriod.getStartTime().substring(0, 2));
                    int endMinute = Integer.parseInt(nextPeriod.getStartTime().substring(2));
                    int endPeriodIndex = endHour * 2 + (endMinute > 0? 1 : 0);
                    for (int j = periodIndex; j < endPeriodIndex; j++) {
                        indexForEachPeriod[j] = currentPeriod.getIndex();
                    }
                } else {
                    // 处理最后一个时间段到24:00的情况
                    for (int k = periodIndex; k < 48; k++) {
                        indexForEachPeriod[k] = currentPeriod.getIndex();
                    }
                }
            }
            byte[] byteArray2 = new byte[48];
            // 输出每个时段对应的index（这里简单打印，可根据需求调整输出格式等）
            for (int i = 0; i < 48; i++) {
                switch (indexForEachPeriod[i]){
                    case 1:
                        byteArray2[i] = 0x00;
                        break;
                    case 2:
                        byteArray2[i] = 0x01;
                        break;
                    case 3:
                        byteArray2[i] = 0x02;
                        break;
                    case 4:
                        byteArray2[i] = 0x03;
                        break;
                    default:
                        byteArray2[i] = 0x00;}
           //     byteArray2[i] = (byte) indexForEachPeriod[i];
                System.out.println("时段 " + i + " 对应的index: " + indexForEachPeriod[i]);
            }

         //  String devNo="18721781700000";

            ChargingPointProtocol<ServerSetRateRequestMessage> chargingPointProtocol =
                    new ChargingPointProtocol<>();
            ServerSetRateRequestMessage serverSetRateRequestMessage = new ServerSetRateRequestMessage(
                    integrateNo,"0100",rate1,serviceChargePerkWh,rate2,serviceChargePerkWh,rate3,serviceChargePerkWh,rate4,serviceChargePerkWh,0,byteArray2  );
            chargingPointProtocol.setBody(serverSetRateRequestMessage);

            MsgHeader header = new MsgHeader();
            header.setCmd(0x0A);


            chargingPointProtocol.setHeader(header);
            return chargingPointProtocol;
        }

        // 添加固定的消息头
       /* MsgHeader header = new MsgHeader();
        header.setPreamble(0x68);
        header.setTime((int) (System.currentTimeMillis() / 1000));
        header.setAddress(integrateNo);
        header.setTerminator(0x16);

        try {
            *//*平台发起，复位终端*//*
            if (identifier.equals("reset")) {

                ChargingPointProtocol<ResetRequestMessage> chargingPointProtocol = new ChargingPointProtocol<>();
                ResetRequestMessage resetRequestMessage = new ResetRequestMessage((byte) json.get("type").asInt());
                chargingPointProtocol.setBody(resetRequestMessage);

                header.setCmd(MsgType.SER_RESET.getType());
                header.setDirection(1);
                header.setSubDirection(1);
                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }

            *//*平台发起，查询终端软硬件版本*//*
            if (identifier.equals("getFirmwareInfo")) {

                ChargingPointProtocol<FirmwareRequestMessage> chargingPointProtocol = new ChargingPointProtocol<>();
                FirmwareRequestMessage firmwareRequestMessage = new FirmwareRequestMessage(
                        (byte) json.get("type").asInt());
                chargingPointProtocol.setBody(firmwareRequestMessage);

                header.setCmd(MsgType.SER_FIRMWARE.getType());
                header.setDirection(1);
                header.setSubDirection(1);
                chargingPointProtocol.setHeader(header);

                return chargingPointProtocol;
            }

            *//*平台发起，设置二维码格式*//*
            if (identifier.equals("setQRFormat")) {

                ChargingPointProtocol<QRCodeRequestMessage> chargingPointProtocol = new ChargingPointProtocol<>();
                QRCodeRequestMessage qrCodeRequestMessage = new QRCodeRequestMessage(json.get("qrcode").asText());
                chargingPointProtocol.setBody(qrCodeRequestMessage);

                header.setCmd(MsgType.SER_SET_QR_FORMAT.getType());
                header.setDirection(1);
                header.setSubDirection(1);
                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }

            *//*平台发起，设置集中器*//*
            if (identifier.equals("setConcentrator")) {


                ArrayList<PointInfo> array = new ArrayList<>();
                JsonNode arrNode = json.get("array");
                if (arrNode.isArray()) {
                    for (JsonNode objNode : arrNode) {
                        PointInfo pointInfo = mapper.convertValue(objNode, PointInfo.class);
                        array.add(pointInfo);
                    }
                }

                ChargingPointProtocol<ConcentratorSetRequestMessage> chargingPointProtocol =
                        new ChargingPointProtocol<>();
                ConcentratorSetRequestMessage concentratorSetRequestMessage = new ConcentratorSetRequestMessage(
                        (byte) json.get("count").asInt(), array);
                chargingPointProtocol.setBody(concentratorSetRequestMessage);

                header.setCmd(MsgType.SER_SET_CONCENTRATOR.getType());
                header.setDirection(1);
                header.setSubDirection(1);

                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }


            *//*设置终端费率*//*
            if (identifier.equals("setRate")) {

                *//*服务费单价，单位：元/kWh, 4位小数*//*
                int serviceChargePerkWh = json.get("serviceChargePerkWh").asInt();
                *//*费率1单价*//*
                int rate1 = json.get("rate1").asInt();
                *//*费率2单价*//*
                int rate2 = json.get("rate2").asInt();
                *//*费率3单价*//*
                int rate3 = json.get("rate3").asInt();
                *//*费率4单价*//*
                int rate4 = json.get("rate4").asInt();
                *//*时段数量，至少1条，最多12条。时段必须按顺序排列，覆盖00：00 - 24：00*//*
                byte timeIntervalCount = (byte) json.get("timeIntervalCount").asInt();


                ArrayList<TimeIntervalInfo> array = new ArrayList<>();
                JsonNode arrNode = json.get("array");
                if (arrNode.isArray()) {
                    for (JsonNode objNode : arrNode) {
                        int index = objNode.get("index").asInt();
                        String startTime = objNode.get("startTime").asText();
                        TimeIntervalInfo timeIntervalInfo = new TimeIntervalInfo((byte) index, startTime);
                        array.add(timeIntervalInfo);
                    }
                }

                ChargingPointProtocol<ServerSetRateRequestMessage> chargingPointProtocol =
                        new ChargingPointProtocol<>();
                ServerSetRateRequestMessage serverSetRateRequestMessage = new ServerSetRateRequestMessage(
                        serviceChargePerkWh, rate1, rate2, rate3, rate4, timeIntervalCount, array);
                chargingPointProtocol.setBody(serverSetRateRequestMessage);

                header.setCmd(MsgType.SER_SET_RATE.getType());
                header.setDirection(1);
                header.setSubDirection(1);

                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }

            *//*获取费率*//*
            if (identifier.equals("getRate")) {

                ChargingPointProtocol<ServerGetRateRequestMessage> chargingPointProtocol =
                        new ChargingPointProtocol<>();
                chargingPointProtocol.setBody(new ServerGetRateRequestMessage());

                header.setCmd(MsgType.SER_GET_RATE.getType());
                header.setDirection(1);
                header.setSubDirection(1);
                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }


            *//*平台发起，平台开始充电*//*
            if (identifier.equals("startCharging")) {

                *//*枪号 1-n*//*
                byte gunNo = (byte) json.get("gunNo").asInt();

                *//*充电流水号BCD*//*
                String serialNumber = json.get("serialNumber").asText();

                *//*充电方式， 0x01按金额（元）， 0x02按电量（度），0x03按时间（分钟）*//*
                byte chargingType = (byte) json.get("chargingType").asInt();

                *//*充电方式附带值， 按金额：充电金额，单位元，2位小数
                 *               按电量，充电电量，单位kw/h
                 *               按时间，充电时间，单位分*//*
                short chargingValue = (short) json.get("chargingValue").asInt();

                *//*普通用户  2集团用户*//*
                byte cardType = (byte) json.get("cardType").asInt();

                *//*用户来源*//*
                byte from = (byte) json.get("from").asInt();

                *//*账户余额，单位元，2位小数*//*
                int accountBalances = json.get("accountBalances").asInt();

                ChargingPointProtocol<ServerStartChargingRequestMessage> chargingPointProtocol =
                        new ChargingPointProtocol<>();
                ServerStartChargingRequestMessage serverStartChargingRequestMessage =
                        new ServerStartChargingRequestMessage(
                                gunNo,
                                serialNumber,
                                chargingType,
                                chargingValue,
                                cardType,
                                from,
                                accountBalances);
                chargingPointProtocol.setBody(serverStartChargingRequestMessage);

                header.setCmd(MsgType.SERVER_START_CHARGING.getType());
                header.setDirection(1);
                header.setSubDirection(1);
                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }


            *//*平台发起，停止充电*//*
            if (identifier.equals("stopCharging")) {

                *//*枪号 1-n*//*
                byte gunNo = (byte) json.get("gunNo").asInt();

                byte stopReason = (byte) json.get("stopReason").asInt();

                *//*充电流水号BCD*//*
                String serialNumber = json.get("serialNumber").asText();

                ChargingPointProtocol<ServerStopChargingRequestMessage> chargingPointProtocol =
                        new ChargingPointProtocol<>();
                ServerStopChargingRequestMessage serverStopChargingRequestMessage =
                        new ServerStopChargingRequestMessage(
                                gunNo, stopReason, serialNumber);
                chargingPointProtocol.setBody(serverStopChargingRequestMessage);

                header.setCmd(MsgType.SERVER_STOP_CHARGING.getType());
                header.setDirection(1);
                header.setSubDirection(1);
                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }


            *//************************************************************************************************************//*
            *//*以下是服务端使用服务调用的方式响应客户端的请求，*//*
            *//*平台响应，响应客户端查询费率*//*
            if (identifier.equals("getRateCliResponse")) {

                *//*服务费单价，单位：元/kWh, 4位小数*//*
                int serviceChargePerkWh = json.get("serviceChargePerkWh").asInt();
                *//*费率1单价*//*
                int rate1 = json.get("rate1").asInt();
                *//*费率2单价*//*
                int rate2 = json.get("rate2").asInt();
                *//*费率3单价*//*
                int rate3 = json.get("rate3").asInt();
                *//*费率4单价*//*
                int rate4 = json.get("rate4").asInt();
                *//*时段数量，至少1条，最多12条。时段必须按顺序排列，覆盖00：00 - 24：00*//*
                byte timeIntervalCount = (byte) json.get("timeIntervalCount").asInt();


                ArrayList<TimeIntervalInfo> array = new ArrayList<>();
                JsonNode arrNode = json.get("array");
                if (arrNode.isArray()) {
                    for (JsonNode objNode : arrNode) {
                        int index = objNode.get("index").asInt();
                        String startTime = objNode.get("startTime").asText();
                        TimeIntervalInfo timeIntervalInfo = new TimeIntervalInfo((byte) index, startTime);
                        array.add(timeIntervalInfo);
                    }
                }

                ChargingPointProtocol<ServerSetRateRequestMessage> chargingPointProtocol =
                        new ChargingPointProtocol<>();
                ServerSetRateRequestMessage serverSetRateRequestMessage = new ServerSetRateRequestMessage(
                        serviceChargePerkWh, rate1, rate2, rate3, rate4, timeIntervalCount, array);
                chargingPointProtocol.setBody(serverSetRateRequestMessage);

                header.setCmd(MsgType.CLI_RATE_REQ.getType());
                header.setDirection(1);
                header.setSubDirection(0);

                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }


            *//*刷卡鉴权响应*//*
            if (identifier.equals("cardAuthResponse")) {
                *//*枪号1-n*//*
                byte gunNo = (byte) json.get("gunNo").asInt();
                *//*16个字节，用户卡号*//*
                String cardNo = json.get("cardNo").asText();
                *//*账户余额，单元元，2位小数*//*
                float accountBalances = (float) json.get("accountBalances").asDouble();
                *//*用户类型 1普通用户 2集团用户*//*
                byte cardType = (byte) json.get("cardType").asInt();
                *//*用户来源 0x00*//*
                byte from = (byte) json.get("from").asInt();

                *//*鉴权结果*
                 * 0x0  正确
                 * 0x01 无效卡号
                 * 0x03 余额不足
                 * 0x04 卡锁住
                 * 0x05 无充电权限
                 * 0x07 无效卡
                 * 0x11 套餐余额不足
                 * 0x15 无效车状态
                 * 0x16 无效账户状态
                 * 0x17 密码错误
                 * 0x90 有未支付订单
                 * 0x92 有正在充电订单
                 * 0x99 系统错误
                 *//*
                byte authResult = (byte) json.get("authResult").asInt();

                ChargingPointProtocol<CardAuthResponseMessage> chargingPointProtocol = new ChargingPointProtocol<>();
                CardAuthResponseMessage cardAuthResponseMessage = new CardAuthResponseMessage(gunNo, cardNo,
                        accountBalances, cardType, from, authResult);
                chargingPointProtocol.setBody(cardAuthResponseMessage);

                header.setCmd(MsgType.CLI_CARD_AUTH.getType());
                header.setDirection(1);
                header.setSubDirection(0);

                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }

            *//*终端启动充电响应*//*
            if (identifier.equals("startChargingCliResponse")) {
                *//*枪号1-n*//*
                byte gunNo = (byte) json.get("gunNo").asInt();
                *//*16个字节，用户卡号*//*
                String cardNo = json.get("cardNo").asText();
                *//*充电流水号 8个字节BCD，由系统生成的唯一性的充电流水号，返回FFFFFFFFFFFFFFFFFF表示启动失败*//*
                String serialNumber = json.get("serialNumber").asText();

                ChargingPointProtocol<CardStartChargingResponseMessage> chargingPointProtocol =
                        new ChargingPointProtocol<>();
                CardStartChargingResponseMessage cardStartChargingResponseMessage =
                        new CardStartChargingResponseMessage(
                                gunNo, cardNo, serialNumber);
                chargingPointProtocol.setBody(cardStartChargingResponseMessage);

                header.setCmd(MsgType.CLI_START_CHARGING.getType());
                header.setDirection(1);
                header.setSubDirection(0);

                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }

            *//*终端停止充电响应*//*
            if (identifier.equals("stopChargingCliResponse") || identifier.equals("stopChargingNewCliResponse")) {
                *//*枪号1-n*//*
                byte gunNo = (byte) json.get("gunNo").asInt();
                *//*充电流水号 8个字节BCD，由系统生成的唯一性的充电流水号，返回FFFFFFFFFFFFFFFFFF表示启动失败*//*
                String serialNumber = json.get("serialNumber").asText();

                ChargingPointProtocol<ClientStopChargingResponseMessage> chargingPointProtocol =
                        new ChargingPointProtocol<>();
                ClientStopChargingResponseMessage clientStopChargingResponseMessage =
                        new ClientStopChargingResponseMessage(
                                gunNo, serialNumber);
                chargingPointProtocol.setBody(clientStopChargingResponseMessage);

                if (identifier.equals("stopChargingCliResponse")) {
                    header.setCmd(MsgType.CLI_STOP_CHARGING.getType());
                }
                if (identifier.equals("stopChargingNewCliResponse")) {
                    header.setCmd(MsgType.CLI_STOP_CHARGING_NEW.getType());
                }
                header.setDirection(1);
                header.setSubDirection(0);

                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }

            *//*主站应答VIN码鉴权结果*//*
            if (identifier.equals("VinAuthResponse")) {
                // 枪号
                byte gunNo = (byte) json.get("gunNo").asInt();
                // 车辆vin码
                String Vin = json.get("VIN").asText();
                // 账户余额，单元元，2位小数
                float accountBalances = (float) json.get("accountBalances").asDouble();
                // 用户类型
                byte userType = (byte) json.get("userType").asInt();
                // 用户来源
                byte from = (byte) json.get("from").asInt();
                // 鉴权结果
                byte authResult = (byte) json.get("authResult").asInt();

                ChargingPointProtocol<VinAuthResponseMessage> chargingPointProtocol = new ChargingPointProtocol<>();
                VinAuthResponseMessage vinAuthResponseMessage = new VinAuthResponseMessage(gunNo, Vin, accountBalances,
                        userType, from
                        , authResult);
                chargingPointProtocol.setBody(vinAuthResponseMessage);

                header.setCmd(MsgType.VIN_AUTH.getType());
                header.setDirection(1);
                header.setSubDirection(0);

                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }

            // 主站应答VIN码启动充电
            if (identifier.equals("VinStartChargingResponse")) {
                // 枪号
                byte gunNo = (byte) json.get("gunNo").asInt();
                // 车辆vin码
                String Vin = json.get("VIN").asText();
                *//*充电流水号 8个字节BCD，由系统生成的唯一性的充电流水号，返回FFFFFFFFFFFFFFFFFF表示启动失败*//*
                String serialNumber = json.get("serialNumber").asText();

                ChargingPointProtocol<VinStartChargingResponseMessage> chargingPointProtocol =
                        new ChargingPointProtocol<>();
                VinStartChargingResponseMessage response = new VinStartChargingResponseMessage(gunNo, Vin,
                        serialNumber);
                chargingPointProtocol.setBody(response);

                header.setCmd(MsgType.VIN_START_CHARGING.getType());
                header.setDirection(1);
                header.setSubDirection(0);

                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }
        } catch (Exception e) {
            log.error(e.toString());
        }*/
        return null;
    }

    @Override
    public void destroy() {

    }

    /**
     * 获取 下发命令的 回调 key
     *
     * @param integrateNo 桩编号
     * @param identifier  下发命令
     * @param jsonNode    下发内容
     * @return
     */
    private String getCallbackKey(String integrateNo, String identifier, JsonNode jsonNode) {
        String callBackKey = integrateNo.concat(identifier);
        switch (identifier) {
            case "startCharging":
                // 启动充电(增加充电流水号)
            case "stopCharging": {
                // 停止充电(增加充电流水号)
                if (null == jsonNode.get("serialNumber")) {
                    break;
                }
                String serialNumber = jsonNode.get("serialNumber").asText();
                callBackKey = callBackKey.concat(serialNumber);
                break;
            }
            default:
        }
        return callBackKey;
    }
}
