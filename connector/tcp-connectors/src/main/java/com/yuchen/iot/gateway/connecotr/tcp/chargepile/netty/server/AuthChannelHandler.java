package com.yuchen.iot.gateway.connecotr.tcp.chargepile.netty.server;

import com.alibaba.fastjson.JSON;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.enums.ChargeCommandCode;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.message.request.AuthOARequest;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.message.request.DataPackRequest;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.message.response.DataPackResponse;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.util.*;
import io.netty.channel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * netty服务认证处理类
 * <AUTHOR>
 * @date 2022/12/07 15:29
 */
@Component("authChannelHandler")
@ChannelHandler.Sharable
public class AuthChannelHandler extends ChannelDuplexHandler {

	private Logger logger = LoggerFactory.getLogger(this.getClass());
	public static final Map<Integer,String> authVerifyCodeMap =new ConcurrentHashMap<>();

	@Override
	public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
		logger.info("authChannelHandler-----channelRead"+msg);
		DataPackRequest dataPack = (DataPackRequest) msg;
		//OA认证发送OA命令
		authVerify(dataPack);
		//校验体和桩发过来的没对上,重新发送OB命令给桩
		Boolean success = sendOBCmd(dataPack);
		if(!success){
			logger.warn("md5解密的校验体与服务端保存的校验体不一致");
			return;
		}
	}

	@Override
	public void write(ChannelHandlerContext ctx, Object msg, ChannelPromise promise) throws Exception {
		logger.info("authChannelHandler-----write"+msg);
		super.write(ctx, msg, promise);
	}


	public boolean sendOBCmd(DataPackRequest dataPack) throws Exception {
		boolean authSuccess = false;
		if (Objects.equals(dataPack.getPackHeader().getCmd(), ChargeCommandCode.PILE_AUTH_OB)) {
			//MD5解密
			String md5Code = MD5Util.convertMD5(MD5Util.convertMD5(dataPack.getData().toString()));
			int pileNo = dataPack.getPackHeader().getPileNo();
			//比对校验体
			String hexCode = authVerifyCodeMap.get(pileNo);
			if (Objects.equals(hexCode, md5Code)) {
				authSendMessage(0, pileNo);
				authSuccess = true;
			} else {
				authSendMessage(1, pileNo);
				authSuccess = false;
			}
		}
		return authSuccess;
	}

	public void authSendMessage(int status,int pileNo) throws Exception{
		String authStatus  = String.format("%02d",status);
		String dateCode = JdkDateUtil.codeDate()+"+";
		int randomInt = RandomUtil.random4Int();
		String codeString = String.format("%s%s%d",authStatus,dateCode,randomInt);
		//生成一个16进制码
		String successHexCode = generateHex(codeString);
		DataPackResponse s2C = new DataPackResponse();
		s2C.setCmd(ChargeCommandCode.PILE_AUTH_OB.getCode());
		s2C.setData(successHexCode);
		Channel channel = ChannelMapByEntityUtil.getChannel(String.valueOf(pileNo));
		//平台认证发送OA命令
		channel.writeAndFlush(s2C);
	}
	public void authVerify(DataPackRequest dataPack) throws Exception {
		if (Objects.equals(dataPack.getPackHeader().getCmd(), ChargeCommandCode.PILE_AUTH_OA)) {
			AuthOARequest authRequest = JSON.parseObject(dataPack.decode().toString(), AuthOARequest.class);
			if (Objects.equals(authRequest.getContent(), 0x00)) {
			int pileNo = dataPack.getPackHeader().getPileNo();
			byte gunNo = dataPack.getPackHeader().getGunNo();
			//生成校验码
			String dateCode = JdkDateUtil.codeDate() + "+";
			String qrCode = pileNo + "+" + String.format("%02d", gunNo) + "+";
			int randomInt = RandomUtil.random4Int();
			String codeString = String.format("%s%s%d", dateCode, qrCode, randomInt);
			//生成一个16进制码
			String hexCode = generateHex(codeString);
			//存储校验体
			authVerifyCodeMap.put(pileNo, hexCode);
			DataPackResponse dataPackResponse = new DataPackResponse();
			dataPackResponse.setCmd(ChargeCommandCode.PILE_AUTH_OA.getCode());
			dataPackResponse.setData(hexCode);
			Channel channel = ChannelMapByEntityUtil.getChannel(String.valueOf(pileNo));
			//平台认证发送OA命令
			channel.writeAndFlush(dataPackResponse);
			}
		}
	}

	public String generateHex(String hexCode) {
		String[] ss = hexCode.split("\\+");
		String hexStr = "";
		for (String s1 : ss) {
			String aa = BytesUtil.hexToInt(Long.parseLong(s1)).toUpperCase();
			if (aa.length() % 2 == 1) {
				aa = '0' + aa;
			}
			hexStr += aa;
		}
		return hexStr;
	}
}
