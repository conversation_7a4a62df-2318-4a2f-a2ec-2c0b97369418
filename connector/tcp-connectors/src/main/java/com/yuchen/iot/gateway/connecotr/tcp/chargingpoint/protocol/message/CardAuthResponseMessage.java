package com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.protocol.message;

import com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.BCDUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @description: 主站刷卡鉴权响应
 * @author: zhonghx
 * @create: 2022-12-09 11:10
 **/
@Data
@AllArgsConstructor
public class CardAuthResponseMessage implements MessageEncoder{


    /*枪号1-n*/
    private byte gunNo;

    /*16个字节，用户卡号*/
    private String cardNo;

    /*账户余额，单元元，2位小数*/
    private float accountBalances;

    /*用户类型 1普通用户 2集团用户*/
    private byte cardType;

    /*用户来源 0x00*/
    private byte from;

    /*鉴权结果*
    * 0x0  正确
    * 0x01 无效卡号
    * 0x03 余额不足
    * 0x04 卡锁住
    * 0x05 无充电权限
    * 0x07 无效卡
    * 0x11 套餐余额不足
    * 0x15 无效车状态
    * 0x16 无效账户状态
    * 0x17 密码错误
    * 0x90 有未支付订单
    * 0x92 有正在充电订单
    * 0x99 系统错误
     */
    private byte authResult;


    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(gunNo);
        buf.writeBytes(BCDUtil.strToBcd(cardNo));
        buf.writeFloatLE(accountBalances);
        buf.writeByte(cardType);
        buf.writeByte(from);
        buf.writeByte(authResult);
        return buf;
    }
}
