package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import com.yuchen.iot.gateway.connecotr.tcp.kehua.util.BCDUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @description: 充电桩校时指令，平台下发
 * @author: zhonghx
 * @create: 2023-02-15 14:19
 **/
@Data
@AllArgsConstructor
public class TimingRequest implements MessageEncoder{

    /*当前时间 7字节BCD
    * YY:MM:DD:HH:MM:SS
    * */
    private String currentTime;

    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeBytes(BCDUtil.strToBcd(currentTime));
        return buf;
    }
}
