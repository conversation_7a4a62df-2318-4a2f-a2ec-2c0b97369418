package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol;

import lombok.Getter;


/**

终端设备上传登录帧 01
4B 48 00 3A 01 96 7E 72 05 35 36 31 35 31 30 30 30 35 34 39 30 4A 35 43 30 30 30 30 31
00 01 00 00 00 02 FF FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF FB 5C 68
云平台下发登录确认帧 81
4B 48 00 2D 01 96 7E 72 05 35 36 31 35 31 30 30 30 35 34 39 30 4A 35 43 30 30 30 30 31
00 81 00 00 00 01 20 20 03 05 10 19 52 73 63 68
云平台下发读取终端数据帧 03
4B 48 00 56 01 96 7E 72 05 35 36 31 35 31 30 30 30 35 34 39 30 4A 35 43 30 30 30 30 31 00
03 00 00 00 18 00 01 00 02 00 03 00 04 00 05 00 06 00 07 00 08 00 0F 00 14 00 15 00 16 00
17 00 1D 00 1E 00 1F 00 20 00 21 00 22 00 23 00 24 00 25 00 37 00 38 97 34 68
云平台下发对时数据 1B
专用于钰辰监控软件开发，严禁外传第三方
6
4B 48 00 2C 01 96 04 34 05 35 36 31 35 31 30 30 30 35 34 39 30 4A 35 43 30 30 30 30 31
00 1B 00 00 00 20 20 03 05 10 19 52 3D 1E 68
终端主动上传数据 13
4B 48 00 2F 01 96 7E 74 05 35 36 31 35 31 30 30 30 35 34 39 30 4A 35 43 30 30 30 30 31
00 13 00 00 01 78 02 10 00 01 04 10 01 01 01 CA DE 68
主动上传数据确认 93
4B 48 00 27 01 96 7E 74 05 35 36 31 35 31 30 30 30 35 34 39 30 4A 35 43 30 30 30 30 31 00
93 00 00 01 78 01 D0 A9 68
终端设备上传终端数据 83
4B 48 01 01 01 96 7E 72 05 35 36 31 35 31 30 30 30 35 34 39 30 4A 35 43 30 30 30 30 31 00
83 00 00 00 01 18 00 01 01 05 00 02 14 35 36 31 35 31 30 30 30 35 34 39 30 4A 35 43 30 30
30 30 31 00 03 04 00 00 00 00 00 04 02 00 1E 00 05 0A 56 34 2E 31 30 41 2E 54 31 34 00
06 0A 56 34 2E 30 30 00 00 00 00 00 00 07 1E 77 77 77 2E 6B 68 7A 7A 63 64 2E 63 6F 6D
00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 08 02 1F 50 00 0F 07 20 20 03 05 10 19
52 00 14 01 00 00 15 01 00 00 16 01 00 00 17 02 00 00 00 1D 02 13 88 00 1E 02 09 60 00 1F
02 13 88 00 20 02 09 C4 00 21 02 07 D0 00 22 02 09 60 00 23 01 01 00 24 01 00 00 25 01 08
00 37 14 30 30 30 30 30 30 30 30 30 30 30 30 30 30 30 30 30 30 30 31 00 38 14 30 30 30 30
30 30 30 30 30 30 30 30 30 30 30 30 30 30 30 31 9B C5 68
终端上传对时结果 9B
4B 48 00 26 01 96 7A FE 05 35 36 31 35 31 30 30 30 35 34 39 30 4A 35 43 30 30 30 30 31
00 9B 00 00 00 01 71 72 68
终端设备上传心跳帧 02
4B 48 00 28 01 96 7E 75 05 35 36 31 35 31 30 30 30 35 34 39 30 4A 35 43 30 30 30 30 31 00
02 00 00 00 38 4B FF 04 E5 68
云平台下发心跳确认帧 82
4B 48 00 26 01 96 7E 75 05 35 36 31 35 31 30 30 30 35 34 39 30 4A 35 43 30 30 30 30 31 00
82 00 00 00 01 75 01 68
终端设备上传实时数据 0D（无需回复 8D）
4B 48 00 BF 01 96 7E 73 05 35 36 31 35 31 30 30 30 35 34 39 30 4A 35 43 30 30 30 30 31
00 0D 00 00 01 18 10 03 04 00 24 53 79 10 04 04 00 00 0E D8 10 05 04 00 00 10 48 10 06
04 00 00 05 6F 10 07 04 00 00 05 7E 10 08 04 00 02 88 14 10 09 04 00 00 EC 2A 10 0A 04
00 00 00 24 10 0B 04 00 CF 89 C1 10 0C 01 55 10 0D 02 0F 2C 10 0E 02 01 AE 10 0F 04 00
00 0E D8 10 10 04 00 00 0E D8 10 11 04 00 00 0E D8 10 12 04 00 00 09 E5 10 13 04 00 00
09 E5 10 14 04 00 00 09 E5 10 15 04 00 19 70 80 10 16 04 00 00 00 00 10 17 02 01 53 10 18
02 01 56 10 19 02 00 E6 10 1A 02 00 00 E3 D5 68
终端设备上传历史充电记录 0E
专用于钰辰监控软件开发，严禁外传第三方
7
4B 48 00 D8 01 96 7E 7E 05 35 36 31 35 31 30 30 30 35 34 39 30 4A 35 43 30 30 30 30 31
00 0E 00 00 01 01 01 05 34 35 34 33 33 34 37 35 30 39 37 38 34 34 34 33 00 00 00 00 FF
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF 00 00 0F C9 0E FB 27 E4 00 00 05 B4 00 01 A4
1E 00 00 05 9A 00 CF 84 43 00 CF 89 DD 41 55 01 43 00 00 00 00 00 00 00 00 00 00 00 00
00 00 04 20 20 03 05 09 56 41 20 20 03 05 10 21 01 01 9D 00 49 00 24 53 79 5E 60 CC B7
00 00 F0 DE 00 02 94 FC 59 43 30 33 00 00 00 00 00 00 00 00 00 00 00 00 05 9A 00 00 00
00 00 00 00 00 00 00 00 00 00 01 A4 1E 00 00 00 00 00 00 00 00 00 00 00 00 00 00 F0 DE
00 00 00 00 00 00 00 00 A8 74 68
云平台下发充电记录确认帧 8E
4B 48 00 3F 01 96 7E 7E 05 35 36 31 35 31 30 30 30 35 34 39 30 4A 35 43 30 30 30 30 31
00 8E 00 00 01 01 34 35 34 33 33 34 37 35 30 39 37 38 34 34 34 33 00 00 00 00 00 24 53 79
01 92 64 68
云平台下发启动充电帧 0A
4B 48 00 4A 01 92 09 6D 05 35 36 31 35 31 30 30 31 30 31 39 30 4B 41 46 30 30 30 30 32
00 0A 00 00 02 01 00 02 00 00 04 0E 38 36 37 30 35 38 32 36 35 39 31 30 35 31 31 33 00 00
00 00 00 24 B7 51 00 00 04 0E 1A 1A 99 CB 68
终端设备上传启动充电通知确认帧 8A
4B 48 00 3F 01 96 0C 5A 05 35 36 31 35 31 30 30 31 30 31 39 30 4B 41 46 30 30 30 30 32
00 8A 00 00 02 38 36 37 30 35 38 32 36 35 39 31 30 35 31 31 33 00 00 00 00 01 00 24 B7 51
01 9D BE 68
云平台下发停止充电帧 0A
4B 48 00 48 01 92 09 D9 05 35 36 31 35 31 30 30 31 30 31 39 30 4B 41 46 30 30 30 30 32
00 0A 00 00 02 02 00 02 00 00 04 0E 38 36 37 30 35 38 32 36 35 39 31 30 35 31 31 33 00 00
00 00 00 24 B7 51 00 00 04 0E 5B B9 68
终端设备上传停止充电通知确认帧 8A
4B 48 00 3F 01 96 0C CD 05 35 36 31 35 31 30 30 31 30 31 39 30 4B 41 46 30 30 30 30 32
00 8A 00 00 02 38 36 37 30 35 38 32 36 35 39 31 30 35 31 31 33 00 00 00 00 02 00 24 B7 51
01 03 2D 68
终端设备上传启停充电结果帧 0B
4B 48 00 3F 01 96 7E 7D 05 35 36 31 35 31 30 30 30 35 34 39 30 4A 35 43 30 30 30 30 31
00 0B 00 00 01 02 34 35 34 33 33 34 37 35 30 39 37 38 34 34 34 33 00 00 00 00 00 24 53 79
00 EC E3 68
监控平回复结果确认帧 8B
4B 48 00 3D 01 96 7E 7D 05 35 36 31 35 31 30 30 30 35 34 39 30 4A 35 43 30 30 30 30 31
00 8B 00 00 01 34 35 34 33 33 34 37 35 30 39 37 38 34 34 34 33 00 00 00 00 00 24 53 79 3F
46 68
终端设备上传刷卡请求帧 0C
专用于钰辰监控软件开发，严禁外传第三方
8
4B 48 00 6B 01 96 0A C1 05 35 36 31 35 31 30 30 30 38 33 39 30 4B 35 46 30 30 30 30 34
00 0C 00 00 01 04 30 30 30 30 30 31 31 39 30 33 30 37 34 30 35 39 00 00 00 00 4B 45 48 55
41 30 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 3C B5
D9 BA 00 00 00 00 00 00 00 00 00 00 00 00 01 0C C7 68
云平台下发刷卡请求确认帧 8C
4B 48 00 45 01 96 0A C1 05 35 36 31 35 31 30 30 30 38 33 39 30 4B 35 46 30 30 30 30 34
00 8C 00 00 01 30 30 30 30 30 31 31 39 30 33 30 37 34 30 35 39 00 00 00 00 04 00 01 ED
D3 01 00 24 53 40 1A D4 18 2D 68
终端设备上传历史告警数据 0F
4B 48 00 35 01 96 6C E1 05 35 36 31 35 31 30 30 30 35 34 39 30 4A 35 43 30 30 30 30 31
00 0F 00 00 01 08 20 20 03 04 14 37 55 20 20 03 04 14 38 02 01 C0 F8 68
云平台下发告警记录确认帧 8F
4B 48 00 2E 01 96 6C E1 05 35 36 31 35 31 30 30 30 35 34 39 30 4A 35 43 30 30 30 30 31
00 8F 00 00 01 01 08 20 20 03 04 14 37 55 63 C1 68
后台主动下发补充金额 1C
4B 48 00 3D 01 92 02 2C 05 35 36 31 35 31 30 30 31 30 31 39 30 4B 41 46 30 30 30 30 33
00 1C 00 00 01 33 32 36 36 30 30 36 31 35 37 34 37 37 37 34 32 00 00 00 00 00 00 12 74 30
76 68
终端回复上传结果 9C
4B 48 00 3A 01 96 06 C7 05 35 36 31 35 31 30 30 31 30 31 39 30 4B 41 46 30 30 30 30 33
00 9C 00 00 01 33 32 36 36 30 30 36 31 35 37 34 37 37 37 34 32 00 00 00 00 01 9C 09 68
VIN 码请求充电 1D
4B 48 00 36 01 96 0F E8 05 35 30 31 35 31 30 30 30 38 34 38 30 4B 41 34 30 30 30 31 39 00
1D 00 00 01 4C 4C 33 41 45 43 4A 33 39 4A 41 30 31 36 32 33 34 6C 74 68
服务器响应结果 9D
4B 48 00 44 01 96 0F E8 05 35 30 31 35 31 30 30 30 38 34 38 30 4B 41 34 30 30 30 31 39 00
9D 00 00 01 01 34 34 31 33 35 30 38 30 38 34 33 39 33 36 35 39 00 00 00 00 00 BB 71 F7
00 24 54 1C 1C 94 B6 23 68
后台主动查询尖峰平谷计费规则 20
4B 48 00 26 01 96 02 16 05 35 36 31 35 31 30 30 30 35 34 39 30 4A 35 43 30 30 30 30 31 00
20 00 00 00 01 F1 1F 68
终端回复结果 A0
4B 48 00 4C 01 96 00 00 05 35 36 31 35 31 30 30 30 35 34 39 30 4A 35 43 30 30 30 30 31
00 A0 00 00 01 59 43 30 33 00 00 00 00 00 00 04 00 00 03 08 00 01 14 00 00 22 00 02 2E E0
1D 4C 1D 4C 0F A0 07 6C 10 CC 10 04 16 A8 D9 33 68
专用于钰辰监控软件开发，严禁外传第三方
后台主动下发尖峰平谷计费规则 1F
4B 48 00 5B 01 94 0B 0B 05 35 36 31 35 31 30 30 31 30 36 35 30 4B 43 46 30 30 30 30 33
00 1F 00 01 00 32 30 32 30 2E 30 33 2E 30 34 09 00 00 03 07 00 02 08 30 01 11 30 02 14 30
01 17 30 02 19 00 01 21 00 02 23 00 03 00 00 10 CC 0B 54 06 40 00 00 0B B8 0B B8 0B B8
D7 58 68
终端回复结果 9F
4B 48 00 30 01 96 00 00 05 35 36 31 35 31 30 30 31 30 36 35 30 4B 43 46 30 30 30 30 33 00
9F 00 00 01 01 32 30 32 30 2E 30 33 2E 30 34 5F 54 68
注：TCP 升级、FTP 升级、密码启动充电、下发功率限制命令，以上 4 个命令
字报文帧如有需要，再单独提供。
 */

public enum MsgType {


    /*登陆*/
    LOGIN(0x01),                    /*登入*/
    LOGIN_ACK(0x81),                /*平台登入响应*/

    /*心跳*/
    HEARTBEAT(0x02),                /*心跳/校时*/
    HEARTBEAT_ACK(0x82),            /*平台心跳/校时确认*/

    /*读写终端参数*/
    GET_DATA(0x03),                 /*平台下发读取终端数据帧*/
    GET_DATA_ACK(0x83),             /*充电桩上传终端数据*/
    SET_DATA(0x04),                 /*云平台下发写终端数据帧*/
    SET_DATA_ACK(0x84),             /*充电桩上传写终端数据确认帧*/

    /*计费规则*/
    GET_BILLING_RULES(0x05),        /*云平台下发计费模型查询帧*/
    GET_BILLING_RULES_ACK(0x05),    /*充电桩上传电价计费模型帧*/
    SET_BILLING_RULES(0x06),        /*云平台下发计费模型设置帧*/
    SET_BILLING_RULES_ACK(0x86),    /*充电桩上传电价计费模型确认帧*/

    /*预约充电*/
    ORDER_CHARGING(0x09),           /*云平台下发预约充电帧*/
    ORDER_CHARGING_ACK(0x89),       /*充电桩上传预约充电确认帧*/

    /*启停充电*/
    START_STOP_CHARGING(0x0A),      /*云平台下发启停充电帧*/
    START_STOP_CHARGING_ACK(0x8A),  /*充电桩上传启停充电通知确认帧*/
    START_STOP_RESULT(0x0B),        /*充电桩上传启停充电结果帧*/
    START_STOP_RESULT_ACK(0x8B),    /*监控平回复结果确认帧*/

    /*刷卡请求*/
    CARD_AUTH(0x0C),                /*充电桩上传刷卡请求帧*/
    CARD_AUTH_ACK(0x8C),            /*云平台下发刷卡请求确认帧*/


    REAL_TIME_DATA(0x0D),         /*充电桩上传实时数据*/
    REAL_TIME_DATA_ACK(0x8D),     /* 0x8D 云平台下发终端数据确认帧*/

    CHARGING_HISTORY_RECORD(0x0E),       /*充电桩上传历史充电记录*/
    CHARGING_HISTORY_RECORD_ACK(0x8E),   /*云平台下发充电记录确认帧*/

    ALARM_HISTORY_RECORD(0x0F),      /* 0x0F 充电桩上传历史告警数据*/
    ALARM_HISTORY_RECORD_ACK(0x8F),  /*云平台下发告警记录确认帧*/

    OPT_HISTORY_RECORD(0x10),        /*充电桩上传操作记录数据*/
    OPT_HISTORY_RECORD_ACK(0x90),    /*云平台下发操作记录确认帧*/

    /*ftp升级*/
    FTP_OTA(0x11),                  /*监控中心下发远程升级指令*/
    FTP_OTA_ACK(0x91),              /*充电桩上传远程升级应答信息*/
    FTP_OTA_RESULT(0x12),           /*充电桩上传升级结果标识*/
    FTP_OTA_RESULT_ACK(0x92),       /*云平台下发升级结果确认帧*/


    /*tcp升级*/
    TCP_OTA(0x16),                  /*服务器下发升级请求*/
    TCP_OTA_ACK(0x96),              /*充电桩应答下发升级参数指令*/
    TCP_OTA_FILE(0x17),             /* 0x17 服务器下发升级文件数据*/
    TCP_OTA_FILE_ACK(0x97),         /* 0x97 充电桩应答服务器下发升级文件数据指令*/
    TCP_OTA_RESULT(0x1A),           /*充电桩上传升级结果*/
    TCP_OTA_RESULT_ACK(0x9A),       /*服务器响应上传升级结果*/


    /*数据主动上传,注意，区别于实时数据上传*/
    REPORT_DATA(0x13),              /*终端主动上传数据*/
    REPORT_DATA_ACK(0x93),          /*主动上传数据确认*/

    /*校时命令*/
    TIMING(0x13),                   /*云平台下发对时数据*/
    TIMING_ACK(0x93),               /*终端上传对时结果*/

    /*充电金额补发*/
    CHARGING_AMOUNT_CHANGED(0x1C),   /*平台主动下发补充金额*/
    CHARGING_AMOUNT_CHANGED_ACK(0x9C),/*终端回复上传结果*/

    /*VIN码启动充电*/
    VIN_REQ_CHARGING(0x1D),         /*VIN 码请求充电*/
    VIN_REQ_CHARGING_ACK(0x9D),     /*服务器响应结果*/

    /*密码启动充电*/
    PWD_REQ_CHARGING(0x1E),         /*密码请求充电*/
    PWD_REQ_CHARGING_ACK(0x9E),     /*服务器响应结果*/

    /*尖峰平谷计费规则设置、查询*/
    SET_TIME_INTERVAL_RATE(0x1F),   /*平台主动下发尖峰平谷计费规则*/
    SET_TIME_INTERVAL_RATE_ACK(0x9F),/*终端回复结果*/
    GET_TIME_INTERVAL_RATE(0x20),   /*平台主动查询尖峰平谷计费规则*/
    GET_TIME_INTERVAL_RATE_ACK(0xA0),/*终端回复结果*/

    GET_POWER_OUT_LIMIT(0x21),      /*充电桩请求输出功率*/
    GET_POWER_OUT_LIMIT_ACK(0xA1),  /*服务器回复可用的输出功率*/
    SET_POWER_OUT_LIMIT(0x22),      /*平台主动下发功率限制指令*/
    SET_POWER_OUT_LIMIT_ACK(0xA2),  /*终端回复结果*/


    GET_OFFLINE_RULES(0x23),        /*平台下发查询终端离线规则*/
    GET_OFFLINE_RULES_ACK(0xA3),    /*终端回复离线规则*/

    SET_OFFLINE_RULES(0x24),        /*平台下发设置终端离线规则*/
    SET_OFFLINE_RULES_ACK(0xA4),    /*终端回复设置结果*/

    DEL_OFFLINE_RULES(0x25),        /*平台下发删除终端离线规则*/
    DEL_OFFLINE_RULES_ACK(0xA5),    /*终端回复删除结果*/

    SET_QR_FORMAT(0x30),            /*平台主动下发二维码规则*/
    SET_QR_FORMAT_ACK(0xB0),        /*终端回复二维码规则设置结果*/

    GET_REAL_DATA(0x32),            /*平台主动下发获取充电实时数据*/
    GET_REAL_DATA_ACK(0xB2),        /*终端回复充电实时数据*/

    REMOTE_DEBUG_LOG_UPLOAD(0x35),  /*平台下发远程调试日志上传指令*/
    REMOTE_DEBUG_LOG_UPLOAD_ACK(0xB5),/*终端回复远程调试日志上传*/

    REMOTE_DEBUG_LOG_UPLOAD_RESULT(0x36),/*终端回复远程调试日志上传结果*/
    REMOTE_DEBUG_LOG_UPLOAD_RESULT_ACK(0xB6),/*平台应答远程调试日志上传结果*/

    /*并机接触器使用次数(这里先理解为充电枪)*/
    GET_GUN_USE_TIME(0x37),         /*平台查询并机接触器使用次数*/
    GET_GUN_USE_TIME_ACK(0xB7),     /*平台查询并机接触器使用次数*/

    REBOOT(0x38),                   /*平台下发系统重启指令*/
    REBOOT_ACK(0xB8),               /*终端回复系统重启结果*/

    /*系统状态*/
    GET_SYSTEM_STATE(0x39),         /*平台下发查询系统状态*/
    GET_SYSTEM_STATE_ACK(0xB9),     /*终端回复系统状态*/

    REPORT_BMS_DATA(0x3A),          /*终端主动上报 bms 数据*/
    REPORT_BMS_DATA_ACK(0xBA),      /*平台应答确认 bms 数据上传*/

    GET_HEALTH_DATA(0x3B),          /*平台下发查询终端健康数据*/
    GET_HEALTH_DATA_ACK(0xBB),      /*终端回复健康数据*/

    /*接触器状态*/
    GET_GUN_STATE(0x3C),            /*平台下发查询接触器状态数据*/
    GET_GUN_STATE_ACK(0xBC),        /*终端回复接触器状态数据*/

    LOGOUT(0x3D),                  /*平台下发下线注销指令*/
    LOGOUT_ACK(0xBD),              /*终端回复下线注销指令结果*/

    TERMINAL_DATA_WRITE(0x3F),      /*平台下发终端器件数据写入指令*/
    TERMINAL_DATA_WRITE_ACK(0xBF);  /*终端回复下发平台设置终端器件数据写入指令结果*/

    @Getter
    private final int type;

    MsgType(int type) {
        this.type = type;
    }

    public static MsgType findByType(int type) {
        for (MsgType msgType : MsgType.values()) {
            if (msgType.getType() == type) {
                return msgType;
            }
        }
        return null;
    }
}
