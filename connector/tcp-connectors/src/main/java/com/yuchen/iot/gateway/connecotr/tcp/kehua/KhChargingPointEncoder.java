package com.yuchen.iot.gateway.connecotr.tcp.kehua;


import com.yuchen.iot.gateway.connecotr.tcp.kehua.comm.Constant;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.ChargingPointProtocol;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.MsgHeader;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message.MessageEncoder;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.util.CRC16Util;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.util.HexStringUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;
import io.netty.util.ReferenceCountUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;


public class KhChargingPointEncoder extends MessageToByteEncoder<ChargingPointProtocol<Object>> {

    private static final Logger log = LoggerFactory.getLogger(KhChargingPointEncoder.class);

    @Override
    protected void encode(ChannelHandlerContext ctx, ChargingPointProtocol<Object> msg, ByteBuf byteBuf) throws Exception {

        Integer devType = ctx.channel().attr(Constant.devTypeKey).get();
        Integer devVer = ctx.channel().attr(Constant.devVerKey).get();
        Integer reqMegId = ctx.channel().attr(Constant.reqMsgId).get();
        Integer devId = ctx.channel().attr(Constant.devId).get();
        Integer gunNumber = ctx.channel().attr(Constant.gunNumber).get();
        String devNumber = ctx.channel().attr(Constant.integrateNoKey).get();

        MessageEncoder messageEncoder = (MessageEncoder)msg.getBody();
        ByteBuf buf = messageEncoder.encode();

        MsgHeader header = msg.getHeader();
        boolean isAck = (header.getCmd() & 0x80) == 0x80;
        /*帧头*/
        byteBuf.writeShort(header.getPreamble());
        /*帧长*/
        byteBuf.writeShort(37 + buf.readableBytes());
        /*版本*/
        byteBuf.writeShort(devVer);
        /*报文流水号，发送端自加1，接收端原值返回*/
        if (isAck){
            byteBuf.writeShort(reqMegId + 1);
        }else {
            byteBuf.writeShort(header.getMsgId());
        }
        /*设备类型*/
        byteBuf.writeByte(devType);
        /*设备串号*/
        byteBuf.writeBytes(devNumber.getBytes(StandardCharsets.US_ASCII));
        /*加密方式*/
        byteBuf.writeByte(0x00);
        /*功能码*/
        byteBuf.writeByte(header.getCmd());
        /*设备ID*/
        byteBuf.writeShort(devId);
        /*枪号*/
        if (isAck){
            byteBuf.writeByte(gunNumber);
        }else {
            byteBuf.writeByte(header.getGunNumber());
        }
        /*信息域*/
        byteBuf.writeBytes(buf);
        /*校验*/
        byteBuf.writeShort(CRC16Util.calcCrc16(ByteBufUtil.getBytes(byteBuf)));
        /*结束字节*/
        byteBuf.writeByte(header.getTerminator());
        // TODO:排查内存泄漏的问题
        ReferenceCountUtil.release(buf);
        log.info("【网关-》设备】KhChargingPointEncoder---"+header.getCmd()+"---"+ HexStringUtil.Bytes2hexStr(ByteBufUtil.getBytes(byteBuf)));
    }

}
