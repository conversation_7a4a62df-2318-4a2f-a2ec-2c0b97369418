package com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.protocol.message;

import io.netty.buffer.ByteBuf;
import lombok.Data;

/**
 * @description: 网络信息上报，主站无需应答，一般5分钟发送一次
 * @author: zhonghx
 * @create: 2022-12-09 10:48
 **/

@Data
public class NetWorkReportMessage implements MessageDecoder{

    /*联网方式 0未知，1-wifi 2有线 11未知的移动网络 12-2G 13-3G 14-4G 15-5G*/
    private byte networkType;

    /*信号强度 百分比0-100*/
    private byte signalStrength;

    /*SIM卡IMEI号 15个字节*/
    private String imei;

    /*SIM卡ICCID号 20个字节*/
    private String iccid;

    /*移动网络基站LAC号*/
    private short lac;

    /*移动网络基站CID*/
    private short cid;

    /*经度*/
    private float longitude;

    /*纬度*/
    private float latitude;

    @Override
    public void decode(ByteBuf in) {
        networkType = in.readByte();
        signalStrength = in.readByte();
        imei = in.readBytes(15).toString();
        iccid = in.readBytes(20).toString();
        lac = in.readShortLE();
        cid = in.readShortLE();
        longitude = in.readShortLE();
        latitude = in.readShortLE();
    }
}
