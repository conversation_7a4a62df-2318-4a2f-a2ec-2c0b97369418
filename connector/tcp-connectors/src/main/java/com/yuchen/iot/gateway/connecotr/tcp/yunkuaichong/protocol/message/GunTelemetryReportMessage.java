package com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.message;

import io.netty.buffer.ByteBuf;
import lombok.Data;

/**
 * @description: 充电枪遥测信息，主站无需应答，一般30秒发送一次
 * @author: zhong
 * @create: 2022-12-08 19:36
 **/

@Data
public class GunTelemetryReportMessage implements MessageDecoder {

    /*枪号 1-n*/
    private byte gunNo;

    /*枪头温度 摄氏度 有符号*/
    private byte gunTemperature;

    /*充电导引电压 V，1位小数*/
    private short voltage;

    /*（交流）CP占空比 %，1位小数*/
    private short ACDutyCycle;

    /*电表类型，0 未知， 1-直流表 2-交流表*/
    private byte meterType;

    /*电表有功电量读书，单位kwh，2位小数*/
    private int ActiveElectricity;

    @Override
    public void decode(ByteBuf in) {
        gunNo = in.readByte();
        gunTemperature = in.readByte();
        voltage = in.readShortLE();
        ACDutyCycle = in.readShortLE();
        meterType = in.readByte();
        ActiveElectricity = in.readIntLE();
    }
}
