package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.nio.charset.StandardCharsets;

/**
 * @description: 远程升级结果上传
 * @author: zhonghx
 * @create: 2023-02-13 18:48
 **/
@Data
@AllArgsConstructor
public class FtpOTAResultRequest implements MessageDecoder{

    /*成功标识
    *
    * 1.升级成功
    * 2.FTP无法连接
    * 3.FTP验证失败
    * 4.升级文件校验失败
    * */
    private byte ack;

    /*软件版本 ASCII*/
    private String version;

    @Override
    public void decode(ByteBuf in) {
        ack = in.readByte();
        byte[] versionBytes = ByteBufUtil.getBytes(in.readBytes(20));
        version = new String(versionBytes, StandardCharsets.US_ASCII);
    }
}
