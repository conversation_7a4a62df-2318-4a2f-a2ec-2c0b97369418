package com.yuchen.iot.gateway.connecotr.tcp.kehua.util;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * @description: BCD编码工具类
 * @author: zhonghx
 * @create: 2022-12-10 14:12
 **/
public class BCDUtil {


    private BCDUtil(){};

    /**
     * 字符串转BCD码
     * @param asc ASCII字符串
     * @return BCD
     */
    public static byte[] strToBcd(String asc) {
        byte[] s = asc.getBytes();
        byte[] b = new byte[s.length/2];
        for(int i=0;i<b.length;i++) {
            b[i] = (byte) (s[2*i] << 4 | (s[2*i+1] & 0xf));
        }
        return b;
    }

    /**
     * BCD转ASCII字符串
     * @param bytes BCD byte数组
     * @return ASCII字符串
     */
    public static String bcdToStr(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for(int i=0;i<bytes.length;i++) {
            sb.append(bytes[i]>>4&0xf)
                    .append(bytes[i]&0xf);
        }
        return sb.toString();
    }

    //待测试
    public static String timestampToBcdString(long timestamp) {
        Date date = new Date(timestamp * 1000);
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        return sdf.format(date);
    }


    //待测试
    public static byte[] bcdStringToByteArray(String bcdString) {
        int len = bcdString.length();
        int mod = len % 2;
        byte[] bcd = new byte[(len + mod) / 2];
        int j = 0;
        if (mod != 0) {
            bcd[j++] = (byte) (bcdString.charAt(0) - '0');
        }
        for (int i = mod; i < len; i += 2) {
            int high = bcdString.charAt(i) - '0';
            int low = bcdString.charAt(i + 1) - '0';
            bcd[j++] = (byte) ((high << 4) | low);
        }
        return bcd;
    }

}