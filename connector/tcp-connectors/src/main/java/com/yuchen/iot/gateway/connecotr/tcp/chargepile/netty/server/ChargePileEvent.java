package com.yuchen.iot.gateway.connecotr.tcp.chargepile.netty.server;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;


@Slf4j
@Data
public class ChargePileEvent {

    private Map<String, String> header = new HashMap<>();

    private JsonNode content;

    private byte[] byteData;

    public ChargePileEvent(Map<String, String> header, JsonNode content, byte[] byteData ){
        this.header = header;
        this.content = content;
        this.byteData = byteData;
    }
}
