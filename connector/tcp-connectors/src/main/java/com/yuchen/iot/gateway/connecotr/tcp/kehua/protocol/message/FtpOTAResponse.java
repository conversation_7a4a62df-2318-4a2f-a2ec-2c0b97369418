package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import lombok.Data;

/**
 * @description: 远程FTP升级应答
 * @author: zhonghx
 * @create: 2023-02-13 14:43
 **/

@Data
public class FtpOTAResponse implements MessageDecoder{

    /*应答信息
    0.桩被占用
    1.确认成功
    * */
    private byte ack;

    /*升级类型*/
    private byte type;

    /*软件版本*/
    private String softVersion;
    @Override
    public void decode(ByteBuf in) {

    }
}
