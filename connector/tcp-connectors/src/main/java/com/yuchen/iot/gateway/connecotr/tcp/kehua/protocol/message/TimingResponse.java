package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import lombok.Data;

/**
 * @description: 充电桩校时应答,充电过程中不允许对时，可能会导致充电记录时间与实际不一致。
 * @author: zhonghx
 * @create: 2023-02-15 14:26
 **/
@Data
public class TimingResponse implements MessageDecoder{

    /*对时结果
    * 0失败
    * 1.成功
    * 2.系统繁忙
    *
    * */
    private byte ack;

    @Override
    public void decode(ByteBuf in) {
        ack = in.readByte();
    }
}
