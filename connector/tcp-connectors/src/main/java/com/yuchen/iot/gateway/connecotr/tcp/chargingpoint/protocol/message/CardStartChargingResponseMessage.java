package com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.protocol.message;

import com.yuchen.iot.gateway.connecotr.tcp.chargepile.util.BytesUtil;
import com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.BCDUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @description: 主站应答终端的请求启动充电指令。
 * @author: zhonghx
 * @create: 2022-12-09 17:32
 **/
@Data
@AllArgsConstructor
public class CardStartChargingResponseMessage implements MessageEncoder{

    /*枪号 1-n*/
    private byte gunNo;

    /*卡号， 16字节BCD*/
    private String cardNo;

    /*充电流水号 8个字节BCD，由系统生成的唯一性的充电流水号，返回FFFFFFFFFFFFFFFFFF表示启动失败*/
    private String serialNumber;

    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(gunNo);
        buf.writeBytes(cardNo.getBytes());
        byte[] bytes = BCDUtil.strToBcd(serialNumber);
        bytes = BytesUtil.bytesReverse(bytes);
        buf.writeBytes(bytes);
        return buf;
    }
}
