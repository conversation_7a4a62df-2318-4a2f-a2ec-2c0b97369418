package com.yuchen.iot.gateway.connecotr.tcp.firefighting;


import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.yuchen.iot.gateway.connecotr.tcp.JsonTcpConnectorMessage;
import com.yuchen.iot.gateway.connecotr.tcp.firefighting.entity.*;
import com.yuchen.iot.gateway.connecotr.tcp.firefighting.util.ByteUtil;
import com.yuchen.iot.gateway.connecotr.tcp.firefighting.util.MessageParser;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.net.InetSocketAddress;
import java.util.Calendar;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


/**
 *
 * 用户传输装置-处理器
 *
 * <AUTHOR>
 * @date 2025/05/15
 */

@Component
@ChannelHandler.Sharable
@Slf4j
public class UserSmartIotByKeAnHandler extends SimpleChannelInboundHandler<String> {

    // 添加静态的ConcurrentHashMap用于存储channel
    private static final ConcurrentHashMap<String, Channel> channelMap = new ConcurrentHashMap<>();
    
    // 新增监听器
    private final ChargingChannelListener<JsonTcpConnectorMessage> listener;
    
    // 存储设备最后心跳时间
    private static final ConcurrentHashMap<String, Long> lastHeartbeatMap = new ConcurrentHashMap<>();
    
    // 心跳超时时间(毫秒)
    private static final long HEARTBEAT_TIMEOUT = 30 * 1000;
    
    // 提供获取channelMap的方法
    public static ConcurrentHashMap<String, Channel> getChannelMap() {
        return channelMap;
    }
    
    public UserSmartIotByKeAnHandler(ChargingChannelListener<JsonTcpConnectorMessage> listener) {
        this.listener = listener;
    }
    
    // 无参构造函数，为了保持兼容性
    public UserSmartIotByKeAnHandler() {
        this.listener = null;
    }

    @Override
    public void handlerRemoved(ChannelHandlerContext ctx) throws Exception{
        InetSocketAddress socket = (InetSocketAddress) ctx.channel().remoteAddress();
        String ip = socket.getAddress().getHostAddress();
        // 客户端断开连接时从Map中移除
        channelMap.remove(ip);
        log.info("客户端断开连接：" + ip);
        // 通知监听器
        if (listener != null) {
            listener.onDisconnect(ip);
        }
    }

    /**
     * 定时检查心跳
     */
    @Scheduled(fixedRate = 5000)
    public void checkHeartbeat() {
        long currentTime = System.currentTimeMillis();
        
        for (Map.Entry<String, Long> entry : lastHeartbeatMap.entrySet()) {
            String deviceCode = entry.getKey();
            Long lastHeartbeat = entry.getValue();
            
            if (currentTime - lastHeartbeat > HEARTBEAT_TIMEOUT) {
                log.info("设备 {} 心跳超时，标记为离线", deviceCode);
                // 如果超过超时时间，标记设备离线
                if (listener != null) {
                    listener.onDisconnect(deviceCode);
                }
                // 从心跳Map中移除该设备
                lastHeartbeatMap.remove(deviceCode);
            }
        }
    }

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, String data) throws Exception {
        //先确认校验值是否相等，再来确认要发送的数据是确定包，或者否定包    确定命令符:3  否定包：6
        log.info("收到用传消息："+data);
    //    data="40400100014b100f15140519000000000000000000000000090002150140100f151405197d2323";
        //头
        String header = data.substring(0,4);
        //流水号
        String flow = data.substring(4,8);
        //版本号
        String version = data.substring(8,12);
        //时间
        String time = data.substring(12,24);
        //源地址  将源地址当作用传的编号
        String src = data.substring(24,36);
        String id=src.substring(10)+src.substring(8,10)+src.substring(6,8)+src.substring(4,6)+src.substring(2,4)
                +src.substring(0,2);
        //给用传设置一个设备编号
        String deviceCode = ByteUtil.buqi12(String.valueOf(new BigInteger(id,16))) ;
        //目的地址
        String mudi = data.substring(36,48);
        //有效数据长度
        String length = data.substring(48,52);
        String cmd = data.substring(52,54);
        //应用数据单元
        String raw = data.substring(54,data.length()-6);
        //crc 验证
        String crc = data.substring(data.length()-6,data.length()-4);
        //尾
        String end = data.substring(data.length()-4);
        //回复用传通用包
        byte [] me = checksumUser(header,flow,version,src,mudi,end);
        //回复用传包
        ctx.writeAndFlush(me);
        


/*        JSONObject jsonObject= new JSONObject();

        jsonObject.put("deviceCode", deviceCode);
        jsonObject.put("data",raw);
        jsonObject.put("hex",data);*/

        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        // 创建一个根节点
        ObjectNode rootNode = objectMapper.createObjectNode();
        rootNode.put("deviceCode", deviceCode);
        rootNode.put("data", raw);
        rootNode.put("cmd", cmd);
        rootNode.put("flow", flow);



        // 处理业务 解析后面部分
        try {
            byte[] bytes = ByteUtil.hexStringToByteArray(raw);
            if (bytes.length > 0) {
                int typeFlag = Integer.parseInt(String.valueOf(bytes[0]), 16);
                
                // 根据类型标志进行不同的处理
                switch (typeFlag) {
                    case 0x01: // 建筑消防设施系统状态
                        SystemStatusMessage systemStatusMessage = (SystemStatusMessage) MessageParser.parseMessage(raw);
                        log.info("解析建筑消防设施系统状态消息: {}", systemStatusMessage);
                        rootNode.set("systemStatusMessage", objectMapper.valueToTree(systemStatusMessage));
                        break;
                        
                    case 0x02: // 火灾报警
                        AlarmMessage alarmMessage = (AlarmMessage) MessageParser.parseMessage(raw);
                        log.info("解析报警消息: {}", alarmMessage);
                        rootNode.set("alarmMessage", objectMapper.valueToTree(alarmMessage));
                        break;
                        
                    case 0x04: // 类型标志04 - 建筑消防设施操作信息记录
                        OperationRecordMessage operationRecordMessage = (OperationRecordMessage) MessageParser.parseMessage(raw);
                        log.info("解析建筑消防设施操作信息记录消息: {}", operationRecordMessage);
                        rootNode.set("operationRecordMessage", objectMapper.valueToTree(operationRecordMessage));
                        break;
                        
                    case 0x21: // 用户信息传输装置运行状态
                        DeviceStatusMessage deviceStatusMessage = (DeviceStatusMessage) MessageParser.parseMessage(raw);
                        log.info("解析用户信息传输装置运行状态消息: {}", deviceStatusMessage);
                        rootNode.set("deviceStatusMessage", objectMapper.valueToTree(deviceStatusMessage));
                        break;
                        
                    case 0x24: // 类型标志24 - 用户信息传输装置操作信息记录
                        DeviceOperationMessage deviceOperationMessage = (DeviceOperationMessage) MessageParser.parseMessage(raw);
                        log.info("解析用户信息传输装置操作信息记录消息: {}", deviceOperationMessage);
                        rootNode.set("deviceOperationMessage", objectMapper.valueToTree(deviceOperationMessage));
                        break;
                        
                    case 0x28: // 类型标志28 - 用户信息传输装置系统时间
                        SystemTimeMessage systemTimeMessage = (SystemTimeMessage) MessageParser.parseMessage(raw);
                        log.info("解析用户信息传输装置系统时间消息: {}", systemTimeMessage);
                        rootNode.set("systemTimeMessage", objectMapper.valueToTree(systemTimeMessage));
                        
                        // 更新设备心跳时间
                        lastHeartbeatMap.put(deviceCode, System.currentTimeMillis());
                        
                        // 调用连接监听器，标记设备在线
                        if (listener != null) {
                            listener.onConnect(deviceCode);
                        }
                        
                        // 心跳包不上报消息
                        return;
                        
                    case 0x03: // 类型标志03 - 建筑消防设施部件模拟量值
                        AnalogValueMessage analogValueMessage = (AnalogValueMessage) MessageParser.parseMessage(raw);
                        log.info("解析建筑消防设施部件模拟量值消息: {}", analogValueMessage);
                        rootNode.set("analogValueMessage", objectMapper.valueToTree(analogValueMessage));
                        break;
                        
                    case 0x06: // 类型标志06 - 建筑消防设施系统配置情况
                        SystemConfigMessage systemConfigMessage = (SystemConfigMessage) MessageParser.parseMessage(raw);
                        log.info("解析建筑消防设施系统配置情况消息: {}", systemConfigMessage);
                        rootNode.set("systemConfigMessage", objectMapper.valueToTree(systemConfigMessage));
                        break;
                        
                    case 0x07: // 类型标志07 - 建筑消防设施部件配置情况
                        ComponentConfigMessage componentConfigMessage = (ComponentConfigMessage) MessageParser.parseMessage(raw);
                        log.info("解析建筑消防设施部件配置情况消息: {}", componentConfigMessage);
                        rootNode.set("componentConfigMessage", objectMapper.valueToTree(componentConfigMessage));
                        break;
                        
                    default: // 其他类型标志，尝试使用原有解析器
                       /* try {
                            AlarmMessage defaultMessage = MessageParser.parseAlarmData(raw);
                            log.info("使用默认解析器解析消息: {}", defaultMessage);
                            jsonObject.put("message", defaultMessage);
                        } catch (Exception ex) {
                            log.error("默认解析器解析失败: ", ex);
                        }*/
                        break;
                }
            }
            
            // 通知监听器
            if (listener != null) {
                listener.onMessage(new JsonTcpConnectorMessage(rootNode));
            }
        } catch (Exception e) {
            log.error("解析应用数据单元异常: ", e);
        }


    }


    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        InetSocketAddress socket = (InetSocketAddress) ctx.channel().remoteAddress();
        String ip = socket.getAddress().getHostAddress();
        log.info("收到客户端IP: " + ip);
        
        // 将channel保存到ConcurrentHashMap中
        channelMap.put(ip, ctx.channel());
        
        // 通知监听器
        if (listener != null) {
            listener.onConnect(ip);
        }
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        cause.printStackTrace();

        log.info(cause.getMessage());
//        ctx.close();
    }


    /**
     * 校验和用户
     *
     * @param headed  领导
     * @param flowId  流id
     * @param version 版本
     * @param src     src
     * @param mu
     * @param end     结束
     * @return {@link byte[]}
     */
    public byte [] checksumUser(String headed,String flowId,String version,String src,String mu,String end){

        Integer f1 = Integer.parseInt(flowId.substring(0,2),16);
        Integer f2 = Integer.parseInt(flowId.substring(2),16);
        Integer vma=Integer.parseInt(version.substring(0,2),16);
        Integer vmi=Integer.parseInt(version.substring(2),16);
        //同步心跳包时间
        Calendar now = Calendar.getInstance();
        Integer year=now.get(Calendar.YEAR)-2000;
        String hex_year = ByteUtil.buqi3(Integer.toHexString(year));
        Integer month=(now.get(Calendar.MONTH) + 1);
        String hex_month = ByteUtil.buqi3(Integer.toHexString(month));
        Integer day= now.get(Calendar.DAY_OF_MONTH);
        String hex_day= ByteUtil.buqi3(Integer.toHexString(day));
        Integer hour=now.get(Calendar.HOUR_OF_DAY);
        String hex_hour=ByteUtil. buqi3(Integer.toHexString(hour));
        Integer min=now.get(Calendar.MINUTE);
        String hex_min= ByteUtil.buqi3(Integer.toHexString(min));
        Integer sec=now.get(Calendar.SECOND);
        String hex_sec= ByteUtil.buqi3(Integer.toHexString(sec));



        Integer src0=Integer.parseInt(src.substring(0,2),16);
        Integer src1=Integer.parseInt(src.substring(2,4),16);
        Integer src2=Integer.parseInt(src.substring(4,6),16);
        Integer src3=Integer.parseInt(src.substring(6,8),16);
        Integer src4=Integer.parseInt(src.substring(8,10),16);
        Integer src5=Integer.parseInt(src.substring(10,12),16);

        Integer dest0=Integer.parseInt(mu.substring(0,2),16);
        Integer dest1=Integer.parseInt(mu.substring(2,4),16);
        Integer dest2=Integer.parseInt(mu.substring(4,6),16);
        Integer dest3=Integer.parseInt(mu.substring(6,8),16);
        Integer dest4=Integer.parseInt(mu.substring(8,10),16);
        Integer dest5=Integer.parseInt(mu.substring(10,12),16);


        Integer len=0;
        String length = "0000";
        String cmd_hex = "03";
        Integer cmd=Integer.parseInt(cmd_hex,16);


        Integer bb=f1+f2+vma+vmi+sec+min+hour+day+month+year+src0+src1+src2+src3+src4+src5+dest0+dest1+dest2+dest3+dest4+dest5+len+cmd;
        Integer check = bb &0xff;
        byte cc = (byte) check.intValue();
        String crc = ByteUtil.buqi3(toHex(cc));

        String message = headed+flowId+version+hex_sec+hex_min+hex_hour+hex_day+hex_month+hex_year+mu+src+length+cmd_hex+crc+"2323";
        log.debug("应答心跳包:"+message);
        return ByteUtil.toBytes(message);

    }



    // 把byte 转化为两位十六进制数
    public static String toHex(byte b) {
        String result = Integer.toHexString(b & 0xFF);
        if (result.length() == 1) {
            result = '0' + result;
        }
        return result;
    }

    private String printHexByte(byte b) {
        return String.format("%02X", b);
    }

    private String printHexBytes(byte[] bytes) {
        String str = "";
        for (int i = 0; i < bytes.length; i++) {
            str += String.format("%02X", bytes[i]);
        }
        return str;
    }

    private String printHexShort(int s) {
        byte[] bytes = hexShort(s);
        return printHexBytes(bytes);
    }

    private byte[] hexShort(int s) {
        byte[] bytes = new byte[2];
        bytes[0] = (byte) ((s << 24) >> 24);
        bytes[1] = (byte) ((s << 16) >> 24);
        return bytes;
    }

}
