package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;



/**
 * @description: 平台应答终端的心跳
 * @author: zhong
 * @create: 2022-12-08 16:55
 *
 * 1) 充电桩默认 120s 向服务器发送心跳包，运营云平台按照协议要求返回
 * 心跳确认帧给充电桩。心跳周期可设置。
 * 2) 充电桩发送心跳帧后开始计数，连续 3 次未收到平台心跳确认帧，认为
 * 心跳超时，关闭当前连接，自动重新发起 TCP 连接，在获得响应后，向运营云
 * 平台发送登录报文。
 **/
@Data
@AllArgsConstructor
public class HeartbeatResponse implements MessageEncoder {

    /*确认标识,0确认失败，1确认成功*/
    private byte ack;
    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(ack);
        return buf;
    }
}
