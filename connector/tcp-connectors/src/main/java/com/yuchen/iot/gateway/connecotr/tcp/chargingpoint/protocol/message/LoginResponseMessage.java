package com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;


/**
 * @description: 平台应答终端登录请求
 * @author: zhong
 * @create: 2022-12-08 16:55
 **/
@Data
@AllArgsConstructor
public class LoginResponseMessage implements MessageEncoder {

    /*登入结果，6表示成功，0表示失败*/
    public byte loginResult;

    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(loginResult);
        return buf;
    }
}

