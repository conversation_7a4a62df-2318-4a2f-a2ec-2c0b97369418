package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.Data;

/**
 * @description: 终端主动请求账号充电
 * @author: zhonghx
 * @create: 2023-02-15 14:56
 * 终端：用户插枪后，选择账号启动充电；输入用户名和密码；点击确定，等待
 * 服务器响应；30 秒超时，验证失败。
 * 服务器：收到账号请求后，查询该账号和密码是否正确，并且回复结果。如果
 * 密码错误或者用户不存在回复失败，否则回复成功；回复成功后，紧接着下发
 * 启动充电命令。
 **/

@Data
public class ChargingWithAccountRequest implements MessageDecoder{

    /*用户名
    * ASCII码
    * 16字节
    * 不足补0
    * */
    private String account;

    /*密码 6字节*/
    private String password;

    @Override
    public void decode(ByteBuf in) {
        byte[] accountBytes = ByteBufUtil.getBytes(in.readBytes(16));
        account = new String(accountBytes);
        byte[] passwordBytes = ByteBufUtil.getBytes(in.readBytes(6));
        password = new String(passwordBytes);
    }
}
