package com.yuchen.iot.gateway.connecotr.tcp.firefighting.entity;

import lombok.Data;

/**
 * 建筑消防设施系统状态消息实体类
 * 类型标志为01
 */
@Data
public class SystemStatusMessage {
    // 原始数据
  //  private String rawData;
    
    // 类型标志
   // private int typeFlag;
    
    // 信息对象数目
  //  private int objectCount;
    
    // 系统类型
    private int systemType;
    
    // 系统地址
    private String systemAddress;
    
    // 系统状态
    private SystemStatus systemStatus;
    
    // 时间
    private String eventTime;
    
    /**
     * 系统状态内部类
     */
    @Data
    public static class SystemStatus {
        // bit0: 0-正常运行, 1-测试运行
        private int testRunning;
        
        // bit1: 0-无, 1-火警
        private int fireAlarm;
        
        // bit2: 0-无, 1-故障
        private int fault;
        
        // bit3: 0-无, 1-屏蔽
        private int shielded;
        
        // bit4: 0-无, 1-监管
        private int supervised;
        
        // bit5: 0-停止, 1-启动
        private int started;
        
        // bit6: 0-无, 1-反馈
        private int feedback;
        
        // bit7: 0-无, 1-延时
        private int delayed;
        
        // bit8: 0-主电正常, 1-主电故障
        private int mainPowerFault;
        
        // bit9: 0-备电正常, 1-备电故障
        private int backupPowerFault;
        
        // bit10: 0-总线正常, 1-总线故障
        private int busFault;
        
        // bit11: 0-自动, 1-手动
        private int manual;
        
        // bit12: 0-无, 1-配置改变
        private int configChanged;
        
        // bit13: 0-正常, 1-复位
        private int reset;
    }
}