package com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.protocol.message;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.netty.buffer.ByteBuf;
import lombok.Data;


/**
 * @description: 终端心跳请求信息
 * @author: zhong
 * @create: 2022-12-08 16:55
 **/
@Data
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class HeartbeatRequestMessage implements MessageDecoder{
    @Override
    public void decode(ByteBuf in) {

    }
}
