package com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.protocol.message;

import com.yuchen.iot.gateway.connecotr.tcp.chargepile.util.BytesUtil;
import com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.BCDUtil;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.util.HexStringUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import io.netty.buffer.ByteBufUtil;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @description: 主站应答终端停止充电请求
 * @author: zhonghx
 * @create: 2022-12-09 18:51
 **/
@Data
@AllArgsConstructor
public class ClientStopChargingResponseMessage implements MessageEncoder{

    /*枪号 1-n*/
    private byte gunNo;

    /*充电流水号BCD*/
    private String serialNumber;

    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(gunNo);
        byte[] bytes = BCDUtil.strToBcd(serialNumber);
        bytes = BytesUtil.bytesReverse(bytes);
        buf.writeBytes(bytes);
        return buf;
    }

}
