package com.yuchen.iot.gateway.connecotr.tcp.chargepile.netty.server;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yuchen.iot.gateway.connecotr.tcp.JsonTcpConnectorMessage;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.business.BusinessHolder;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.business.IBusiness;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.connector.ChargePileTcpConnector;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.constants.ChargePileConstants;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.enums.ChargeCommandCode;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.message.request.DataPackRequest;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.util.ChannelMapByEntityUtil;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.util.SpringUtil;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * netty服务业务处理类
 * <AUTHOR>
 * @date 2022/12/07 15:29
 */
@Component("simpleChannelHandler")
@ChannelHandler.Sharable
public class BusinessChannelHandler extends SimpleChannelInboundHandler<DataPackRequest> {
	private Logger logger = LoggerFactory.getLogger(this.getClass());

	@Override
	protected void channelRead0(ChannelHandlerContext ctx, DataPackRequest dataPack) throws Exception {
		logger.debug("dataPack-----"+dataPack);
		if(Objects.nonNull(dataPack)){
			return;
		}
		byte cmd = dataPack.getPackHeader().getCmd();
		Channel incoming = ctx.channel();
		String pileNo = incoming.attr(ChargePileConstants.DEVICE_ATTRIBUTE_KEY).get();
		ChannelMapByEntityUtil.addChannel(incoming,pileNo);
		ChannelMapByEntityUtil.addChannel(pileNo,incoming);
		//有些业务不需要发给IOT单独处理
		BusinessHolder businessHolder = SpringUtil.getBean(BusinessHolder.class);
		IBusiness iBusiness = businessHolder.getBusiness(ChargePileConstants.BUSINESS_TYPE+dataPack.getPackHeader().getCmd());
		iBusiness.process(dataPack,incoming);
		if(Objects.equals(cmd, ChargeCommandCode.PILE_HEART.getCode())){
			return;
		}
		//获取已经设置了的监听
		//需要根据命令去组装上传的数据 todo
		ChargePileChannelListener listener = (ChargePileChannelListener)ChargePileTcpConnector.lister.
				get(ChargePileConstants.CHARGE_LISTENER_KEY+pileNo);
		Map<String,String> header = new HashMap<>();
		header.put(ChargePileConstants.CHARGE_DEVICE_KEY,pileNo);
		if (listener != null) {
			ObjectMapper mapper = new ObjectMapper();
			JsonNode node = mapper.valueToTree(new ChargePileEvent(header, null, dataPack.getData()));
			//发给上行处理器
			listener.onMessage(new JsonTcpConnectorMessage(node));
		}
	}

	@Override
	public void handlerAdded(ChannelHandlerContext ctx) throws Exception {
		Channel incoming = ctx.channel();
		logger.debug("add connect:" + incoming.remoteAddress());
		String pileNo = incoming.attr(ChargePileConstants.DEVICE_ATTRIBUTE_KEY).get();
		ChargePileChannelListener listener = (ChargePileChannelListener) ChargePileTcpConnector.lister.get(ChargePileConstants.CHARGE_LISTENER_KEY+pileNo);
		if (listener != null) {
			listener.onConnect();
		}
	}

	@Override
	public void handlerRemoved(ChannelHandlerContext ctx) throws Exception {
		Channel incoming = ctx.channel();
		logger.debug("remove connect:" + incoming.remoteAddress());
		ChannelMapByEntityUtil.removeChannel(incoming);
	}


	/**
	 * 当Channel变成活跃状态时被调用；Channel是连接/绑定、就绪的
	 */
	@Override
	public void channelActive(ChannelHandlerContext ctx) throws Exception {
		Channel incoming = ctx.channel();
		logger.debug("channelActive:" + incoming.remoteAddress() + "在线");
		super.channelActive(ctx);
		String pileNo = incoming.attr(ChargePileConstants.DEVICE_ATTRIBUTE_KEY).get();
		ChargePileChannelListener listener = (ChargePileChannelListener) ChargePileTcpConnector.lister.get(ChargePileConstants.CHARGE_LISTENER_KEY+pileNo);
		if (listener != null) {
			listener.onDisconnect();
		}
	}

	/**
	 * Channel未连接到远端
	 */
	@Override
	public void channelInactive(ChannelHandlerContext ctx) throws Exception { // (6)
		Channel incoming = ctx.channel();
		logger.debug("channelInactive:" + incoming.remoteAddress() + "掉线");
		incoming.close();
		ctx.close();
	}

	@Override
	public void channelReadComplete(ChannelHandlerContext ctx) throws Exception {
		super.channelReadComplete(ctx);
	}

	@Override
	public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
		logger.error("exceptioncaught," + ctx.channel().remoteAddress(), cause);
	}
}
