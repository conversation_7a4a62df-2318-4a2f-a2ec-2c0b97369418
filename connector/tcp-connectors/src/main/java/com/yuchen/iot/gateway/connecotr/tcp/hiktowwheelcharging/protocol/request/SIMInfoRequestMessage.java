package com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.request;

import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.BCDUtil;
import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.message.MessageDecoder;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.Data;


@Data
public class SIMInfoRequestMessage implements MessageDecoder {


    private String simCardNo;
    @Override
    public void decode(ByteBuf in) {
          ByteBuf byteBuf = in.readBytes(10);
        byte[] bytes = ByteBufUtil.getBytes(byteBuf);

        simCardNo= byteArrayToHexString(bytes);
      //  String hexString = byteArrayToHexString(bytes);

     //   System.out.println("Converted Back to Hex: " + hexString);
    }

    public static String byteArrayToHexString(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X", b));
        }
        return sb.toString();
    }
}
