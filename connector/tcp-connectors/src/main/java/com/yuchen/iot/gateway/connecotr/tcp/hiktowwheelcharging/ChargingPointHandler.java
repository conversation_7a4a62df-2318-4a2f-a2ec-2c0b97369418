package com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yuchen.iot.gateway.connecotr.tcp.JsonTcpConnectorMessage;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.util.ExecutorUtils;
import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.comm.Constant;
import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.ChargingPointProtocol;
import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.MsgHeader;
import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.MsgType;
import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.ProtocolConstants;
import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.message.MessageDecoder;
import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.request.*;
import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.util.BytesUtil;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.util.HexStringUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import io.netty.buffer.Unpooled;
import io.netty.channel.*;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import io.netty.util.ReferenceCountUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.InvocationTargetException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

public class ChargingPointHandler extends ChannelInboundHandlerAdapter  {

    private final Logger logger = LoggerFactory.getLogger(ChargingPointHandler.class);

    private final ChargingChannelListener chargingChannelListener;

    private static final Logger log = LoggerFactory.getLogger(ChargingPointDecoder.class);

    private static ExecutorService chargingEventExecutor;

    static {
        chargingEventExecutor = new ExecutorUtils(50, 100, 60000L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(100000), "hik-charging-iot-executor");
    }
    public ChargingPointHandler(ChargingChannelListener chargingChannelListener){
        this.chargingChannelListener = chargingChannelListener;
    }

    @Override
    public void handlerAdded(ChannelHandlerContext ctx) {
        logger.info("有新的连接：[{}]", ctx.channel().id().asLongText());

    }

    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
        logger.info("Channel heartBeat lost");
        if (evt instanceof IdleStateEvent && IdleState.READER_IDLE == ((IdleStateEvent) evt).state()) {
            ctx.close();
        }
    }

    @Override
    public void channelRegistered(ChannelHandlerContext ctx) throws Exception {
        logger.info("REGISTERED");
        ctx.fireChannelRegistered();
    }

    @Override
    public void channelUnregistered(ChannelHandlerContext ctx) throws Exception {
        logger.info("UNREGISTERED");
        ctx.fireChannelUnregistered();
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        logger.info("ACTIVE");
        ctx.channel().attr(Constant.eventListenerKey).set(chargingChannelListener);
        ctx.fireChannelActive();
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        logger.info("ChargingPointHandler-INACTIVE");

       // 获取channel中id
        String ChargingPointId = ctx.channel().attr(Constant.integrateNoKey).get();
        logger.info("INACTIVE ChargingPointId：{}",ChargingPointId);
        if (chargingChannelListener != null && StringUtils.isNotEmpty(ChargingPointId)){
            logger.info("ChargingPointHandler-通知 IOT 设备离线");
            chargingChannelListener.onDisconnect(ChargingPointId);
            removeChannel(ctx);
        }
        ctx.fireChannelInactive();
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        logger.info("ChargingPointHandler-EXCEPTION");
      //  cause.printStackTrace();
        logger.info("ChargingPointHandler-EXCEPTION"+ JSONObject.toJSONString(cause));
        // 获取channel中id
  /*      String ChargingPointId = ctx.channel().attr(Constant.integrateNoKey).get();
        logger.info("EXCEPTION ChargingPointId：{}",ChargingPointId);
        if (chargingChannelListener != null && StringUtils.isNotEmpty(ChargingPointId)){
            logger.info("ChargingPointHandler-通知 IOT 设备离线");
            chargingChannelListener.onDisconnect(ChargingPointId);
            removeChannel(ctx);
        }*/
        ctx.fireExceptionCaught(cause);
    }

/*    @Override
    public void write(ChannelHandlerContext ctx, Object msg, ChannelPromise promise) throws Exception {
        logger.info("WRITE {}", JSON.toJSONString(msg));
        ctx.write(msg, promise);
    }*/

    private void removeChannel(ChannelHandlerContext ctx) {
        logger.info("ChargingPointHandler-设备离线、开始清除通道：{}",ctx);
        // 获取channel中id
        String noKey = ctx.channel().attr(Constant.integrateNoKey).get();
        logger.info("ChargingPointHandler-设备离线、开始清除通道noKey：{}",noKey);
        // map移除channel
        ChargingPointChannelManager.removeChannelByName(noKey);
        logger.info("ChargingPointHandler-设备离线、清除通道结束");
    }


    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
      /*  ByteBuf byteBuf = (ByteBuf) msg;
        byte[] receivedBytes = new byte[byteBuf.readableBytes()];
        byteBuf.readBytes(receivedBytes);
        // Print received bytes as hex
        StringBuilder sb = new StringBuilder();
        for (byte b : receivedBytes) {
            sb.append(String.format("%02X ", b));
        }
        System.out.println("Received from client: " + sb.toString().trim());*/
/*        System.out.println("Received from client: " + (String)msg);
        byte[] bytesToSend = hexStringToByteArray((String)msg);
        ByteBuf buffer = Unpooled.wrappedBuffer(bytesToSend);*/
        //ctx.fireChannelRead(msg);
        ByteBuf in = (ByteBuf) msg;
        log.info("ChargingPointDecoder---" + BytesUtil.bytesToHexString(ByteBufUtil.getBytes(in)));


        log.info("ChargingPointDecoder---while---begin ---{}", System.currentTimeMillis());
        in.markReaderIndex();
        byte[] inArray = new byte[in.readableBytes()];
        in.getBytes(in.readerIndex(), inArray);

        byte[] newInArray = inDecode(inArray);


        ByteBuf newIn=  Unpooled.wrappedBuffer(newInArray);

        byte preamble = newIn.readByte();
        if (preamble != ProtocolConstants.PREAMBLE) {
            log.info("preamble number is illegal, " + preamble);
            log.info("ChargingPointDecoder---preamble---{}", HexStringUtil.Bytes2hexStr(ByteBufUtil.getBytes(newIn)));
            newIn.clear();
            return;
        }

        byte msgId = newIn.readByte();

        ByteBuf byteBuf = newIn.readBytes(6);
        byte[] bytes = ByteBufUtil.getBytes(byteBuf);

        ByteBuf messageBody = newIn.readBytes(newIn.readableBytes() - 2);


        // 组装消息
        MsgHeader header = new MsgHeader();
        header.setPreamble(preamble);
        header.setMsgId(msgId);
        header.setDeviceNo(BCDUtil.bcdToStr(bytes));

        chargingEventExecutor.submit(() -> {
            //构建消费发送
            processMessageBody(ctx, header, messageBody);
            ReferenceCountUtil.release(messageBody);
        });

        log.info("ChargingPointDecoder---while---end---{}", System.currentTimeMillis());

    }


    public static byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
                    + Character.digit(s.charAt(i+1), 16));
        }
        return data;
    }

    public static byte[] hexStringToByteArray1(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
                    + Character.digit(s.charAt(i + 1), 16));
        }
        return data;
    }

    // 计算字节数组的异或校验
    public static byte calculateXorChecksum(byte[] bytes) {
        byte checksum = 0;
        for (byte b : bytes) {
            checksum ^= b;
        }
        return checksum;
    }




    public byte[] inDecode(byte[] input) {
        // Calculate the length of the output array
        int outputLength = 0;
        for (int i = 0; i < input.length; i++) {
            if (input[i] == (byte) 0x7d && i + 1 < input.length) {
                outputLength++;
                i++; // Skip the next byte as it is part of the escape sequence
            } else {
                outputLength++;
            }
        }

        byte[] output = new byte[outputLength];
        int j = 0;
        for (int i = 0; i < input.length; i++) {
            if (input[i] == (byte) 0x7d && i + 1 < input.length) {
                switch (input[i + 1]) {
                    case 0x02:
                        output[j++] = 0x7e;
                        break;
                    case 0x01:
                        output[j++] = 0x7d;
                        break;
                    default:
                        // Handle unexpected escape sequences by copying them as-is
                        output[j++] = input[i];
                        output[j++] = input[i + 1];
                }
                i++; // Skip the next byte as it is part of the escape sequence
            } else {
                output[j++] = input[i];
            }
        }

        return output;
    }

    private void processMessageBody(ChannelHandlerContext ctx, MsgHeader header, ByteBuf data) {
        // 根据不同的messageId解析消息体
         try {
             MsgType msgTypeEnum = MsgType.findByType(header.getMsgId());
             if (msgTypeEnum == null) {
                 log.info("ChargingPointDecoder---msgTypeEnum---{}", JSON.toJSONString(header));
                 return;
             }
             ChargingPointProtocol<?> chargingPointProtocol = null;
             ChargingChannelListener listener = ctx.channel().attr(Constant.eventListenerKey).get();
             switch (msgTypeEnum) {
                 case deviceState: {
                     chargingPointProtocol = packet(DeviceStateRequestMessage.class, header, data);

                   /*  // 如果充电桩的channel不存在则不进行回应
                     Channel channel = ChargingPointChannelManager.getChannelByName(header.deviceNo);
                     log.info("海康充电桩进行心跳回应:{}", header.deviceNo);
                     if (null == channel) {
                         log.info("海康充电桩的channel不存在则不进行心跳回应");
                         break;
                     }*/
                     Channel channel = ChargingPointChannelManager.getChannelByName(header.deviceNo);
                     log.info("海康充电桩进行心跳回应1:{}", header.deviceNo);
                     if (null == channel) {
                         log.info("通道不存在重新建立通道", header.deviceNo);
                         ChargingPointChannelManager.addChannel(header.deviceNo, ctx.channel());
                     }else{
                         if(!channel.isActive()){
                             log.info("通道未激活重新建立通道", header.deviceNo);
                             ChargingPointChannelManager.addChannel(header.deviceNo, ctx.channel());
                         }
                         log.info("通道状态isActive:"+ channel.isActive());
                         log.info("通道状态isOpen:"+channel.isOpen());
                     }

                     DeviceStateResponeMessage deviceStateResponeMessage = new DeviceStateResponeMessage();
                     ChargingPointProtocol<DeviceStateResponeMessage> response = new ChargingPointProtocol<>();
                     response.setBody(deviceStateResponeMessage);
                     response.setHeader(header);
                     ctx.channel().writeAndFlush(response);
                     log.info("海康充电桩进行心跳回应:{}", header.deviceNo);
                     break;
                 }
                 case DeviceAlarm:
                     chargingPointProtocol = packet(DeviceAlarmRequestMessage.class, header, data);
                     break;
                 case BeginChargingInfo:
                     chargingPointProtocol = packet(BeginChargingRequestMessage.class, header, data);
                     break;
                 case DeviceRegistr:
                     chargingPointProtocol = packet(DeviceRegistrRequestMessage.class, header, data);
                     DeviceRegistrRequestMessage deviceRegistrRequestMessage = (DeviceRegistrRequestMessage) chargingPointProtocol.getBody();


                     Channel oldChannel = ChargingPointChannelManager.getChannelByName(header.deviceNo);
                     if (oldChannel != null) {
                         log.info("存在旧的channel，清理旧的channel配置信息，避免后期旧channel关闭时把新的channel关闭了");
                         oldChannel.attr(Constant.integrateNoKey).remove();
                     }

                     // 存储新channel
                     ChargingPointChannelManager.addChannel(header.deviceNo, ctx.channel());


                     log.info("海康登陆:{}", header.deviceNo);

                     // 将客户端ID作为自定义属性加入到channel中，方便随时channel中获取用户ID
                     ctx.channel().attr(Constant.integrateNoKey).setIfAbsent(header.deviceNo);

                     // iot设备上线
                     if (listener != null) {
                         listener.onConnect(header.deviceNo);
                     }


                     break;
                 case FreeModeChannle:
                     chargingPointProtocol = packet(FreeModeChannleRequestMessage.class, header, data);
                     break;
                 case MeterReading:
                     chargingPointProtocol = packet(MeterReadingRequestMessage.class, header, data);
                     break;
                 case UserSwipeCard:
                     chargingPointProtocol = packet(UserSwipeCardRequestMessage.class, header, data);
                     break;
                 case SIMInfo:
                     chargingPointProtocol = packet(SIMInfoRequestMessage.class, header, data);
                     break;


             }


             if (listener != null) {
                 ObjectMapper objectMapper = new ObjectMapper();
                 objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                 JsonNode node = objectMapper.valueToTree(chargingPointProtocol);
                 listener.onMessage(new JsonTcpConnectorMessage(node));
             }
         }catch (Exception ex){
             ex.printStackTrace();
             log.error("绑定通知异常", ex);
         }
    }

    private void validateChecksum(byte checksum) {
        // 实现校验码的验证
    }


    private static <T extends MessageDecoder> ChargingPointProtocol<T> packet(Class<T> clazz, MsgHeader msgHeader,
                                                                              ByteBuf data) {

        try {
            T t = clazz.getDeclaredConstructor().newInstance();
            t.decode(data);
            ChargingPointProtocol<T> protocol = new ChargingPointProtocol<>();
            protocol.setHeader(msgHeader);
            protocol.setBody(t);
            return protocol;
        } catch (InstantiationException | IllegalAccessException | InvocationTargetException |
                 NoSuchMethodException e) {
            throw new RuntimeException(e);
        }

    }

    private static String bytesToHex(final byte[] bytes) {
        final int numBytes = bytes.length;
        final char[] container = new char[numBytes * 2];

        for (int i = 0; i < numBytes; i++) {
            final int b = bytes[i] & 0xFF;
            container[i * 2] = Character.forDigit(b >>> 4, 0x10);
            container[i * 2 + 1] = Character.forDigit(b & 0xF, 0x10);
        }

        return new String(container);
    }
}