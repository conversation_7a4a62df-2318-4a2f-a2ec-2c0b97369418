package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * @description: TCP远程升级
 * @author: zhonghx
 * @create: 2023-02-14 11:37
 * 充电桩通过主站进行 TCP 软件升级，首先服务器下发擦除指令、升级参数
 * 指令，并下发文件数据，最后充电桩进行软件升级并返回升级结果。TCP 软件
 * 升级具体步骤如下：
 * 1) 运营云平台下发升级参数，充电桩响应；
 * 2) 运营云平台按多包顺序下发文件数据包，充电桩响应直至文件发送完成;
 * 3) 充电桩上传升级结果。
 * 4) 云平台响应。
 * 如果60s没有收到回复，则关闭升级流程。
 **/
@Data
@AllArgsConstructor
public class TcpOTARequest implements MessageEncoder{

    /*程序类型
    * 0.一体机程序
    1.模块柜程序
    3.分体桩程序
    4.充电模块程序
    5.环境监控程序
    6.交流桩程序
    * */
    private byte softType;

    /*文件名称 64*/
    private String fileName;

    /*文件总长度*/
    private int totalSize;

    /*校验码*/
    private int verifyCode;

    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(softType);
        buf.writeBytes(Arrays.copyOf(fileName.getBytes(StandardCharsets.US_ASCII),64));
        buf.writeInt(totalSize);
        buf.writeInt(verifyCode);
        return buf;
    }
}
