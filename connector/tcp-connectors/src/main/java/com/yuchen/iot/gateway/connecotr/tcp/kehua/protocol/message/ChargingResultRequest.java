package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.Data;

import java.nio.charset.StandardCharsets;

/**
 * @description: 启停充电结果上报到平台
 * @author: zhonghx
 * @create: 2023-02-11 18:13
 **/
@Data
public class ChargingResultRequest implements MessageDecoder{

    /*操作类型 1启动 2停止*/
    private int optType;

    /*用户账号 20len ASCII*/
    private String account;

    /*充电流水号 4*/
    private int chargingSN;

    /*确认标识 0成功 1失败*/
    private int ack;
    @Override
    public void decode(ByteBuf in) {
        optType = in.readByte();
        byte[] accountBytes = ByteBufUtil.getBytes(in.readBytes(20));
        account = new String(accountBytes, StandardCharsets.US_ASCII);
        chargingSN = in.readInt();
        ack = in.readByte();
    }
}
