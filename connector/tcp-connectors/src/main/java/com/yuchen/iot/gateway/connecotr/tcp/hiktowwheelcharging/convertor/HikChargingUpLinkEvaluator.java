package com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.convertor;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.MsgType;
import com.yuchen.iot.gateway.connector.api.converter.AbstractKeyEvaluator;
import com.yuchen.iot.gateway.connector.api.converter.evaluator.UpLinkEvaluator;
import com.yuchen.iot.gateway.connector.api.data.UpLinkData;
import com.yuchen.iot.gateway.connector.api.data.UpLinkMetaData;
import com.yuchen.iot.gateway.connector.api.data.message.CallResponse;
import com.yuchen.iot.gateway.connector.api.data.message.Event;
import com.yuchen.iot.gateway.connector.api.data.message.EventValue;
import org.pf4j.Extension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 海康奥能充电桩上行转换器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/28
 */
@Component
@Extension
public class HikChargingUpLinkEvaluator extends AbstractKeyEvaluator implements UpLinkEvaluator {

    private final Logger logger = LoggerFactory.getLogger(HikChargingUpLinkEvaluator.class);

    @Override
    public ListenableFuture<Object> execute(byte[] data, UpLinkMetaData upLinkMetaData) {
        List<UpLinkData> upLinkDataList = new ArrayList<>();
        try {

            JsonNode jsonNode = this.objectMapper.readTree(data);
            JsonNode header = jsonNode.get("header");
            JsonNode body = jsonNode.get("body");

            MsgType msgType = MsgType.findByType(header.get("msgId").asInt());
            if (msgType == null) {
                throw new NullPointerException();
            }
            String integrateNo = header.get("deviceNo").asText();
            Event event = new Event();
            EventValue eventValue = new EventValue();

            CallResponse callResponse = new CallResponse();
            String callId = null;

            if (msgType.getValue().equals("meterReading")) {

                callId = integrateNo.concat("searchElectricMeter");
                logger.info("meterReading充电上报callId==" + callId);
                body.fields().forEachRemaining(json -> {
                    callResponse.put(json.getKey(), json.getValue().asText());
                });
            }else if(msgType.getValue().equals("simInfo")){
                callId = integrateNo.concat("getSIMInfo");
                logger.info("getSIMInfo充电上报callId==" + callId);
                body.fields().forEachRemaining(json -> {
                    callResponse.put(json.getKey(), json.getValue().asText());
                });
            } else {


                event.setIdentifier(msgType.getValue());
                event.setValue(eventValue);
                body.fields().forEachRemaining(json -> {
                    //   eventValue.put(json.getKey(), json.getValue().asText());
                    JsonNode fieldValue = json.getValue();
                    if (fieldValue.isArray()) {
                        eventValue.put(json.getKey(), json.getValue().toString());
                    } else {
                        eventValue.put(json.getKey(), json.getValue().asText());
                    }
                });
            }

            if (null != event.getIdentifier()) {
                logger.info("海康--终端主动上报事件信息---{}---{}---{}", integrateNo, msgType.name(), JSONObject.toJSONString(event));
            }
            if (callResponse.size() > 0) {
                logger.info("海康--平台下发指令响应信息---{}---{}---{}", integrateNo, msgType.name(),
                        JSONObject.toJSONString(callResponse));
            }

            UpLinkData upLinkData = new UpLinkData(integrateNo, event, null, callResponse, callId, null);
            upLinkDataList.add(upLinkData);

        } catch (IOException | NullPointerException e) {
            e.printStackTrace();
            logger.warn("Evaluator execute failed {}", e.getMessage());
        }
        return Futures.immediateFuture(this.gson.toJson(upLinkDataList));
    }

    @Override
    public String getKey() {
        return "towWheel-charging-up-link";
    }

    @Override
    public void destroy() {
        // do nothing
    }
}
