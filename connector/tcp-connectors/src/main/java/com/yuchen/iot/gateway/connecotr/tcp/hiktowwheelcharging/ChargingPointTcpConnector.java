package com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging;


import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.yuchen.iot.gateway.connecotr.tcp.AbstractTcpServerConnector;
import com.yuchen.iot.gateway.connecotr.tcp.JsonTcpConnectorMessage;
import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.ChargingPointProtocol;
import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.MsgHeader;
import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.MsgType;
import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.down.*;
import com.yuchen.iot.gateway.connecotr.tcp.isapi.netty.ISAPIEvent;
import com.yuchen.iot.gateway.connector.api.ConnectorInitParams;
import com.yuchen.iot.gateway.connector.api.WaitCallResponseCallback;
import com.yuchen.iot.gateway.connector.api.data.DownLinkData;
import com.yuchen.iot.gateway.connector.api.data.UpLinkData;
import com.yuchen.iot.gateway.connector.api.data.UpLinkMetaData;
import com.yuchen.iot.gateway.connector.api.message.ThingMessage;
import com.yuchen.iot.gateway.dao.integration.MetaConfig;
import io.netty.channel.Channel;
import io.netty.channel.ChannelInitializer;
import org.pf4j.Extension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.Executors;

@Extension
public class ChargingPointTcpConnector extends AbstractTcpServerConnector<JsonTcpConnectorMessage> {

    private static final Logger log = LoggerFactory.getLogger(ChargingPointTcpConnector.class);

    private final Map<String, Channel> channels = new HashMap<>();

    /**
     * 定义默认端口号
     */
    private int PORT = 2404;

    @Override
    public void init(ConnectorInitParams params) {
        super.init(params);
        ListeningExecutorService pool = MoreExecutors.listeningDecorator(Executors.newFixedThreadPool(2));
        // 获取自定义配置
        MetaConfig configuration = params.getConfiguration().getConfiguration();
        // 如果端口号不为空则继续执行
        if (configuration.containsKey("port")) {
            // 获取端口号
            PORT = (int) configuration.get("port");
            ListenableFuture<Boolean> future = pool.submit(this::startServer);
            future.addListener(this::startServer, pool);
        }
    }

    @Override
    protected ChannelInitializer<?> getChannelInitializer() {
        //return new ChargingPointChannelInitializer(/*new ChargingPointEncoder(), new ChargingPointDecoder()*/);
        return new ChargingPointChannelInitializer(new ChargingChannelListener<JsonTcpConnectorMessage>() {
            @Override
            public void onDisconnect(String chargingPointId) {
                getUpLinkDataListener(chargingPointId).onSouthDisconnect();
            }

            @Override
            public void onConnect(String chargingPointId) {
                getUpLinkDataListener(chargingPointId).onSouthConnect();
            }

            @Override
            public void onMessage(JsonTcpConnectorMessage event) {
                process(event);
            }
        });
    }

    /**
     * @see ISAPIEvent
     */
    protected void doProcess(JsonTcpConnectorMessage message) throws Exception {
        // TODO 需要过滤一遍消息，文件单独上传
        JsonNode json = mapper.readTree(message.getMessageInBytes());
        List<UpLinkData> upLinkDataList = this.convert(message);
        if (upLinkDataList != null && !upLinkDataList.isEmpty()) {
            for (UpLinkData upLinkData : upLinkDataList) {
                this.processUpLinkData(upLinkData);
                log.trace("[{}] Processing up link data", upLinkData);
            }
        }
    }

    protected List<UpLinkData> convert(JsonTcpConnectorMessage message) throws Exception {
        byte[] data = message.getMessageInBytes();
        Map<String, String> mdMap = new HashMap<>(this.metadataTemplate.getKvMap());
        return this.convertToUpLinkDataList(this.connectorContext.getCallbackExecutor(), data,
                new UpLinkMetaData(message.getContentType(), mdMap));
    }

    private Boolean startServer() {
        try {
            log.info("start hik charging server port : {}", PORT);
            // Make the connection attempt.
            this.bootstrap.bind(PORT).addListener(future -> {
                if (future.isSuccess()) {
                    // connect success
                    log.info("start hik charging server successfully port : {}", PORT);
                } else {
                    future.cause().printStackTrace();
                }
            }).sync().channel().closeFuture().sync();

        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

    @Override
    protected void doListenOffEndpoint(String deviceKey, Map<String, String> endpointMeta) {
        if (this.channels.containsKey(deviceKey)) {
            Channel channel = this.channels.get(deviceKey);
            if (channel != null) {
                channel.close().addListener(future -> {
                    if (future.isSuccess()) {
                        this.getUpLinkDataListener(deviceKey).onSouthDisconnect();
                    } else {
                        future.cause().printStackTrace();
                    }
                });
            }
        }
    }


    @Override
    public void onDownLinkMessage(ThingMessage message, WaitCallResponseCallback callback) {
        if (this.downLinkConverter != null) {
            this.processDownLinkMessage(message, callback);
        }
    }

    @Override
    protected void doListenOnEndpoint(String deviceKey, Map<String, String> endpointMeta) {

    }

    /**
     * 处理下行链路消息
     *
     * @param message  物模型消息
     * @param callback 回调
     */
    private void processDownLinkMessage(ThingMessage message, WaitCallResponseCallback callback) {
        Map<String, String> mdMap = new HashMap<>(this.metadataTemplate.getKvMap());
        try {
            List<DownLinkData> result = this.downLinkConverter
                    .convertDownLink(Collections.singletonList(message), mdMap);
            if (!result.isEmpty()) {
                DownLinkData downLink = result.get(0); // 设计上只支持单个指令下发
                if (!downLink.isEmpty()) {
                    Map<String, String> metadata = downLink.getMetadata();
                    String identifier = metadata.get("identifier");
                    JsonNode jsonNode = mapper.readTree(downLink.getData());
                    ChargingPointProtocol obj = packetObjectFromJson(message.getIntegrateNo(), identifier, jsonNode);
                    ChargingPointChannelManager.sendMessageToSN(message.getIntegrateNo(), obj);

                    if (callback != null) {
                        if(identifier.equals("searchElectricMeter")||identifier.equals("getSIMInfo")) {

                            String key=message.getIntegrateNo().concat(identifier);
                            log.info("searchElectricMeter等待响应key===" +key);
                            callback.waitForResponse(key, downLink);
                        }else{
                            Map<String, String> respon = new HashMap<>();
                            callback.responseNow(respon);
                        }
                    }
                    log.info("海康--平台下发指令---" + message.getIntegrateNo() + "---" + identifier + "---" + JSONObject
                            .toJSONString(obj));
                }
            }
        } catch (Exception e) {
            log.warn("Failed to process downLink message", e);
        }
    }

    /**
     * 下行充电桩协议转换
     *
     * @param integrateNo 设备唯一编码
     * @param identifier  下行消息类别
     * @param json        下行数据
     * @return
     */
    public ChargingPointProtocol packetObjectFromJson(String integrateNo, String identifier, JsonNode json) {
        MsgType msgType = MsgType.findByValue(identifier);
        if (msgType == null) {
            return null;
        }

        // 添加固定的消息头
        MsgHeader header = new MsgHeader();
        header.setPreamble(0x7e);
        header.setMsgId(msgType.getType());

        try {
            ChargingPointProtocol chargingPointProtocol = new ChargingPointProtocol<>();
            switch (msgType) {
                case StartCharging: // 开始充电

                    StartChargingDownMessage startChargingDownMessage = new StartChargingDownMessage(json.get("channel").asInt());
                    chargingPointProtocol.setBody(startChargingDownMessage);
                    chargingPointProtocol.setHeader(header);
                    break;
                case StopCharging:// 停止充电

                    StopChargingDownMessage startStopChargingDownMessage = new StopChargingDownMessage(json.get("channel").asInt());
                    chargingPointProtocol.setBody(startStopChargingDownMessage);
                    chargingPointProtocol.setHeader(header);
                    break;
                case SettingMaxCurrent: // 设置最大电流

                    SettingMaxCurrentDownMessage settingMaxCurrentUpMessage = new SettingMaxCurrentDownMessage(json.get("maxCurrent").asInt());
                    chargingPointProtocol.setBody(settingMaxCurrentUpMessage);
                    chargingPointProtocol.setHeader(header);
                    // 解析设备状态消息
                    break;
                case DeviceRestart:// 设备重启

                    DeviceRestartDownMessage disconnectDownMessage = new DeviceRestartDownMessage();
                    chargingPointProtocol.setBody(disconnectDownMessage);
                    chargingPointProtocol.setHeader(header);
                    break;

                case SettingVolume:// 设置音量

                    SettingVolumeDownMessage setVolumeDownMessage = new SettingVolumeDownMessage(json.get("volume").asInt());
                    chargingPointProtocol.setBody(setVolumeDownMessage);
                    chargingPointProtocol.setHeader(header);

                    break;
                case ClosedVolume:// 关闭音量

                    ClosedVolumeDownMessage closedVolumeDownMessage = new ClosedVolumeDownMessage();
                    chargingPointProtocol.setBody(closedVolumeDownMessage);
                    chargingPointProtocol.setHeader(header);

                    break;
                case OpenSmokeAlarm:// 打开烟感报警

                    OpenSmokeAlarmDownMessage openSmokeAlarmDownMessage = new OpenSmokeAlarmDownMessage();
                    chargingPointProtocol.setBody(openSmokeAlarmDownMessage);
                    chargingPointProtocol.setHeader(header);

                    break;
                case ClosedSmokeAlarm:// 关闭烟感报警

                    ClosedSmokeAlarmDownMessage closedSmokeAlarmDownMessage = new ClosedSmokeAlarmDownMessage();
                    chargingPointProtocol.setBody(closedSmokeAlarmDownMessage);
                    chargingPointProtocol.setHeader(header);

                    break;
                case OpenPanelLights:// 打开面板灯

                    OpenPanelLightsDownMessage openPanelLightsDownMessage = new OpenPanelLightsDownMessage();
                    chargingPointProtocol.setBody(openPanelLightsDownMessage);
                    chargingPointProtocol.setHeader(header);

                    break;
                case ClosedPanelLights:// 关闭面板灯

                    ClosedPanelLightsDownMessage closedPanelLightsDownMessage = new ClosedPanelLightsDownMessage();
                    chargingPointProtocol.setBody(closedPanelLightsDownMessage);
                    chargingPointProtocol.setHeader(header);

                    break;
                case OpenUnplugPowerOff:// 打开拔出断电

                    OpenUnplugPowerOffDownMessage openUnplugPowerOffDownMessage = new OpenUnplugPowerOffDownMessage();
                    chargingPointProtocol.setBody(openUnplugPowerOffDownMessage);
                    chargingPointProtocol.setHeader(header);

                    break;
                case ClosedUnplugPowerOff:// 关闭拔出断电

                    ClosedUnplugPowerOffDownMessage closedUnplugPowerOffDownMessage = new ClosedUnplugPowerOffDownMessage();
                    chargingPointProtocol.setBody(closedUnplugPowerOffDownMessage);
                    chargingPointProtocol.setHeader(header);
                    break;
                case OpenSoftwareOverCurrent:// 打开软件过流监测

                    OpenSoftwareOvercurrentDownMessage openSoftwareOvercurrentDownMessage = new OpenSoftwareOvercurrentDownMessage();
                    chargingPointProtocol.setBody(openSoftwareOvercurrentDownMessage);
                    chargingPointProtocol.setHeader(header);

                    break;
                case ClosedSoftwareOverCurrent:// 关闭软件过流监测

                    ClosedSoftwareOvercurrentDownMessage closedSoftwareOvercurrentDownMessage = new ClosedSoftwareOvercurrentDownMessage();
                    chargingPointProtocol.setBody(closedSoftwareOvercurrentDownMessage);
                    chargingPointProtocol.setHeader(header);

                    break;

                case SettingDisconnectEnteringCount:// 设置断电充电

                    SettingDisconnectEnteringCountDownMessage settingDisconnectEnteringCountDownMessage = new SettingDisconnectEnteringCountDownMessage(json.get("enteringCount").asInt());
                    chargingPointProtocol.setBody(settingDisconnectEnteringCountDownMessage);
                    chargingPointProtocol.setHeader(header);

                    break;
                case SearchElectricMeter:// 查询电表数据

                    SearchElectricMeterDownMessage searchElectricMeterDownMessage = new SearchElectricMeterDownMessage();
                    chargingPointProtocol.setBody(searchElectricMeterDownMessage);
                    chargingPointProtocol.setHeader(header);

                    break;
                case SettingTrickleTime:// 设置 涓流时间

                    SettingTrickleTimeDownMessage settingTrickleTimeDownMessage = new SettingTrickleTimeDownMessage(json.get("trickleTime").asInt());
                    chargingPointProtocol.setBody(settingTrickleTimeDownMessage);
                    chargingPointProtocol.setHeader(header);
                    break;
                case SettingDeviceTemperatureAlarm:// 设置设备温度报警

                    SettingDeviceTemperatureAlarmDownMessage settingDeviceTemperatureAlarmUpMessage = new SettingDeviceTemperatureAlarmDownMessage(json.get("temperatureAlarmValue").asInt());
                    chargingPointProtocol.setBody(settingDeviceTemperatureAlarmUpMessage);
                    chargingPointProtocol.setHeader(header);

                    break;
                case SettingCableTemperatureAlarm:// 设置电缆温度报警

                    SettingCableTemperatureAlarmDownMessage settingCableTemperatureAlarmDownMessage = new SettingCableTemperatureAlarmDownMessage(json.get("cableTemperatureAlarmValue").asInt());
                    chargingPointProtocol.setBody(settingCableTemperatureAlarmDownMessage);
                    chargingPointProtocol.setHeader(header);

                    break;
                case OpenFullAutoStop:// 打开满流自动停止

                    OpenFullAutoStopMessage openFullAutoStopMessage = new OpenFullAutoStopMessage();
                    chargingPointProtocol.setBody(openFullAutoStopMessage);
                    chargingPointProtocol.setHeader(header);

                    break;
                case ClosedFullAutoStop:// 关闭满流自动停止

                    ClosedFullAutoStopMessage closedFullAutoStopMessage = new ClosedFullAutoStopMessage();
                    chargingPointProtocol.setBody(closedFullAutoStopMessage);
                    chargingPointProtocol.setHeader(header);

                    break;
                case SettingEnteringTrickleCurrent:// 设置进入涓流电流

                    SettingEnteringTrickleCurrentDownMessage settingEnteringTrickleCurrentUpMessage = new SettingEnteringTrickleCurrentDownMessage(json.get("trickleCurrentValue").asInt());
                    chargingPointProtocol.setBody(settingEnteringTrickleCurrentUpMessage);
                    chargingPointProtocol.setHeader(header);

                    break;
                case SettingDisconnectEntering:// 设置断电充电值

                    SettingDisconnectEnteringDownMessage settingDisconnectEnteringDownMessage = new SettingDisconnectEnteringDownMessage(json.get("enteringValue").asInt());
                    chargingPointProtocol.setBody(settingDisconnectEnteringDownMessage);
                    chargingPointProtocol.setHeader(header);
                    // 解析设备状态消息
                    break;
                case OpenTemperatureAlarm:// 打开温度报警

                    OpenTemperatureAlarmDownMessage openTemperatureAlarmDownMessage = new OpenTemperatureAlarmDownMessage();
                    chargingPointProtocol.setBody(openTemperatureAlarmDownMessage);
                    chargingPointProtocol.setHeader(header);
                    break;
                case ClosedTemperatureAlarm:// 关闭温度报警

                    ClosedTemperatureAlarmDownMessage closedTemperatureAlarmDownMessage = new ClosedTemperatureAlarmDownMessage();
                    chargingPointProtocol.setBody(closedTemperatureAlarmDownMessage);
                    chargingPointProtocol.setHeader(header);

                    break;
                case SettingTemperatureAlarmTime:// 设置温度报警时间

                    SettingTemperatureAlarmTimeDownMessage settingTemperatureTimeDownMessage = new SettingTemperatureAlarmTimeDownMessage(json.get("cableTemperatureAlarmTime").asInt());
                    chargingPointProtocol.setBody(settingTemperatureTimeDownMessage);
                    chargingPointProtocol.setHeader(header);

                    break;
                case GetSIMInfo:// 获取SIM卡信息

                    GetSIMInfoMessage getSIMInfoMessage = new GetSIMInfoMessage();
                    chargingPointProtocol.setBody(getSIMInfoMessage);
                    chargingPointProtocol.setHeader(header);

                    break;

                case SwipeCardBeginCharging:// 刷卡充电回复开始充电

                    SwipeCardBeginChargingDownMessage swipeCardBeginChargingDownMessage = new SwipeCardBeginChargingDownMessage(
                            json.get("type").asInt(),
                            json.get("cardType").asInt(),
                            json.get("money").asInt(),
                            json.get("channel").asInt()
                    );
                    chargingPointProtocol.setBody(swipeCardBeginChargingDownMessage);
                    chargingPointProtocol.setHeader(header);

                    break;
                default:

            }
            return chargingPointProtocol;
        } catch (Exception e) {
            log.error(e.toString());
        }
        return null;
    }

    @Override
    public void destroy() {

    }


}
