package com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.protocol.message;

import com.yuchen.iot.gateway.connecotr.tcp.chargepile.util.BytesUtil;
import com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.BCDUtil;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.util.HexStringUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.Data;

/**
 * @description: 终端充电结束/交易记录，终端因为用户操作，车辆充满，故障等原因会上报充电结束
 * 本指令同时也作为最终的交易记录用于费用结算，其他指令的费用数据均为中间过程数据，仅供参考，终端会反复发送充电结束
 * 直到收到应答。不论终端充电结束的充电流水号是否正确有效，主站都要给与应答，否则终端会反复发送。
 * @author: zhonghx
 * @create: 2022-12-09 18:29
 **/
@Data
public class ClientStopChargingRequestMessage implements MessageDecoder{

    /*枪号 1-n*/
    private byte gunNo;

    /*用户账号或卡号， 16字节BCD*/
    private String userOrCardNo;

    /*充电流水号BCD*/
    private String serialNumber;

    /*费率1起始电表读数*/
    private int rate1BeginElectricity;

    /*费率2起始电表读数*/
    private int rate2BeginElectricity;

    /*费率3起始电表读数*/
    private int rate3BeginElectricity;

    /*费率4起始电表读数*/
    private int rate4BeginElectricity;

    /*总起始电表读数*/
    private int mainMeterBeginElectricity;

    /*本次充电总电量*/
    private int totalChargingElectricity;

    /*本次充电总时长，秒单位*/
    private int totalChargingDuration;

    /*充电总费用，单位元，2位小数*/
    private int totalCharge;

    /*服务费用*/
    private int serviceCharge;

    /*费率1充电费用*/
    private int rate1ChargingCharge;

    /*费率2充电费用*/
    private int rate2ChargingCharge;

    /*费率3充电费用*/
    private int rate3ChargingCharge;

    /*费率4充电费用*/
    private int rate4ChargingCharge;

    /*充电停止原因*/
    private String stopChargingReason;

    @Override
    public void decode(ByteBuf in) {
        gunNo = in.readByte();
        byte[] userOrCardNoBytes = ByteBufUtil.getBytes(in.readBytes(16));
        userOrCardNo = BCDUtil.bcdToStr(userOrCardNoBytes);
        byte[] serialNumberBytes = ByteBufUtil.getBytes(in.readBytes(8));
        serialNumberBytes = BytesUtil.bytesReverse(serialNumberBytes);
        serialNumber = BCDUtil.bcdToStr(serialNumberBytes);
        rate1BeginElectricity = in.readIntLE();
        rate2BeginElectricity = in.readIntLE();
        rate3BeginElectricity = in.readIntLE();
        rate4BeginElectricity = in.readIntLE();
        mainMeterBeginElectricity = in.readIntLE();
        totalChargingElectricity = in.readIntLE();
        totalChargingDuration = in.readIntLE();
        totalCharge = in.readIntLE();
        serviceCharge = in.readIntLE();
        rate1ChargingCharge = in.readIntLE();
        rate2ChargingCharge = in.readIntLE();
        rate3ChargingCharge = in.readIntLE();
        rate4ChargingCharge = in.readIntLE();
        stopChargingReason = HexStringUtil.Bytes2hexStr(ByteBufUtil.getBytes(in.readBytes(1)));
    }
}
