package com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.protocol.message;

import io.netty.buffer.ByteBuf;
import lombok.Data;

/**
 * @description: 终端应答主站的复位指令
 * @author: zhonghx
 * @create: 2022-12-09 19:23
 **/
@Data
public class ResetResponseMessage implements MessageDecoder{

    /*复位结果，6成功 0失败*/
    private byte result;

    @Override
    public void decode(ByteBuf in) {
        result = in.readByte();
    }
}
