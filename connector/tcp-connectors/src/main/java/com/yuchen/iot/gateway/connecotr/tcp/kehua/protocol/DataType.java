package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol;

public enum DataType {

    /* 无符号8位整型 */
    UINT_8(0x1),

    /* 无符号 16 位整型 */
    UINT_16(0x2),

    /* 无符号 32 位整型 */
    UINT_32(0x3),

    /* 传输先后顺序为 0x12,0x00 */
    BCD(0x4),

    /* 字符型 */
    ASCII(0x5),

    /* 保留 */
    RESERVED(0x6);

    private int type;
    DataType(int code)
    {
        this.type = code;
    }

    public int getType()
    {
        return type;
    }

    public static DataType codeOf(int type) {
        for (DataType fdType : values()) {
            if (fdType.getType() == type) {
                return fdType;
            }
        }
        return UINT_8;
    }

}
