package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.ProtocolConstants;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.util.HexStringUtil;
import io.netty.buffer.ByteBuf;
import lombok.Data;


/**
 * @description: 登录请求
 * @author: zhong
 * @create: 2022-12-08 16:55
 * 1 起始帧头 2 ASCII，eg：0x4B 0x48
 * 2 帧长 2 整帧总长度，eg：0x00 0x3A
 * 3 协议版本 2 两位小数，0x01 0x96 表示 4.06
 * 4 报文流水号 2 发送端自加 1，接收端原值返回
 * 5 设备类型 1 参考附录 5.5
 * 6 设备串号 20 ASCII
 * 7 加密方式 1 默认：0x00
 * 8 功能码 1 参考 1.4
 * 9 设备 ID 2 默认 0x00 0x01
 * 10
 * 枪号 1
 * 0x00.终端
 * 0x01.枪号 1
 * 0x02.枪号 2
 * 11 信息域 N 功能码对应的信息域
 * 12 校验 2 用 CRC16 作为校验，校验的区域为帧头到信
 * 息域。
 * 13 结束字节 1 0x68
 * 4B 48 00 3A 01 96 7E 72 05 35 36 31 35 31 30 30 30 35 34 39 30 4A 35 43 30 30 30
 * 30 31 00 01 00 00 00 02 FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF
 * FB 5C 68
 *
 * 4B 48 00 2D 01 96 7E 72 05 35 36 31 35 31 30 30 30 35 34 39 30 4A 35 43 30 30 30
 * 30 31 00 81 00 00 00 01 20 20 03 05 10 19 52 73 63 68
 **/

@JsonIgnoreProperties(value = { "handler" })
@Data
public class LoginRequest implements MessageDecoder {

    /*总枪数*/
    private int gunCount;

    /*父设备串号，全部传0xFF*/
    private String devSerialNumber;

    @Override
    public void decode(ByteBuf in) {

        gunCount = in.readByte();
        byte[] bytes = new byte[ProtocolConstants.DEV_SN_LENGTH];
        in.readBytes(bytes);
        devSerialNumber = HexStringUtil.bytesToHex(bytes);
    }
}
