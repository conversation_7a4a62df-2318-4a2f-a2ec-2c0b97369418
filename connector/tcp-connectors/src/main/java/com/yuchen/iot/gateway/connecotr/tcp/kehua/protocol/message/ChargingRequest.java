package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * @description: 平台控制启停充电
 * @author: zhonghx
 * @create: 2023-02-11 16:54
 **/
@Data
@AllArgsConstructor
public class ChargingRequest implements MessageEncoder{

    /*操作类型
    * 1:  充电启动
    * 2： 充电停止
    * 3:  放电启动
    * 4:  放电停止
    * */
    private int optType;

    /*辅源电压 0:12V（默认） 1:24V*/
    private int voltage;

    /*充电策略
    * 0. 充满为止（默认）
    * 1. 时间控制充电
    * 2. 金额控制充电
    * 3. 电量控制充电
    * 4. 电流控制充电
    * */
    private int chargingStrategy;

    /*充电策略参数
    * 0.
    * 1.时间单位为1秒
    * 2.金额单位为0.01元
    * 3.电量时单位为0.01KW
    * 4.电流时单位为0.01A
    * */
    private int params;


    /*用户账号 20len ASCII*/
    private String account;

    /*充电流水号 平台生成后下发*/
    private int chargingSN;

    /*余额 单位为分
    * 充电策略为自动充满才去判定余
    * 额，其他策略余额后台默认下发
    * 500.00 元。*/
    private int balance;

    /*停止校验码 手机后四位*/
    private int verifyCode;

    /*启动功率 KW*/
    private int power;
    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(optType);
        buf.writeByte(voltage);
        buf.writeByte(chargingStrategy);
        buf.writeInt(params);
        buf.writeBytes(account.getBytes(StandardCharsets.US_ASCII));
        buf.writeInt(chargingSN);
        buf.writeFloat(balance);
        buf.writeShort(verifyCode);
        buf.writeShort(power);
        return buf;
    }
}
