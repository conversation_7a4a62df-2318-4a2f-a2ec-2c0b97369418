package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.DataType;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.TerminalData;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.util.BCDUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.Data;

import java.util.HashMap;

/**
 * @description: 终端应答终端数据读取指令
 * @author: zhonghx
 * @create: 2023-02-16 14:32
 **/
@Data
public class GetTerminalDataResponse implements MessageDecoder {

    /*
     * 0失败
     * 1成功
     * */
    private byte ack;

    /*数据单元个数*/
    private byte dataCount;

    /*
     * name--value
     * */
    private HashMap<String, Object> dataMap = new HashMap<>();

    @Override
    public void decode(ByteBuf in) {
        ack = in.readByte();
        dataCount = in.readByte();
        for (int i = 0; i < dataCount; i++) {

            int id = in.readShort();
            int len = in.readByte();

            TerminalData data = TerminalData.idOf(id);
            DataType dataType = data.getType();
            if (dataType == DataType.UINT_8 ){
                Integer value = (int) in.readByte();
                dataMap.put(data.getKey(),value);
            }else if (dataType == DataType.UINT_16 ){
                Integer value = (int) in.readShort();
                dataMap.put(data.getKey(),value);
            }else if (dataType == DataType.UINT_32 ){
                Integer value = (int) in.readInt();
                dataMap.put(data.getKey(),value);
            }else if (dataType == DataType.BCD){
                byte[] timeBytes = ByteBufUtil.getBytes(in.readBytes(len));
                String time = BCDUtil.bcdToStr(timeBytes);
                dataMap.put(data.getKey(),time);
            }else if(dataType == DataType.ASCII){
                byte[] contentBytes = ByteBufUtil.getBytes(in.readBytes(len));
                String content = new String(contentBytes);
                dataMap.put(data.getKey(),content);
            }else if(dataType == DataType.RESERVED){
                byte[] b = ByteBufUtil.getBytes(in.readBytes(len));
            }else {
                throw new IllegalArgumentException("dataType is illegal" );
            }
        }

    }
}
