package com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.request;

import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.message.MessageDecoder;
import io.netty.buffer.ByteBuf;
import lombok.Data;

@Data
public class FreeModeChannleRequestMessage implements MessageDecoder {

    private int channel ;


    @Override
    public void decode(ByteBuf in) {
         channel = in.readByte();
    }
}
