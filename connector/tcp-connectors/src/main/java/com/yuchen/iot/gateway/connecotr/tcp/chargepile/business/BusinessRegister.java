package com.yuchen.iot.gateway.connecotr.tcp.chargepile.business;

import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

@Component
public class BusinessRegister implements ApplicationListener {
    @Resource
    private Map<String,IBusiness> businessMap;
    @Resource
    private BusinessHolder businessHolder;

    @Override
    public void onApplicationEvent(ApplicationEvent event) {
        businessMap.forEach((k,v)->{
            businessHolder.register(v.type(),v);
        });

    }
}
