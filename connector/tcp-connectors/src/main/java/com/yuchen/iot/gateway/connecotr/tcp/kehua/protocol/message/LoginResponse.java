package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.BCDUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

import static com.yuchen.iot.gateway.connecotr.tcp.kehua.util.BCDUtil.timestampToBcdString;


/**
 * @description: 平台应答终端登录请求
 * @author: zhong
 * @create: 2022-12-08 16:55
 **/
@Data
@AllArgsConstructor
public class LoginResponse implements MessageEncoder {

    /*登入结果，
    0. 确认失败
    1. 确认成功
    2. 串号未注册
    3. 设备类型未知
    4. 帧长度错误
    5. 帧校验码错误
    * */
    private byte ack;

    private int time;

    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(ack);
        //TODO sec to BCD
        //byte[] t = bcdStringToByteArray(timestampToBcdString(time));
        String timeStr = timestampToBcdString(time);
        byte[] timeByte = BCDUtil.strToBcd(timeStr);
        buf.writeBytes(timeByte);
        return buf;
    }
}

