package com.yuchen.iot.gateway.connecotr.tcp;

import com.yuchen.iot.gateway.connector.api.controller.HttpConnectorMessage;
import com.yuchen.iot.gateway.connector.api.data.UpLinkContentType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.async.DeferredResult;

import java.util.Map;

/**
 * TODO 
 * <AUTHOR>
 * @date 2022/10/26 下午5:22
 * @version 2.0.0
 */
public class BinaryTcpConnectorMessage extends TcpConnectorMessage<byte[]> {

    public BinaryTcpConnectorMessage(byte[] message) {
        super(message);
    }

    public UpLinkContentType getContentType() {
        return UpLinkContentType.BINARY;
    }

    public byte[] getMessageInBytes() {
        return this.message;
    }
}
