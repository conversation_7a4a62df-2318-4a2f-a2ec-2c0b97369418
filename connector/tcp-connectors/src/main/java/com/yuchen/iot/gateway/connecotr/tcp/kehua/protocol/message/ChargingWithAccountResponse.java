package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @description: 服务器应答账号充电
 * @author: zhonghx
 * @create: 2023-02-15 14:59
 **/

@Data
@AllArgsConstructor
public class ChargingWithAccountResponse implements MessageEncoder
{
    /*请求结果  0失败  1成功*/
    private byte ack;

    /*用户卡号*/
    private String cardNumber;

    /*用户余额
    * 两位小数，单位：分
    0x1234 = 4660(Dec) = 46.60 元
    * */
    private int balance;

    /*充电流水号*/
    private int chargingSN;

    /*停止校验码*/
    private short verifyCode;

    /*失败原因
    *请求结果为 0 时有效
    请求结果为 0 时有效
    1：账户不存在；
    2：账户密码不匹配
    3：账户余额不足
    4：该账户正在充电中
    5：账户已禁用
    * */
    private byte reason;


    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        return buf;
    }
}
