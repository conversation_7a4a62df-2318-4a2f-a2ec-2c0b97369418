package com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.protocol.message;

import io.netty.buffer.ByteBuf;
import lombok.Data;

/**
 * @description: VIN码启动充电，由充电桩主动获取车辆VIN发起鉴权通过开始启动充电，需要车辆先进行VIN鉴权
 * @author: zhonghx
 * @create: 2022-12-09 17:53
 **/
@Data
public class VinStartChargingRequestMessage implements MessageDecoder{

    /*枪号 1-n*/
    private byte gunNo;

    /*VIN， 17字节BCD*/
    private String VIN;

    /*普通用户  2集团用户*/
    private byte cardType;

    /*用户来源*/
    private byte from;

    /*充电方式， 0x01按金额（元）， 0x02按电量（度），0x03按时间（分钟）*/
    private byte chargingType;

    /*充电方式附带值， 按金额：充电金额，单位元，2位小数
     *               按电量，充电电量，单位kw/h
     *               按时间，充电时间，单位分*/
    private short chargingValue;


    @Override
    public void decode(ByteBuf in) {
        gunNo = in.readByte();
        VIN = in.readBytes(17).toString();
        cardType = in.readByte();
        from = in.readByte();
        chargingType = in.readByte();
        chargingValue = in.readShortLE();
    }
}
