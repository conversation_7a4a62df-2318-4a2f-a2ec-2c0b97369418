package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol;

import lombok.Getter;

/**
 * @description: 终端数据，包括基本信息、BMS数据，环境监控数据，模块数据，终端器件数据
 * @author: zhonghx
 * @create: 2023-02-11 10:14
 **/
public enum TerminalData {

    DEV_TYPE(0x0001, 1, "devType", DataType.UINT_8),                                        /*设备类型 1*/
    DEV_SN(0x0002, 20, "devSN", DataType.ASCII),                                            /*设备串号 ASCII 码，35 36 31 35 31 30 20字节*/
    DEV_NUMBER(0x0003, 4, "devID", DataType.RESERVED),                                      /*预留 4*/
    HEART_BEAT_CYCLE(0x0004, 2, "heartBeatCycle", DataType.UINT_16),                        /*心跳间隔 2*/
    SOFTWARE_VERSION(0x0005, 10, "softwareVersion", DataType.ASCII),                        /*软件版本 10*/
    HARDWARE_VERSION(0x0006, 10, "hardwareVersion", DataType.ASCII),                        /*硬件版本 10*/
    SERVER_ADDRESS(0x0007, 50, "serverAddress", DataType.ASCII),                            /*服务器地址 50*/
    SERVER_PORT(0x0008, 2, "serverPort", DataType.UINT_16),                                 /*服务器端口 2*/
    SERVER_ADDRESS_BACKUP(0x0009, 4, "serverAddressBackup", DataType.UINT_32),              /*备用服务器IP地址 4 预留*/
    SERVER_PORT_BACKUP(0x000A, 2, "serverPortBackup", DataType.UINT_16),                    /*备用服务器端口 2*/
    LOCAL_IP(0x000B, 4, "localIP", DataType.UINT_32),                                       /*本地IP地址 4*/
    LOCAL_MASK(0x000C, 4, "localMask", DataType.ASCII),                                     /*子网掩码 4*/
    LOCAL_GATEWAY(0x000D, 4, "localGateway", DataType.ASCII),                               /*本地网关 4*/
    LOCAL_MAC(0x000E, 6, "localMac", DataType.ASCII),                                       /*本地MAC地址 6*/
    SYSTEM_TIME(0x000F, 7, "systemTime", DataType.BCD),                                     /*系统时间 BCD 7*/
    AD_SCREEN_SWITCH(0x0010, 1, "adScreenSwitch", DataType.UINT_8),                         /*广告灯屏开关 1 0关 1开*/
    PWM_DUTY_CYCLE(0x0011, 1, "pwmDutyCycle", DataType.UINT_8),                             /*PWM 占空比 1*/
    GUN_LOCK_SWITCH(0x0012, 1, "gunLockSwitch", DataType.UINT_8),                           /*枪锁控制 0解锁 1锁定 1*/
    FLOOR_LOCK_SWITCH(0x0013, 1, "floorClockSwitch", DataType.UINT_8),                      /*地锁控制 0解锁 1锁定 1*/
    SETTLEMENT_TYPE(0x0014, 1, "settlementType", DataType.UINT_8),                          /*结算方式 0本地结算 1远程结算 1*/
    MEASUREMENT_TYPE(0x0015, 1, "measurementType", DataType.UINT_8),                        /*计量方式 0分时段计量 1.统一电价 1*/
    PARKING_FEE(0x0016, 1, "parkingFee", DataType.UINT_8),                   /*是否加收停车费 0不加收 1加收*/
    PARKING_UNIT_PRICE(0x0017, 2, "parkingUintPrice", DataType.UINT_16),     /*停车费单价*/
    AD_SCREEN_START_TIME(0x0018, 7, "adScreenStartTime", DataType.ASCII),                   /*广告屏/灯开启时间 BCD 7*/
    AD_SCREEN_CLOSE_TIME(0x0019, 7, "adScreenCloseTime", DataType.ASCII),                   /*广告屏/灯关闭时间 BCD 7*/
    PILE_VOLTAGE(0x001D, 2, "pileVoltage", DataType.UINT_16),                               /*充电桩额定电压 一位小数点，范围：300- 1000V 2*/
    PILE_CURRENT(0x001E, 2, "pileCurrent", DataType.UINT_16),                               /*充电桩额定电流 一位小数点，范围：20- 250A 2*/
    PILE_MAX_VOLTAGE(0x001F, 2, "pileMaxVoltage", DataType.UINT_16),                        /*充电桩最高电压 一位小数点，范围：300- 1000V 2*/
    PILE_MAX_CURRENT(0x0020, 2, "pileMaxCurrent", DataType.UINT_16),                        /*充电桩最高电流*/
    PILE_MIN_VOLTAGE(0x0021, 2, "pileMinVoltage", DataType.UINT_16),                        /*充电桩最低电压*/
    PILE_POWER(0x0022, 2, "pilePower", DataType.UINT_16),                                   /*桩体额定功率 一位小数点，KW，范围：15-500KW, 例如：0x1E 表示 30KW 2*/
    ASSIST_TYPE(0x0023, 1, "assistType", DataType.UINT_8),                                  /*单双辅源类型 0单辅源 1双辅源 1*/
    DEFAULT_ASSIST_VOLTAGE(0x0024, 1, "defaultAssistVoltage", DataType.UINT_8),             /*默认辅源类型 0.12V 1.24V 1*/
    MODULES_NUMBER(0x0025, 1, "modulesNumber", DataType.UINT_8),                            /*模块数量 1-24个 1*/
    AC_HALL(0x0026, 2, "acHall", DataType.UINT_16),                                         /*AC霍尔 两位小数，默认100，范围0-200 2*/
    DC_HALL(0x0027, 2, "dcHall", DataType.UINT_16),                                         /*DC霍尔 两位小数，默认100，范围0-200 2*/
    METER_DIFF(0x0028, 2, "meterDiff", DataType.UINT_16),                                   /*电量变化值 两位小数，默认100，范围0-200 2*/
    INSULATION_THRESHOLD(0x0029, 2, "insulationThreshold", DataType.UINT_16),               /*绝缘告警点 默认：38；单位 KΩ；范围 0-100 2*/
    OUTPUT_MODE(0x002A, 1, "outputMode", DataType.UINT_8),                                  /*输出模式 0,均充 1.轮询 1字节*/
    CARD_READER_TYPE(0x002B, 1, "cardReaderType", DataType.UINT_8),                         /*读卡器类型 0无卡 1莱卡 2cpu卡 3 M1卡 其他：非标定制卡 1*/
    CHARGING_MODULE_TYPE(0x002C, 1, "chargingModuleType", DataType.UINT_8),                 /*充电模块类型 8：VEV3811_015K 9： EV3751_015K 10： EV3811_020K 11： EV3150_010K 12： UR1000_30K_SW*/
    DEBUG_MODE(0x002D, 1, "debugMode", DataType.UINT_8),                                    /*调试模式 0禁止 1是能 1*/
    ALARM_SOUND(0x002E, 1, "alarmSound", DataType.UINT_8),                                  /*告警声音是能 0禁止 1是能 1*/
    A_GUN_PILE_NUMBER(0x0037, 20, "aGunPileNumber", DataType.ASCII),                        /*A枪桩体号 ASCII 20*/
    B_GUN_PILE_NUMBER(0x0038, 20, "bGunPileNumber", DataType.ASCII),                        /*B枪桩体号 ASCII 20*/
    POWER_LIMIT(0x0039, 1, "powerLimit", DataType.UINT_8),                                  /*功率限制使能 0禁止 1使能 1*/
    REMOTE_CHARGING_RECORD_UPLOAD(0x0045, 1, "remoteChargingRecordUpload", DataType.UINT_8),/*远程充电记录上传 0 否 1 是 1*/
    REMOTE_ALARM_RECORD_UPLOAD(0x0046, 1, "remoteAlarmRecordUpload", DataType.UINT_8),      /*远程告警记录上传 0 否 1 是 1*/
    REMOTE_OPT_RECORD_UPLOAD(0x0047, 1, "remoteOptRecordUpload", DataType.UINT_8),          /*远程操作记录上传 0 否 1 是 1*/
    REMOTE_DATAGRAM_RECORD_UPLOAD(0x0048, 1, "remoteDatagramRecordUpload", DataType.UINT_8),/*远程报文记录上传 0 否 1 是 1*/
    REMOTE_LOG_RECORD_UPLOAD(0x0049, 1, "remoteLogRecordUpload", DataType.UINT_8),          /*远程日志记录上传 0 否 1 是 1*/
    HTTP_OTA(0x004A, 1, "httpOta", DataType.UINT_8),                                        /*HTTP远程升级 0否 1是 1*/
    REAL_DATA_INTERVAL(0x004B, 2, "realDataInterval", DataType.UINT_16),                    /*实时数据间隔 10-300s  2*/
    SWITCH_VERSION(0x004C, 1, "switchVersion", DataType.UINT_8),                            /*版本切换 0家庭版本 1网络版本 1*/
    MAX_SOC(0x004E, 1, "maxSoc", DataType.UINT_8),                                          /*限制最大SOC 1*/
    MAX_CHARGING_ELECTRIC(0x004F, 2, "maxChargingElectric", DataType.UINT_16),              /*最大充电电量限制 两位小数 2*/
    GUN_TEMPERATURE_THRESHOLD(0x0050, 1, "gunTemperatureThreshold", DataType.UINT_8),       /*枪温度阈值 偏移50度 1*/
    METER_TYPE(0x0051, 1, "meterType", DataType.UINT_8),                                    /*电表类型 1交流板载电表 2单相交流电表  3三相交流电表*/
    DC_METER_CHANGE_CONF(0x0052, 1, "dcMeterChangeConf", DataType.UINT_8),                  /*直流电表变化配置*/
    AC_METER_CHANGE_CONF(0x0053, 1, "acMeterChangeConf", DataType.UINT_8),                  /*交流电表变化配置*/
    LOW_CURRENT_KEEP_CHARGING(0x0054, 2, "lowCurrentKeepCharging", DataType.UINT_16),       /*小电流持续充电时间 分钟 2*/
    MAX_CP(0x0055, 1, "maxCP", DataType.UINT_8),                                            /*最大CP（交流）1*/
    BMS_PROTECT_VOLTAGE(0x0056, 2, "bmsProtectVoltage", DataType.UINT_16),                  /*BMS单体保护电压 2*/
    BMS_PROTECT_TEMPERATURE(0x0057, 2, "bmsProtectTemperature", DataType.UINT_16),          /*BMS单体保护温度 2*/
    CHARGING_SWITCH(0x0058, 1, "chargingSwitch", DataType.UINT_8),                          /*禁止/允许充电 1禁止 0允许 默认值0*/
    OFFLINE_CHARGING_SWITCH(0x0059, 1, "offlineChargingSwitch", DataType.UINT_8),           /*离线禁止/允许充电 同上*/
    SLEEP_SCREEN(0x005A, 1, "sleepScreen", DataType.UINT_8),                                /*息屏保护时间 0常开 ，默认常开*/
    BLUETOOTH(0x005B, 1, "bluetooth", DataType.UINT_8),                                     /*蓝牙启用开关 1禁止 0允许 默认值0*/
    SCREEN_SHOW_TYPE(0x005C, 1, "screenShowType", DataType.UINT_8),                         /*当前屏幕显示模式 0标准 1用户自定义*/
    SHOW_CHARGING_PRICE(0x005E, 1, "showChargingPrice", DataType.UINT_8),                   /*充电价格显示 0显示 1不显示*/
    WAKE_SHOW_AD_TIME(0x005F, 1, "wakeShowAdTime", DataType.UINT_8),                        /*唤醒时广告显示时间 分钟*/
    SCAN_SHOW_AD_TIME(0x0060, 1, "scanShowAdTime", DataType.UINT_8),                        /*扫码后广告显示时间 分钟*/
    GUN_DRAW_SHOW_AD_TIME(0x0061, 1, "gunDrawShowAdTime", DataType.UINT_8),                 /*拔枪后广告显示时间 分钟*/
    AD_MODE_IDLE(0x0062, 1, "adModeIdle", DataType.UINT_8),                                 /*空闲时广告模式 0标准 1用户自定义*/
    AD_CAROUSEL_TIME_IDLE(0x0063, 1, "adCarouselTimeIdle", DataType.UINT_8),                /*空闲时广告轮播时间 分钟*/
    PAY_QRCODE(0x0064, 100, "payQrcode", DataType.ASCII),                                   /*用户支付二维码 ASCII码*/
    SCREEN_LOGO(0x0065, 1, "screenLogo", DataType.UINT_8),                                  /*0无LOG 1有LOG*/
    LEFT_GUN_QR_TEXT(0x0066, 100, "leftGunQrText", DataType.ASCII),                         /*左枪二维码文字描述 ASCII*/
    LEFT_GUN_QR(0x0067, 100, "leftGunQr", DataType.ASCII),                                  /*左枪二维码ASCII码*/
    RIGHT_GUN_QR_TEXT(0x0066, 100, "rightGunQrText", DataType.ASCII),                       /*右枪二维码文字描述 ASCII*/
    RIGHT_GUN_QR(0x0067, 100, "rightGunQr", DataType.ASCII);                                /*右枪二维码ASCII码*/


    /*数据单元标识ID*/
    @Getter
    private final int id;

    /*字节形态的长度*/
    @Getter
    private final int len;

    /*名称*/
    @Getter
    private final String key;

    /*数据类型*/
    @Getter
    private final DataType type;

    TerminalData(int id, int len, String key, DataType type) {
        this.id = id;
        this.len = len;
        this.key = key;
        this.type = type;
    }

    public static TerminalData idOf(int id) {
        for (TerminalData data : TerminalData.values()) {
            if (data.getId() == id) {
                return data;
            }
        }
        return null;
    }

    public static TerminalData keyOf(String key) {
        for (TerminalData data : TerminalData.values()) {
            if (data.key.equals(key)) {
                return data;
            }
        }
        return null;
    }
}
