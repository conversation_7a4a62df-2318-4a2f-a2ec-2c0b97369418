package com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging;


import com.yuchen.iot.gateway.connecotr.tcp.chargepile.util.BytesUtil;
import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.comm.Constant;
import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.ChargingPointProtocol;
import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.MsgHeader;
import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.message.MessageEncoder;
import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.util.CRCUtil;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.util.HexStringUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import io.netty.buffer.ByteBufUtil;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;
import io.netty.util.ReferenceCountUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;


public class ChargingPointEncoder extends MessageToByteEncoder<ChargingPointProtocol<Object>> {

    private static final Logger log = LoggerFactory.getLogger(ChargingPointEncoder.class);

    @Override
    protected void encode(ChannelHandlerContext ctx, ChargingPointProtocol<Object> msg, ByteBuf byteBuf) throws Exception {


        MessageEncoder messageEncoder = (MessageEncoder)msg.getBody();
        ByteBuf buf = messageEncoder.encode();

        byte[] byteArray = new byte[buf.readableBytes()];
        buf.getBytes(buf.readerIndex(), byteArray);
        byte[] newbyteArray =escapeBytes(byteArray);

        MsgHeader header = msg.getHeader();
        byteBuf.writeByte(header.getPreamble());
        byteBuf.writeByte(header.getMsgId());
        byteBuf.writeBytes(newbyteArray);


        byte[] crcbytes= mergeByteAndArray((byte)header.getMsgId(),byteArray);
        byte crcByteArray=CRCUtil.crcCheck(crcbytes);
        byte[] crcbyteArray =escapeBytes( new byte[] { crcByteArray });

        byteBuf.writeByte(crcbyteArray[0]);
        byteBuf.writeByte(header.getPreamble());


        // TODO:排查内存泄漏的问题
        ReferenceCountUtil.release(buf);

        log.info("响应二轮充电内容"+header.deviceNo+"---msgId"+header.getMsgId()+"-----"+ HexStringUtil.Bytes2hexStr(ByteBufUtil.getBytes(byteBuf)));

    }


    public static byte[] escapeBytes(byte[] bytes) {
        List<Byte> escapedList = new ArrayList<>();

        for (byte b : bytes) {
            if (b == 0x7E) {
                escapedList.add((byte) 0x7D);
                escapedList.add((byte) 0x02);
            } else if (b == 0x7D) {
                escapedList.add((byte) 0x7D);
                escapedList.add((byte) 0x01);
            } else {
                escapedList.add(b);
            }
        }

        // 将 List<Byte> 转换为 byte[]
        byte[] escapedBytes = new byte[escapedList.size()];
        for (int i = 0; i < escapedList.size(); i++) {
            escapedBytes[i] = escapedList.get(i);
        }

        return escapedBytes;
    }


    public static byte[] mergeByteAndArray(byte singleByte, byte[] byteArray) {
        // 创建一个新数组，长度为单个字节 + 原数组的长度
        byte[] mergedArray = new byte[1 + byteArray.length];

        // 将单个字节放入新数组的第一个位置
        mergedArray[0] = singleByte;

        // 将原字节数组复制到新数组的剩余部分
        System.arraycopy(byteArray, 0, mergedArray, 1, byteArray.length);

        return mergedArray;
    }



    private static byte charToByte(char c) {
        return (byte) "0123456789ABCDEF".indexOf(c);
    }

    public static byte[] hexString2Bytes(String hex) {

        if ((hex == null) || (hex.equals(""))){
            return null;
        }
        else if (hex.length()%2 != 0){
            return null;
        }
        else{
            hex = hex.toUpperCase();
            int len = hex.length()/2;
            byte[] b = new byte[len];
            char[] hc = hex.toCharArray();
            for (int i=0; i<len; i++){
                int p=2*i;
                b[i] = (byte) (charToByte(hc[p]) << 4 | charToByte(hc[p+1]));
            }
            return b;
        }

    }
}
