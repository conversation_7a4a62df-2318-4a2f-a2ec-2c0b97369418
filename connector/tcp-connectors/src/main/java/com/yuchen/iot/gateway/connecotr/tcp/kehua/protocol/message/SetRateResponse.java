package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.Data;

/**
 * @description: 终端应答平台设置尖峰平谷计费规则的请求
 * @author: zhonghx
 * @create: 2023-02-15 15:32
 **/
@Data
public class SetRateResponse implements MessageDecoder{

    /*下发结果 0失败 1成功*/
    private byte ack;

    /*版本 10字节*/
    private String version;


    @Override
    public void decode(ByteBuf in) {
        ack = in.readByte();
        byte[] versionBytes = ByteBufUtil.getBytes(in.readBytes(10));
        version = new String(versionBytes);
    }
}
