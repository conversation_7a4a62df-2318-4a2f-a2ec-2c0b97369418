package com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.message;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.Data;


/**
 * @description: 登录请求
 * @author: zhong
 * @create: 2022-12-08 16:55
 **/

@JsonIgnoreProperties(value = { "handler" })
@Data
public class RateVerifyMessage implements MessageDecoder {


    public String devNo;

    public String rateMOdelNo;

    @Override
    public void decode(ByteBuf in) {
        ByteBuf byteBuf=  in.readBytes(7);

        byte[] bytes = ByteBufUtil.getBytes(byteBuf);

         devNo = bytesToHex(bytes);

        ByteBuf byteBuf2=  in.readBytes(2);

        byte[] bytes2 = ByteBufUtil.getBytes(byteBuf2);

        rateMOdelNo = bytesToHex(bytes2);
    }

    private static String bytesToHex(final byte[] bytes) {
        final int numBytes = bytes.length;
        final char[] container = new char[numBytes * 2];

        for (int i = 0; i < numBytes; i++) {
            final int b = bytes[i] & 0xFF;
            container[i * 2] = Character.forDigit(b >>> 4, 0x10);
            container[i * 2 + 1] = Character.forDigit(b & 0xF, 0x10);
        }

        return new String(container);
    }
}
