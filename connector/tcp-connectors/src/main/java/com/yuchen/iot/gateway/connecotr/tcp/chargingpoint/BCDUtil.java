package com.yuchen.iot.gateway.connecotr.tcp.chargingpoint;

/**
 * @description: BCD编码工具类
 * @author: zhonghx
 * @create: 2022-12-10 14:12
 **/
public class BCDUtil {


    private BCDUtil(){};

    /**
     * 字符串转BCD码
     * @param asc ASCII字符串
     * @return BCD
     */
    public static byte[] strToBcd(String asc) {
        byte[] s = asc.getBytes();
        byte[] b = new byte[s.length/2];
        for(int i=0;i<b.length;i++) {
            b[i] = (byte) (s[2*i] << 4 | (s[2*i+1] & 0xf));
        }
        return b;
    }

    /**
     * BCD转ASCII字符串
     * @param bytes BCD byte数组
     * @return ASCII字符串
     */
    public static String bcdToStr(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for(int i=0;i<bytes.length;i++) {
            sb.append(bytes[i]>>4&0xf)
                    .append(bytes[i]&0xf);
        }
        return sb.toString();
    }


}