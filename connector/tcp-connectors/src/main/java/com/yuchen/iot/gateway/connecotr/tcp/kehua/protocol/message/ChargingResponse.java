package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.Data;

import java.nio.charset.StandardCharsets;

/**
 * @description: 终端应答平台的启停充电指令
 * @author: zhonghx
 * @create: 2023-02-11 17:22
 **/
@Data
public class ChargingResponse implements MessageDecoder{

    /*用户账号 ASCII*/
    private String resAccount;

    /*操作类型 1启动 2停止*/
    private int resOptType;

    /*充电流水号，原值返回*/
    private int resChargingSN;

    /*确认标识
    * 1.接收启停命令成功
    * 2.充电枪未连接
    * 3.充电桩故障
    * 4.已在充电态
    * 5.内存不足
    * 6.系统不在充电中
    * */
    private int ack;
    @Override
    public void decode(ByteBuf in) {

        byte[] resAccountBytes = ByteBufUtil.getBytes(in.readBytes(20));
        resAccount = new String(resAccountBytes, StandardCharsets.US_ASCII);
        resOptType = in.readByte();
        resChargingSN = in.readInt();
        ack = in.readByte();
    }
}
