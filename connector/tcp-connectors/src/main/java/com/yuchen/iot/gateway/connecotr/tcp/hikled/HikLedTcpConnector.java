package com.yuchen.iot.gateway.connecotr.tcp.hikled;

import com.yuchen.iot.gateway.connector.api.AbstractConnector;
import com.yuchen.iot.gateway.connector.api.WaitCallResponseCallback;
import com.yuchen.iot.gateway.connector.api.data.DownLinkData;
import com.yuchen.iot.gateway.connector.api.message.ThingMessage;
import io.netty.buffer.ByteBuf;
import io.netty.channel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.InputStream;
import java.io.OutputStream;
import java.net.Socket;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * TODO 
 * <AUTHOR>
 * @date 2022/11/23 下午4:04
 * @version 2.0.0
 */
public class HikLedTcpConnector extends AbstractConnector<Void> {

    private final Logger logger = LoggerFactory.getLogger(HikLedTcpConnector.class);

    static final int PORT = Integer.parseInt(System.getProperty("port", "8009"));
    static final int SIZE = Integer.parseInt(System.getProperty("size", "256"));

    @Override
    public void process(Void message) {
        // no nothing
    }

    @Override
    public void onDownLinkMessage(ThingMessage message, WaitCallResponseCallback callback) {
        if (this.downLinkConverter != null) {
            this.processDownLinkMessage(message, callback);
        }
    }

    private void processDownLinkMessage(ThingMessage message, WaitCallResponseCallback callback) {
        Map<String, String> mdMap = new HashMap<>(this.metadataTemplate.getKvMap());
        try {
            List<DownLinkData> result = this.downLinkConverter
                    .convertDownLink(Collections.singletonList(message), mdMap);
            if (!result.isEmpty()) {
                DownLinkData downLink = result.get(0);
                if (!downLink.isEmpty()) {
                    Map<String, String> metadata = downLink.getMetadata();
                    String ipAddress = metadata.get("ipAddress");
                    int port = Integer.parseInt(metadata.get("port"));

                    Socket socket = new Socket(ipAddress, port);
                    OutputStream outputStream = socket.getOutputStream();
                    InputStream inputStream = socket.getInputStream();
                    outputStream.write(downLink.getData());
                    byte[] serverContent = new byte[512];
                    while (inputStream.read(serverContent) != -1) {
                        byte[] dataLenArray = new byte[4];
                        System.arraycopy(serverContent, 10, dataLenArray, 0, dataLenArray.length);
                        int len = byteArrayToInt(dataLenArray);
                        logger.info("get data package len from led server is: {}", len);
                    }
                    outputStream.close();
                    inputStream.close();
                    socket.close();
                }
            }
        } catch (Exception e) {
            logger.warn("Failed to process downLink message", e);
        }
    }

    public class DiscardClientHandler extends SimpleChannelInboundHandler<Object> {

        private ByteBuf content;
        private ChannelHandlerContext ctx;

        @Override
        public void channelActive(ChannelHandlerContext ctx) {
            this.ctx = ctx;

            // Initialize the message.
            content = ctx.alloc().directBuffer(SIZE).writeZero(SIZE);

            // Send the initial messages.
            generateTraffic();
        }

        @Override
        public void channelInactive(ChannelHandlerContext ctx) {
            content.release();
        }

        @Override
        public void channelRead0(ChannelHandlerContext ctx, Object msg) throws Exception {
            // Server is supposed to send nothing, but if it sends something, discard it.
            System.out.println();
        }

        @Override
        public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
            // Close the connection when an exception is raised.
            cause.printStackTrace();
            ctx.close();
        }

        long counter;

        private void generateTraffic() {
            // Flush the outbound buffer to the socket.
            // Once flushed, generate the same amount of traffic again.
            ctx.writeAndFlush(content.retainedDuplicate()).addListener(trafficGenerator);
        }

        private final ChannelFutureListener trafficGenerator = new ChannelFutureListener() {
            @Override
            public void operationComplete(ChannelFuture future) {
                if (future.isSuccess()) {
                    generateTraffic();
                } else {
                    future.cause().printStackTrace();
                    future.channel().close();
                }
            }
        };
    }

    /**
     * byte[]转int
     *
     * @param bytes 需要转换成int的数组
     * @return int值
     */
    public static int byteArrayToInt(byte[] bytes) {
        int value = 0;
        for (int i = 0; i < 4; i++) {
            int shift = (3 - i) * 8;
            value += (bytes[i] & 0xFF) << shift;
        }
        return value;
    }

    @Override
    protected void doListenOnEndpoint(String deviceKey, Map<String, String> endpointMeta) {
        this.getUpLinkDataListener(deviceKey).onSouthConnect();
    }

    @Override
    protected void doListenOffEndpoint(String deviceKey, Map<String, String> endpointMeta) {
        this.getUpLinkDataListener(deviceKey).onSouthDisconnect();
    }

    @Override
    public void destroy() {

    }
}
