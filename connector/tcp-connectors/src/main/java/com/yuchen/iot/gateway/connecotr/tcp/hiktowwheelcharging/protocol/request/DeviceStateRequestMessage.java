package com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.request;

import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.BCDUtil;
import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.message.MessageDecoder;
import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.util.BytesUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: 终端启动充电的请求，需要主站应答
 * @author: zhonghx
 * @create: 2022-12-09 17:27
 **/
@Data
public class DeviceStateRequestMessage implements MessageDecoder {

    //电表值
    private double electricityMeter;

    /*卡号， 16字节BCD*/
    private double temperature;

    /*普通用户  2集团用户*/
    private int workStatus;

    /*用户来源*/
    private int gpsStatus;

    /*充电方式， 0x01按金额（元）， 0x02按电量（度），0x03按时间（分钟）*/
    private double voltage;

    private List<Map<String, Object>> channels = new ArrayList<>();

    private List<Map<String, Object>> channelEnergies = new ArrayList<>();

    private List<Map<String, Object>> channelCurrents = new ArrayList<>();
    /*充电方式附带值， 按金额：充电金额，单位元，2位小数
     *               按电量，充电电量，单位kw/h
     *               按时间，充电时间，单位分*/
    private short chargingValue;


    @Override
    public void decode(ByteBuf in) {


        int value = BytesUtil.byteArrayToInt(ByteBufUtil.getBytes(in.readBytes(4)));
        electricityMeter = (double) value / 100;

        int value2 = in.readUnsignedShort();

        temperature = (double) value2 / 10.0;
        workStatus = in.readByte();
        gpsStatus = in.readByte();

        voltage = (double) Integer.valueOf(BCDUtil.bcdToStr(ByteBufUtil.getBytes(in.readBytes(4))))/100.0;

        int[] all = mergeBitArrays(changeByteTOBit(in.readByte()), changeByteTOBit(in.readByte()));
        for (int i = 0; i < all.length; i++) {
            Map<String, Object> channel = new HashMap<>();
            channel.put("number", i + 1);
            channel.put("status", all[i]);
            channels.add(channel);
        }
        in.readBytes(2);

        double[] channelEnergiesValue = parseChannelEnergies( in.readBytes(20));
        for (int i = 0; i < channelEnergiesValue.length; i++) {
            Map<String, Object> channel = new HashMap<>();
            channel.put("number", i + 1);
            channel.put("energiesValue", channelEnergiesValue[i]);
            channelEnergies.add(channel);
        }

        double[] channelCurrentsValue = parseChannelCurrents( in.readBytes(20));
        for (int i = 0; i < channelCurrentsValue.length; i++) {
            Map<String, Object> channel = new HashMap<>();
            channel.put("number", i + 1);
            channel.put("currentsValue", channelCurrentsValue[i]);
            channelCurrents.add(channel);
        }

    }

    public static double[] parseChannelEnergies(ByteBuf buf) {
        double[] energies = new double[10];

        for (int i = 0; i < 10; i++) {
            // 读取两个字节，并将其转换为int类型
            int energy = buf.readUnsignedShort();
            // 计算实际的电量值，单位为度
            energies[i] = energy / 10000.0;
        }

        return energies;
    }

    public static double[] parseChannelCurrents(ByteBuf buf) {
        double[] currents = new double[10];

        for (int i = 0; i < 10; i++) {
            // 读取一个字节，并将其转换为无符号整数
            int current = buf.readUnsignedByte();
            // 计算实际的电流值，单位为安培
            currents[i] = current / 10.0;
        }

        return currents;
    }

    public static int[] changeByteTOBit(byte bt) {

        String str = String.format("%8s", Integer.toBinaryString(bt & 0xFF)).replace(' ', '0');

        int[] list = new int[8];
        list[0] = Character.getNumericValue(str.charAt(7));
        list[1] = Character.getNumericValue(str.charAt(6));
        list[2] = Character.getNumericValue(str.charAt(5));
        list[3] = Character.getNumericValue(str.charAt(4));
        list[4] = Character.getNumericValue(str.charAt(3));
        list[5] = Character.getNumericValue(str.charAt(2));
        list[6] = Character.getNumericValue(str.charAt(1));
        list[7] = Character.getNumericValue(str.charAt(0));

        return list;
    }

    public static int[] mergeBitArrays(int[] first, int[] second) {
        int[] merged = new int[first.length + second.length];
       // System.arraycopy(first, 0, merged, 0, first.length);
      //  System.arraycopy(second, 0, merged, first.length, second.length);

        System.arraycopy(second, 0, merged, 0, second.length);
        System.arraycopy(first, 0, merged, second.length, first.length);
        return merged;
    }

}
