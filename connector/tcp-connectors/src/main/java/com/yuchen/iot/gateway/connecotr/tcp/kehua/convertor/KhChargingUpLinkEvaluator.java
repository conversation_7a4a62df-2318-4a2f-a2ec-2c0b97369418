package com.yuchen.iot.gateway.connecotr.tcp.kehua.convertor;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.yuchen.iot.gateway.connector.api.converter.AbstractKeyEvaluator;
import com.yuchen.iot.gateway.connector.api.converter.evaluator.UpLinkEvaluator;
import com.yuchen.iot.gateway.connector.api.data.UpLinkData;
import com.yuchen.iot.gateway.connector.api.data.UpLinkMetaData;
import com.yuchen.iot.gateway.connector.api.data.message.*;
import org.pf4j.Extension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 科华充电桩上行转换器
 *
 * <AUTHOR>
 * @date 2022/12/28
 * @version 1.0
 */
@Component
@Extension
public class KhChargingUpLinkEvaluator extends AbstractKeyEvaluator implements UpLinkEvaluator {

    private final Logger logger = LoggerFactory.getLogger(KhChargingUpLinkEvaluator.class);

    @Override
    public ListenableFuture<Object> execute(byte[] data, UpLinkMetaData upLinkMetaData) {
        List<UpLinkData> upLinkDataList = new ArrayList<>();
        try {

            JsonNode jsonNode = this.objectMapper.readTree(data);
            JsonNode header = jsonNode.get("header");
            JsonNode body = jsonNode.get("body");

            MsgType msgType = MsgType.findByType(header.get("cmd").asInt());
            if (msgType == null){
                throw new NullPointerException();
            }
            String integrateNo =  header.get("devNumber").asText();/*"AABBCCDDEEFF"*//*header.get("address").asText()*/;
            Event event = new Event();
            EventValue eventValue = new EventValue();

            // 同步事件 消息返回
            CallResponse callResponse = new CallResponse();
            String callId = null;
            Property property = new Property();

            switch(msgType){
                /*登入*/
                case LOGIN:
                {
                    event.setIdentifier("LoginRequest");
                    event.setValue(eventValue);
                    body.fields().forEachRemaining(json -> {
                        eventValue.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                /*心跳/校时*/
                case HEARTBEAT:
                case GET_DATA_ACK:
                case REAL_TIME_DATA:
                case REPORT_DATA:
                case GET_REAL_DATA_ACK:
                {
                    // 此处为上报 设备属性 并非上报时间
                    body.fields().forEachRemaining(json -> {

                        if (Objects.equals(json.getKey(), "dataMap")){
                            JsonNode propertyMap = json.getValue();
                            propertyMap.fields().forEachRemaining(p->{
                                PropertyValue propertyValue = new PropertyValue();
                                propertyValue.put("value", p.getValue().asText());
                                propertyValue.put("time", String.valueOf(System.currentTimeMillis() / 1000L));
                                property.put(p.getKey(), propertyValue);
                            });
                        }
                    });
                }
                break;
                /*充电桩上传启停充电通知确认帧*/
                case START_STOP_CHARGING_ACK:
                {
                    // 获取充电流水号
                    String serialNumber = body.get("resChargingSN").asText();
                    callId = integrateNo.concat("ChargingRequest").concat(serialNumber);
                    body.fields().forEachRemaining(json -> {
                        callResponse.put(json.getKey(), json.getValue().asText());
                    });
//                    event.setIdentifier("ChargingRequest");
//                    event.setValue(eventValue);
//                    body.fields().forEachRemaining(json -> {
//                        eventValue.put(json.getKey(), json.getValue().asText());
//                    });
                }
                break;
                /*充电桩上传启停充电结果帧*/
                case START_STOP_RESULT:
                {
                    event.setIdentifier("ChargingResultRequest");
                    event.setValue(eventValue);
                    body.fields().forEachRemaining(json -> {
                        eventValue.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                /*刷卡鉴权*/
                case CARD_AUTH:
                {
                    event.setIdentifier("CardAuthRequest");
                    event.setValue(eventValue);
                    body.fields().forEachRemaining(json -> {
                        eventValue.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                /*充电历史记录上传*/
                case CHARGING_HISTORY_RECORD:
                {
                    event.setIdentifier("ChargingHistoryRequest");
                    event.setValue(eventValue);
                    body.fields().forEachRemaining(json -> {
                        eventValue.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                /*报警历史记录上传*/
                case ALARM_HISTORY_RECORD:
                {
                    event.setIdentifier("AlarmHistoryRequest");
                    event.setValue(eventValue);
                    body.fields().forEachRemaining(json -> {
                        eventValue.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                /*操作历史记录上传*/
                case OPT_HISTORY_RECORD:
                {
                    event.setIdentifier("OptHistoryRequest");
                    event.setValue(eventValue);
                    body.fields().forEachRemaining(json -> {
                        eventValue.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                /*FTP升级响应*/
                case FTP_OTA_ACK:
                {
                    event.setIdentifier("FtpOTAResponse");
                    event.setValue(eventValue);
                    body.fields().forEachRemaining(json -> {
                        eventValue.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                /*FTP升级结果响应*/
                case FTP_OTA_RESULT:
                {
                    event.setIdentifier("FtpOTAResultRequest");
                    event.setValue(eventValue);
                    body.fields().forEachRemaining(json -> {
                        eventValue.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                /*TCP升级响应*/
                case TCP_OTA_ACK:
                {
                    event.setIdentifier("TcpOTAResponse");
                    event.setValue(eventValue);
                    body.fields().forEachRemaining(json -> {
                        eventValue.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                /*充电桩应答服务器下发升级文件数据指令*/
                case TCP_OTA_FILE_ACK:
                {
                    event.setIdentifier("TcpOTATransferFileResponse");
                    event.setValue(eventValue);
                    body.fields().forEachRemaining(json -> {
                        eventValue.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                /*充电桩上传升级结果*/
                case TCP_OTA_RESULT:
                {
                    event.setIdentifier("TcpOTAResultRequest");
                    event.setValue(eventValue);
                    body.fields().forEachRemaining(json -> {
                        eventValue.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                /*终端上传对时结果*/
                case TIMING_ACK:
                {
                    event.setIdentifier("TimingResponse");
                    event.setValue(eventValue);
                    body.fields().forEachRemaining(json -> {
                        eventValue.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                /*终端回复充电金额变化结果*/
                case CHARGING_AMOUNT_CHANGED_ACK:
                {
                    event.setIdentifier("ChargingAmountChangedResponse");
                    event.setValue(eventValue);
                    body.fields().forEachRemaining(json -> {
                        eventValue.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                /*VIN码启动充电*/
                case VIN_REQ_CHARGING:
                {
                    event.setIdentifier("ChargingWithVINRequest");
                    event.setValue(eventValue);
                    body.fields().forEachRemaining(json -> {
                        eventValue.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                /*密码请求充电*/
                case PWD_REQ_CHARGING:
                {
                    event.setIdentifier("ChargingWithAccountRequest");
                    event.setValue(eventValue);
                    body.fields().forEachRemaining(json -> {
                        eventValue.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                case SET_TIME_INTERVAL_RATE_ACK:
                {
                    callId = integrateNo.concat("SetRateRequest");
                    body.fields().forEachRemaining(json -> {
                        callResponse.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                case GET_TIME_INTERVAL_RATE_ACK:
                {
                    callId = integrateNo.concat("GetRateRequest");
                    body.fields().forEachRemaining(json -> {
                        callResponse.put(json.getKey(), json.getValue().asText());
                    });
                    callResponse.remove("array");
                }
                break;
                case SET_POWER_OUT_LIMIT_ACK:
                {
                    event.setIdentifier("LimitPowerResponse");
                    event.setValue(eventValue);
                    body.fields().forEachRemaining(json -> {
                        eventValue.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                case SET_QR_FORMAT_ACK:
                {
                    event.setIdentifier("QRCodeResponse");
                    event.setValue(eventValue);
                    body.fields().forEachRemaining(json -> {
                        eventValue.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                case REBOOT_ACK:
                {
                    event.setIdentifier("RebootResponse");
                    event.setValue(eventValue);
                    body.fields().forEachRemaining(json -> {
                        eventValue.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                case LOGOUT_ACK:
                {
                    event.setIdentifier("LogoutResponse");
                    event.setValue(eventValue);
                    body.fields().forEachRemaining(json -> {
                        eventValue.put(json.getKey(), json.getValue().asText());
                    });
                }
                break;
                default:{
                    logger.error("Not support message type +++ ",msgType.getType());
                    break;
                }
            }

            if (null != event.getIdentifier()){
                logger.info("科华--终端主动上报事件信息---{}---{}---{}",integrateNo,msgType.name(), JSONObject.toJSONString(event));
            }
            if (callResponse.size() > 0){
                logger.info("科华--平台下发指令响应信息---{}---{}---{}",integrateNo,msgType.name(),
                        JSONObject.toJSONString(callResponse));
            }

            UpLinkData upLinkData = new UpLinkData(integrateNo, event, property, callResponse, callId, null);
            upLinkDataList.add(upLinkData);

        } catch (IOException | NullPointerException e ) {
            e.printStackTrace();
            logger.warn("Evaluator execute failed {}", e.getMessage());
        }
        return Futures.immediateFuture(this.gson.toJson(upLinkDataList));
    }

    @Override
    public String getKey() {
        return "kehua-charging-up-link";
    }

    @Override
    public void destroy() {
        // do nothing
    }
}
