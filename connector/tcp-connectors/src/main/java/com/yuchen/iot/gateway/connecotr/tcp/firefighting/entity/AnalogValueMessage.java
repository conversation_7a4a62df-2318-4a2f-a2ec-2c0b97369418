package com.yuchen.iot.gateway.connecotr.tcp.firefighting.entity;

import lombok.Data;

@Data
public class AnalogValueMessage {
   // private String rawData;        // 原始数据
   // private int typeFlag;         // 类型标志
   // private int objectCount;      // 信息对象数目
    private int systemType;       // 系统类型
    private String systemAddress; // 系统地址
    private int componentType;    // 部件类型
    private String componentAddress; // 部件地址
    private int analogType;       // 模拟量类型
    private double analogValue;   // 模拟量值
    

} 