package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.Data;

import java.nio.charset.StandardCharsets;

/**
 * @description: 操作记录数据主动上传
 * @author: zhonghx
 * @create: 2023-02-13 14:27
 **/

@Data
public class OptHistoryRequest implements MessageDecoder{

    /*当前条数*/
    private short count;

    /*修改类型*/
    private byte modifyType;

    /*修改时间*/
    private int modifyTime;

    /*修改内容*/
    private String content;
    @Override
    public void decode(ByteBuf in) {

        count = in.readShort();
        modifyType = in.readByte();
        modifyTime = in.readInt();
        byte[] contentBytes = ByteBufUtil.getBytes(in.readBytes(36));
        content = new String(contentBytes, StandardCharsets.US_ASCII);
    }
}
