package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @description: 云平台获取实时数据
 * @author: zhonghx
 * @create: 2023-02-17 10:30
 **/

@AllArgsConstructor
@Data
public class GetRealtimeChargingDataRequest implements MessageEncoder{

    /*数据单元个数*/
    private byte dataCount;

    /*数据标识ID
     * 数据单元标识
     * */
    private Short[] DataIdArray;

    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(dataCount);
        for (short i : DataIdArray) {
            buf.writeShort(i);
        }
        return buf;
    }
}
