package com.yuchen.iot.gateway.connecotr.tcp;

import com.yuchen.iot.gateway.connector.api.data.UpLinkContentType;

/**
 * TODO 
 * <AUTHOR>
 * @date 2022/12/2 下午1:49
 * @version 2.0.0
 */
public abstract class TcpConnectorMessage<T> {

    protected final T message;

    public abstract UpLinkContentType getContentType();

    public abstract byte[] getMessageInBytes();

    public T getMessage() {
        return this.message;
    }

    public TcpConnectorMessage(T message) {
        this.message = message;
    }
}
