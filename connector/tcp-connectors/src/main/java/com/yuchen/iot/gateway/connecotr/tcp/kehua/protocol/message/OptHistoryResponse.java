package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @description: 平台响应操作记录数据主动上传，由网关应答
 * @author: zhonghx
 * @create: 2023-02-13 14:28
 **/
@Data
@AllArgsConstructor
public class OptHistoryResponse implements MessageEncoder{

    private byte ack;

    private byte modifyType;

    private int  modifyTime;
    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(ack);
        buf.writeByte(modifyType);
        buf.writeInt(modifyTime);
        return buf;
    }
}
