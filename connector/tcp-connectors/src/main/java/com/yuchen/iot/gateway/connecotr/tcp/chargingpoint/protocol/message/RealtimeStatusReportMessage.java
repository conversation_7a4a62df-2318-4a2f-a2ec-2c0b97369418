package com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.protocol.message;


import com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.protocol.GunState;
import io.netty.buffer.ByteBuf;
import lombok.Data;

/**
 * @description: 终端实时状态定期上报，服务端无需应答，一般约30秒或状态变化时发送一次
 * @author: zhong
 * @create: 2022-12-08 17:02
 **/
@Data
public class RealtimeStatusReportMessage implements MessageDecoder {

    /*枪编号，1-n */
    private byte gunNo;

    /*枪工作状态，0-空闲 1-充电 2充完  4故障 5等待充电 6 预约 7 在线更新*/
    private byte state;

    /*其他状态数据
    状态名称                交、直流      位                             说明
    是否连接车辆（插枪）      共有         D0-D1                        1-连接，2-未连接，其他-无此设备
    充电桩充电枪座状态        共有         D2-D3                        1-连接，2-未连接，其他-他-无此设备
    充电接口电子锁状态        共有         D4-D5                        1-解锁，2-锁止，其他-无此设备
    车位状态                共有         D6-D7                        1-占用，2-未占用，3-异常，其他-无此设备
    车位锁状态               共有         D8-D9                        1-锁闭，2-开启，3-异常，其他-无此设备
    保留  D10~D31 0
     */

    /*是否连接车辆（插枪）1-连接，2-未连接，其他-无此设备*/
    private byte gunConnectCar;
    /*充电桩充电枪座状态1-连接，2-未连接，其他-他-无此设备*/
    private byte gunConnectPoint;
    /*充电接口电子锁状态1-解锁，2-锁止，其他-无此设备*/
    private byte pointLockState;
    /*车位状态，1-占用，2-未占用，3-异常，其他-无此设备*/
    private byte parkingPlaceState;
    /*车位锁状态 1-锁闭，2-开启，3-异常，其他-无此设备*/
    private byte parkingPlaceLockState;


    //告警信息说明
    /*烟雾报警告警 0正常 1异常*/
    private byte smoke;

    /*充电中车辆控制导引告警*/
    private byte chargingCarControl;

    /*充电枪未归位告警 0正常 1异常*/
    private byte pointLostGun;

    /*BMS通信异常告警 0正常 1异常*/
    private byte bms;

    /*输入过电压告警 0正常 1异常*/
    private byte inputOverVoltage;

    /*输入欠压告警 0正常 1异常*/
    private byte inputUnderVoltage;

    /*直流母线输出过压告警 0正常 1异常*/
    private byte outDCLineOverVoltage;

    /*直流母线输出欠压压告警 0正常 1异常*/
    private byte outDCLineUnderVoltage;

    /*直流母线输出过流告警 0正常 1异常*/
    private byte outDCLineOverCurrent;

    /*直流母线输出欠流压告警 0正常 1异常*/
    private byte outDCLineUnderCurrent;


    /*D10 充电模块交流输入告警*/
    private byte chargingModuleACInputAlarm;

    /*D11 充电模块交流输入过压告警*/
    private byte chargingModuleACInputOverVoltage;

    /*D12 充电模块交流输入欠压告警*/
    private byte chargingModuleACInputUnderVoltage;

    /*D13 充电模块输入过压告警*/
    private byte chargingModuleInputOverVoltage;

    /*D14 充电模块输入缺相告警*/
    private byte chargingModuleInputLostPhase;

    /*D15 充电模块直流输出过流告警*/
    private byte chargingModuleDCOutputOverCurrent;

    /*D16 充电模块直流输出过压告警*/
    private byte chargingModuleDCOutputOverVoltage;

    /*D17 充电模块直流输出欠压告警*/
    private byte chargingModuleDCOutputUnderVoltage;

    /*D18 充电模块通讯告警*/
    private byte chargingModuleCommunicationAlarm;

    /*D19 输出过流告警*/
    private byte outputOverCurrentAlarm;

    /*D20 输出过流保护动作*/
    private byte outputOverCurrentProtect;

    /*D21 输出接触器状态*/
    private byte outputContactorState;

    /*D22 绝缘状态告警*/
    private byte insulationState;


    /*----------------------故障信息--------------*/
    /*急停按钮动作故障*/
    private byte emergencyStopButtonActionFault;

    /*交流断路器故障*/
    private byte acCircuitBreakerFault;

    /*输出接触器故障*/
    private byte outputContactorFault;

    /*输出熔断器故障*/
    private byte outputFuseFault;

    /*充电接口电子锁故障*/
    private byte chargingInterfaceLockFault;

    /*充电机风扇故障*/
    private byte chargingFanFault;

    /*避雷器故障*/
    private byte arresterFault;

    /*绝缘监测故障*/
    private byte insulationFault;

    /*D8 电池反接故障*/
    private byte batteryConnectFault;

    /*充电桩过温故障*/
    private byte pointOverTemperature;

    /*充电枪过温故障*/
    private byte gunOverTemperature;

    /*充电模块故障*/
    private byte chargingModuleFault;

    /*充电桩模块直流输出短路故障*/
    private byte chargingModuleDCOutputShortCircuit;

    /*电表故障*/
    private byte electricityMeterFault;

    /*读卡器故障*/
    private byte cardReaderFault;

    /*PE断线故障*/
    private byte peBreakageFault;


    @Override
    public void decode(ByteBuf in) {

        gunNo = in.readByte();
        state = in.readByte();

        /*其他状态数据*/
        // TODO: 此处是取反操作。因为刚开始测试海康的慢充，发现 工作状态和插枪状态为反。以为文档有误，就将数值进行了取反，
        // 在测试快充的时候，发现原本的数值为正确数值。
        int otherState = in.readIntLE();
        gunConnectCar = (byte)((~(otherState & 0x3)) & 0x3);
        gunConnectPoint = (byte)((~(otherState & 0xc)) & 0x3);
        pointLockState = (byte)(~((otherState & 0x30)) & 0x3);
        parkingPlaceState =(byte)(~((otherState & 0xc0)) & 0x3);
        parkingPlaceLockState = (byte)(~((otherState & 0x300)) & 0x3);

        /*告警信息*/
        int alarmState = in.readIntLE();
        //D0
        smoke = (byte)(alarmState & 0x1);
        chargingCarControl = (byte)(alarmState & 0x2);
        pointLostGun = (byte)(alarmState & 0x4);
        bms =(byte)(alarmState & 0x8);
        inputOverVoltage = (byte)(alarmState & 0x10);
        //D5
        inputUnderVoltage = (byte) (alarmState & 0x20);
        outDCLineOverVoltage = (byte) (alarmState & 0x40);
        outDCLineUnderVoltage = (byte) (alarmState & 0x80);
        outDCLineOverCurrent = (byte) (alarmState & 0x100);
        outDCLineUnderCurrent = (byte) (alarmState & 0x200);
        //D10
        chargingModuleACInputAlarm = (byte) (alarmState & 0x400);
        chargingModuleACInputOverVoltage = (byte) (alarmState & 0x800);
        chargingModuleACInputUnderVoltage = (byte) (alarmState & 0x1000);
        chargingModuleInputOverVoltage = (byte) (alarmState & 0x2000);
        chargingModuleInputLostPhase = (byte) (alarmState & 0x4000);
        //D15
        chargingModuleDCOutputOverCurrent = (byte) (alarmState & 0x8000);
        chargingModuleDCOutputOverVoltage = (byte) (alarmState & 0x10000);
        chargingModuleDCOutputUnderVoltage = (byte) (alarmState & 0x20000);
        chargingModuleCommunicationAlarm = (byte) (alarmState & 0x40000);
        outputOverCurrentAlarm = (byte) (alarmState & 0x80000);
        //D20
        outputOverCurrentProtect = (byte) (alarmState & 0x100000);
        outputContactorState = (byte) (alarmState & 0x200000);
        insulationState = (byte) (alarmState & 0x400000);


        /*故障信息*/
        int faultState = in.readIntLE();
        emergencyStopButtonActionFault = (byte)(faultState & 0x1);
        acCircuitBreakerFault = (byte)(faultState & 0x2);
        outputContactorFault = (byte)(faultState & 0x4);
        outputFuseFault =(byte)(faultState & 0x8);
        chargingInterfaceLockFault = (byte)(faultState & 0x10);
        //D5
        chargingFanFault = (byte) (faultState & 0x20);
        arresterFault = (byte) (faultState & 0x40);
        insulationFault = (byte) (faultState & 0x80);
        batteryConnectFault = (byte) (faultState & 0x100);
        pointOverTemperature = (byte) (faultState & 0x200);
        //D10
        gunOverTemperature = (byte) (faultState & 0x400);
        chargingModuleFault = (byte) (faultState & 0x800);
        chargingModuleDCOutputShortCircuit = (byte) (faultState & 0x1000);
        electricityMeterFault = (byte) (faultState & 0x2000);
        cardReaderFault = (byte) (faultState & 0x4000);
        //D15
        peBreakageFault = (byte) (faultState & 0x8000);

    }
}
