package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol;

import lombok.Data;


@Data
public class MsgHeader {

    /*起始符，0x4b 0x48*/
    public int preamble;

    /*帧总字节长度*/
    public int frameLength;

    /*版本号，*/
    public int version;

    /*报文流水号 发送端自加 1，接收端原值返回*/
    public int msgId;

    /*设备类型*/
    public int devType;

    /*设备串号 20ASCII*/
    public String devNumber;

    /*加密方式，默认0x0*/
    public int secretType;

    /*命令，功能码*/
    public int cmd;

    /*设备ID 默认0x0，0x1*/
    public int devId;

    /*枪号 0x00终端 0x01枪号1 0x02枪号2*/
    public int gunNumber;

    /*数据*/
    //public ByteBuf data;

    /*验证码 用 CRC16 作为校验，校验的区域为帧头到信息域。*/
    public int verifyCode;

    /*结束符 0x68*/
    public int terminator;
}
