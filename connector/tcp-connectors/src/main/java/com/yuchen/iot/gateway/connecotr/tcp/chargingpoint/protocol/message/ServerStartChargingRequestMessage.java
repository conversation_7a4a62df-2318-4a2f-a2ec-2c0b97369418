package com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.protocol.message;

import com.yuchen.iot.gateway.connecotr.tcp.chargepile.util.BytesUtil;
import com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.BCDUtil;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.util.HexStringUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @description: 主站启动充电，用于APP/微信等启动充电，由主站发起启动命令，如果未收到终端应答，则通常重发三次
 * @author: zhonghx
 * @create: 2022-12-09 18:54
 **/
@Data
@AllArgsConstructor
public class ServerStartChargingRequestMessage implements MessageEncoder{


    /*枪号 1-n*/
    private byte gunNo;

    /*充电流水号BCD*/
    private String serialNumber;

    /*充电方式， 0x01按金额（元）， 0x02按电量（度），0x03按时间（分钟）*/
    private byte chargingType;

    /*充电方式附带值， 按金额：充电金额，单位元，2位小数
     *               按电量，充电电量，单位kw/h
     *               按时间，充电时间，单位分*/
    private short chargingValue;

    /*普通用户  2集团用户*/
    private byte cardType;

    /*用户来源*/
    private byte from;

    /*账户余额，单位元，2位小数*/
    private int accountBalances;

    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(gunNo);
        byte[] bytes = BCDUtil.strToBcd(serialNumber);
        bytes = BytesUtil.bytesReverse(bytes);
        buf.writeBytes(bytes);
        buf.writeByte(chargingType);
        buf.writeShortLE(chargingValue);
        buf.writeByte(cardType);
        buf.writeByte(from);
        buf.writeIntLE(accountBalances);
        return buf;
    }
}
