package com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.request;

import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.BCDUtil;
import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.message.MessageDecoder;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.Data;

@Data
public class UserSwipeCardRequestMessage implements MessageDecoder {



    private int channel ;


    private int cardType ;

    private int numLength;

    private String cardNo;

    @Override
    public void decode(ByteBuf in) {

        channel = in.readByte();


        // 读取报警通道号
        cardType = in.readByte();



        numLength = in.readByte();

        ByteBuf byteBuf = in.readBytes(6);
        byte[] bytes = ByteBufUtil.getBytes(byteBuf);
        cardNo= BCDUtil.bcdToStr(bytes);

    }
}
