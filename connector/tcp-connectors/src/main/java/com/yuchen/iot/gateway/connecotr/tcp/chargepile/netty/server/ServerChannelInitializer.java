package com.yuchen.iot.gateway.connecotr.tcp.chargepile.netty.server;

import com.yuchen.iot.gateway.connecotr.tcp.chargepile.codec.ChargePileDataPackageDecoder;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.codec.ChargePileDataPackageEncoder;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.timeout.ReadTimeoutHandler;


/**
 * netty服务channel初始化服务
 * <AUTHOR>
 * @date 2022/12/07 15:29
 */
public class ServerChannelInitializer extends ChannelInitializer<SocketChannel> {

	@Override
	protected void initChannel(SocketChannel ch) throws Exception {
		ChannelPipeline pipeline = ch.pipeline();
		pipeline.addLast("frameDecoder", new ChargePileDataPackageDecoder());
		pipeline.addLast("frameEncoder", new ChargePileDataPackageEncoder());
		pipeline.addLast( new ReadTimeoutHandler(60));
		pipeline.addLast("authHandler", new BusinessChannelHandler());
		pipeline.addLast("handler", new AuthChannelHandler());
	}
}
