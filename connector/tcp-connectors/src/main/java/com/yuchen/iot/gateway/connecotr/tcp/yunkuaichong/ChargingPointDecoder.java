package com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yuchen.iot.gateway.connecotr.tcp.JsonTcpConnectorMessage;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.util.BytesUtil;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.util.ExecutorUtils;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.util.HexStringUtil;
import com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.comm.Constant;
import com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.ChargingPointProtocol;
import com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.MsgHeader;
import com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.MsgType;
import com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.ProtocolConstants;
import com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.message.*;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import io.netty.util.ReferenceCountUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * 处理海康充电桩 字节流 解码
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023年5月25日 20:03:54
 */
public class ChargingPointDecoder extends ByteToMessageDecoder {

    private static final Logger log = LoggerFactory.getLogger(ChargingPointDecoder.class);

    private static ExecutorService chargingEventExecutor;

    private ByteBuf mergedBuf;

    static {
        chargingEventExecutor = new ExecutorUtils(50, 100, 60000L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(100000), "hik-charging-iot-executor");
    }
    @Override
    public void channelReadComplete(ChannelHandlerContext ctx) throws Exception {
        super.channelReadComplete(ctx);
    }
    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) throws Exception {
        log.info("ChargingPointDecoder---" + HexStringUtil.Bytes2hexStr(ByteBufUtil.getBytes(in)));
/*        if(in.readableBytes()>=64){
            log.info("ChargingPointDecoder---1");
            mergedBuf=in;
            return;
        }
        if(mergedBuf!=null){
            ByteBuf newMergedBuf = Unpooled.buffer(mergedBuf.readableBytes() + in.readableBytes());
            newMergedBuf.writeBytes(mergedBuf);
            newMergedBuf.writeBytes(in);
            log.info("ChargingPointDecoder---" + HexStringUtil.Bytes2hexStr(ByteBufUtil.getBytes(newMergedBuf)));
            in=newMergedBuf;
            mergedBuf.release();
            mergedBuf=null;
            log.info("ChargingPointDecoder2---" + HexStringUtil.Bytes2hexStr(ByteBufUtil.getBytes(in)));
        }*/
        // 如果有粘包情况，则可以多次读取
     /*   String aa="684000000013000000000000000000000000000000001872178170000001020000D50800004C00000000000000000000000000001C1F01001C1F0100000000000000232E";

        ByteBuf byteBuf = Unpooled.wrappedBuffer(BCDUtil.hexStringToByteArray(aa));
        in=byteBuf;
*/        while (in.readableBytes() > ProtocolConstants.HEADER_TOTAL_LEN) {
            log.info("ChargingPointDecoder---while---begin ---{}", System.currentTimeMillis());
            in.markReaderIndex();
            byte preamble = in.readByte();
            if (preamble != ProtocolConstants.PREAMBLE) {
                log.info("preamble number is illegal, " + preamble);
                log.info("ChargingPointDecoder---preamble---{}", HexStringUtil.Bytes2hexStr(ByteBufUtil.getBytes(in)));
                in.clear();
                return;
            }
            byte frameLength = in.readByte();
            short version = in.readShortLE();
            byte encryptionFlag = in.readByte();
            byte cmd = in.readByte();
            int a = in.readableBytes();
            ByteBuf data = in.readBytes(a - 2);
            ByteBuf data2 = in.readBytes(2);
            // 组装消息
            MsgHeader header = new MsgHeader();
            header.setPreamble(preamble);
            header.setFrameLength(frameLength);
            header.setVersion(version);
            header.setEncryptionFlag(encryptionFlag);
            header.setCmd(cmd);
            chargingEventExecutor.submit(() -> {
                //构建消费发送
                header(ctx, header, data);
                // TODO:排查内存泄漏的问题
                ReferenceCountUtil.release(data);
            });
            log.info("ChargingPointDecoder---while---end---{}", System.currentTimeMillis());
        }
    }

    /**
     * 处理消息
     *
     * @param ctx    通道处理上下文
     * @param header 消息体
     * @param data   数据内容
     */
    public void header(ChannelHandlerContext ctx, MsgHeader header, ByteBuf data) {
        log.info("ChargingPointDecoder---header---{}", JSON.toJSONString(header));
        MsgType msgTypeEnum = MsgType.findByType(header.getCmd());
        if (msgTypeEnum == null) {
            log.info("ChargingPointDecoder---msgTypeEnum---{}", JSON.toJSONString(header));
            return;
        }
        ChargingPointProtocol<?> chargingPointProtocol = null;
        ChargingChannelListener listener = ctx.channel().attr(Constant.eventListenerKey).get();
        switch (msgTypeEnum) {
            case CLI_LOGIN: {

                chargingPointProtocol = packet(LoginRequestMessage.class, header, data);
                LoginRequestMessage loginRequestMessage=   (LoginRequestMessage) chargingPointProtocol.getBody();
                // 如果设备极短时间内发起两次登录请求，需要清理旧的channel，把旧的channel中设备ID信息抹除，
                // 以防旧的channel关闭时根据设备ID把新的channel关闭了
                Channel oldChannel = ChargingPointChannelManager.getChannelByName(loginRequestMessage.getDevNo());
                if (oldChannel != null){
                    log.info("存在旧的channel，清理旧的channel配置信息，避免后期旧channel关闭时把新的channel关闭了");
                    oldChannel.attr(Constant.integrateNoKey).remove();
                }

                // 存储新channel
                ChargingPointChannelManager.addChannel(loginRequestMessage.getDevNo(), ctx.channel());

                log.info("海康登陆:{}", loginRequestMessage.getDevNo());

                // 将客户端ID作为自定义属性加入到channel中，方便随时channel中获取用户ID
                ctx.channel().attr(Constant.integrateNoKey).setIfAbsent(loginRequestMessage.getDevNo());

                //保存devType
             //   ctx.channel().attr(Constant.devTypeKey).set(header.devType);

                //保存devVer
          //      ctx.channel().attr(Constant.devVerKey).set(header.version);

                // iot设备上线
                if (listener != null) {
                    listener.onConnect(loginRequestMessage.getDevNo());
                }else {
                    return;
                }
                //   ByteBuf byteBuf=  data.readBytes(7);

                //    byte[] bytes = ByteBufUtil.getBytes(byteBuf);
                // TODO:排查内存泄漏的问题
                //     ReferenceCountUtil.release(byteBuf);
                //    bytes = BytesUtil.bytesReverse(bytes);
                //     String devNo = bytesToHex(bytes);
                // 响应设备，业务处理。
                MsgHeader headerAck = JSON.parseObject(JSON.toJSONString(header), MsgHeader.class);
                LoginResponseMessage loginResponseMessage = new LoginResponseMessage(((LoginRequestMessage) chargingPointProtocol.getBody()).getDevNo(), (byte) 0);
                ChargingPointProtocol<LoginResponseMessage> response = new ChargingPointProtocol<>();
                response.setBody(loginResponseMessage);
                headerAck.setCmd(2);
                //       headerAck.setDirection(1);
                //     headerAck.setSubDirection(0);
                response.setHeader(headerAck);
                ctx.channel().writeAndFlush(response);
                break;
            }
            case RATE_VERIFY: {

                chargingPointProtocol = packet(RateVerifyMessage.class, header, data);

                MsgHeader headerAck = JSON.parseObject(JSON.toJSONString(header), MsgHeader.class);
                RateVerifyMessage rateVerifyMessage = ((RateVerifyMessage) chargingPointProtocol.getBody());
                RateVerifyResponseMessage rateVerifyResponseMessage = new RateVerifyResponseMessage(rateVerifyMessage.getDevNo(), rateVerifyMessage.getRateMOdelNo(), (byte) 0x01);
                ChargingPointProtocol<RateVerifyResponseMessage> response = new ChargingPointProtocol<>();
                response.setBody(rateVerifyResponseMessage);
                headerAck.setCmd(6);
                response.setHeader(headerAck);
                ctx.channel().writeAndFlush(response);
                break;
            }
            case CLI_HEARTBEAT: {
                chargingPointProtocol = packet(HeartbeatRequestMessage.class, header, data);
                HeartbeatRequestMessage heartbeatRequestMessage=   (HeartbeatRequestMessage) chargingPointProtocol.getBody();
                // 如果充电桩的channel不存在则不进行回应
                Channel channel = ChargingPointChannelManager.getChannelByName(heartbeatRequestMessage.getDevNo());
                log.info("海康充电桩进行心跳回应:{}", heartbeatRequestMessage.getDevNo());
                if (null == channel) {
                    log.info("海康充电桩的channel不存在则不进行心跳回应");
                    break;
                }

                // 进行心跳回应
   /*             HeartbeatResponseMessage heartbeatResponseMessage = new HeartbeatResponseMessage();
                ChargingPointProtocol<HeartbeatResponseMessage> response = new ChargingPointProtocol<>();
                response.setBody(heartbeatResponseMessage);
                header.setCmd(0x04);

                response.setHeader(header);
                ctx.channel().writeAndFlush(response);*/
                MsgHeader headerAck = JSON.parseObject(JSON.toJSONString(header), MsgHeader.class);
                HeartbeatResponseMessage heartbeatResponseMessage = new HeartbeatResponseMessage(heartbeatRequestMessage.getDevNo(),heartbeatRequestMessage.getGunNo(),0);
             //   LoginResponseMessage loginResponseMessage = new LoginResponseMessage(((LoginRequestMessage) chargingPointProtocol.getBody()).getDevNo(), (byte) 0);
                ChargingPointProtocol<HeartbeatResponseMessage> response = new ChargingPointProtocol<>();
                response.setBody(heartbeatResponseMessage);
                headerAck.setCmd(0x04);
                //       headerAck.setDirection(1);
                //     headerAck.setSubDirection(0);
                response.setHeader(headerAck);
                ctx.channel().writeAndFlush(response);
                break;
            }
            case SER_RESET: {
                log.info("海康-终端复位");
                chargingPointProtocol = packet(ResetResponseMessage.class, header, data);
                break;
            }
            case SER_FIRMWARE: {
                log.info("海康-终端软硬件版本");
                chargingPointProtocol = packet(FirmwareResponseMessage.class, header, data);
                break;
            }
            case SER_SET_QR_FORMAT: {
                log.info("海康-二维码格式");
                chargingPointProtocol = packet(QRCodeResponseMessage.class, header, data);
                break;
            }
            case SER_SET_CONCENTRATOR: {
                log.info("海康-设置集中器");
                chargingPointProtocol = packet(ConcentratorSetResponseMessage.class, header, data);
                break;
            }
            case SER_SET_RATE: {
                log.info("海康-费率设置");
                chargingPointProtocol = packet(ServerSetRateResponseMessage.class, header, data);
                break;
            }
            case SER_GET_RATE: {
                log.info("海康-主站读取终端费率");
                chargingPointProtocol = packet(ServerGetRateResponseMessage.class, header, data);
                break;
            }
            case CLI_RATE_REQ: {
                log.info("海康-终端请求费率");
                chargingPointProtocol = packet(ClientGetRateRequestMessage.class, header, data);
                break;
            }
            case CLI_REAL_TIME_STATUS: {
                log.info("海康-实时状态信息");
                chargingPointProtocol = packet(RealtimeStatusReportMessage.class, header, data);
                break;
            }
            case CLI_GUN_STATUS: {
                log.info("海康-充电枪遥测信息");
                chargingPointProtocol = packet(GunTelemetryReportMessage.class, header, data);
                break;
            }
            case CLI_CHARGING_INFO:
            case CLI_CHARGING_INFO_NEW: {
                log.info("海康-充电实时信息");
                chargingPointProtocol = packet(RealtimeChargingInfoReportMessage.class, header, data);
                break;
            }
            case CLI_NETWORK_INFO: {
                log.info("海康-网络信息");
                chargingPointProtocol = packet(NetWorkReportMessage.class, header, data);
                break;
            }
            case CLI_TELEMETRY: {
                log.info("海康-终端遥测信息");
                chargingPointProtocol = packet(ClientTelemetryReportMessage.class, header, data);
                break;
            }
            case CLI_CARD_AUTH: {
                log.info("海康-刷卡鉴权");
                chargingPointProtocol = packet(CardAuthRequestMessage.class, header, data);
                break;
            }
            case CLI_START_CHARGING: {
                log.info("海康-终端启动充电");
                chargingPointProtocol = packet(CardStartChargingRequestMessage.class, header, data);
                break;
            }
            case CLI_STOP_CHARGING: {
                log.info("海康-终端充电结束");
                chargingPointProtocol = packet(ClientStopChargingRequestMessage.class, header, data);
                break;
            }
            case CLI_STOP_CHARGING_NEW: {
                log.info("海康-终端充电结束新");
                chargingPointProtocol = packet(ClientStopChargingNewRequestMessage.class, header, data);
                break;
            }
            case SERVER_START_CHARGING: {
                log.info("海康-主站启动充电");
                chargingPointProtocol = packet(ServerStartChargingResponseMessage.class, header, data);
                break;
            }
            case SERVER_STOP_CHARGING: {
                log.info("海康-主站停止充电");
                chargingPointProtocol = packet(ServerStopChargingResponseMessage.class, header, data);
                break;
            }
            case VIN_AUTH: {
                log.info("海康-车辆VIN鉴权");
                chargingPointProtocol = packet(VinAuthRequestMessage.class, header, data);
                break;
            }
            case VIN_START_CHARGING: {
                log.info("海康-VIN码启动充电");
                chargingPointProtocol = packet(VinStartChargingRequestMessage.class, header, data);
                break;
            }
        }
        if (listener != null) {
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            JsonNode node = objectMapper.valueToTree(chargingPointProtocol);
            listener.onMessage(new JsonTcpConnectorMessage(node));
        }
    }

    private <T extends MessageDecoder> ChargingPointProtocol<T> packet(Class<T> clazz, MsgHeader msgHeader,
                                                                       ByteBuf data) {

        try {
            T t = clazz.getDeclaredConstructor().newInstance();
            t.decode(data);
            ChargingPointProtocol<T> protocol = new ChargingPointProtocol<>();
            protocol.setHeader(msgHeader);
            protocol.setBody(t);
            return protocol;
        } catch (InstantiationException | IllegalAccessException | InvocationTargetException |
                 NoSuchMethodException e) {
            throw new RuntimeException(e);
        }

    }

    private static String bytesToHex(final byte[] bytes) {
        final int numBytes = bytes.length;
        final char[] container = new char[numBytes * 2];

        for (int i = 0; i < numBytes; i++) {
            final int b = bytes[i] & 0xFF;
            container[i * 2] = Character.forDigit(b >>> 4, 0x10);
            container[i * 2 + 1] = Character.forDigit(b & 0xF, 0x10);
        }

        return new String(container);
    }

}
