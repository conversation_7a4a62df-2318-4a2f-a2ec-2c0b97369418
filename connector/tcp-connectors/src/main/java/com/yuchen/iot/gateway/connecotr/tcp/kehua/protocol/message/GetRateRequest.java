package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @description:服务器下发尖峰平谷计费查询
 * @author: zhonghx
 * @create: 2023-02-15 15:36
 **/

@Data
@AllArgsConstructor
public class GetRateRequest implements MessageEncoder{
    /*查询
    * 1.计费规则模型
    * */
    private byte type;

    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(type);
        return buf;
    }
}
