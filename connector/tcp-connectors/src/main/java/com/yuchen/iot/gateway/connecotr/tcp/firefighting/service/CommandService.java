package com.yuchen.iot.gateway.connecotr.tcp.firefighting.service;

/**
 * 下行命令服务接口
 */
public interface CommandService {
    // 系统命令
    boolean sendReadSystemStatus(String flowId, int systemType, int systemAddress);
    boolean sendReadComponentStatus(String flowId, int systemType, int systemAddress, String componentAddress);
    boolean sendReadAnalogValue(String flowId, int systemType, int systemAddress, String componentAddress);
    boolean sendReadOperationRecord(String flowId, int systemType, int systemAddress, int recordCount, String startTime);
    boolean sendReadSoftwareVersion(String flowId, int systemType, int systemAddress);
    boolean sendReadSystemConfig(String flowId, int systemType, int systemAddress);
    boolean sendReadComponentConfig(String flowId, int systemType, int systemAddress, String componentAddress);
    boolean sendReadSystemTime(String flowId, int systemType, int systemAddress);
    
    // 设备命令
    boolean sendReadDeviceStatus(String flowId);
    boolean sendReadDeviceOperation(String flowId, int recordCount, String startTime);
    boolean sendReadDeviceVersion(String flowId);
    boolean sendReadDeviceConfig(String flowId);
    boolean sendReadDeviceTime(String flowId);
    boolean sendInitDevice(String flowId);
    boolean sendSyncDeviceTime(String flowId, String time);
    boolean sendCheckPost(String flowId, int timeout);
}