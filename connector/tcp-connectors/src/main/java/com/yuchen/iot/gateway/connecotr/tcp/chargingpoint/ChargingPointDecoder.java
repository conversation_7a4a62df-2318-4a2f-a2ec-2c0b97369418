package com.yuchen.iot.gateway.connecotr.tcp.chargingpoint;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yuchen.iot.gateway.connecotr.tcp.JsonTcpConnectorMessage;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.util.BytesUtil;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.util.ExecutorUtils;
import com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.comm.Constant;
import com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.protocol.ChargingPointProtocol;
import com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.protocol.MsgHeader;
import com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.protocol.MsgType;
import com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.protocol.ProtocolConstants;
import com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.protocol.message.*;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.util.HexStringUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import io.netty.util.ReferenceCountUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * 处理海康充电桩 字节流 解码
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023年5月25日 20:03:54
 */
public class ChargingPointDecoder extends ByteToMessageDecoder {

    private static final Logger log = LoggerFactory.getLogger(ChargingPointDecoder.class);

    private static ExecutorService chargingEventExecutor;

    static {
        chargingEventExecutor = new ExecutorUtils(50, 100, 60000L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(100000), "hik-charging-iot-executor");
    }

    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) throws Exception {
        log.info("ChargingPointDecoder---" + HexStringUtil.Bytes2hexStr(ByteBufUtil.getBytes(in)));
        // 如果有粘包情况，则可以多次读取
        while (in.readableBytes() > ProtocolConstants.HEADER_TOTAL_LEN) {
            log.info("ChargingPointDecoder---while---begin ---{}", System.currentTimeMillis());
            in.markReaderIndex();
            byte preamble = in.readByte();
            if (preamble != ProtocolConstants.PREAMBLE) {
                log.info("preamble number is illegal, " + preamble);
                log.info("ChargingPointDecoder---preamble---{}",HexStringUtil.Bytes2hexStr(ByteBufUtil.getBytes(in)));
                in.clear();
                return;
            }

            short frameLength = in.readShortLE();
            if (in.readableBytes() < frameLength + 2) {
                in.resetReaderIndex();
                return;
            }

            byte version = in.readByte();
            byte dir = in.readByte();
            int time = in.readIntLE();
            byte devType = in.readByte();

            ByteBuf byteBuf = in.readBytes(ProtocolConstants.ADDRESS_LEN);
            byte[] bytes = ByteBufUtil.getBytes(byteBuf);
            // TODO:排查内存泄漏的问题
            ReferenceCountUtil.release(byteBuf);
            bytes = BytesUtil.bytesReverse(bytes);
            String address = bytesToHex(bytes);

            short cmd = in.readShortLE();
            ByteBuf data = in.readBytes(frameLength - 15);
            // todo:要进行验证，如果不对，则是脏数据，需要清除
            byte verifyCode = in.readByte();
            byte terminator = in.readByte();

            // 组装消息
            MsgHeader header = new MsgHeader();
            header.setPreamble(preamble);
            header.setFrameLength(frameLength);
            header.setVersion(version);
            header.setDirection(dir & 0x80);
            header.setSubDirection(dir & 0x40);
            header.setTime(time);
            header.setDevType(devType);
            header.setAddress(address);
            header.setCmd(cmd);
            header.setVerifyCode(verifyCode);
            header.setTerminator(terminator);
            chargingEventExecutor.submit(() -> {
                //构建消费发送
                header(ctx, header, data);
                // TODO:排查内存泄漏的问题
                ReferenceCountUtil.release(data);
            });
            log.info("ChargingPointDecoder---while---end---{}", System.currentTimeMillis());
        }
    }

    /**
     * 处理消息
     *
     * @param ctx    通道处理上下文
     * @param header 消息体
     * @param data   数据内容
     */
    public void header(ChannelHandlerContext ctx, MsgHeader header, ByteBuf data) {
        log.info("ChargingPointDecoder---header---{}", JSON.toJSONString(header));
        MsgType msgTypeEnum = MsgType.findByType(header.getCmd());
        if (msgTypeEnum == null) {
            log.info("ChargingPointDecoder---msgTypeEnum---{}", JSON.toJSONString(header));
            return;
        }
        ChargingPointProtocol<?> chargingPointProtocol = null;
        ChargingChannelListener listener = ctx.channel().attr(Constant.eventListenerKey).get();
        switch (msgTypeEnum) {
            case CLI_LOGIN: {

                chargingPointProtocol = packet(LoginRequestMessage.class, header, data);

                // 如果设备极短时间内发起两次登录请求，需要清理旧的channel，把旧的channel中设备ID信息抹除，
                // 以防旧的channel关闭时根据设备ID把新的channel关闭了
                Channel oldChannel = ChargingPointChannelManager.getChannelByName(header.address);
                if (oldChannel != null){
                    log.info("存在旧的channel，清理旧的channel配置信息，避免后期旧channel关闭时把新的channel关闭了");
                    oldChannel.attr(Constant.integrateNoKey).remove();
                }

                // 存储新channel
                ChargingPointChannelManager.addChannel(header.address, ctx.channel());

                log.info("海康登陆:{}", header.address);

                // 将客户端ID作为自定义属性加入到channel中，方便随时channel中获取用户ID
                ctx.channel().attr(Constant.integrateNoKey).setIfAbsent(header.address);

                //保存devType
                ctx.channel().attr(Constant.devTypeKey).set(header.devType);

                //保存devVer
                ctx.channel().attr(Constant.devVerKey).set(header.version);

                // iot设备上线
                if (listener != null) {
                    listener.onConnect(header.address);
                }

                // 响应设备，业务处理。
                MsgHeader headerAck = JSON.parseObject(JSON.toJSONString(header), MsgHeader.class);
                LoginResponseMessage loginResponseMessage = new LoginResponseMessage((byte) 6);
                ChargingPointProtocol<LoginResponseMessage> response = new ChargingPointProtocol<>();
                response.setBody(loginResponseMessage);
                headerAck.setDirection(1);
                headerAck.setSubDirection(0);
                response.setHeader(headerAck);
                ctx.channel().writeAndFlush(response);
                break;
            }
            case CLI_HEARTBEAT: {
                chargingPointProtocol = packet(HeartbeatRequestMessage.class, header, data);

                // 如果充电桩的channel不存在则不进行回应
                Channel channel = ChargingPointChannelManager.getChannelByName(header.address);
                log.info("海康充电桩进行心跳回应:{}", header.address);
                if (null == channel) {
                    log.info("海康充电桩的channel不存在则不进行心跳回应");
                    break;
                }

                // 进行心跳回应
                HeartbeatResponseMessage heartbeatResponseMessage = new HeartbeatResponseMessage();
                ChargingPointProtocol<HeartbeatResponseMessage> response = new ChargingPointProtocol<>();
                response.setBody(heartbeatResponseMessage);
                header.setDirection(1);
                header.setSubDirection(0);
                response.setHeader(header);
                ctx.channel().writeAndFlush(response);
                break;
            }
            case SER_RESET: {
                log.info("海康-终端复位");
                chargingPointProtocol = packet(ResetResponseMessage.class, header, data);
                break;
            }
            case SER_FIRMWARE: {
                log.info("海康-终端软硬件版本");
                chargingPointProtocol = packet(FirmwareResponseMessage.class, header, data);
                break;
            }
            case SER_SET_QR_FORMAT: {
                log.info("海康-二维码格式");
                chargingPointProtocol = packet(QRCodeResponseMessage.class, header, data);
                break;
            }
            case SER_SET_CONCENTRATOR: {
                log.info("海康-设置集中器");
                chargingPointProtocol = packet(ConcentratorSetResponseMessage.class, header, data);
                break;
            }
            case SER_SET_RATE: {
                log.info("海康-费率设置");
                chargingPointProtocol = packet(ServerSetRateResponseMessage.class, header, data);
                break;
            }
            case SER_GET_RATE: {
                log.info("海康-主站读取终端费率");
                chargingPointProtocol = packet(ServerGetRateResponseMessage.class, header, data);
                break;
            }
            case CLI_RATE_REQ: {
                log.info("海康-终端请求费率");
                chargingPointProtocol = packet(ClientGetRateRequestMessage.class, header, data);
                break;
            }
            case CLI_REAL_TIME_STATUS: {
                log.info("海康-实时状态信息");
                chargingPointProtocol = packet(RealtimeStatusReportMessage.class, header, data);
                break;
            }
            case CLI_GUN_STATUS: {
                log.info("海康-充电枪遥测信息");
                chargingPointProtocol = packet(GunTelemetryReportMessage.class, header, data);
                break;
            }
            case CLI_CHARGING_INFO:
            case CLI_CHARGING_INFO_NEW: {
                log.info("海康-充电实时信息");
                chargingPointProtocol = packet(RealtimeChargingInfoReportMessage.class, header, data);
                break;
            }
            case CLI_NETWORK_INFO: {
                log.info("海康-网络信息");
                chargingPointProtocol = packet(NetWorkReportMessage.class, header, data);
                break;
            }
            case CLI_TELEMETRY: {
                log.info("海康-终端遥测信息");
                chargingPointProtocol = packet(ClientTelemetryReportMessage.class, header, data);
                break;
            }
            case CLI_CARD_AUTH: {
                log.info("海康-刷卡鉴权");
                chargingPointProtocol = packet(CardAuthRequestMessage.class, header, data);
                break;
            }
            case CLI_START_CHARGING: {
                log.info("海康-终端启动充电");
                chargingPointProtocol = packet(CardStartChargingRequestMessage.class, header, data);
                break;
            }
            case CLI_STOP_CHARGING: {
                log.info("海康-终端充电结束");
                chargingPointProtocol = packet(ClientStopChargingRequestMessage.class, header, data);
                break;
            }
            case CLI_STOP_CHARGING_NEW: {
                log.info("海康-终端充电结束新");
                chargingPointProtocol = packet(ClientStopChargingNewRequestMessage.class, header, data);
                break;
            }
            case SERVER_START_CHARGING: {
                log.info("海康-主站启动充电");
                chargingPointProtocol = packet(ServerStartChargingResponseMessage.class, header, data);
                break;
            }
            case SERVER_STOP_CHARGING: {
                log.info("海康-主站停止充电");
                chargingPointProtocol = packet(ServerStopChargingResponseMessage.class, header, data);
                break;
            }
            case VIN_AUTH: {
                log.info("海康-车辆VIN鉴权");
                chargingPointProtocol = packet(VinAuthRequestMessage.class, header, data);
                break;
            }
            case VIN_START_CHARGING: {
                log.info("海康-VIN码启动充电");
                chargingPointProtocol = packet(VinStartChargingRequestMessage.class, header, data);
                break;
            }
        }
        if (listener != null) {
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            JsonNode node = objectMapper.valueToTree(chargingPointProtocol);
            listener.onMessage(new JsonTcpConnectorMessage(node));
        }
    }

    private <T extends MessageDecoder> ChargingPointProtocol<T> packet(Class<T> clazz, MsgHeader msgHeader,
                                                                       ByteBuf data) {

        try {
            T t = clazz.getDeclaredConstructor().newInstance();
            t.decode(data);
            ChargingPointProtocol<T> protocol = new ChargingPointProtocol<>();
            protocol.setHeader(msgHeader);
            protocol.setBody(t);
            return protocol;
        } catch (InstantiationException | IllegalAccessException | InvocationTargetException |
                NoSuchMethodException e) {
            throw new RuntimeException(e);
        }

    }

    private static String bytesToHex(final byte[] bytes) {
        final int numBytes = bytes.length;
        final char[] container = new char[numBytes * 2];

        for (int i = 0; i < numBytes; i++) {
            final int b = bytes[i] & 0xFF;
            container[i * 2] = Character.forDigit(b >>> 4, 0x10);
            container[i * 2 + 1] = Character.forDigit(b & 0xF, 0x10);
        }

        return new String(container);
    }

}
