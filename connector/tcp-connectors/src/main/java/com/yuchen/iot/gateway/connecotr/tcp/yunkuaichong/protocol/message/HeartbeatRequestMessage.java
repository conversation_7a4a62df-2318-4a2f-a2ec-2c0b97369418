package com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.message;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.BCDUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.Data;


/**
 * @description: 终端心跳请求信息
 * @author: zhong
 * @create: 2022-12-08 16:55
 **/
@Data
@JsonIgnoreProperties(value = { "hibernateLazyInitializer", "handler" })
public class HeartbeatRequestMessage implements MessageDecoder {
    private String devNo;
    private String gunNo;
    private int gunStatus;
    @Override
    public void decode(ByteBuf in) {
        ByteBuf byteBuf=  in.readBytes(7);
        byte[] bytes = ByteBufUtil.getBytes(byteBuf);
        devNo = BCDUtil.bytesToHex(bytes);

        ByteBuf byteBuf2=  in.readBytes(1);
        byte[] bytes2 = ByteBufUtil.getBytes(byteBuf2);
        gunNo = BCDUtil.bytesToHex(bytes2);

        gunStatus= in.readByte();
    }
}
