package com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol;


import lombok.Getter;


public enum GunState {

    /*----控制----*/
    IDLE(0),                /*空闲*/
    CHARGING(1),            /*充电*/
    COMPLETED(2),           /*充完*/
    FAULT(4),               /*故障*/
    WAIT_CHARGING(5),       /*等待充电*/
    ORDER(6),               /*预约*/
    UPDATE(7);              /*在线更新*/
    @Getter
    private final int value;

    GunState(int value) {
        this.value = value;
    }

    public static GunState findByValue(int value) {
        for (GunState msgType : GunState.values()) {
            if (msgType.getValue() == value) {
                return msgType;
            }
        }
        return null;
    }
}
