package com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong;


import com.yuchen.iot.gateway.connecotr.tcp.chargepile.util.BytesUtil;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.util.HexStringUtil;
import com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.comm.Constant;
import com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.ChargingPointProtocol;
import com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.MsgHeader;
import com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.message.MessageEncoder;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;
import io.netty.util.ReferenceCountUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class ChargingPointEncoder extends MessageToByteEncoder<ChargingPointProtocol<Object>> {

    private static final Logger log = LoggerFactory.getLogger(ChargingPointEncoder.class);

    @Override
    protected void encode(ChannelHandlerContext ctx, ChargingPointProtocol<Object> msg, ByteBuf byteBuf) throws Exception {

    //    Integer devType = ctx.channel().attr(Constant.devTypeKey).get();
     //   Integer devVer = ctx.channel().attr(Constant.devVerKey).get();
        MessageEncoder messageEncoder = (MessageEncoder)msg.getBody();
        ByteBuf buf = messageEncoder.encode();
        int count=buf.readableBytes();
        MsgHeader header = msg.getHeader();
        byteBuf.writeByte(header.getPreamble());
        byteBuf.writeByte(4 + buf.readableBytes());
        byteBuf.writeShort(header.getVersion());
        byteBuf.writeByte(header.getEncryptionFlag());
        byteBuf.writeByte(header.getCmd());
        byteBuf.writeBytes(buf);
        byte[] byteArray = new byte[byteBuf.readableBytes()];
        byteBuf.readBytes(byteArray);
        System.out.println("a======" +HexStringUtil.Bytes2hexStr(ByteBufUtil.getBytes(byteBuf)));
        System.out.println("c======" +HexStringUtil.Bytes2hexStr(byteArray));
        byte[] newDecryptArray = new byte[byteArray.length -2];
        System.arraycopy(byteArray, 2, newDecryptArray, 0, byteArray.length-2);
        int crc= CRC16Util.modbusCRC(newDecryptArray);


        System.out.println(CRC16Util.intToHex2(crc));
        byteBuf.writeBytes(byteArray);
        byteBuf.writeBytes(hexStringToByteArray1(CRC16Util.intToHex2(crc)));
        System.out.println("b======" +HexStringUtil.Bytes2hexStr(ByteBufUtil.getBytes(byteBuf)));
     //   byteBuf.writeByte(header.getTerminator());
        // TODO:排查内存泄漏的问题
        ReferenceCountUtil.release(buf);

        log.info("ChargingPointEncoder---"+header.getCmd()+"---"+ HexStringUtil.Bytes2hexStr(ByteBufUtil.getBytes(byteBuf)));

    }

    public static void main(String[] args) {
        String data = "680C000000021872178170000000";
        byte[] bytes = hexStringToByteArray1(data);
        byte[] newDecryptArray = new byte[bytes.length -2];
        System.arraycopy(bytes, 2, newDecryptArray, 0, bytes.length-2);
        int crc= CRC16Util.modbusCRC(newDecryptArray);
        System.out.println(CRC16Util.intToHex2(crc));


        short crc1 = crc16X25(data);
        System.out.printf("CRC校验码: %04X\n", crc1);
    }



    public static byte[] hexStringToByteArray1(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
                    + Character.digit(s.charAt(i + 1), 16));
        }
        return data;
    }
    public static short crc16X25(String data) {
        int crc = 0xFFFF; // 初始值
        for (int i = 0; i < data.length(); i++) {
            int index = (crc ^ data.charAt(i)) & 0xFF;
            crc = (crc >>> 8) ^ (index * 0x1D) & 0xFFFF; // 0x1D 是CRC-16/X-25的多项式
        }
        return (short) crc;
    }
    public static byte[] hexStringToByteArray(String hexString) {
      /*  int len = hexString.length();
        byte[] byteArray = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            byteArray[i / 2] = (byte) ((Character.digit(hexString.charAt(i), 16) << 4)
                    + Character.digit(hexString.charAt(i+1), 16));
        }*/
        int len = hexString.length();
        byte[] byteArray = new byte[len / 2];
        for (int i = 0; i < len - 1; i += 2) {
            byteArray[i / 2] = (byte) ((Character.digit(hexString.charAt(i), 16) << 4)
                    + Character.digit(hexString.charAt(i + 1), 16));
        }
        return byteArray;
    }

    private static byte charToByte(char c) {
        return (byte) "0123456789ABCDEF".indexOf(c);
    }

    public static byte[] hexString2Bytes(String hex) {

        if ((hex == null) || (hex.equals(""))){
            return null;
        }
        else if (hex.length()%2 != 0){
            return null;
        }
        else{
            hex = hex.toUpperCase();
            int len = hex.length()/2;
            byte[] b = new byte[len];
            char[] hc = hex.toCharArray();
            for (int i=0; i<len; i++){
                int p=2*i;
                b[i] = (byte) (charToByte(hc[p]) << 4 | charToByte(hc[p+1]));
            }
            return b;
        }

    }
}
