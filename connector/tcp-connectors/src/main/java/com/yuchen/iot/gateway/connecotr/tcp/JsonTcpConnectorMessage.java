package com.yuchen.iot.gateway.connecotr.tcp;

import com.fasterxml.jackson.databind.JsonNode;
import com.yuchen.iot.gateway.connector.api.data.UpLinkContentType;
import com.yuchen.iot.gateway.util.JacksonUtil;

/**
 * TODO 
 * <AUTHOR>
 * @date 2022/12/2 下午1:50
 * @version 2.0.0
 */
public class JsonTcpConnectorMessage extends TcpConnectorMessage<JsonNode> {

    public JsonTcpConnectorMessage(JsonNode message) {
        super(message);
    }

    @Override
    public UpLinkContentType getContentType() {
        return UpLinkContentType.JSON;
    }

    @Override
    public byte[] getMessageInBytes() {
        return JacksonUtil.writeValueAsBytes(this.message);
    }
}
