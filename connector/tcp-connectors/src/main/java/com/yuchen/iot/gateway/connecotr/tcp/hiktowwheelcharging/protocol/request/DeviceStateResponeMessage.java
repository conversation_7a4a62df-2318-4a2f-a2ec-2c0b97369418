package com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.request;

import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.message.MessageEncoder;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.Data;


@Data
public class DeviceStateResponeMessage implements MessageEncoder {



    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        return buf;
    }
}
