package com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.message;

import com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.BCDUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import io.netty.buffer.ByteBufUtil;
import io.netty.buffer.Unpooled;
import lombok.AllArgsConstructor;
import lombok.Data;


/**
 * @description: 平台应答终端登录请求
 * @author: zhong
 * @create: 2022-12-08 16:55
 **/
@Data
@AllArgsConstructor
public class LoginResponseMessage implements MessageEncoder {

    public String devNo;
    /*登入结果，6表示成功，0表示失败*/
    public byte loginResult;

    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
      //  buf.writeByte(devNo);
    //    Unpooled.copiedBuffer(devNo, java.nio.charset.StandardCharsets.UTF_8);
        buf.writeBytes(BCDUtil.hexStringToByteArray(devNo));
        buf.writeByte(loginResult);
        return buf;
    }


}

