package com.yuchen.iot.gateway.connecotr.tcp.chargepile.business;

import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class BusinessHolder {
    private Map<String, IBusiness> handlers = new ConcurrentHashMap<>();


    public IBusiness getBusiness(String type) {
        return handlers.get(type);
    }

  public void register(String type, IBusiness handler) {
        handlers.put(type, handler);
    }
}
