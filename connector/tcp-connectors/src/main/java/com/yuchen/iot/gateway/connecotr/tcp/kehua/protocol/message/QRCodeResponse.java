package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import lombok.Data;

/**
 * @description: 充电桩应答二维码设置
 * @author: zhonghx
 * @create: 2023-02-16 10:42
 **/

@Data
public class QRCodeResponse implements MessageDecoder{

    /*0失败
    * 1成功
    * 2长度越界*/
    private byte ack;
    @Override
    public void decode(ByteBuf in) {
        ack = in.readByte();
    }
}

