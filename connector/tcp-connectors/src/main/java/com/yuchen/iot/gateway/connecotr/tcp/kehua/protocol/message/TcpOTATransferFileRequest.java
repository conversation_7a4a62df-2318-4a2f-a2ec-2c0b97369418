package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @description: 服务器下发升级文件数据
 * @author: zhonghx
 * @create: 2023-02-14 13:49
 * 1. 为保证升级数据准确性，发送序号必须严格每发送一次不同文件数据内容时
 * 依次加 1，服务器只能收到桩应答完本次发送的 SN 文件数据后才能再发下
 * 一包。
 * 2. 此报文在没有收到桩应答时，以整个应用帧要完全一样间隔 5s 频率重发，
 * 超时次数为 3 次。
 * 3. 只允许最后一包报文因剩余数据长度没有达到指定的数据长度 N 时，所发
 * 送的数据长度按实际剩余长度发送，其它的数据报文都必须按 2.15.2 中的
 * 桩上报的单包长度长度发送。
 * 4. 接收到充电桩应答失败时，从失败的 SN 号开始，重发后续数据帧。
 * 2.12.4 充电桩应答服务器下发升级文件数据指令
 * 报文功能：应答服务器指令，代表桩已正确接收服务下发的升级数据，可以
 * 接收下一包了。
 **/
@Data
@AllArgsConstructor
public class TcpOTATransferFileRequest implements MessageEncoder{

    /*发送序号SN*/
    private short sn;

    /*文件数据 hex字符串*/
    private String data;
    @Override
    public ByteBuf encode() {

        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeShort(sn);
        //TODO base64 ==> byte
        //buf.writeBytes(data.getBytes(StandardCharsets));
        return buf;
    }
}
