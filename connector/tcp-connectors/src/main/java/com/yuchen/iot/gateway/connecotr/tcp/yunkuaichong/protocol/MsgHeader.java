package com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol;

import lombok.Data;


@Data
public class MsgHeader {

    /*起始符，0x68*/
    public int preamble=0x68;

    /*帧长度，协议版本至数据，总字节长度*/
    public int frameLength;

    /*序列号域，0x20*/
    public int version=0;

    /*加密标志*/
    public int encryptionFlag=0;


    /*命令*/
    public int cmd;

    /*数据*/
    //public ByteBuf data;

    /*验证码*/
    public int verifyCode;

}
