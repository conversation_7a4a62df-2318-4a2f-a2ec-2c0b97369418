package com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.protocol.message;

import io.netty.buffer.ByteBuf;
import lombok.Data;

/**
 * @description: 遥测信息由终端定期上报，主站无需应答
 * @author: zhong
 * @create: 2022-12-08 19:27
 **/
@Data
public class ClientTelemetryReportMessage implements MessageDecoder{

    /*终端温度*/
    private byte temperature;

    /*充电总时间*/
    private int chargingTotalTime;

    /*充电总次数*/
    private int chargingTotalCount;

    /*充电总电量*/
    private int chargingTotalElectricity;

    @Override
    public void decode(ByteBuf in) {

        temperature = in.readByte();
        chargingTotalTime = in.readIntLE();
        chargingTotalCount = in.readIntLE();
        chargingTotalElectricity = in.readIntLE();

    }
}
