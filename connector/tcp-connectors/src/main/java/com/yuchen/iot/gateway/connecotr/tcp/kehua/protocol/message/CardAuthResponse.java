package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * @description: 主站刷卡鉴权响应
 * @author: zhonghx
 * @create: 2022-12-09 11:10
 **/
@Data
@AllArgsConstructor
public class CardAuthResponse implements MessageEncoder {


    /*用户卡号 20字节 ASCII 码，不足 20 位填'\0'*/
    private String cardNumber;

    /*卡类型*/
    private byte cardType;

    /*账户余额，单元元，2位小数*/
    private int balances;

    /*卡状态
    * 1.认证成功
    * 2.账户已过期
    * 3.账户无效，账号不存在
    * 4.账号有效，但当前正在其他桩上进行充电
    * 5.账号密码错误
    * 6.余额不足
    * */
    private int cardState;

    /*充电流水号*/
    private int chargingSN;

    /*停止校验码 手机后四位2*/
    private short verifyCode;
    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeBytes(cardNumber.getBytes(StandardCharsets.US_ASCII));
        buf.writeByte(cardType);
        buf.writeInt(balances);
        buf.writeByte(cardState);
        buf.writeInt(chargingSN);
        buf.writeShort(verifyCode);
        return buf;
    }
}
