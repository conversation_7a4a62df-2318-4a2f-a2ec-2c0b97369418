package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.ActiveReportData;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.DataType;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.TerminalData;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.util.BCDUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.Data;

import java.util.HashMap;

/**
 * @description: 主动上传数据，用于充电枪变化数据主动上传，
 * 需要主动上传的数据为充电枪状态、车辆 连接状态、告警及充电
 * 数据同步。如果终端在 5S 内未收到平台回复确认，则 重复上传
 * 该数据帧
 * @author: zhonghx
 * @create: 2023-02-14 15:35
 **/
@Data
public class RealTimeGunStateChangedRequest implements MessageDecoder{


    /*帧ID*/
    private byte frameID;

    /*数据单元个数*/
    private byte dataCount;

    /*
     * name--value
     * */
    private HashMap<String, Object> dataMap = new HashMap<>();

    @Override
    public void decode(ByteBuf in) {
        frameID = in.readByte();
        dataCount = in.readByte();

        for (int i = 0; i < dataCount; i++) {

            int id = in.readShort();
            int len = in.readByte();

            ActiveReportData data = ActiveReportData.idOf(id);
            if (data == null){
                throw new IllegalArgumentException("dataType is illegal" );
            }
            DataType dataType = data.getType();
            if (dataType == DataType.UINT_8 ){
                Integer value = (int) in.readByte();
                dataMap.put(data.getKey(),value);
            }else if (dataType == DataType.UINT_16 ){
                Integer value = (int) in.readShort();
                dataMap.put(data.getKey(),value);
            }else if (dataType == DataType.UINT_32 ){
                Integer value = (int) in.readInt();
                dataMap.put(data.getKey(),value);
            }else if (dataType == DataType.BCD){
                byte[] timeBytes = ByteBufUtil.getBytes(in.readBytes(len));
                String time = BCDUtil.bcdToStr(timeBytes);
                dataMap.put(data.getKey(),time);
            }else if(dataType == DataType.ASCII){
                byte[] contentBytes = ByteBufUtil.getBytes(in.readBytes(len));
                String content = new String(contentBytes);
                dataMap.put(data.getKey(),content);
            }else if(dataType == DataType.RESERVED){
                byte[] b = ByteBufUtil.getBytes(in.readBytes(len));
            }else {
                throw new IllegalArgumentException("dataType is illegal" );
            }
        }
    }
}
