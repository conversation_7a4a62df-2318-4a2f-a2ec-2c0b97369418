package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.Data;

/**
 * @description: 充电桩上传升级结果
 * @author: zhonghx
 * @create: 2023-02-14 14:45
 **/

@Data
public class TcpOTAResultRequest implements MessageDecoder{

    /*结果
    * 0成功 1校验失败
    * */
    private byte ack;

    /*版本信息 30字节*/
    private String version;

    @Override
    public void decode(ByteBuf in) {
        ack = in.readByte();
        byte[] versionBytes = ByteBufUtil.getBytes(in.readBytes(30));
        version = new String(versionBytes);
    }
}
