package com.yuchen.iot.gateway.connecotr.tcp.firefighting.entity;

import lombok.Data;

@Data
public class DownCommandMessage {
    private String header;           // 启动符 @@
    private String flowId;           // 业务流水号
    private String version;          // 协议版本号
    private String time;             // 时间标签
    private String srcAddress;       // 源地址
    private String destAddress;      // 目的地址
    private String dataLength;       // 应用数据单元长度
    private String command;          // 命令字节
    private String data;             // 应用数据单元
    private String checksum;         // 校验和
    private String end;              // 结束符 ##
    
/*    // 命令类型常量
    public static final String CMD_READ_SYSTEM_STATUS = "61";        // 读建筑消防设施系统状态
    public static final String CMD_READ_COMPONENT_STATUS = "62";     // 读建筑消防设施部件运行状态
    public static final String CMD_READ_ANALOG_VALUE = "63";         // 读建筑消防设施部件模拟量值
    public static final String CMD_READ_OPERATION_RECORD = "64";     // 读建筑消防设施操作信息记录
    public static final String CMD_READ_SOFTWARE_VERSION = "65";     // 读建筑消防设施软件版本
    public static final String CMD_READ_SYSTEM_CONFIG = "66";        // 读建筑消防设施系统配置情况
    public static final String CMD_READ_COMPONENT_CONFIG = "67";     // 读建筑消防设施部件配置情况
    public static final String CMD_READ_SYSTEM_TIME = "68";          // 读建筑消防设施系统时间
    public static final String CMD_READ_DEVICE_STATUS = "81";        // 读用户信息传输装置运行状态
    public static final String CMD_READ_DEVICE_OPERATION = "84";     // 读用户信息传输装置操作信息记录
    public static final String CMD_READ_DEVICE_VERSION = "85";       // 读用户信息传输装置软件版本
    public static final String CMD_READ_DEVICE_CONFIG = "86";        // 读用户信息传输装置配置情况
    public static final String CMD_READ_DEVICE_TIME = "88";          // 读用户信息传输装置系统时间
    public static final String CMD_INIT_DEVICE = "89";               // 初始化用户信息传输装置
    public static final String CMD_SYNC_DEVICE_TIME = "90";          // 同步用户信息传输装置时钟
    public static final String CMD_CHECK_POST = "91";                // 查岗命令*/
} 