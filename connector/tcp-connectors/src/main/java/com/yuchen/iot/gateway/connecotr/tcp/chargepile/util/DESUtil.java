package com.yuchen.iot.gateway.connecotr.tcp.chargepile.util;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

public class DESUtil {
	public static final byte[] key = hexStringToBytes("AA3FAC4C691A889D306F73553C5B4F90AA3FAC4C691A889D");
	/**
	 * 生成密钥
	 * @throws Exception 
	 */
	public static byte[] initKey() throws Exception{
		//密钥生成器
		KeyGenerator keyGen = KeyGenerator.getInstance("DESede");
		//初始化密钥生成器
		keyGen.init(168);   //可指定密钥长度为112或168，默认168
		//生成密钥
		SecretKey secretKey = keyGen.generateKey();
		return secretKey.getEncoded();
	}
	
	/**
	 * 加密
	 * @throws Exception 
	 */
	public static byte[] encrypt3DES(byte[] data, byte[] key) throws Exception{
		//恢复密钥
		SecretKey secretKey = new SecretKeySpec(key, "DESede");
		//Cipher完成加密
		Cipher cipher = Cipher.getInstance("DESede/ECB/NoPadding");
		//cipher初始化
		cipher.init(Cipher.ENCRYPT_MODE, secretKey);
		byte[] encrypt = cipher.doFinal(data);
		
		return encrypt;
	}
	
	/**
	 * 解密
	 */
	public static byte[] decrypt3DES(byte[] data, byte[] key) throws Exception{
		//恢复密钥
		SecretKey secretKey = new SecretKeySpec(key, "DESede");
		//Cipher完成解密
		Cipher cipher = Cipher.getInstance("DESede/ECB/NoPadding");
		//初始化cipher
		cipher.init(Cipher.DECRYPT_MODE, secretKey);
		byte[] plain = cipher.doFinal(data);
		
		return plain;
	}



	public static void main(String[] args) throws Exception{
		byte[] data = hexStringToBytes("0015011B121406313233343536000000");
		byte[] key = hexStringToBytes("AA3FAC4C691A889D306F73553C5B4F90AA3FAC4C691A889D");
		//String res = new String(decrypt3DES(data,key),"ASCII");
		byte[] result = encrypt3DES(data,key);
		System.out.println(bytesToHexString(result));
	}

	public static byte[] hexStringToBytes(String hexString) {
		if (hexString == null || hexString.equals("")) {
			return null;
		}
		// toUpperCase将字符串中的所有字符转换为大写
		hexString = hexString.toUpperCase();
		int length = hexString.length() / 2;
		// toCharArray将此字符串转换为一个新的字符数组。
		char[] hexChars = hexString.toCharArray();
		byte[] d = new byte[length];
		for (int i = 0; i < length; i++) {
			int pos = i * 2;
			d[i] = (byte) (charToByte(hexChars[pos]) << 4
					| charToByte(hexChars[pos + 1]));
		}
		return d;
	}

	public static String bytesToHexString(byte... src) {
		StringBuilder stringBuilder = new StringBuilder();
		if (src == null || src.length <= 0) {
			return null;
		}
		for (int i = 0; i < src.length; i++) {
			int v = src[i] & 0xFF;
			String hv = Integer.toHexString(v);
			if (hv.length() < 2) {
				stringBuilder.append(0);
			}
			stringBuilder.append(hv);
		}
		return stringBuilder.toString();
	}

	// charToByte返回在指定字符的第一个发生的字符串中的索引，即返回匹配字符
	private static byte charToByte(char c) {
		return (byte) "0123456789ABCDEF".indexOf(c);
	}

}
