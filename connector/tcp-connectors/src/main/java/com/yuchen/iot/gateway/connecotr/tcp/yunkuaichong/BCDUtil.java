package com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong;

/**
 * @description: BCD编码工具类
 * @author: zhonghx
 * @create: 2022-12-10 14:12
 **/
public class BCDUtil {


    private BCDUtil(){};

    /**
     * 字符串转BCD码
     * @param asc ASCII字符串
     * @return BCD
     */
    public static byte[] strToBcd(String asc) {
        byte[] s = asc.getBytes();
        byte[] b = new byte[s.length/2];
        for(int i=0;i<b.length;i++) {
            b[i] = (byte) (s[2*i] << 4 | (s[2*i+1] & 0xf));
        }
        return b;
    }

    /**
     * BCD转ASCII字符串
     * @param bytes BCD byte数组
     * @return ASCII字符串
     */
    public static String bcdToStr(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for(int i=0;i<bytes.length;i++) {
            sb.append(bytes[i]>>4&0xf)
                    .append(bytes[i]&0xf);
        }
        return sb.toString();
    }

    public static byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
                    + Character.digit(s.charAt(i + 1), 16));
        }
        return data;
    }
    public static String bytesToHex(final byte[] bytes) {
        final int numBytes = bytes.length;
        final char[] container = new char[numBytes * 2];

        for (int i = 0; i < numBytes; i++) {
            final int b = bytes[i] & 0xFF;
            container[i * 2] = Character.forDigit(b >>> 4, 0x10);
            container[i * 2 + 1] = Character.forDigit(b & 0xF, 0x10);
        }

        return new String(container);
    }

}