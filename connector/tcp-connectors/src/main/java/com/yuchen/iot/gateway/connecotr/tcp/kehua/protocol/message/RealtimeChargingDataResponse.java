package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @description: 充电实时数据主动上传应答
 * @author: zhonghx
 * @create: 2023-02-13 11:13
 **/
@Data
@AllArgsConstructor
public class RealtimeChargingDataResponse implements MessageEncoder{

    /*确认标识 0确认失败 1确认成功*/
    private byte ack;
    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(ack);
        return buf;
    }
}
