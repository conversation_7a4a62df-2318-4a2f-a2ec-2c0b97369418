package com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;


/**
 * @description: 平台应答终端登录请求
 * @author: zhong
 * @create: 2022-12-08 16:55
 **/
@Data
@AllArgsConstructor
public class RateVerifyResponseMessage implements MessageEncoder {

    public String devNo;
    /*登入结果，6表示成功，0表示失败*/

    public String rateMOdelNo;


    public byte result;

    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
      //  buf.writeByte(devNo);
    //    Unpooled.copiedBuffer(devNo, java.nio.charset.StandardCharsets.UTF_8);
        buf.writeBytes(hexStringToByteArray(devNo));
        buf.writeBytes(hexStringToByteArray(rateMOdelNo));
        buf.writeByte(result);
        return buf;
    }

    public static byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
                    + Character.digit(s.charAt(i + 1), 16));
        }
        return data;
    }
}

