package com.yuchen.iot.gateway.connecotr.tcp.firefighting.service.impl;


import com.yuchen.iot.gateway.connecotr.tcp.firefighting.entity.DownCommandMessage;
import com.yuchen.iot.gateway.connecotr.tcp.firefighting.service.CommandService;
import com.yuchen.iot.gateway.connecotr.tcp.firefighting.util.DownCommandUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CommandServiceImpl implements CommandService {

    // 固定值设置
    private static final String TARGET_IP = "*************";
    private static final String SRC_ADDRESS = "000000000000";
    private static final String DEST_ADDRESS = "000000000000";

    /**
     * 发送读建筑消防设施系统状态命令
     */
    @Override
    public boolean sendReadSystemStatus(String flowId, int systemType, int systemAddress) {
        String commandData = DownCommandUtil.buildReadSystemStatusCommand(systemType, systemAddress);
        return sendCommand(flowId, commandData);
    }

    /**
     * 发送读建筑消防设施部件运行状态命令
     */
    @Override
    public boolean sendReadComponentStatus(String flowId, int systemType, int systemAddress, String componentAddress) {
        String commandData = DownCommandUtil.buildReadComponentStatusCommand(systemType, systemAddress, componentAddress);
        return sendCommand(flowId, commandData);
    }

    /**
     * 发送读建筑消防设施部件模拟量值命令
     * 
     * @param systemType 系统类型
     * @param systemAddress 系统地址
     * @param componentAddress 部件地址
     * @return 发送结果
     */
    @Override
    public boolean sendReadAnalogValue(String flowId, int systemType, int systemAddress, String componentAddress) {
        String commandData = DownCommandUtil.buildReadAnalogValueCommand(systemType, systemAddress, componentAddress);
        return sendCommand(flowId, commandData);
    }

    /**
     * 发送读建筑消防设施操作信息记录命令
     * 
     * @param systemType 系统类型
     * @param systemAddress 系统地址
     * @param recordCount 记录数量
     * @param startTime 起始时间
     * @return 发送结果
     */
    @Override
    public boolean sendReadOperationRecord(String flowId, int systemType, int systemAddress, int recordCount, String startTime) {
        String commandData = DownCommandUtil.buildReadOperationRecordCommand(systemType, systemAddress, recordCount, startTime);
        return sendCommand(flowId, commandData);
    }

    /**
     * 发送读建筑消防设施软件版本命令
     * 
     * @param systemType 系统类型
     * @param systemAddress 系统地址
     * @return 发送结果
     */
    @Override
    public boolean sendReadSoftwareVersion(String flowId, int systemType, int systemAddress) {
        String commandData = DownCommandUtil.buildReadSoftwareVersionCommand(systemType, systemAddress);
        return sendCommand(flowId, commandData);
    }

    /**
     * 发送读建筑消防设施系统配置情况命令
     * 
     * @param systemType 系统类型
     * @param systemAddress 系统地址
     * @return 发送结果
     */
    @Override
    public boolean sendReadSystemConfig(String flowId, int systemType, int systemAddress) {
        String commandData = DownCommandUtil.buildReadSystemConfigCommand(systemType, systemAddress);
        return sendCommand(flowId, commandData);
    }

    /**
     *
     *
     * 
     * @param systemType 系统类型
     * @param systemAddress 系统地址
     * @param componentAddress 部件地址
     * @return 发送结果
     */
    @Override
    public boolean sendReadComponentConfig(String flowId, int systemType, int systemAddress, String componentAddress) {
        String commandData = DownCommandUtil.buildReadComponentConfigCommand(systemType, systemAddress, componentAddress);
        return sendCommand(flowId, commandData);
    }

    /**
     * 发送读建筑消防设施系统时间命令
     * 
     * @param systemType 系统类型
     * @param systemAddress 系统地址
     * @return 发送结果
     */
    @Override
    public boolean sendReadSystemTime(String flowId, int systemType, int systemAddress) {
        String commandData = DownCommandUtil.buildReadSystemTimeCommand(systemType, systemAddress);
        return sendCommand(flowId, commandData);
    }

    /**
     * 发送读用户信息传输装置运行状态命令
     * 
     * @return 发送结果
     */
    @Override
    public boolean sendReadDeviceStatus(String flowId) {
        String commandData = DownCommandUtil.buildReadDeviceStatusCommand();
        return sendCommand(flowId, commandData);
    }

    /**
     * 发送读用户信息传输装置操作信息记录命令
     * 
     * @param recordCount 记录数量
     * @param startTime 起始时间
     * @return 发送结果
     */
    @Override
    public boolean sendReadDeviceOperation(String flowId, int recordCount, String startTime) {
        String commandData = DownCommandUtil.buildReadDeviceOperationCommand(recordCount, startTime);
        return sendCommand(flowId, commandData);
    }

    /**
     * 发送读用户信息传输装置软件版本命令
     * 
     * @return 发送结果
     */
    @Override
    public boolean sendReadDeviceVersion(String flowId) {
        String commandData = DownCommandUtil.buildReadDeviceVersionCommand();
        return sendCommand(flowId, commandData);
    }

    /**
     * 发送读用户信息传输装置配置情况命令
     * 
     * @return 发送结果
     */
    @Override
    public boolean sendReadDeviceConfig(String flowId) {
        String commandData = DownCommandUtil.buildReadDeviceConfigCommand();
        return sendCommand(flowId, commandData);
    }

    /**
     * 发送读用户信息传输装置系统时间命令
     * 
     * @return 发送结果
     */
    @Override
    public boolean sendReadDeviceTime(String flowId) {
        String commandData = DownCommandUtil.buildReadDeviceTimeCommand();
        return sendCommand(flowId, commandData);
    }

    /**
     * 发送初始化用户信息传输装置命令
     * 
     * @return 发送结果
     */
    @Override
    public boolean sendInitDevice(String flowId) {
        String commandData = DownCommandUtil.buildInitDeviceCommand();
        return sendCommand(flowId, commandData);
    }

    /**
     * 发送同步用户信息传输装置时钟命令
     * 
     * @param time 时间参数
     * @return 发送结果
     */
    @Override
    public boolean sendSyncDeviceTime(String flowId, String time) {
        String commandData = DownCommandUtil.buildSyncDeviceTimeCommand(time);
        return sendCommand(flowId, commandData);
    }

    /**
     * 发送查岗命令
     * 
     * @param timeout 超时时间（分钟）
     * @return 发送结果
     */
    @Override
    public boolean sendCheckPost(String flowId, int timeout) {
        String commandData = DownCommandUtil.buildCheckPostCommand(timeout);
        return sendCommand(flowId, commandData);
    }
    
    /**
     * 公共发送命令方法
     * 
     * @param flowId 流水号
     * @param commandData 命令数据
     * @return 发送结果
     */
    private boolean sendCommand(String flowId, String commandData) {
        try {
            // 构建命令消息
            DownCommandMessage message = DownCommandUtil.buildCommandMessage(commandData, SRC_ADDRESS, DEST_ADDRESS);
            message.setFlowId(flowId); // 设置传入的流水号
            
            log.info("发送下行命令: IP={}, 流水号={}, 源地址={}, 目标地址={}, 命令数据={}", 
                    TARGET_IP, flowId, SRC_ADDRESS, DEST_ADDRESS, commandData);
                    
            return DownCommandUtil.sendCommandMessage(TARGET_IP, message);
        } catch (Exception e) {
            log.error("发送命令失败: ", e);
            return false;
        }
    }
}