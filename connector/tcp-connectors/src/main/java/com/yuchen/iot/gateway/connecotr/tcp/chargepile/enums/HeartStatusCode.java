package com.yuchen.iot.gateway.connecotr.tcp.chargepile.enums;

/**
 * 心跳状态码
 */
public enum HeartStatusCode {
    PILE_AUTH_OA(0x00, "空闲(未插枪)"),
    PILE_AUTH_OB(0x0B, "空闲(已插枪)"),
    PILE_HEART(0x01, "充电中"),
    PLATFORM_START(0x02, "故障"),
    PILE_UPLOAD(0x04, "维护中"),
    PLATFORM_STOP(0x05, "离线"),
    PILE_STOP(0x07, "升级中")
    ;
    private HeartStatusCode(int code, String value) {
        this.code = code;
        this.value = value;
    }

    private int code;
    private String value;

    public int getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static HeartStatusCode findCode(int code) {
        for (HeartStatusCode item : HeartStatusCode.values()) {
            if (item.getCode() == code) {
                return item;
            }
        }
        return null;
    }
}
