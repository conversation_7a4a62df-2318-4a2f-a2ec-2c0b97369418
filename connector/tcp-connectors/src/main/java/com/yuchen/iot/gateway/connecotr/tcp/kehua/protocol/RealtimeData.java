package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol;

import lombok.Getter;

/**
 * 数据单元有三大类
 * 终端数据
 * 充电实时数据
 * 主动上报数据
 *
 * @description: 充电实时数据
 * @author: zhonghx
 * @create: 2023-02-17 15:56
 **/
public enum RealtimeData {

    CHARGING_SN(0x1003, 4, "chargingSN", DataType.UINT_32),                                             /*充电流水号 */
    CHARGING_VOLTAGE(0x1004, 4, "chargingVoltage", DataType.UINT_32),                                   /*充电电压0.1 V  DC*/
    CHARGING_CURRENT(0x1005, 4, "chargingCurrent", DataType.UINT_32),                                   /*充电电流0.01 A  */
    CHARGING_DURATION(0x1006, 4, "chargingDuration", DataType.UINT_32),                                 /*充电时长 s*/
    CHARGING_ELECTRICITY_QUANTITY(0x1007, 4, "chargingElectricityQuantity", DataType.UINT_32),          /*充电电量 0.01kWh*/
    CHARGING_AMOUNT(0x1008, 4, "chargingAmount", DataType.UINT_32),                                     /*充电总金额 0.0001元*/
    CHARGING_SERVICE_CHARGE(0x1009, 4, "chargingServiceCharge", DataType.UINT_32),                      /*充电服务费 0.0001元*/
    TIME_REMAINING(0x100A, 4, "timeRemaining", DataType.UINT_32),                                       /*剩余时间 分钟*/
    CURRENT_METER_NUMBER(0x100B, 4, "currentMeterNumber", DataType.UINT_32),                            /*当前电表读数 0.01kWh*/
    CURRENT_SOC(0x100C, 1, "currentSoc", DataType.UINT_8),                                              /*当前SOC 0.01kWh*/
    BMS_EXPECT_VOLTAGE(0x100D, 2, "bmsExpectVoltage", DataType.UINT_16),                                /*bms需求电压 0.1 VDC*/
    BMS_EXPECT_CURRENT(0x100E, 2, "bmsExpectCurrent", DataType.UINT_16),                                /*BMS需求电流*/
    INPUT_A_VOLTAGE(0x100F, 4, "inputAVoltage", DataType.UINT_32),                                      /*输入A相电压 0.01VAC*/
    INPUT_B_VOLTAGE(0x1010, 4, "inputBVoltage", DataType.UINT_32),                                      /*输入B相电压*/
    INPUT_C_VOLTAGE(0x1011, 4, "inputCVoltage", DataType.UINT_32),                                      /*输入C相电压*/
    INPUT_A_CURRENT(0x1012, 4, "inputACurrent", DataType.UINT_32),                                      /*输入A相电流 */
    INPUT_B_CURRENT(0x1013, 4, "inputBCurrent", DataType.UINT_32),                                      /*输入B相电流*/
    INPUT_C_CURRENT(0x1014, 4, "inputCCurrent", DataType.UINT_32),                                      /*输入C相电流 0.01A*/
    TOTAL_ACTIVE_POWER(0x1015, 4, "totalActivePower", DataType.UINT_32),                                /*总有功功率 0.01kwh*/
    TOTAL_REACTIVE_POWER(0x1016, 4, "totalReactivePower", DataType.UINT_32),                            /*总无功功率*/

    GUN_DC_PLUS_TEMPERATURE(0x1017, 2, "gunDCPlusTemperature", DataType.UINT_16),                       /*充电DC+温度 0.1℃ 便宜-50℃ 0x64=50*/
    GUN_DC_SUBTRACT_TEMPERATURE(0x1018, 2, "gunDCSubtractTemperature", DataType.UINT_16),               /*充电DC-温度 同上*/
    MAX_BATTERY_TEMPERATURE(0x1019, 2, "maxBatteryTemperature", DataType.UINT_16),                      /*最高动力电池温度 同上*/
    MIN_BATTERY_TEMPERATURE(0x101A, 2, "minBatteryTemperature", DataType.UINT_16),                      /*最低动力电池温度 同上*/
    DISTRIBUTION_POWER(0x101B, 2, "distributionPower", DataType.UINT_16),                               /*分配功率 0.01KW*/
    VIN(0x101C, 17, "vin", DataType.ASCII),                                                             /*vin码 */
    CARD_TYPE(0x101D, 1, "cardType", DataType.UINT_8),                                                  /*充电卡类型0. 无卡充1. 个人卡 2. 超级卡 3. 管理卡4. 在线卡5. APP 启动6. VIN 码启动7. 密码启动8. 蓝牙启动*/
    PWM(0x101E, 1, "pwm", DataType.UINT_8),                                                             /*充电桩PWM百分比 直流桩无需发送*/
    LEAKAGE_CURRENT(0x101F, 2, "leakageCurrent", DataType.UINT_16),                                     /*漏电电流 mA，放大100倍上传*/
    CHARGE_DISCHARGE_STATE(0x1020, 1,"chargeDischargeState", DataType.UINT_8),                          /*充放电状态 0充电 1放电*/
    CP_VOLTAGE(0x1021, 2, "cpVoltage", DataType.UINT_16),                                               /*CP电压 V放大100倍上传 1000=10.00V*/
    LINE_UP_STATE(0x1022, 1, "lineUpState", DataType.UINT_8);                                           /*排队状态 0不排队 1排队*/







    /*数据单元标识ID*/
    @Getter
    private final int id;

    /*字节形态的长度*/
    @Getter
    private final int len;

    /*名称*/
    @Getter
    private final String key;

    /*数据类型*/
    @Getter
    private final DataType type;

    RealtimeData(int id, int len, String key, DataType type) {
        this.id = id;
        this.len = len;
        this.key = key;
        this.type = type;
    }

    public static RealtimeData idOf(int id) {
        for (RealtimeData data : RealtimeData.values()) {
            if (data.getId() == id) {
                return data;
            }
        }
        return null;
    }

    public static RealtimeData keyOf(String key) {
        for (RealtimeData data : RealtimeData.values()) {
            if (data.key.equals(key)) {
                return data;
            }
        }
        return null;
    }
}
