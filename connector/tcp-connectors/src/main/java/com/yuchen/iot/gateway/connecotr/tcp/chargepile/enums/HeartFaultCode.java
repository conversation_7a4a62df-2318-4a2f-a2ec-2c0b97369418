package com.yuchen.iot.gateway.connecotr.tcp.chargepile.enums;

/**
 * 心跳状态码
 */
public enum HeartFaultCode {
    FAULT_CODE1(0x01, "急停按钮被按下"),
    FAULT_COD2(0x02, "电表通信故障"),
    FAULT_COD3(0x03, "读卡器通信故障"),
    FAULT_COD4(0x04, "接地故障"),
    FAULT_COD5(0x05, "电源模块通信故障"),
    FAULT_COD6(0x06, "电源模块全部通信故障"),
    FAULT_COD7(0x07, "桩温度过高故障"),
    FAULT_COD8(0x08, "漏电保护")
    ;
    private HeartFaultCode(int code, String value) {
        this.code = code;
        this.value = value;
    }

    private int code;
    private String value;

    public int getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static HeartFaultCode findCode(int code) {
        for (HeartFaultCode item : HeartFaultCode.values()) {
            if (item.getCode() == code) {
                return item;
            }
        }
        return null;
    }
}
