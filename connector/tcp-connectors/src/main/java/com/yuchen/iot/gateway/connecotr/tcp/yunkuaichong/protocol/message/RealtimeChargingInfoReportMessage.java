package com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.message;

import com.yuchen.iot.gateway.connecotr.tcp.chargepile.util.BytesUtil;
import com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.BCDUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.Data;

/**
 * @description: 充电实时信息，由终端定期上报，服务端无需应答，一般充电时15秒发送一次
 * @author: zhong
 * @create: 2022-12-08 19:49
 **/
@Data
public class RealtimeChargingInfoReportMessage implements MessageDecoder {

    /*枪号*/
    private byte gunNo;

    /*8个字节，流水号 BCD*/
    private String serialNumber;

    /*充电时长，单位秒*/
    private int duration;

    /*（直流）预计充满时长*/
    private int estimatedDuration;

    /*本次充电总电量*/
    private int totalElectricity;

    /*费率1充电电量*/
    private int rate1Electricity;

    /*费率2充电电量*/
    private int rate2Electricity;

    /*费率3充电电量*/
    private int rate3Electricity;

    /*费率4充电电量*/
    private int rate4Electricity;

    /*本次充电总费用，单位元，2位小数*/
    private int totalCharge;

    /*服务费*/
    private int serviceCharge;

    /*费率1电费*/
    private int rate1Charge;

    /*费率2电费*/
    private int rate2Charge;

    /*费率3电费*/
    private int rate3Charge;

    /*费率4电费*/
    private int rate4Charge;

    //TODO 2个字节？？确认
    /*（直流）充电输出电压*/
    private short outVoltage;

    /*直流，充电输出电流*/
    private short current;

    /*直流，soc 0-100*/
    private short soc;

    /*直流，电池组最低温度*/
    private short batteryGroupMinTemperature;

    /*直流，电池组最高温度*/
    private short batteryGroupMaxTemperature;

    /*直流，单体电池最高电压*/
    private short batteryMaxVoltage;

    /*直流，单体电池最低电压*/
    private short batteryMinVoltage;

    /*交流，充电输出A相电压*/
    private short aPhaseVoltage;

    /*交流，充电输出B相电压*/
    private short bPhaseVoltage;

    /*交流，充电输出C相电压*/
    private short cPhaseVoltage;

    /*交流，充电输出A相电流*/
    private short aPhaseCurrent;

    /*交流，充电输出B相电流*/
    private short bPhaseCurrent;

    /*交流，充电输出C相电流*/
    private short cPhaseCurrent;

    @Override
    public void decode(ByteBuf in) {
        gunNo = in.readByte();
        byte[] bytes = ByteBufUtil.getBytes(in.readBytes(8));
        bytes = BytesUtil.bytesReverse(bytes);
        serialNumber = BCDUtil.bcdToStr(bytes);
        duration = in.readIntLE();
        estimatedDuration = in.readIntLE();
        totalElectricity = in.readIntLE();
        rate1Electricity = in.readIntLE();
        rate2Electricity = in.readIntLE();
        rate3Electricity = in.readIntLE();
        rate4Electricity = in.readIntLE();
        totalCharge = in.readIntLE();
        serviceCharge = in.readIntLE();
        rate1Charge = in.readIntLE();
        rate2Charge = in.readIntLE();
        rate3Charge = in.readIntLE();
        rate4Charge = in.readIntLE();
        outVoltage = in.readShortLE();
        current = in.readShortLE();
        soc = in.readShortLE();
        batteryGroupMinTemperature = in.readShortLE();
        batteryGroupMaxTemperature = in.readShortLE();
        batteryMaxVoltage = in.readShortLE();
        batteryMinVoltage = in.readShortLE();
        aPhaseVoltage = in.readShortLE();
        bPhaseVoltage = in.readShortLE();
        cPhaseVoltage = in.readShortLE();
        aPhaseCurrent = in.readShortLE();
        bPhaseCurrent = in.readShortLE();
        cPhaseCurrent = in.readShortLE();

    }
}
