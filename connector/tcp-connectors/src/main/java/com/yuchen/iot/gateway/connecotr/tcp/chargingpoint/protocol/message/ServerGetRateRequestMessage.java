package com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.Data;


/**
 * @description: 平台读取终端费率
 * @author: zhong
 * @create: 2022-12-08 16:55
 **/
@Data
public class ServerGetRateRequestMessage implements MessageEncoder{

    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        return buf;
    }
}
