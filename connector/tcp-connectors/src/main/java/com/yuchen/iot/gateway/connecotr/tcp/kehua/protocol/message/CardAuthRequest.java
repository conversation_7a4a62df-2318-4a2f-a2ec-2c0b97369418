package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;


import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.Data;

/**
 * @description: 用于充电桩主动上传刷卡请求命令。当用户刷卡后，充电桩将卡号上报到
 * 运营云平台，运营云平台根据卡号进行认证卡片的有效性
 * @author: zhonghx
 * @create: 2022-12-09 11:01
 **/
@Data
public class CardAuthRequest implements MessageDecoder {

    /*卡类型
    * 4.在线卡
    * */
    private byte cardType;

    /*用户卡号 20字节 ASCII 码，不足 20 位填'\0'*/
    private String cardNumber;

    /*ASCII 码，用户密码为 6 位，用户输入密
    码后，进行 MD5 加密。无密码时每字节均为 0xFF
    */
    private String password;

    /*卡的全球唯一 Id，充电卡存储区 0 号扇区，0 号 Block 上的数据*/
    private String cardId;

    /*操作类型 1启动 2停止*/
    private int optType;



    @Override
    public void decode(ByteBuf in) {

        cardType = in.readByte();
        byte[] cardNumberBytes = ByteBufUtil.getBytes(in.readBytes(20));
        cardNumber = new String(cardNumberBytes);
        byte[] passwordBytes = ByteBufUtil.getBytes(in.readBytes(32));
        password = new String(passwordBytes);
        byte[] cardIdBytes = ByteBufUtil.getBytes(in.readBytes(16));
        cardId = new String(cardIdBytes);
        optType = in.readByte();
    }


}
