package com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.message;

import io.netty.buffer.ByteBuf;
import lombok.Data;

/**
 * @description: 终端应答主站查询软硬件版本的请求
 * @author: zhonghx
 * @create: 2022-12-09 19:26
 **/
@Data
public class FirmwareResponseMessage implements MessageDecoder {

    /*硬件名称 16字节 ASCII*/
    private String hardwareName;
    /*硬件版本 ，16个字节， ASCII */
    private String hardwareVersion;
    /*软件名称 16字节 ASCII*/
    private String softwareName;
    /*软件版本 16个字节， ASCII */
    private String softwareVersion;

    @Override
    public void decode(ByteBuf in) {
        hardwareName = in.readBytes(16).toString();
        hardwareVersion = in.readBytes(16).toString();
        softwareName = in.readBytes(16).toString();
        softwareVersion = in.readBytes(16).toString();
    }
}
