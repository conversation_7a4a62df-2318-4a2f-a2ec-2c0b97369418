package com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @description: 主站应答VIN码鉴权结果
 * @author: zhonghx
 * @create: 2022-12-09 17:44
 **/
@Data
@AllArgsConstructor
public class VinAuthResponseMessage implements MessageEncoder{

    /*枪号*/
    private byte gunNo;

    /*VIN码 17个字节 ASCII*/
    private String VIN;

    /*账户余额，单位元，2位小数*/
    private float accountBalances;

    /*用户类型 1普通用户 2集团用户*/
    private byte userType;

    /*用户来源 0x00*/
    private byte from;

    /*鉴权结果*
     * 0x0  正确
     * 0x01 车辆无充电权限
     * 0x03 余额不足
     * 0x90 有未支付订单
     * 0x92 有正在充电订单
     * 0x99 系统错误
     */
    private byte authResult;

    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(gunNo);
        buf.writeBytes(VIN.getBytes());
        buf.writeFloatLE(accountBalances);
        buf.writeByte(userType);
        buf.writeByte(from);
        buf.writeByte(authResult);
        return buf;
    }
}
