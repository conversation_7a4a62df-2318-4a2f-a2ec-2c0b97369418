package com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.protocol.message;

import com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.BCDUtil;
import io.netty.buffer.ByteBuf;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;

/**
 * @description: 终端主动请求费率
 * @author: zhong
 * @create: 2022-12-08 16:51
 **/
@Data
public class ClientGetRateRequestMessage implements MessageDecoder{

    /*服务费单价，单位：元/kWh, 4位小数*/
    private int serviceChargePerkWh;
    /*费率1单价*/
    private int rate1;
    /*费率2单价*/
    private int rate2;
    /*费率3单价*/
    private int rate3;
    /*费率4单价*/
    private int rate4;
    /*时段数量，至少1条，最多12条。时段必须按顺序排列，覆盖00：00 - 24：00*/
    private byte timeIntervalCount;

    private ArrayList<TimeIntervalInfo> array = new ArrayList<>();

    @Override
    public void decode(ByteBuf in) {
    }
}
