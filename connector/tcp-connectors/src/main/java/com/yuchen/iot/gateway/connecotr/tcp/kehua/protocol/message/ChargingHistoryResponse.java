package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * @description: 历史充电记录上传的响应,网关直接响应！！
 * @author: zhonghx
 * @create: 2023-02-13 14:06
 **/

@Data
@AllArgsConstructor
public class ChargingHistoryResponse implements MessageEncoder{

    /*确认标识 0失败 1成功*/
    private byte ack;

    /*充电卡号 20字节，原值返回*/
    private String cardNumber;

    /*充电流水号
    * App 在线卡充电传充电流水号；离线卡充电传存储 ID
    * */
    private int chargingSN;

    /*充电方式
    * 0.离线卡充
    * 1.app充
    * 2.在线卡充、vin码充
    * 1、扫码充电，22 字段是平台产生的流水号，23 是桩本地存储的流水号，订单确认需要使用 22 字段
    2、VIN 启动，22 字段是平台产生的流水号，23 是桩本地存储的流水号，订单确认需要使用 23 字段
    3、在线卡充电，22 是平台产生的流水号，23 是桩本地存储的流水号，订单确认需要使用 23 字段
    * */
    private byte chargingType;

    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(ack);
        buf.writeBytes(cardNumber.getBytes(StandardCharsets.US_ASCII));
        buf.writeInt(chargingSN);
        buf.writeByte(chargingType);
        return buf;
    }


}
