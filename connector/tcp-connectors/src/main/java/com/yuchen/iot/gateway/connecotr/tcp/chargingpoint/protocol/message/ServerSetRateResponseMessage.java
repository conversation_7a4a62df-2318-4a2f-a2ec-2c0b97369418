package com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.protocol.message;

import io.netty.buffer.ByteBuf;
import lombok.Data;

/**
 * @description: 终端应答平台设置费率的请求
 * @author: zhong
 * @create: 2022-12-08 16:55
 **/
@Data
public class ServerSetRateResponseMessage implements MessageDecoder{

    /*费率设置结果 6表示成功，0表示失败*/
    private byte result;
    @Override
    public void decode(ByteBuf in) {
        result = in.readByte();
    }
}
