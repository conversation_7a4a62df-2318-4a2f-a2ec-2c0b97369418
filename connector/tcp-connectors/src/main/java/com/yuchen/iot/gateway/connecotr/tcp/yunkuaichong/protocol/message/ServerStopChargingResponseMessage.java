package com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.message;

import com.yuchen.iot.gateway.connecotr.tcp.chargepile.util.BytesUtil;
import com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.BCDUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.Data;

/**
 * @description: 终端应答主站要求停止充电的请求
 * @author: zhonghx
 * @create: 2022-12-09 19:04
 **/
@Data
public class ServerStopChargingResponseMessage implements MessageDecoder {

    /*枪号 1-n*/
    private byte gunNumber;

    /*充电流水号BCD*/
    private String chargingSerialNumber;

    /*结果 6成功 0失败*/
    private byte result;
    @Override
    public void decode(ByteBuf in) {
        gunNumber = in.readByte();
        byte[] bytes = ByteBufUtil.getBytes(in.readBytes(8));
        bytes = BytesUtil.bytesReverse(bytes);
        chargingSerialNumber = BCDUtil.bcdToStr(bytes);
        result = in.readByte();
    }
}
