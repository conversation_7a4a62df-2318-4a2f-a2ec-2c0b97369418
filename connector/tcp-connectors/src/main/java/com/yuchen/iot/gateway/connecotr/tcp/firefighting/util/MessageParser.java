package com.yuchen.iot.gateway.connecotr.tcp.firefighting.util;


import com.yuchen.iot.gateway.connecotr.tcp.firefighting.entity.*;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * 消息解析工具类
 */
@Slf4j
public class MessageParser {

    // 系统类型映射表 (使用十进制作为Key)
    private static final Map<Integer, String> SYSTEM_TYPE_MAP = new HashMap<>();
    // 部件类型映射表 (使用十进制作为Key)
    private static final Map<Integer, String> COMPONENT_TYPE_MAP = new HashMap<>();
    // 部件状态映射表
    private static final Map<String, String> COMPONENT_STATUS_MAP = new HashMap<>();
    
    static {
        // 初始化系统类型映射 (使用十进制)
        SYSTEM_TYPE_MAP.put(0, "通用");
        SYSTEM_TYPE_MAP.put(1, "火灾报警系统");
        // 2-9预留
        SYSTEM_TYPE_MAP.put(10, "消防联动控制器");
        SYSTEM_TYPE_MAP.put(11, "消火栓系统");
        SYSTEM_TYPE_MAP.put(12, "自动喷水灭火系统");
        SYSTEM_TYPE_MAP.put(13, "气体灭火系统");
        SYSTEM_TYPE_MAP.put(14, "水喷雾灭火系统(泵启动方式)");
        SYSTEM_TYPE_MAP.put(15, "水喷雾灭火系统(压力容器启动方式)");
        SYSTEM_TYPE_MAP.put(16, "泡沫灭火系统");
        SYSTEM_TYPE_MAP.put(17, "干粉灭火系统");
        SYSTEM_TYPE_MAP.put(18, "防烟排烟系统");
        SYSTEM_TYPE_MAP.put(19, "防火门及卷帘系统");
        SYSTEM_TYPE_MAP.put(20, "消防电梯");
        SYSTEM_TYPE_MAP.put(21, "消防应急广播");
        SYSTEM_TYPE_MAP.put(22, "消防应急照明和疏散指示系统");
        SYSTEM_TYPE_MAP.put(23, "消防电源");
        SYSTEM_TYPE_MAP.put(24, "消防电话");
        // 25预留
        // 128-255用户自定义
        SYSTEM_TYPE_MAP.put(201, "电气火灾监控系统");
        SYSTEM_TYPE_MAP.put(202, "消防设备电源状态监控器系统");
        SYSTEM_TYPE_MAP.put(203, "可燃气体报警系统");
        
        // 初始化部件类型映射 (使用十进制)
        COMPONENT_TYPE_MAP.put(0, "通用");
        COMPONENT_TYPE_MAP.put(1, "火灾报警控制器");
        // 2-9预留
        COMPONENT_TYPE_MAP.put(10, "可燃气体探测器");
        COMPONENT_TYPE_MAP.put(11, "点型可燃气体探测器");
        COMPONENT_TYPE_MAP.put(12, "独立式可燃气体探测器");
        COMPONENT_TYPE_MAP.put(13, "线型可燃气体探测器");
        // 14预留
        COMPONENT_TYPE_MAP.put(15, "电气火灾监控报警器");
        COMPONENT_TYPE_MAP.put(16, "剩余电流式电气火灾监控探测器");
        COMPONENT_TYPE_MAP.put(17, "测温式电气火灾监控探测器");
        // 18-20预留
        COMPONENT_TYPE_MAP.put(21, "探测回路");
        COMPONENT_TYPE_MAP.put(22, "火灾显示盘");
        COMPONENT_TYPE_MAP.put(23, "手动火灾报警按钮");
        COMPONENT_TYPE_MAP.put(24, "消火栓按钮");
        COMPONENT_TYPE_MAP.put(25, "火灾探测器");
        // 26-29预留
        COMPONENT_TYPE_MAP.put(30, "感温火灾探测器");
        COMPONENT_TYPE_MAP.put(31, "点型感温火灾探测器");
        COMPONENT_TYPE_MAP.put(32, "点型感温火灾探测器(S型)");
        COMPONENT_TYPE_MAP.put(33, "点型感温火灾探测器(R型)");
        COMPONENT_TYPE_MAP.put(34, "线型感温火灾探测器");
        COMPONENT_TYPE_MAP.put(35, "线型感温火灾探测器(S型)");
        COMPONENT_TYPE_MAP.put(36, "线型感温火灾探测器(R型)");
        COMPONENT_TYPE_MAP.put(37, "光纤感温火灾探测器");
        // 38-39预留
        COMPONENT_TYPE_MAP.put(40, "感烟火灾探测器");
        COMPONENT_TYPE_MAP.put(41, "点型离子感烟火灾探测器");
        COMPONENT_TYPE_MAP.put(42, "点型光电感烟火灾探测器");
        COMPONENT_TYPE_MAP.put(43, "线型光束感烟火灾探测器");
        COMPONENT_TYPE_MAP.put(44, "吸气式感烟火灾探测器");
        // 45-49预留
        COMPONENT_TYPE_MAP.put(50, "复合式火灾探测器");
        COMPONENT_TYPE_MAP.put(51, "复合式感烟感温火灾探测器");
        COMPONENT_TYPE_MAP.put(52, "复合式感光感温火灾探测器");
        COMPONENT_TYPE_MAP.put(53, "复合式感光感烟火灾探测器");
        // 54-59预留
        // 60预留
        COMPONENT_TYPE_MAP.put(61, "紫外火焰探测器");
        COMPONENT_TYPE_MAP.put(62, "红外火焰探测器");
        // 63-68预留
        COMPONENT_TYPE_MAP.put(69, "感光火灾探测器");
        // 70-73预留
        COMPONENT_TYPE_MAP.put(74, "气体探测器");
        // 75-77预留
        COMPONENT_TYPE_MAP.put(78, "图像摄像方式火灾探测器");
        COMPONENT_TYPE_MAP.put(79, "感声火灾探测器");
        // 80预留
        COMPONENT_TYPE_MAP.put(81, "气体灭火控制器");
        COMPONENT_TYPE_MAP.put(82, "消防电气控制装置");
        COMPONENT_TYPE_MAP.put(83, "消防控制室图形显示装置");
        COMPONENT_TYPE_MAP.put(84, "模块");
        COMPONENT_TYPE_MAP.put(85, "输入模块");
        COMPONENT_TYPE_MAP.put(86, "输出模块");
        COMPONENT_TYPE_MAP.put(87, "输入/输出模块");
        COMPONENT_TYPE_MAP.put(88, "中继模块");
        // 89-90预留
        COMPONENT_TYPE_MAP.put(91, "消防水泵");
        COMPONENT_TYPE_MAP.put(92, "消防水箱");
        // 93-94预留
        COMPONENT_TYPE_MAP.put(95, "喷淋泵");
        COMPONENT_TYPE_MAP.put(96, "水流指示器");
        COMPONENT_TYPE_MAP.put(97, "信号阀");
        COMPONENT_TYPE_MAP.put(98, "报警阀");
        COMPONENT_TYPE_MAP.put(99, "压力开关");
        // 100预留
        COMPONENT_TYPE_MAP.put(101, "阀驱动装置");
        COMPONENT_TYPE_MAP.put(102, "防火门");
        COMPONENT_TYPE_MAP.put(103, "防火阀");
        COMPONENT_TYPE_MAP.put(104, "通风空调");
        COMPONENT_TYPE_MAP.put(105, "泡沫液泵");
        COMPONENT_TYPE_MAP.put(106, "管网电磁阀");
        // 107-110预留
        COMPONENT_TYPE_MAP.put(111, "防烟排烟风机");
        // 112预留
        COMPONENT_TYPE_MAP.put(113, "排烟防火阀");
        COMPONENT_TYPE_MAP.put(114, "常闭送风口");
        COMPONENT_TYPE_MAP.put(115, "排烟口");
        COMPONENT_TYPE_MAP.put(116, "电控挡烟垂壁");
        COMPONENT_TYPE_MAP.put(117, "防火卷帘控制器");
        COMPONENT_TYPE_MAP.put(118, "防火门监控器");
        // 119-120预留
        COMPONENT_TYPE_MAP.put(121, "警报装置");
        // 122-127预留
        // 128-255用户自定义
        COMPONENT_TYPE_MAP.put(200, "选择阀");
        COMPONENT_TYPE_MAP.put(201, "放气指示灯");
        COMPONENT_TYPE_MAP.put(202, "控制阀");
        
        // 初始化部件状态映射
        COMPONENT_STATUS_MAP.put("01", "火警");
        COMPONENT_STATUS_MAP.put("02", "故障");
        COMPONENT_STATUS_MAP.put("03", "屏蔽");
        COMPONENT_STATUS_MAP.put("04", "监管");
        // 可以根据需要继续添加其他状态...
    }

    /**
     * 将byte[]数组转换为String[]数组
     * 每个byte转换为对应的十六进制字符串
     * @param bytes byte类型数组
     * @return 十六进制字符串数组
     */
    private static String[] byteArrayToStringArray(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return new String[0];
        }
        
        String[] result = new String[bytes.length];
        for (int i = 0; i < bytes.length; i++) {
            result[i] = String.format("%02X", bytes[i]);
        }
        return result;
    }

    /**
     * 根据类型标志解析消息
     * @param raw 原始数据
     * @return 解析后的消息对象
     */
    public static Object parseMessage(String raw) {
        log.info("开始解析消息: {}", raw);
        
        // 去除空格，将字符串拆分为字节数组
        byte[] byteArray = ByteUtil.hexStringToByteArray(raw);
        if (byteArray.length == 0) {
            log.error("无效的消息数据");
            return null;
        }
        int typeFlag = Integer.parseInt(String.valueOf(byteArray[0]), 16);
        
        // 根据类型标志进行不同的解析
        switch (typeFlag) {
            case 0x01:
                return parseSystemStatusMessage(byteArrayToStringArray(byteArray));
            case 0x02:
                return parseAlarmData(byteArrayToStringArray(byteArray));
            case 0x03:
                return parseAnalogValueMessage(byteArrayToStringArray(byteArray));
            case 0x04:
                return parseOperationRecordMessage(byteArrayToStringArray(byteArray));
            case 0x06:
                return parseSystemConfigMessage(byteArrayToStringArray(byteArray));
            case 0x07:
                return parseComponentConfigMessage(byteArrayToStringArray(byteArray));
            case 0x21:
                return parseDeviceStatusMessage(byteArrayToStringArray(byteArray));
            case 0x24:
                return parseDeviceOperationMessage(byteArrayToStringArray(byteArray));
            case 0x28:
                return parseSystemTimeMessage(byteArrayToStringArray(byteArray));
            default:
                log.error("未知的类型标志: {}", typeFlag);
                return null;
        }
    }
    
    /**
     * 解析建筑消防设施系统状态消息 (类型标志01)
     * @param bytes 字节数组
     * @return 系统状态消息对象
     */
    private static SystemStatusMessage parseSystemStatusMessage(String[] bytes) {
        SystemStatusMessage message = new SystemStatusMessage();
        
        try {
            // 设置原始数据
          //  message.setRawData(String.join(" ", bytes));
            
            // 类型标志
        //    message.setTypeFlag(Integer.parseInt(bytes[0], 16));
            
            // 信息对象数目
          //  message.setObjectCount(Integer.parseInt(bytes[1], 16));
            
            // 系统类型
            int systemTypeDecimal = Integer.parseInt(bytes[2], 16);
          //  String systemType = SYSTEM_TYPE_MAP.getOrDefault(systemTypeDecimal, "未知系统类型(" + systemTypeDecimal + ")");
            message.setSystemType(systemTypeDecimal);
            
            // 系统地址
            message.setSystemAddress(bytes[3]);
            
            // 系统状态 (使用低位字节在前，高位字节在后)
            String statusLowByte = bytes[4];
            String statusHighByte = bytes[5];
            
            SystemStatusMessage.SystemStatus status = parseSystemStatus(statusLowByte, statusHighByte);
            message.setSystemStatus(status);
            
            // 时间
            int second = Integer.parseInt(bytes[6], 16);
            int minute = Integer.parseInt(bytes[7], 16);
            int hour = Integer.parseInt(bytes[8], 16);
            int day = Integer.parseInt(bytes[9], 16);
            int month = Integer.parseInt(bytes[10], 16);
            int year = Integer.parseInt(bytes[11], 16) + 2000;
            
            String timeStr = String.format("%04d-%02d-%02d %02d:%02d:%02d", 
                    year, month, day, hour, minute, second);
            message.setEventTime(timeStr);
            
            log.info("解析建筑消防设施系统状态消息完成");
            
        } catch (Exception e) {
            log.error("解析建筑消防设施系统状态消息异常: ", e);
        }
        
        return message;
    }
    
    /**
     * 解析系统状态
     * @param lowByte 低字节
     * @param highByte 高字节
     * @return 系统状态对象
     */
    private static SystemStatusMessage.SystemStatus parseSystemStatus(String lowByte, String highByte) {
        SystemStatusMessage.SystemStatus status = new SystemStatusMessage.SystemStatus();
        
        try {
            // 解析低字节
            int lowByteValue = Integer.parseInt(lowByte, 16);
            
            status.setTestRunning((lowByteValue & 0x01) == 0x01 ? 1 : 0);  // bit0
            status.setFireAlarm((lowByteValue & 0x02) == 0x02 ? 1 : 0);    // bit1
            status.setFault((lowByteValue & 0x04) == 0x04 ? 1 : 0);        // bit2
            status.setShielded((lowByteValue & 0x08) == 0x08 ? 1 : 0);     // bit3
            status.setSupervised((lowByteValue & 0x10) == 0x10 ? 1 : 0);   // bit4
            status.setStarted((lowByteValue & 0x20) == 0x20 ? 1 : 0);      // bit5
            status.setFeedback((lowByteValue & 0x40) == 0x40 ? 1 : 0);     // bit6
            status.setDelayed((lowByteValue & 0x80) == 0x80 ? 1 : 0);      // bit7
            
            // 解析高字节
            int highByteValue = Integer.parseInt(highByte, 16);
            
            status.setMainPowerFault((highByteValue & 0x01) == 0x01 ? 1 : 0);     // bit8
            status.setBackupPowerFault((highByteValue & 0x02) == 0x02 ? 1 : 0);   // bit9
            status.setBusFault((highByteValue & 0x04) == 0x04 ? 1 : 0);           // bit10
            status.setManual((highByteValue & 0x08) == 0x08 ? 1 : 0);             // bit11
            status.setConfigChanged((highByteValue & 0x10) == 0x10 ? 1 : 0);       // bit12
            status.setReset((highByteValue & 0x20) == 0x20 ? 1 : 0);              // bit13
            
        } catch (Exception e) {
            log.error("解析系统状态异常: ", e);
        }
        
        return status;
    }
    
    /**
     * 解析建筑消防设施操作信息记录消息 (类型标志04)
     * @param bytes 字节数组
     * @return 操作信息记录消息对象
     */
    private static OperationRecordMessage parseOperationRecordMessage(String[] bytes) {
        OperationRecordMessage message = new OperationRecordMessage();
        
        try {
            // 设置原始数据
           // message.setRawData(String.join(" ", bytes));
            
            // 类型标志
          //  message.setTypeFlag(Integer.parseInt(bytes[0], 16));
            
            // 信息对象数目
          //  message.setObjectCount(Integer.parseInt(bytes[1], 16));
            
            // 系统类型
            int systemTypeDecimal = Integer.parseInt(bytes[2], 16);
            String systemType = SYSTEM_TYPE_MAP.getOrDefault(systemTypeDecimal, "未知系统类型(" + systemTypeDecimal + ")");
            message.setSystemType(systemType);
            
            // 系统地址
            message.setSystemAddress(bytes[3]);
            
            // 操作标志
            String operationFlagHex = bytes[4];
            OperationRecordMessage.OperationFlag operationFlag = parseOperationFlag(operationFlagHex);
            message.setOperationFlag(operationFlag);
            
            // 操作员编号
            message.setOperatorId(bytes[5]);
            
            // 时间
            int second = Integer.parseInt(bytes[6], 16);
            int minute = Integer.parseInt(bytes[7], 16);
            int hour = Integer.parseInt(bytes[8], 16);
            int day = Integer.parseInt(bytes[9], 16);
            int month = Integer.parseInt(bytes[10], 16);
            int year = Integer.parseInt(bytes[11], 16) + 2000;
            
            String timeStr = String.format("%04d-%02d-%02d %02d:%02d:%02d", 
                    year, month, day, hour, minute, second);
            message.setEventTime(timeStr);
            
            log.info("解析建筑消防设施操作信息记录消息完成");
            
        } catch (Exception e) {
            log.error("解析建筑消防设施操作信息记录消息异常: ", e);
        }
        
        return message;
    }
    
    /**
     * 解析操作标志
     * @param operationFlagHex 操作标志十六进制字符串
     * @return 操作标志对象
     */
    private static OperationRecordMessage.OperationFlag parseOperationFlag(String operationFlagHex) {
        OperationRecordMessage.OperationFlag operationFlag = new OperationRecordMessage.OperationFlag();
        
        try {
            int flagValue = Integer.parseInt(operationFlagHex, 16);
            
            operationFlag.setReset((flagValue & 0x01) == 0x01 ? 1 : 0);         // bit0
            operationFlag.setSilenced((flagValue & 0x02) == 0x02 ? 1 : 0);      // bit1
            operationFlag.setManualAlarm((flagValue & 0x04) == 0x04 ? 1 : 0);   // bit2
            operationFlag.setAlarmClear((flagValue & 0x08) == 0x08 ? 1 : 0);    // bit3
            operationFlag.setSelfCheck((flagValue & 0x10) == 0x10 ? 1 : 0);     // bit4
            operationFlag.setConfirmed((flagValue & 0x20) == 0x20 ? 1 : 0);     // bit5
            operationFlag.setTesting((flagValue & 0x40) == 0x40 ? 1 : 0);       // bit6
            operationFlag.setDelayed((flagValue & 0x80) == 0x80 ? 1 : 0);       // bit7
            
        } catch (Exception e) {
            log.error("解析操作标志异常: ", e);
        }
        
        return operationFlag;
    }
    
    /**
     * 解析用户信息传输装置运行状态消息 (类型标志21)
     * @param bytes 字节数组
     * @return 设备状态消息对象
     */
    private static DeviceStatusMessage parseDeviceStatusMessage(String[] bytes) {
        DeviceStatusMessage message = new DeviceStatusMessage();
        
        try {
            // 设置原始数据
        //    message.setRawData(String.join(" ", bytes));
            
            // 类型标志
         //   message.setTypeFlag(Integer.parseInt(bytes[0], 16));
            
            // 信息对象数目
         //   message.setObjectCount(Integer.parseInt(bytes[1], 16));
            
            // 设备状态
            String statusHex = bytes[2];
            DeviceStatusMessage.DeviceStatus deviceStatus = parseDeviceStatus(statusHex);
            message.setDeviceStatus(deviceStatus);
            
            // 时间
            int second = Integer.parseInt(bytes[3], 16);
            int minute = Integer.parseInt(bytes[4], 16);
            int hour = Integer.parseInt(bytes[5], 16);
            int day = Integer.parseInt(bytes[6], 16);
            int month = Integer.parseInt(bytes[7], 16);
            int year = Integer.parseInt(bytes[8], 16) + 2000;
            
            String timeStr = String.format("%04d-%02d-%02d %02d:%02d:%02d", 
                    year, month, day, hour, minute, second);
            message.setEventTime(timeStr);
            
            log.info("解析用户信息传输装置运行状态消息完成");
            
        } catch (Exception e) {
            log.error("解析用户信息传输装置运行状态消息异常: ", e);
        }
        
        return message;
    }
    
    /**
     * 解析设备状态
     * @param statusHex 状态十六进制字符串
     * @return 设备状态对象
     */
    private static DeviceStatusMessage.DeviceStatus parseDeviceStatus(String statusHex) {
        DeviceStatusMessage.DeviceStatus status = new DeviceStatusMessage.DeviceStatus();
        
        try {
            int statusValue = Integer.parseInt(statusHex, 16);
            
            status.setNormal((statusValue & 0x01) == 0x01 ? 1 : 0);              // bit0
            status.setFireAlarm((statusValue & 0x02) == 0x02 ? 1 : 0);           // bit1
            status.setFault((statusValue & 0x04) == 0x04 ? 1 : 0);               // bit2
            status.setMainPowerFault((statusValue & 0x08) == 0x08 ? 1 : 0);      // bit3
            status.setBackupPowerFault((statusValue & 0x10) == 0x10 ? 1 : 0);    // bit4
            status.setCommunicationFault((statusValue & 0x20) == 0x20 ? 1 : 0);  // bit5
            status.setConnectionFault((statusValue & 0x40) == 0x40 ? 1 : 0);     // bit6
            status.setDelayed((statusValue & 0x80) == 0x80 ? 1 : 0);             // bit7
            
        } catch (Exception e) {
            log.error("解析设备状态异常: ", e);
        }
        
        return status;
    }
    
    /**
     * 解析用户信息传输装置操作信息记录消息 (类型标志24)
     * @param bytes 字节数组
     * @return 设备操作消息对象
     */
    private static DeviceOperationMessage parseDeviceOperationMessage(String[] bytes) {
        DeviceOperationMessage message = new DeviceOperationMessage();
        
        try {
            // 设置原始数据
       //     message.setRawData(String.join(" ", bytes));
            
            // 类型标志
         //   message.setTypeFlag(Integer.parseInt(bytes[0], 16));
            
            // 信息对象数目
         //   message.setObjectCount(Integer.parseInt(bytes[1], 16));
            
            // 操作信息
            String operationInfoHex = bytes[2];
            DeviceOperationMessage.OperationInfo operationInfo = parseDeviceOperationInfo(operationInfoHex);
            message.setOperationInfo(operationInfo);
            
            // 时间
            int second = Integer.parseInt(bytes[3], 16);
            int minute = Integer.parseInt(bytes[4], 16);
            int hour = Integer.parseInt(bytes[5], 16);
            int day = Integer.parseInt(bytes[6], 16);
            int month = Integer.parseInt(bytes[7], 16);
            int year = Integer.parseInt(bytes[8], 16) + 2000;
            
            String timeStr = String.format("%04d-%02d-%02d %02d:%02d:%02d", 
                    year, month, day, hour, minute, second);
            message.setEventTime(timeStr);
            
            log.info("解析用户信息传输装置操作信息记录消息完成");
            
        } catch (Exception e) {
            log.error("解析用户信息传输装置操作信息记录消息异常: ", e);
        }
        
        return message;
    }
    
    /**
     * 解析设备操作信息
     * @param operationInfoHex 操作信息十六进制字符串
     * @return 操作信息对象
     */
    private static DeviceOperationMessage.OperationInfo parseDeviceOperationInfo(String operationInfoHex) {
        DeviceOperationMessage.OperationInfo operationInfo = new DeviceOperationMessage.OperationInfo();
        
        try {
            int infoValue = Integer.parseInt(operationInfoHex, 16);
            
            operationInfo.setReset((infoValue & 0x01) == 0x01 ? 1 : 0);          // bit0
            operationInfo.setSilenced((infoValue & 0x02) == 0x02 ? 1 : 0);       // bit1
            operationInfo.setManualAlarm((infoValue & 0x04) == 0x04 ? 1 : 0);    // bit2
            operationInfo.setAlarmClear((infoValue & 0x08) == 0x08 ? 1 : 0);     // bit3
            operationInfo.setSelfCheck((infoValue & 0x10) == 0x10 ? 1 : 0);      // bit4
            operationInfo.setCheckResponse((infoValue & 0x20) == 0x20 ? 1 : 0);  // bit5
            operationInfo.setTesting((infoValue & 0x40) == 0x40 ? 1 : 0);        // bit6
            operationInfo.setDelayed((infoValue & 0x80) == 0x80 ? 1 : 0);        // bit7
            
        } catch (Exception e) {
            log.error("解析设备操作信息异常: ", e);
        }
        
        return operationInfo;
    }
    
    /**
     * 解析用户信息传输装置系统时间消息 (类型标志28)
     * @param bytes 字节数组
     * @return 系统时间消息对象
     */
    private static SystemTimeMessage parseSystemTimeMessage(String[] bytes) {
        SystemTimeMessage message = new SystemTimeMessage();
        
        try {
            // 设置原始数据
          //  message.setRawData(String.join(" ", bytes));
            
            // 类型标志
          //  message.setTypeFlag(Integer.parseInt(bytes[0], 16));
            
            // 信息对象数目
          //  message.setObjectCount(Integer.parseInt(bytes[1], 16));
            
            // 时间
            int second = Integer.parseInt(bytes[2], 16);
            int minute = Integer.parseInt(bytes[3], 16);
            int hour = Integer.parseInt(bytes[4], 16);
            int day = Integer.parseInt(bytes[5], 16);
            int month = Integer.parseInt(bytes[6], 16);
            int year = Integer.parseInt(bytes[7], 16) + 2000;
            
            String timeStr = String.format("%04d-%02d-%02d %02d:%02d:%02d", 
                    year, month, day, hour, minute, second);
            message.setSystemTime(timeStr);
            
            log.info("解析用户信息传输装置系统时间消息完成");
            
        } catch (Exception e) {
            log.error("解析用户信息传输装置系统时间消息异常: ", e);
        }
        
        return message;
    }
    
    /**
     * 解析部件状态，使用位解析
     * @param statusHex 状态的十六进制字符串
     * @return 解析后的ComponentStatus对象
     */
    public static ComponentStatus parseComponentStatus(String statusHex) {
        return ComponentStatus.parseFromHex(statusHex);
    }
    
    /**
     * 解析报警数据时更新部件状态信息
     * @param alarmMessage 报警消息对象
     * @param statusHex 状态的十六进制字符串
     */
    public static void updateComponentStatus(AlarmMessage alarmMessage, String statusHex) {
        // 创建并解析ComponentStatus对象
        ComponentStatus componentStatus = parseComponentStatus(statusHex);
        
        // 设置到AlarmMessage对象
        alarmMessage.setComponentStatus(componentStatus);
        
        // 更新状态描述
       // alarmMessage.setStatus(componentStatus.getStatusDescription());
    }

    /**
     * 将小端序十六进制字符串转换为十进制
     * @param hex 十六进制字符串
     * @return 十进制值
     */
    public static int hexLittleEndianToDecimal(String hex) {
        // 确保字符串长度为偶数
        if (hex.length() % 2 != 0) {
            hex = "0" + hex;
        }
        
        // 处理小端序
        StringBuilder sb = new StringBuilder();
        for (int i = hex.length() - 2; i >= 0; i -= 2) {
            sb.append(hex.substring(i, i + 2));
        }
        
        // 转换为十进制
        try {
            return Integer.parseInt(sb.toString(), 16);
        } catch (NumberFormatException e) {
            log.error("小端序十六进制转换失败: {}", hex);
            return 0;
        }
    }

    /**
     * 解析报警数据
     *
     * @param bytes 转换后的字符串数组
     * @return 报警消息实体类
     */
    public static AlarmMessage parseAlarmData(String[] bytes) {
       // log.info("开始解析应用数据单元: {}", raw);
        
        // 创建报警消息对象
        AlarmMessage alarmMessage = new AlarmMessage();
     //   alarmMessage.setRawData(String.join(" ", bytes)); // 保存原始数据
        
        try {
            // 根据新的协议定义解析数据
            // bytes[0] :类型标志
        //    alarmMessage.setTypeValue(Integer.parseInt(bytes[0], 16));
            
            // bytes[1] :信息对象数目 (暂不处理)
            
            // bytes[2] :系统类型 (将16进制字符串转为10进制整数)
            int systemTypeDecimal = Integer.parseInt(bytes[2], 16);
        //    String systemType = SYSTEM_TYPE_MAP.getOrDefault(systemTypeDecimal, "未知系统类型(" + systemTypeDecimal + ")");
            alarmMessage.setSystemType(systemTypeDecimal);
            
            // bytes[3] :系统地址 (存储为字符串)
            alarmMessage.setMachineNo(bytes[3]);
            
            // bytes[4] :部件类型 (将16进制字符串转为10进制整数)
            int componentTypeDecimal = Integer.parseInt(bytes[4], 16);
           // String componentType = COMPONENT_TYPE_MAP.getOrDefault(componentTypeDecimal, "未知部件类型(" + componentTypeDecimal + ")");
            alarmMessage.setComponentType(componentTypeDecimal);
            
            // bytes[5]到bytes[6] :位号 (使用小端序解析)
            int position;
            if (bytes.length > 6) {
                // 使用小端序解析位号
                String positionHex = bytes[5] + bytes[6];
                position = hexLittleEndianToDecimal(positionHex);
                log.debug("位号16进制: {}, 小端序解析后: {}", positionHex, position);
            } else {
                position = Integer.parseInt(bytes[5], 16);
            }
            alarmMessage.setPosition(position);
            
            // bytes[7]到bytes[8] :区号 (使用小端序解析)
            int area;
            if (bytes.length > 8) {
                // 使用小端序解析区号
                String areaHex = bytes[7] + bytes[8];
                area = hexLittleEndianToDecimal(areaHex);
                log.debug("区号16进制: {}, 小端序解析后: {}", areaHex, area);
            } else {
                area = Integer.parseInt(bytes[7], 16);
            }
            alarmMessage.setArea(area);
            
            // bytes[9]到bytes[10] :部件状态
            String statusHex = "";
            if (bytes.length > 10) {
                statusHex = bytes[9] + bytes[10];
            } else {
                statusHex = bytes[9];
            }
            updateComponentStatus(alarmMessage, statusHex);
            
            // bytes[11]到bytes[41] :部件说明 (暂不处理，使用生成的说明)
            
            // bytes[42]到bytes[47] :时间信息
            if (bytes.length > 47) {
                int second = Integer.parseInt(bytes[42], 16);
                int minute = Integer.parseInt(bytes[43], 16);
                int hour = Integer.parseInt(bytes[44], 16);
                int day = Integer.parseInt(bytes[45], 16);
                int month = Integer.parseInt(bytes[46], 16);
                int year = Integer.parseInt(bytes[47], 16) + 2000;
                
                // 修改为新的时间格式："yyyy-MM-dd HH:mm:ss"
                String timeStr = String.format("%04d-%02d-%02d %02d:%02d:%02d", 
                        year, month, day, hour, minute, second);
                alarmMessage.setEventTime(timeStr);
            }
            
            log.info("解析完成: {}", alarmMessage);
            
        } catch (Exception e) {
            log.error("解析报警数据异常: ", e);
        }
        
        return alarmMessage;
    }

    /**
     * 解析建筑消防设施系统配置情况消息 (类型标志06)
     * @param bytes 字节数组
     * @return 系统配置消息对象
     */
    private static SystemConfigMessage parseSystemConfigMessage(String[] bytes) {
        SystemConfigMessage message = new SystemConfigMessage();
        
        try {
            // 设置原始数据
          //  message.setRawData(String.join(" ", bytes));
            
            // 类型标志
            message.setTypeFlag(Integer.parseInt(bytes[0], 16));
            
            // 系统地址
            message.setSystemAddress(bytes[1]);
            
            log.info("解析建筑消防设施系统配置情况消息完成");
            
        } catch (Exception e) {
            log.error("解析建筑消防设施系统配置情况消息异常: ", e);
        }
        
        return message;
    }
    
    /**
     * 解析建筑消防设施部件配置情况消息 (类型标志07)
     * @param bytes 字节数组
     * @return 部件配置消息对象
     */
    private static ComponentConfigMessage parseComponentConfigMessage(String[] bytes) {
        ComponentConfigMessage message = new ComponentConfigMessage();
        
        try {
            // 设置原始数据
            message.setRawData(String.join(" ", bytes));
            
            // 类型标志
            message.setTypeFlag(Integer.parseInt(bytes[0], 16));
            
            // 系统地址
            message.setSystemAddress(bytes[1]);
            
            // 部件类型
            message.setComponentType(Integer.parseInt(bytes[2], 16));
            
            // 部件地址 (bytes[3]到bytes[6])
            StringBuilder componentAddress = new StringBuilder();
            for (int i = 3; i <= 6; i++) {
                componentAddress.append(bytes[i]);
            }
            message.setComponentAddress(componentAddress.toString());
            
            log.info("解析建筑消防设施部件配置情况消息完成");
            
        } catch (Exception e) {
            log.error("解析建筑消防设施部件配置情况消息异常: ", e);
        }
        
        return message;
    }
    
    /**
     * 解析建筑消防设施部件模拟量值消息 (类型标志03)
     * @param bytes 字节数组
     * @return 模拟量值消息对象
     */
    private static AnalogValueMessage parseAnalogValueMessage(String[] bytes) {
        AnalogValueMessage message = new AnalogValueMessage();
        
        try {
            // 设置原始数据
          //  message.setRawData(String.join(" ", bytes));
            
            // 类型标志
          //  message.setTypeFlag(Integer.parseInt(bytes[0], 16));
            
            // 信息对象数目
          //  message.setObjectCount(Integer.parseInt(bytes[1], 16));
            
            // 系统类型
            message.setSystemType(Integer.parseInt(bytes[2], 16));
            
            // 系统地址
            message.setSystemAddress(bytes[3]);
            
            // 部件类型
            message.setComponentType(Integer.parseInt(bytes[4], 16));
            
            // 部件地址 (bytes[5]到bytes[8])
            StringBuilder componentAddress = new StringBuilder();
            for (int i = 5; i <= 8; i++) {
                componentAddress.append(bytes[i]);
            }
            message.setComponentAddress(componentAddress.toString());
            
            // 模拟量类型
            message.setAnalogType(Integer.parseInt(bytes[9], 16));
            
            // 模拟量值
            message.setAnalogValue(Double.parseDouble(bytes[10]));
            
            log.info("解析建筑消防设施部件模拟量值消息完成");
            
        } catch (Exception e) {
            log.error("解析建筑消防设施部件模拟量值消息异常: ", e);
        }
        
        return message;
    }
} 