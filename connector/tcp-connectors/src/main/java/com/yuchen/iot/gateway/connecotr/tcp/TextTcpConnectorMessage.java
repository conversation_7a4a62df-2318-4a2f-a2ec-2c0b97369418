package com.yuchen.iot.gateway.connecotr.tcp;

import com.yuchen.iot.gateway.connector.api.controller.HttpConnectorMessage;
import com.yuchen.iot.gateway.connector.api.data.UpLinkContentType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.async.DeferredResult;

import java.util.Map;

/**
 * TODO 
 * <AUTHOR>
 * @date 2022/11/8 下午12:01
 * @version 2.0.0
 */
public class TextTcpConnectorMessage extends TcpConnectorMessage<String> {

    public TextTcpConnectorMessage(String message) {
        super(message);
    }

    public UpLinkContentType getContentType() {
        return UpLinkContentType.TEXT;
    }

    public byte[] getMessageInBytes() {
        return (this.message).getBytes();
    }
}
