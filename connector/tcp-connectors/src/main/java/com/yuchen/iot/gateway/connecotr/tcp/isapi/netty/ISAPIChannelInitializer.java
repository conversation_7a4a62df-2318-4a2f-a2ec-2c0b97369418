package com.yuchen.iot.gateway.connecotr.tcp.isapi.netty;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.codec.DelimiterBasedFrameDecoder;
import io.netty.handler.codec.http.HttpClientCodec;
import io.netty.handler.timeout.IdleStateHandler;

import java.util.concurrent.TimeUnit;

/**
 * TODO 
 * <AUTHOR>
 * @date 2022/12/2 下午3:27
 * @version 2.0.0
 */
public class ISAPIChannelInitializer extends ChannelInitializer<SocketChannel> {

    private ISAPIDigestHandler digestHandler;

    private ISAPIClientHandler clientHandler;

    private ISAPIIOHandler ioHandler;

    public ISAPIChannelInitializer(ISAPIDigestHandler digestHandler,
                                   ISAPIClientHandler clientHandler,
                                   ISAPIIOHandler ioHandler) {
        this.digestHandler = digestHandler;
        this.clientHandler = clientHandler;
        this.ioHandler = ioHandler;
    }

    protected void initChannel(SocketChannel socketChannel) throws Exception {
        ChannelPipeline p = socketChannel.pipeline();
        ByteBuf delimiter = Unpooled.buffer();
        delimiter.writeBytes("--MIME_boundary--".getBytes());
        p.addLast(new IdleStateHandler(100, 0, 0, TimeUnit.SECONDS));
        p.addLast(new HttpClientCodec());
        p.addLast(digestHandler);
        p.addLast(clientHandler);
        p.addLast(new DelimiterBasedFrameDecoder(1024 * 1024, false, true, delimiter));
        p.addLast(ioHandler);
    }
}
