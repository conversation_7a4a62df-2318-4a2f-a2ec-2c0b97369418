package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import com.yuchen.iot.gateway.connecotr.tcp.kehua.util.BCDUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @description: 历史告警数据主动上传应答,网关直接应答终端，不再由平台响应
 * @author: zhonghx
 * @create: 2023-02-13 14:17
 **/

@Data
@AllArgsConstructor
public class AlarmHistoryResponse implements  MessageEncoder{

    /*0 失败  1成功*/
    private byte ack;

    /*告警点*/
    private byte type;

    /*告警开始时间*/
    private String startTime;
    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(ack);
        buf.writeByte(type);
        byte[] timeByte = BCDUtil.strToBcd(startTime);
        buf.writeBytes(timeByte);
        return buf;
    }
}
