package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @description: 下线注销
 * @author: zhonghx
 * @create: 2023-02-16 10:57
 **/
@Data
@AllArgsConstructor
public class LogoutRequest implements MessageEncoder{

    /*固定AA*/
    private byte logout;

    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(logout);
        return buf;
    }
}
