package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @description: 终端数据读取, 运营云平台可以根据需要读取充电桩相关的终端数据，每次最大读取字节
 * 长度 256。
 * @author: zhonghx
 * @create: 2023-02-16 14:23
 **/
@Data
@AllArgsConstructor
public class GetTerminalDataRequest implements MessageEncoder{

    /*数据单元个数*/
    private byte dataCount;

    /*数据标识ID
    * 数据单元标识
    * */
    private Short[] DataIdArray;

    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(dataCount);
        for (short i : DataIdArray) {
            buf.writeShort(i);
        }
        return buf;
    }
}
