package com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.nio.charset.StandardCharsets;


/**
 * @description: 平台设置二维码请求
 * @author: zhong
 * @create: 2022-12-08 16:55
 **/
@Data
@AllArgsConstructor
public class QRCodeRequestMessage implements MessageEncoder {

    /**
     * 二维码内容 长度 128 不足 补 0
     */
    private String qrcode;

    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        byte[] qrcodeByte =  qrcode.getBytes(StandardCharsets.US_ASCII);
        buf.writeBytes(qrcodeByte);
        // 获取设置二维码的长度
        int byteLen = qrcodeByte.length;
        // 如果长度不够 128 则进行 补零
        while( byteLen < 128 ) {
            buf.writeByte((byte) 0x00);
            byteLen++;
        }
        return buf;
    }

}
