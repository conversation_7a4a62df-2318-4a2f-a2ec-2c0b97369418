package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.TerminalData;
import io.netty.buffer.ByteBuf;
import lombok.Data;

import java.util.HashMap;

/**
 * @description: 充电桩响应设置终端数据命令
 * @author: zhonghx
 * @create: 2023-02-16 17:51
 **/
@Data
public class SetTerminalDataResponse implements MessageDecoder{

    /*数据单元个数*/
    private byte dataCount;

    /*响应map*/
    private HashMap<String, Integer>ackMap;

    @Override
    public void decode(ByteBuf in) {
        dataCount = in.readByte();
        for (int i = 0; i < dataCount; i++) {
            int id = in.readShort();
            int result = in.readByte();
            TerminalData data = TerminalData.idOf(id);
            if (data == null){
                throw new IllegalArgumentException("dataType is illegal" );
            }
            ackMap.put(data.getKey(), result);
        }
    }
}
