package com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.down;


import com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging.protocol.message.MessageEncoder;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class OpenTemperatureAlarmDownMessage implements MessageEncoder {



    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        return buf;
    }
}
