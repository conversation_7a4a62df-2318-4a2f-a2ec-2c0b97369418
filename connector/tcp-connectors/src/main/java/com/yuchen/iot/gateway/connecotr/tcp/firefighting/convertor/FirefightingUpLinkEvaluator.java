package com.yuchen.iot.gateway.connecotr.tcp.firefighting.convertor;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.yuchen.iot.gateway.connecotr.tcp.firefighting.util.ByteUtil;
import com.yuchen.iot.gateway.connector.api.converter.AbstractKeyEvaluator;
import com.yuchen.iot.gateway.connector.api.converter.evaluator.UpLinkEvaluator;
import com.yuchen.iot.gateway.connector.api.data.UpLinkData;
import com.yuchen.iot.gateway.connector.api.data.UpLinkMetaData;
import com.yuchen.iot.gateway.connector.api.data.message.CallResponse;
import com.yuchen.iot.gateway.connector.api.data.message.Event;
import com.yuchen.iot.gateway.connector.api.data.message.EventValue;
import com.yuchen.iot.gateway.connecotr.tcp.firefighting.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.pf4j.Extension;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 消防设备上行数据解析器
 *
 * <AUTHOR>
 * @date 2023/05/20
 * @version 1.0
 */
@Component
@Extension
@Slf4j
public class FirefightingUpLinkEvaluator extends AbstractKeyEvaluator implements UpLinkEvaluator {

    @Override
    public ListenableFuture<Object> execute(byte[] data, UpLinkMetaData upLinkMetaData) {
        List<UpLinkData> upLinkDataList = new ArrayList<>();
        try {
            JsonNode jsonNode = this.objectMapper.readTree(data);
            
            // 获取设备唯一标识
            String deviceCode = jsonNode.get("deviceCode").asText();
            String cmd = jsonNode.get("cmd").asText();
            String rawData = jsonNode.get("data").asText();
            String flow = jsonNode.get("flow").asText();
            // 创建事件消息
            Event event = new Event();
            EventValue eventValue = new EventValue();

            CallResponse callResponse = new CallResponse();
            String callId = flow;
            
            // 解析消息类型
            int typeFlag = -1;
            
            try {
                // 尝试获取类型标识
                if (rawData != null && !rawData.isEmpty()) {
                    byte[] bytes = ByteUtil.hexStringToByteArray(rawData);
                     typeFlag = Integer.parseInt(String.valueOf(bytes[0]), 16);
                }
            } catch (Exception e) {
                log.warn("Failed to parse type flag from raw data: {}", e.getMessage());
            }
            
            // 根据cmd决定处理方式
            boolean isCmd02 = "02".equals(cmd);
            boolean isCmd05 = "05".equals(cmd);
            
            // 根据消息类型进行处理
            switch (typeFlag) {
                case 0x01: // 类型标志01 - 建筑消防设施系统状态
                    processJsonNode(jsonNode, "systemStatusMessage", "systemStatus", isCmd02, isCmd05, event, eventValue, callResponse);
                    break;
                    
                case 0x02: // 类型标志02 - 火灾报警
                    processJsonNode(jsonNode, "alarmMessage", "fireAlarm", isCmd02, isCmd05, event, eventValue, callResponse);
                    break;
                    
                case 0x03: // 类型标志03 - 建筑消防设施部件模拟量值
                    processJsonNode(jsonNode, "analogValueMessage", "analogValue", isCmd02, isCmd05, event, eventValue, callResponse);
                    break;
                    
                case 0x04: // 类型标志04 - 建筑消防设施操作信息记录
                    processJsonNode(jsonNode, "operationRecordMessage", "operationRecord", isCmd02, isCmd05, event, eventValue, callResponse);
                    break;
                    
                case 0x06: // 类型标志06 - 建筑消防设施系统配置情况
                    processJsonNode(jsonNode, "systemConfigMessage", "systemConfig", isCmd02, isCmd05, event, eventValue, callResponse);
                    break;
                    
                case 0x07: // 类型标志07 - 建筑消防设施部件配置情况
                    processJsonNode(jsonNode, "componentConfigMessage", "componentConfig", isCmd02, isCmd05, event, eventValue, callResponse);
                    break;
                    
                case 0x21: // 类型标志21 - 用户信息传输装置运行状态
                    processJsonNode(jsonNode, "deviceStatusMessage", "deviceStatus", isCmd02, isCmd05, event, eventValue, callResponse);
                    break;
                    
                case 0x24: // 类型标志24 - 用户信息传输装置操作信息记录
                    processJsonNode(jsonNode, "deviceOperationMessage", "deviceOperation", isCmd02, isCmd05, event, eventValue, callResponse);
                    break;
                    
                case 0x28: // 类型标志28 - 用户信息传输装置系统时间
                    processJsonNode(jsonNode, "systemTimeMessage", "systemTime", isCmd02, isCmd05, event, eventValue, callResponse);
                    break;
                    
                default:
                    event.setIdentifier("unknownMessage");
                    if (isCmd02) {
                        eventValue.put("typeFlag", String.valueOf(typeFlag));
                    } else if (isCmd05) {
                        callResponse.put("code", "0");
                        callResponse.put("data", "{\"typeFlag\":\"" + typeFlag + "\"}");
                    }
                    break;
            }
            
            // 设置事件值
            event.setValue(eventValue);
            
            if (event.getIdentifier() != null) {
                log.info("消防设备上报事件: deviceId={}, typeFlag={}, identifier={}, cmd={}", 
                        deviceCode, typeFlag, event.getIdentifier(), cmd);
            }
            
            // 创建上行数据，根据cmd类型设置不同的参数
            UpLinkData upLinkData;
            if (isCmd02) {
                // cmd=02：使用event，不使用callResponse
                upLinkData = new UpLinkData(deviceCode, event, null, null, null, null);
            } else if (isCmd05) {
                // cmd=05：使用callResponse，不使用event，callId为flowId
                upLinkData = new UpLinkData(deviceCode, null, null, callResponse, callId, null);
            } else {
                // 默认处理，保持原有逻辑
                upLinkData = new UpLinkData(deviceCode, event, null, callResponse, callId, null);
            }
            
            upLinkDataList.add(upLinkData);
            
        } catch (IOException e) {
            log.error("解析消防设备上行数据失败: ", e);
        }
        
        return Futures.immediateFuture(this.gson.toJson(upLinkDataList));
    }

    /**
     * 处理JsonNode中的特定消息节点
     *
     * @param jsonNode 原始JSON节点
     * @param messageKey 消息键名
     * @param identifier 事件标识符
     * @param isCmd02 是否为cmd=02
     * @param isCmd05 是否为cmd=05
     * @param event 事件对象
     * @param eventValue 事件值对象
     * @param callResponse 调用响应对象
     */
    private void processJsonNode(JsonNode jsonNode, String messageKey, String identifier, 
                               boolean isCmd02, boolean isCmd05, 
                               Event event, EventValue eventValue, CallResponse callResponse) {
        if (jsonNode.has(messageKey)) {
            JsonNode messageNode = jsonNode.get(messageKey);
            event.setIdentifier(identifier);
            
            if (messageNode != null) {
                if (isCmd02) {
                    // 处理顶层属性
                    messageNode.fields().forEachRemaining(field -> {
                        // 检查是否是嵌套对象
                        if (field.getValue().isObject()) {
                            eventValue.put(field.getKey(), field.getValue().toString());
                        } else {
                            eventValue.put(field.getKey(), field.getValue().asText());
                        }
                    });
                } else if (isCmd05) {
                    // 统一格式输出
                    callResponse.put("code", "0");
                    callResponse.put("data", messageNode.toString());
                }
            }
        }
    }

    @Override
    public String getKey() {
        return "fire-fighting-up-link";
    }

    @Override
    public void destroy() {
        // 无需实现
    }
}
