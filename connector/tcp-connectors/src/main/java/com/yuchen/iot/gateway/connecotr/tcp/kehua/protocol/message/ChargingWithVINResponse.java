package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * @description: 服务器应答VIN码充电请求
 * @author: zhonghx
 * @create: 2023-02-15 14:45
 **/

@Data
@AllArgsConstructor
public class ChargingWithVINResponse implements MessageEncoder{

    /*请求结果  0失败  1成功*/
    private byte ack;

    /*用户卡号*/
    private String cardNumber;

    /*用户余额
    * 两位小数，单位：分
    0x1234 = 4660(Dec) = 46.60 元
    * */
    private int balance;

    /*充电流水号*/
    private int chargingSN;

    /*停止校验码*/
    private short verifyCode;

    /*失败原因
    *请求结果为 0 时有效
    1：VIN 码未注册；
    2：该 VIN 关联账户已禁用
    3：该 VIN 关联账户余额不足
    4：该 VIN 关联账户正在充电中
    5：其他
    * */
    private byte reason;
    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(ack);
        buf.writeBytes(cardNumber.getBytes(StandardCharsets.US_ASCII));
        buf.writeInt(balance);
        buf.writeInt(chargingSN);
        buf.writeShort(verifyCode);
        buf.writeByte(reason);
        return buf;
    }
}
