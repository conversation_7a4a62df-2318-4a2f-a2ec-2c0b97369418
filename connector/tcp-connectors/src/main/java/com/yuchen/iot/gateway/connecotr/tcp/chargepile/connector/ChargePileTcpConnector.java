package com.yuchen.iot.gateway.connecotr.tcp.chargepile.connector;

import com.yuchen.iot.gateway.connecotr.tcp.JsonTcpConnectorMessage;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.constants.ChargePileConstants;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.message.response.DataPackResponse;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.netty.server.ChargePileChannelListener;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.netty.server.NettyServer;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.util.ChannelMapByEntityUtil;
import com.yuchen.iot.gateway.connector.api.AbstractConnector;
import com.yuchen.iot.gateway.connector.api.ConnectorInitParams;
import com.yuchen.iot.gateway.connector.api.WaitCallResponseCallback;
import com.yuchen.iot.gateway.connector.api.data.DownLinkData;
import com.yuchen.iot.gateway.connector.api.data.UpLinkData;
import com.yuchen.iot.gateway.connector.api.data.UpLinkMetaData;
import com.yuchen.iot.gateway.connector.api.message.ThingMessage;
import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class ChargePileTcpConnector extends AbstractConnector<JsonTcpConnectorMessage> {

    private static final Logger log = LoggerFactory.getLogger(ChargePileTcpConnector.class);

    private NettyServer nettyServer;
    public ChargePileTcpConnector(NettyServer nettyServer){
        this.nettyServer = nettyServer;
    }

    public static Map<String,Object> lister = new ConcurrentHashMap<>();

   @Override
   public void init(ConnectorInitParams params) {
        super.init(params);
        try {
           /* if (StringUtils.isBlank(params.getConfiguration().getConfiguration())) {
                log.error("充电桩连接没有配置信息");
                return;
            }
            JSONObject jsonObject = (JSONObject) JSONObject.parse(params.getConfiguration().getConfiguration());
            if (jsonObject.get("metadata") == null) {
                log.error("充电桩连接没有配置netty元数据");
                return;
            }
            JSONObject jsonMetadata = (JSONObject) JSONObject.parse(jsonObject.get("metadata").toString());
            int port = jsonMetadata.getInteger("port");
            nettyServer.start(port);*/
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void process(JsonTcpConnectorMessage message) {
        try {
            //todo 充电桩上报04 充电启动请求
            //todo 充电桩上报06 充电数据上传
            //todo 桩充电停止请求05 上报充电数据
            //todo 充电桩上发12  付费卡刷卡启动请求
            //todo 充电桩上发11 充电离线请求
            //todo 充电桩上发10 电源模块数据上传
            //todo 充电桩上发控制0C 命令下发 调整枪实时功率数据
            doProcess(message);
        } catch (Exception e) {
            log.error("[{}] charge pile message deal exception",message,e);
        }
    }

    protected void doProcess(JsonTcpConnectorMessage message) throws Exception {
        List<UpLinkData> upLinkDataList = this.convert(message);
        if (upLinkDataList != null && !upLinkDataList.isEmpty()) {
            for (UpLinkData upLinkData : upLinkDataList) {
                this.processUpLinkData(upLinkData);
                log.trace("[{}] Processing up link data", upLinkData);
            }
        }
    }

    protected List<UpLinkData> convert(JsonTcpConnectorMessage message) throws Exception {
        byte[] data = message.getMessageInBytes();
        Map<String, String> mdMap = new HashMap<>(this.metadataTemplate.getKvMap());
        return this.convertToUpLinkDataList(this.connectorContext.getCallbackExecutor(), data,
                new UpLinkMetaData(message.getContentType(), mdMap));
    }

    @Override
    public void onDownLinkMessage(ThingMessage message, WaitCallResponseCallback callback) {
        if (this.downLinkConverter != null) {
            this.processDownLinkMessage(message, callback);
        }
    }

    private void processDownLinkMessage(ThingMessage message, WaitCallResponseCallback callback) {
        Map<String, String> mdMap = new HashMap<>(this.metadataTemplate.getKvMap());
        try {
            List<DownLinkData> result = this.downLinkConverter
                    .convertDownLink(Collections.singletonList(message), mdMap);
            if(result.isEmpty()){
                return;
            }
            //根据标识符处理,相关相关数据,暂时做成if-else,后面需要好改成每个处理一个类
            if(message.getIdentifier().equals("app_start")){//todo
               Channel channel = ChannelMapByEntityUtil.getChannel(message.getIntegrateNo());
                DataPackResponse appStartResponse = new DataPackResponse();
                appStartResponse.setCmd(0);//需要解析IOT平台传过来的数据 todo
                appStartResponse.setData(null);//需要解析平台传过来的数据 todo
                if(channel != null && channel.isActive()){
                    channel.writeAndFlush(appStartResponse);
                }
            }else if(message.getIdentifier().equals("app_send_charge_price")){//app端下发电价04 todo

            }else if (message.getIdentifier().equals("app_send_charge_price_owe")) {//app端上报欠费数据06 todo

            }else if(message.getIdentifier().equals("app_stop")){//充电未完成,中途停止app端下发08 todo

            }else if(message.getIdentifier().equals("app_charge_finish_stop")){//充电完成,app端下发充电停止请求 平台下发05 todo

            }else if(message.getIdentifier().equals("app_charge_finish_stop")){//app端对卡检验不通过，下发0x09消息；若检验通过，则下发0x08消息 todo

            }else if(message.getIdentifier().equals("app_charge_offline_request")){//app端下发11 充电离线请求 todo

            }else if(message.getIdentifier().equals("app_charge_power_upload")){//业务端上发10 电源模块数据上传 todo

            }else if(message.getIdentifier().equals("app_cmd_down")){//平台下发控制0C 命令下发 调整枪实时功率数据 todo

            }
        } catch (Exception e) {
            log.warn("Failed to process downLink message", e);
        }
    }

    @Override
    protected void doListenOnEndpoint(String deviceKey, Map<String, String> endpointMeta) {
        try {
            //初始化监听
            lister.put(ChargePileConstants.CHARGE_LISTENER_KEY+deviceKey, new ChargePileChannelListener<JsonTcpConnectorMessage>() {
                @Override
                public void onDisconnect() {getUpLinkDataListener(deviceKey).onSouthDisconnect();}

                @Override
                public void onConnect() {
                    getUpLinkDataListener(deviceKey).onSouthConnect();
                }
                @Override
                public void onMessage(JsonTcpConnectorMessage event) {
                    process(event);
                }
            });

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void doListenOffEndpoint(String deviceKey, Map<String, String> endpointMeta) {
        if (ChannelMapByEntityUtil.channelBySnNoMap.containsKey(deviceKey)) {
            Channel channel = ChannelMapByEntityUtil.channelBySnNoMap.get(deviceKey);
            if (channel != null) {
                channel.close().addListener(future -> {
                    if (future.isSuccess()) {
                        this.getUpLinkDataListener(deviceKey).onSouthDisconnect();
                    } else {
                        future.cause().printStackTrace();
                    }
                });
            }
        }
    }

    @Override
    public void destroy() {

    }
}
