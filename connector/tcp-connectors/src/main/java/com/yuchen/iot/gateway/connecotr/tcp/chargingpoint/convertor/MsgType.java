package com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.convertor;

import lombok.Getter;

public enum MsgType {

    /*----链路检测----*/
    CLI_LOGIN(0x0101),              /*登入*/
    CLI_HEARTBEAT(0x0102),          /*心跳/校时*/

    /*----维护----*/
    SER_RESET(0x0F01),              /*终端复位*/
    SER_FIRMWARE(0x0F02),           /*终端软硬件版本*/

    /*----参数设置----*/
    SER_SET_QR_FORMAT(0x0401),      /*二维码格式*/
    SER_SET_CONCENTRATOR(0X0402),   /*设置集中器*/
    SER_SET_RATE(0x0403),           /*费率设置*/
    SER_GET_RATE(0x0A03),           /*主站读取终端费率*/
    CLI_RATE_REQ(0x0A04),           /*终端请求费率*/


    /*----参数设置----*/
    /*----状态----*/
    CLI_REAL_TIME_STATUS(0x0C01),   /*实时状态信息*/
    CLI_GUN_STATUS(0x0C02),         /*充电枪遥测信息*/
    CLI_CHARGING_INFO(0x0C03),      /*充电实时信息*/
    CLI_CHARGING_INFO_NEW(0x0C10),      /*充电实时信息*/
    CLI_NETWORK_INFO(0x0C04),       /*网络信息*/
    CLI_TELEMETRY(0x0C05),          /*终端遥测信息*/

    /*----控制----*/
    CLI_CARD_AUTH(0x0501),          /*刷卡鉴权*/
    CLI_START_CHARGING(0x0502),     /*终端启动充电*/
    CLI_STOP_CHARGING(0x0503),      /*终端充电结束*/
    CLI_STOP_CHARGING_NEW(0x0512),      /*终端充电结束*/
    SERVER_START_CHARGING(0x0504),  /*主站启动充电*/
    SERVER_STOP_CHARGING(0x0505),   /*主站停止充电*/
    /**
     * 车辆VIN鉴权
     */
    VIN_AUTH(0x0508),
    /**
     * VIN码启动充电
     */
    VIN_START_CHARGING(0x0509);

    @Getter
    private final int type;

    MsgType(int type) {
        this.type = type;
    }

    public static MsgType findByType(int type) {
        for (MsgType msgType : MsgType.values()) {
            if (msgType.getType() == type) {
                return msgType;
            }
        }
        return null;
    }
}
