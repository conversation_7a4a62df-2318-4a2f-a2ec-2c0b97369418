package com.yuchen.iot.gateway.connecotr.tcp.firefighting.util;


import com.yuchen.iot.gateway.connecotr.tcp.firefighting.UserSmartIotByKeAnHandler;
import com.yuchen.iot.gateway.connecotr.tcp.firefighting.entity.DownCommandMessage;
import io.netty.channel.Channel;
import lombok.extern.slf4j.Slf4j;

import java.util.Calendar;

@Slf4j
public class DownCommandUtil {
    
    /**
     * 构建下行命令消息
     * @param data 应用数据单元
     * @param srcAddress 源地址
     * @param destAddress 目的地址
     * @return 完整的下行命令消息
     */
    public static DownCommandMessage buildCommandMessage(String data, String srcAddress, String destAddress) {
        DownCommandMessage message = new DownCommandMessage();
        
        // 设置固定值
        message.setHeader("4040");  // @@
        message.setEnd("2323");     // ##
        
        // 设置业务流水号（默认值，可以被外部设置覆盖）
        message.setFlowId("0100");
        
        // 设置协议版本号
        message.setVersion("014b");  // 主版本号1，用户版本号0
        
        // 设置时间标签
        Calendar now = Calendar.getInstance();
        String time = String.format("%02d%02d%02d%02d%02d%02d",
                now.get(Calendar.SECOND),
                now.get(Calendar.MINUTE),
                now.get(Calendar.HOUR_OF_DAY),
                now.get(Calendar.DAY_OF_MONTH),
                now.get(Calendar.MONTH) + 1,
                now.get(Calendar.YEAR) - 2000);
        message.setTime(time);
        
        // 设置地址
        message.setSrcAddress(srcAddress);
        message.setDestAddress(destAddress);
        
        // 设置命令
        message.setCommand("04");
        
        // 设置应用数据单元
        message.setData(data);
        
        // 计算应用数据单元长度
        // 修改为
       String dataLength = String.format("%02X%02X", (data.length() / 2) & 0xFF, ((data.length() / 2) >> 8) & 0xFF);
        message.setDataLength(dataLength);
        
        // 计算校验和
        String checksum = calculateChecksum(message);
        message.setChecksum(checksum);
        
        return message;
    }
    
    /**
     * 计算校验和
     * @param message 下行命令消息
     * @return 校验和
     */
    private static String calculateChecksum(DownCommandMessage message) {
        // 计算控制单元和应用数据单元的算术校验和
        int sum = 0;
        
        // 业务流水号
        sum += Integer.parseInt(message.getFlowId().substring(0, 2), 16);
        sum += Integer.parseInt(message.getFlowId().substring(2), 16);
        
        // 协议版本号
        sum += Integer.parseInt(message.getVersion().substring(0, 2), 16);
        sum += Integer.parseInt(message.getVersion().substring(2), 16);
        
        // 时间标签
        for (int i = 0; i < message.getTime().length(); i += 2) {
            sum += Integer.parseInt(message.getTime().substring(i, i + 2), 16);
        }
        
        // 源地址
        for (int i = 0; i < message.getSrcAddress().length(); i += 2) {
            sum += Integer.parseInt(message.getSrcAddress().substring(i, i + 2), 16);
        }
        
        // 目的地址
        for (int i = 0; i < message.getDestAddress().length(); i += 2) {
            sum += Integer.parseInt(message.getDestAddress().substring(i, i + 2), 16);
        }
        
        // 应用数据单元长度
        sum += Integer.parseInt(message.getDataLength().substring(0, 2), 16);
        sum += Integer.parseInt(message.getDataLength().substring(2), 16);
        
        // 命令字节
        sum += Integer.parseInt(message.getCommand(), 16);
        
        // 应用数据单元
        if (message.getData() != null && !message.getData().isEmpty()) {
            for (int i = 0; i < message.getData().length(); i += 2) {
                sum += Integer.parseInt(message.getData().substring(i, i + 2), 16);
            }
        }
        
        // 取低8位
        return String.format("%02X", sum & 0xFF);
    }
    
    /**
     * 将下行命令消息转换为字节数组
     * @param message 下行命令消息
     * @return 字节数组
     */
    public static byte[] toBytes(DownCommandMessage message) {
        StringBuilder sb = new StringBuilder();
        sb.append(message.getHeader())
          .append(message.getFlowId())
          .append(message.getVersion())
          .append(message.getTime())
          .append(message.getSrcAddress())
          .append(message.getDestAddress())
          .append(message.getDataLength())
          .append(message.getCommand())
          .append(message.getData())
          .append(message.getChecksum())
          .append(message.getEnd());
        
        return ByteUtil.hexStringToByteArray(sb.toString());
    }
    
    /**
     * 构建读系统状态命令
     * @param systemType 系统类型
     * @param systemAddress 系统地址
     * @return 命令数据
     */
    public static String buildReadSystemStatusCommand(int systemType, int systemAddress) {
        // 十进制61 -> 十六进制3D
        return String.format("3D%02X%02X", systemType, systemAddress);
    }
    
    /**
     * 构建读部件状态命令
     * @param systemType 系统类型
     * @param systemAddress 系统地址
     * @param componentAddress 部件地址
     * @return 命令数据
     */
    public static String buildReadComponentStatusCommand(int systemType, int systemAddress, String componentAddress) {
        // 十进制62 -> 十六进制3E
        return String.format("3E%02X%02X%s", systemType, systemAddress, componentAddress);
    }
    
    /**
     * 构建读模拟量值命令
     * @param systemType 系统类型
     * @param systemAddress 系统地址
     * @param componentAddress 部件地址
     * @return 命令数据
     */
    public static String buildReadAnalogValueCommand(int systemType, int systemAddress, String componentAddress) {
        // 十进制63 -> 十六进制3F
        return String.format("3F%02X%02X%s", systemType, systemAddress, componentAddress);
    }
    
    /**
     * 构建读操作记录命令
     * @param systemType 系统类型
     * @param systemAddress 系统地址
     * @param recordCount 记录数量
     * @param startTime 开始时间
     * @return 命令数据
     */
    public static String buildReadOperationRecordCommand(int systemType, int systemAddress, int recordCount, String startTime) {
        // 十进制64 -> 十六进制40
        return String.format("40%02X%02X%02X%s", systemType, systemAddress, recordCount, startTime);
    }
    
    /**
     * 构建读软件版本命令
     * @param systemType 系统类型
     * @param systemAddress 系统地址
     * @return 命令数据
     */
    public static String buildReadSoftwareVersionCommand(int systemType, int systemAddress) {
        // 十进制65 -> 十六进制41
        return String.format("41%02X%02X", systemType, systemAddress);
    }
    
    /**
     * 构建读系统配置命令
     * @param systemType 系统类型
     * @param systemAddress 系统地址
     * @return 命令数据
     */
    public static String buildReadSystemConfigCommand(int systemType, int systemAddress) {
        // 十进制66 -> 十六进制42
        return String.format("42%02X%02X", systemType, systemAddress);
    }
    
    /**
     * 构建读部件配置命令
     * @param systemType 系统类型
     * @param systemAddress 系统地址
     * @param componentAddress 部件地址
     * @return 命令数据
     */
    public static String buildReadComponentConfigCommand(int systemType, int systemAddress, String componentAddress) {
        // 十进制67 -> 十六进制43
        return String.format("43%02X%02X%s", systemType, systemAddress, componentAddress);
    }
    
    /**
     * 构建读系统时间命令
     * @param systemType 系统类型
     * @param systemAddress 系统地址
     * @return 命令数据
     */
    public static String buildReadSystemTimeCommand(int systemType, int systemAddress) {
        // 十进制68 -> 十六进制44
        return String.format("44%02X%02X", systemType, systemAddress);
    }
    
    /**
     * 构建读设备状态命令
     * @return 命令数据
     */
    public static String buildReadDeviceStatusCommand() {
        // 十进制81 -> 十六进制51
        return "510100";
    }
    
    /**
     * 构建读设备操作记录命令
     * @param recordCount 记录数量
     * @param startTime 开始时间
     * @return 命令数据
     */
    public static String buildReadDeviceOperationCommand(int recordCount, String startTime) {
        // 十进制84 -> 十六进制54
        return String.format("5401%02X%s", recordCount, startTime);
    }
    
    /**
     * 构建读设备软件版本命令
     * @return 命令数据
     */
    public static String buildReadDeviceVersionCommand() {
        // 十进制85 -> 十六进制55
        return "550100";
    }
    
    /**
     * 构建读设备配置命令
     * @return 命令数据
     */
    public static String buildReadDeviceConfigCommand() {
        // 十进制86 -> 十六进制56
        return "560100";
    }
    
    /**
     * 构建读设备时间命令
     * @return 命令数据
     */
    public static String buildReadDeviceTimeCommand() {
        // 十进制88 -> 十六进制58
        return "580100";
    }
    
    /**
     * 构建初始化设备命令
     * @return 命令数据
     */
    public static String buildInitDeviceCommand() {
        // 十进制89 -> 十六进制59
        return "590100";
    }
    
    /**
     * 构建同步设备时间命令
     * @param time 时间
     * @return 命令数据
     */
    public static String buildSyncDeviceTimeCommand(String time) {
        // 十进制90 -> 十六进制5A
        return "5A01" + time;
    }
    
    /**
     * 构建查岗命令
     * @param timeout 超时时间（分钟）
     * @return 命令数据
     */
    public static String buildCheckPostCommand(int timeout) {
        // 十进制91 -> 十六进制5B
        return String.format("5B01%02X", timeout);
    }
    
    /**
     * 发送下行命令消息
     * @param ip 目标客户端IP
     * @param message 下行命令消息
     * @return 是否发送成功
     */
    public static boolean sendCommandMessage(String ip, DownCommandMessage message) {
        try {
            Channel channel = UserSmartIotByKeAnHandler.getChannelMap().get(ip);
            if (channel != null && channel.isActive()) {
                byte[] messageBytes = toBytes(message);
                channel.writeAndFlush(messageBytes);
                log.info("成功发送下行命令到IP: {}, 消息内容: {}", ip, message);
                return true;
            } else {
                log.error("目标客户端未连接或连接已断开: {}", ip);
                return false;
            }
        } catch (Exception e) {
            log.error("发送下行命令失败: ", e);
            return false;
        }
    }
    
    /**
     * 发送下行命令消息（使用字节数组）
     * @param ip 目标客户端IP
     * @param messageBytes 下行命令消息字节数组
     * @return 是否发送成功
     */
    public static boolean sendCommandBytes(String ip, byte[] messageBytes) {
        try {
            Channel channel = UserSmartIotByKeAnHandler.getChannelMap().get(ip);
            if (channel != null && channel.isActive()) {
                channel.writeAndFlush(messageBytes);
                log.info("成功发送下行命令字节数组到IP: {}", ip);
                return true;
            } else {
                log.error("目标客户端未连接或连接已断开: {}", ip);
                return false;
            }
        } catch (Exception e) {
            log.error("发送下行命令失败: ", e);
            return false;
        }
    }
} 