package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @description: 充电桩主动上传数据的应答，平台应答确认信息域
 * @author: zhonghx
 * @create: 2023-02-14 15:37
 **/
@Data
@AllArgsConstructor
public class RealTimeGunStateChangedResponse implements MessageEncoder{

    /*帧ID，平台获取帧ID后原值返回*/
    private byte frameId;

    /*结果 0失败 1成功*/
    private byte result;
    @Override
    public ByteBuf encode() {

        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(frameId);
        buf.writeByte(result);
        return buf;
    }
}
