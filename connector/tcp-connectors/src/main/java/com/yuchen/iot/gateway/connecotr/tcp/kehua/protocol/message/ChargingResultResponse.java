package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * @description: 启停充电结果上传回复,平台回复,网关直接回复！！
 * @author: zhonghx
 * @create: 2023-02-11 18:23
 **/
@AllArgsConstructor
@Data
public class ChargingResultResponse implements MessageEncoder{

    /*用户账号*/
    private String account;

    /*充电流水号*/
    private int chargingSN;

    @Override
    public ByteBuf encode() {

        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeBytes(account.getBytes(StandardCharsets.US_ASCII));
        buf.writeInt(chargingSN);
        return buf;
    }
}
