package com.yuchen.iot.gateway.connecotr.tcp.chargingpoint;

import com.alibaba.fastjson.JSON;
import com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.comm.Constant;
import io.netty.channel.ChannelDuplexHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelPromise;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ChargingPointHandler extends ChannelDuplexHandler {

    private final Logger logger = LoggerFactory.getLogger(ChargingPointHandler.class);

    private final ChargingChannelListener chargingChannelListener;

    public ChargingPointHandler(ChargingChannelListener chargingChannelListener){
        this.chargingChannelListener = chargingChannelListener;
    }

    @Override
    public void handlerAdded(ChannelHandlerContext ctx) {
        logger.info("有新的连接：[{}]", ctx.channel().id().asLongText());

    }

    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
        logger.info("Channel heartBeat lost");
        if (evt instanceof IdleStateEvent && IdleState.READER_IDLE == ((IdleStateEvent) evt).state()) {
            ctx.close();
        }
    }

    @Override
    public void channelRegistered(ChannelHandlerContext ctx) throws Exception {
        logger.info("REGISTERED");
        ctx.fireChannelRegistered();
    }

    @Override
    public void channelUnregistered(ChannelHandlerContext ctx) throws Exception {
        logger.info("UNREGISTERED");
        ctx.fireChannelUnregistered();
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        logger.info("ACTIVE");
        ctx.channel().attr(Constant.eventListenerKey).set(chargingChannelListener);
        ctx.fireChannelActive();
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        logger.info("ChargingPointHandler-INACTIVE");

        // 获取channel中id
        String ChargingPointId = ctx.channel().attr(Constant.integrateNoKey).get();
        logger.info("INACTIVE ChargingPointId：{}",ChargingPointId);
        if (chargingChannelListener != null && StringUtils.isNotEmpty(ChargingPointId)){
            logger.info("ChargingPointHandler-通知 IOT 设备离线");
            chargingChannelListener.onDisconnect(ChargingPointId);
            removeChannel(ctx);
        }
        ctx.fireChannelInactive();
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        logger.info("ChargingPointHandler-EXCEPTION");
        cause.printStackTrace();

        // 获取channel中id
        String ChargingPointId = ctx.channel().attr(Constant.integrateNoKey).get();
        logger.info("EXCEPTION ChargingPointId：{}",ChargingPointId);
        if (chargingChannelListener != null && StringUtils.isNotEmpty(ChargingPointId)){
            logger.info("ChargingPointHandler-通知 IOT 设备离线");
            chargingChannelListener.onDisconnect(ChargingPointId);
            removeChannel(ctx);
        }
        ctx.fireExceptionCaught(cause);
    }

    @Override
    public void write(ChannelHandlerContext ctx, Object msg, ChannelPromise promise) throws Exception {
        logger.info("WRITE {}", JSON.toJSONString(msg));
        ctx.write(msg, promise);
    }

    private void removeChannel(ChannelHandlerContext ctx) {
        logger.info("ChargingPointHandler-设备离线、开始清除通道：{}",ctx);
        // 获取channel中id
        String noKey = ctx.channel().attr(Constant.integrateNoKey).get();
        logger.info("ChargingPointHandler-设备离线、开始清除通道noKey：{}",noKey);
        // map移除channel
        ChargingPointChannelManager.removeChannelByName(noKey);
        logger.info("ChargingPointHandler-设备离线、清除通道结束");
    }


    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        ctx.fireChannelRead(msg);
    }

}