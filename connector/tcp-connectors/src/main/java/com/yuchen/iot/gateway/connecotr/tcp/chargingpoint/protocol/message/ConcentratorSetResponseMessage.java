package com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.protocol.message;

import io.netty.buffer.ByteBuf;
import lombok.Data;

/**
 * @description: 集中器应答设置
 * @author: zhonghx
 * @create: 2022-12-10 10:21
 **/
@Data
public class ConcentratorSetResponseMessage implements MessageDecoder{

    /*设置集中器结果 6成功 0失败*/
    private byte result;
    @Override
    public void decode(ByteBuf in) {
        result = in.readByte();
    }
}
