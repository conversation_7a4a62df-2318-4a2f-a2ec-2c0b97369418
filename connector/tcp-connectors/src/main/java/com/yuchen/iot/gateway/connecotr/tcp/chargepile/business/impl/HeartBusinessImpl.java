package com.yuchen.iot.gateway.connecotr.tcp.chargepile.business.impl;

import com.alibaba.fastjson.JSON;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.business.IBusiness;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.constants.ChargePileConstants;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.enums.ChargeCommandCode;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.message.request.DataPackRequest;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.message.request.HeartRequest;
import com.yuchen.iot.gateway.connecotr.tcp.chargepile.message.response.DataPackResponse;
import io.netty.channel.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 心跳业务处理类
 */
@Service
@Slf4j
public class HeartBusinessImpl implements IBusiness {

    @Override
    public void process(Object request, Channel channel) {
        DataPackRequest dataPack = (DataPackRequest)request;
        HeartRequest heartRequest = JSON.parseObject(dataPack.decode().toString(),HeartRequest.class);
        log.info("接收到心跳报文================", JSON.toJSONString(heartRequest));
        if(channel == null){
            log.info("heart channel is null==========");
            return;
        }
        //回复心跳消息
        DataPackResponse response = new DataPackResponse();
        response.setCmd(ChargeCommandCode.PILE_HEART.getCode());
        channel.writeAndFlush(response);
    }

    @Override
    public String type() {
        return String.format("%s%d",ChargePileConstants.BUSINESS_TYPE,ChargeCommandCode.PILE_HEART.getCode());
    }
}
