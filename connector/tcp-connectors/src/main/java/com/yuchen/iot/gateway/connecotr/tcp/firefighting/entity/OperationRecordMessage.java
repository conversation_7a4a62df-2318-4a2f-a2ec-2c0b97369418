package com.yuchen.iot.gateway.connecotr.tcp.firefighting.entity;

import lombok.Data;

/**
 * 建筑消防设施操作信息记录消息实体类
 * 类型标志为04
 */
@Data
public class OperationRecordMessage {
    // 原始数据
   // private String rawData;
    
    // 类型标志
   // private int typeFlag;
    
    // 信息对象数目
   // private int objectCount;
    
    // 系统类型
    private String systemType;
    
    // 系统地址
    private String systemAddress;
    
    // 操作标志
    private OperationFlag operationFlag;
    
    // 操作员编号
    private String operatorId;
    
    // 时间
    private String eventTime;
    
    /**
     * 操作标志内部类
     */
    @Data
    public static class OperationFlag {
        // bit0: 0-无, 1-复位
        private int reset;
        
        // bit1: 0-无, 1-消音
        private int silenced;
        
        // bit2: 0-无, 1-手动报警
        private int manualAlarm;
        
        // bit3: 0-无, 1-警情消除
        private int alarmClear;
        
        // bit4: 0-无, 1-自检
        private int selfCheck;
        
        // bit5: 0-无, 1-确认
        private int confirmed;
        
        // bit6: 0-无, 1-测试
        private int testing;
        
        // bit7: 0-无, 1-延时
        private int delayed;
    }
}