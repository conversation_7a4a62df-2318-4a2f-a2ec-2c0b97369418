package com.yuchen.iot.gateway.connecotr.tcp.chargingpoint;

import io.netty.channel.Channel;

import java.util.concurrent.ConcurrentHashMap;

/**
 * @description: 管理充电桩的channel
 * @author: zhonghx
 * @create: 2022-12-30 15:07
 **/
public class ChargingPointChannelManager {

    //private static ConcurrentHashMap<String, Channel> channelHashMap=null;
    private static final ConcurrentHashMap<String, Channel> channelHashMap = new ConcurrentHashMap<>();

    private ChargingPointChannelManager(){}

    public static ConcurrentHashMap<String, Channel> getChannelHashMap() {
        return channelHashMap;
    }

    public static Channel getChannelByName(String name){
        if (name == null || name.isEmpty()){
            return null;
        }
        return channelHashMap.get(name);
    }


    public static void addChannel(String name,Channel channel){
        if (name == null || name.isEmpty()){
            return;
        }
        channelHashMap.put(name,channel);
    }


    public static void removeChannelByName(String name){
        if (name == null || name.isEmpty()){
            return;
        }
        channelHashMap.remove(name);
    }


    // 向指定 SN 的设备发送消息
    public static boolean sendMessageToSN(String sn, Object message) {
        Channel channel = getChannelByName(sn);
        if (channel != null) {
            channel.writeAndFlush(message);
        } else {
            // 处理设备不存在或未连接的情况
            return false;
        }
        return true;
    }
}
