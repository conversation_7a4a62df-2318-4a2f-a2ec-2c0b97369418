package com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.message;


import com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.BCDUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.Data;

/**
 * @description: 终端实时状态定期上报，服务端无需应答，一般约30秒或状态变化时发送一次
 * @author: zhong
 * @create: 2022-12-08 17:02
 **/
@Data
public class RealtimeStatusReportMessage implements MessageDecoder {

    /*枪编号，1-n */
    private String  serialNumber;

    private String  devNo;

    private int  gunNo;

    /*枪工作状态，0-离线 1-故障 2空闲 3 充电 */
    private byte gunState;

    /*充电桩充电枪座状态   0-否  1-归位，2-未知*/
    private byte gunConnectPoint;

    /*是否连接车辆（插枪）0-否  1-连接*/
    private byte gunConnectCar;

    private int outputVoltage;

    private int outputCurrent;

    private int gunLineTemperature;

    private String gunLineNo;

    private int SOC;

    private int maximumTemperature;

    private int  accumulatedChargingTime;

    private int chargingDegree;

    private int calculatedChargingDegree;


    private int rechargeAmount;


    private int hardwareFailure;

    @Override
    public void decode(ByteBuf in) {

        ByteBuf byteBuf=  in.readBytes(16);

        byte[] bytes = ByteBufUtil.getBytes(byteBuf);

        serialNumber = BCDUtil.bytesToHex(bytes);

        ByteBuf byteBuf1=  in.readBytes(7);

        byte[] bytes1 = ByteBufUtil.getBytes(byteBuf1);

        devNo = BCDUtil.bytesToHex(bytes1);

        gunNo = in.readByte();

        gunState = in.readByte();
        gunConnectPoint = in.readByte();

        gunConnectCar = in.readByte();
        outputVoltage = in.readUnsignedShort();
        outputCurrent = in.readUnsignedShort();
        gunLineTemperature= in.readByte();


        ByteBuf byteBuf3=  in.readBytes(8);

        byte[] bytes3 = ByteBufUtil.getBytes(byteBuf3);

        gunLineNo = BCDUtil.bytesToHex(bytes3);

        SOC=in.readByte();
        maximumTemperature=in.readByte();
        accumulatedChargingTime=in.readUnsignedShort();
        chargingDegree=in.readIntLE();
        calculatedChargingDegree=in.readIntLE();
        rechargeAmount=in.readIntLE();
        hardwareFailure=in.readUnsignedShort();
    }
}
