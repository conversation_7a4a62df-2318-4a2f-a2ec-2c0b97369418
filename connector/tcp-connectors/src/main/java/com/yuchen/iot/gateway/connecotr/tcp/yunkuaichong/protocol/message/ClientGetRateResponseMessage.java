package com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;

/**
 * @description: 平台应答终端费率请求。
 * @author: zhong
 * @create: 2022-12-08 16:55
 **/
@Data
@AllArgsConstructor
public class ClientGetRateResponseMessage implements MessageEncoder {

    /*服务费单价，单位：元/kWh, 4位小数*/
    private int serviceChargePerkWh;
    /*费率1单价*/
    private int rate1;
    /*费率2单价*/
    private int rate2;
    /*费率3单价*/
    private int rate3;
    /*费率4单价*/
    private int rate4;
    /*时段数量，至少1条，最多12条。时段必须按顺序排列，覆盖00：00 - 24：00*/
    private byte timeIntervalCount;

    private ArrayList<TimeIntervalInfo> array = new ArrayList<>();

    @Override
    public ByteBuf encode() {

        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeFloatLE(serviceChargePerkWh);
        buf.writeFloatLE(rate1);
        buf.writeFloatLE(rate2);
        buf.writeFloatLE(rate3);
        buf.writeFloatLE(rate4);
        buf.writeByte(timeIntervalCount);
        for (TimeIntervalInfo t: array) {
            buf.writeBytes(t.encode());
        }
        return buf;
    }
}
