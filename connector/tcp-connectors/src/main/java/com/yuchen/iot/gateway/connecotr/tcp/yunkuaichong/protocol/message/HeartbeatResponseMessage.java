package com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.message;

import com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.BCDUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;



/**
 * @description: 平台应答终端的心跳
 * @author: zhong
 * @create: 2022-12-08 16:55
 **/
@Data
@AllArgsConstructor
public class HeartbeatResponseMessage implements MessageEncoder {

    private String devNo;
    private String gunNo;
    private int result=0;

    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeBytes(BCDUtil.hexStringToByteArray(devNo));
        buf.writeBytes(BCDUtil.hexStringToByteArray(gunNo));
        buf.writeByte(result);
        return buf;
    }
}
