package com.yuchen.iot.gateway.connecotr.tcp.chargingpoint;


import com.yuchen.iot.gateway.connecotr.tcp.chargepile.util.BytesUtil;
import com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.comm.Constant;
import com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.protocol.ChargingPointProtocol;
import com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.protocol.MsgHeader;
import com.yuchen.iot.gateway.connecotr.tcp.chargingpoint.protocol.message.MessageEncoder;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.util.HexStringUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;
import io.netty.util.ReferenceCountUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class ChargingPointEncoder extends MessageToByteEncoder<ChargingPointProtocol<Object>> {

    private static final Logger log = LoggerFactory.getLogger(ChargingPointEncoder.class);

    @Override
    protected void encode(ChannelHandlerContext ctx, ChargingPointProtocol<Object> msg, ByteBuf byteBuf) throws Exception {

        Integer devType = ctx.channel().attr(Constant.devTypeKey).get();
        Integer devVer = ctx.channel().attr(Constant.devVerKey).get();
        MessageEncoder messageEncoder = (MessageEncoder)msg.getBody();
        ByteBuf buf = messageEncoder.encode();

        MsgHeader header = msg.getHeader();
        byteBuf.writeByte(header.getPreamble());
        byteBuf.writeShortLE(15 + buf.readableBytes());
        byteBuf.writeByte(devVer);

        int direction = header.getDirection() == 1 ? 0x80 : 0;
        int subDirection = header.getSubDirection() == 1 ? 0x40 : 0;
        byteBuf.writeByte(direction + subDirection);
        byteBuf.writeIntLE(header.getTime());
        byteBuf.writeByte(devType);
        byte[] bytes = hexString2Bytes(header.getAddress());
        bytes = BytesUtil.bytesReverse(bytes);
        byteBuf.writeBytes(bytes);
        byteBuf.writeShortLE(header.getCmd());
        byteBuf.writeBytes(buf);

        int sum = 0;
        ByteBuf calBuf = byteBuf.copy();
        while (calBuf.readableBytes() > 0){
            byte b = calBuf.readByte();
            sum = sum + b;
        }

        byteBuf.writeByte(sum & 0xff);
        byteBuf.writeByte(header.getTerminator());
        // TODO:排查内存泄漏的问题
        ReferenceCountUtil.release(buf);

        log.info("ChargingPointEncoder---"+header.getCmd()+"---"+ HexStringUtil.Bytes2hexStr(ByteBufUtil.getBytes(byteBuf)));

    }

    private static byte charToByte(char c) {
        return (byte) "0123456789ABCDEF".indexOf(c);
    }

    public static byte[] hexString2Bytes(String hex) {

        if ((hex == null) || (hex.equals(""))){
            return null;
        }
        else if (hex.length()%2 != 0){
            return null;
        }
        else{
            hex = hex.toUpperCase();
            int len = hex.length()/2;
            byte[] b = new byte[len];
            char[] hc = hex.toCharArray();
            for (int i=0; i<len; i++){
                int p=2*i;
                b[i] = (byte) (charToByte(hc[p]) << 4 | charToByte(hc[p+1]));
            }
            return b;
        }

    }
}
