package com.yuchen.iot.gateway.connecotr.tcp.chargepile.util;


import io.netty.channel.Channel;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * netty服务channelMap类
 * <AUTHOR>
 * @date 2022/12/07 15:29
 */
public class ChannelMapByEntityUtil {

    private ChannelMapByEntityUtil() {
    }

    /**
     * key:桩编号,value:channel
     */
    public static Map<String, Channel> channelBySnNoMap = new ConcurrentHashMap<>();

    /**
     * key:channel,value:桩编号
     */
    public static Map<Channel, String> snNoByChannelMap =  new ConcurrentHashMap();

    /**
     *
     * add channelInfo
     *
     * @param channel
     * @return void
     * @exception
     */
    public static void addChannel(String pileNo, Channel channel) {
        channelBySnNoMap.put(pileNo, channel);
    }

    public static void addChannel(Channel channel, String pileNo) {
        snNoByChannelMap.put(channel, pileNo);
    }


    /**
     * 根据桩编号移出channel
     */
    public static void removeChannel(String pileNo) {
        Channel channel = channelBySnNoMap.get(pileNo);
        if (channel != null) {
            snNoByChannelMap.remove(channel);
        }
        channelBySnNoMap.remove(pileNo);
    }


    /**
     * 根据充电桩实体获取通道
     */
    public static Channel getChannel(String pileNo) {
        return channelBySnNoMap.get(pileNo);
    }

    /**
     * 根据通道获取充电桩实体信息
     */
    public static String getChannel(Channel channel) {
        String temp= snNoByChannelMap.get(channel);
        if (StringUtils.isNotEmpty(temp)){
            return temp;
        }
        return null;
    }


    /**
     * 根据通道信息移除通道对应的信息
     * <AUTHOR>
     * @date 2022/12/07 15:29
     */
    public static void removeChannel(Channel channel) {
        String pileNo = snNoByChannelMap.get(channel);
        if (pileNo != null) {
            channelBySnNoMap.remove(pileNo);
        }
        snNoByChannelMap.remove(channel);
    }
}
