package com.yuchen.iot.gateway.connecotr.tcp.yunkuaichong.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @description: 主站发起，查询终端的软硬件版本
 * @author: zhonghx
 * @create: 2022-12-09 19:25
 **/
@Data
@AllArgsConstructor
public class FirmwareRequestMessage implements MessageEncoder {

    /*方式， 1-终端软硬件版本*/
    private byte type;

    @Override
    public ByteBuf encode() {
        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeByte(type);
        return buf;
    }
}
