package com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging;

import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;

import java.util.List;

public class CustomDelimiterBasedFrameDecoder extends ByteToMessageDecoder {

    private static final byte DELIMITER = 0x7E;

    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) throws Exception {
        int readableBytes = in.readableBytes();

        if (readableBytes < 2) {
            return; // Not enough data to read
        }

        int frameStartIndex = in.indexOf(in.readerIndex(), in.readerIndex() + readableBytes, DELIMITER);
        if (frameStartIndex == -1) {
            return; // No start delimiter found
        }

        int frameEndIndex = in.indexOf(frameStartIndex + 1, in.readerIndex() + readableBytes, DELIMITER);
        if (frameEndIndex == -1) {
            return; // No end delimiter found
        }

        // Include the end delimiter in the frame
        ByteBuf frame = in.retainedSlice(frameStartIndex, frameEndIndex - frameStartIndex + 1);
        in.readerIndex(frameEndIndex + 1);
        out.add(frame);
    }
}
