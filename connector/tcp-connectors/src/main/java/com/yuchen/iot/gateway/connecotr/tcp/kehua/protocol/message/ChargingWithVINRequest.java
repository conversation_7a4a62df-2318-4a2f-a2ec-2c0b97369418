package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufUtil;
import lombok.Data;

/**
 * @description: VIN码启动充电
 * @author: zhonghx
 * @create: 2023-02-15 14:43
 * VIN 码请求机制
 * 终端：用户插枪后，终端闭合辅助电源，与 BMS 握手通讯，解析车辆的 VIN
 * 码，上传给服务器，并且等待服务器的响应，在此过程中途有刷卡则优先响应
 * 刷卡充电；等待 30 秒超时，验证失败。
 * 服务器：收到 VIN 请求后，查询该 VIN 码的车辆是否可以直接充电，可以充
 * 电则回复成功，否则回复失败；回复成功后，紧接着桩启动充电。
 **/
@Data
public class ChargingWithVINRequest implements MessageDecoder{

    /*车辆VIN码 ASCII 17字节*/
    private String vin;

    @Override
    public void decode(ByteBuf in) {
        byte[] vinBytes = ByteBufUtil.getBytes(in.readBytes(17));
        vin = new String(vinBytes);
    }

}
