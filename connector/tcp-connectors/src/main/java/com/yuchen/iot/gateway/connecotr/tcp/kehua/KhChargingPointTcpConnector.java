package com.yuchen.iot.gateway.connecotr.tcp.kehua;


import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.yuchen.iot.gateway.connecotr.tcp.AbstractTcpServerConnector;
import com.yuchen.iot.gateway.connecotr.tcp.JsonTcpConnectorMessage;
import com.yuchen.iot.gateway.connecotr.tcp.isapi.netty.ISAPIEvent;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.*;
import com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message.*;
import com.yuchen.iot.gateway.connector.api.ConnectorInitParams;
import com.yuchen.iot.gateway.connector.api.WaitCallResponseCallback;
import com.yuchen.iot.gateway.connector.api.data.DownLinkData;
import com.yuchen.iot.gateway.connector.api.data.UpLinkData;
import com.yuchen.iot.gateway.connector.api.data.UpLinkMetaData;
import com.yuchen.iot.gateway.connector.api.message.ThingMessage;
import com.yuchen.iot.gateway.dao.integration.MetaConfig;
import io.netty.channel.Channel;
import io.netty.channel.ChannelInitializer;
import org.pf4j.Extension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.Executors;




@Extension

public class KhChargingPointTcpConnector extends AbstractTcpServerConnector<JsonTcpConnectorMessage> {

    private static final Logger log = LoggerFactory.getLogger(KhChargingPointTcpConnector.class);

    private final Map<String, Channel> channels = new HashMap<>();

    /**
     * 定义默认端口号
     */
    private int PORT = 2410;

    @Override
    public void init(ConnectorInitParams params) {
        super.init(params);
        ListeningExecutorService pool = MoreExecutors.listeningDecorator(Executors.newFixedThreadPool(2));
        // 获取自定义配置
        MetaConfig configuration = params.getConfiguration().getConfiguration();
        // 如果端口号不为空则继续执行
        if (configuration.containsKey("port")){
            // 获取端口号
            PORT = (int) configuration.get("port");
            ListenableFuture<Boolean> future = pool.submit(this::startServer);
            future.addListener(this::startServer, pool);
        }
    }

    @Override
    protected ChannelInitializer<?> getChannelInitializer() {
        //return new ChargingPointChannelInitializer(/*new ChargingPointEncoder(), new ChargingPointDecoder()*/);
        return new KhChargingPointChannelInitializer(new KhChargingChannelListener<JsonTcpConnectorMessage>() {
            @Override
            public void onDisconnect(String chargingPointId) {
                getUpLinkDataListener(chargingPointId).onSouthDisconnect();
            }

            @Override
            public void onConnect(String chargingPointId) {
                getUpLinkDataListener(chargingPointId).onSouthConnect();
            }

            @Override
            public void onMessage(JsonTcpConnectorMessage event) {
                process(event);
            }
        });
    }

    /**
     * @see ISAPIEvent
     */
    @Override
    protected void doProcess(JsonTcpConnectorMessage message) throws Exception {
        // TODO 需要过滤一遍消息，文件单独上传
        JsonNode json = mapper.readTree(message.getMessageInBytes());
        List<UpLinkData> upLinkDataList = this.convert(message);
        if (upLinkDataList != null && !upLinkDataList.isEmpty()) {
            for (UpLinkData upLinkData : upLinkDataList) {
                this.processUpLinkData(upLinkData);
                log.trace("[{}] Processing up link data", upLinkData);
            }
        }
    }

    protected List<UpLinkData> convert(JsonTcpConnectorMessage message) throws Exception {
        byte[] data = message.getMessageInBytes();
        Map<String, String> mdMap = new HashMap<>(this.metadataTemplate.getKvMap());
        return this.convertToUpLinkDataList(this.connectorContext.getCallbackExecutor(), data,
                new UpLinkMetaData(message.getContentType(), mdMap));
    }

    private Boolean startServer() {
        try {
            log.info("start kehua charging server port : {}",PORT);
            // Make the connection attempt.
            this.bootstrap.bind(PORT).addListener(future -> {
                if (future.isSuccess()) {
                    // connect success
                    log.info("start kehua charging server successfully port : {}",PORT);
                } else {
                    future.cause().printStackTrace();
                }
            }).sync().channel().closeFuture().sync();

        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

    @Override
    protected void doListenOffEndpoint(String deviceKey, Map<String, String> endpointMeta) {
        if (this.channels.containsKey(deviceKey)) {
            Channel channel = this.channels.get(deviceKey);
            if (channel != null) {
                channel.close().addListener(future -> {
                    if (future.isSuccess()) {
                        this.getUpLinkDataListener(deviceKey).onSouthDisconnect();
                    } else {
                        future.cause().printStackTrace();
                    }
                });
            }
        }
    }


    @Override
    public void onDownLinkMessage(ThingMessage message, WaitCallResponseCallback callback) {
        if (this.downLinkConverter != null) {
            this.processDownLinkMessage(message, callback);
        }
    }

    @Override
    protected void doListenOnEndpoint(String deviceKey, Map<String, String> endpointMeta) {

    }


    private void processDownLinkMessage(ThingMessage message, WaitCallResponseCallback callback) {
        Map<String, String> mdMap = new HashMap<>(this.metadataTemplate.getKvMap());
        try {
            List<DownLinkData> result = this.downLinkConverter
                    .convertDownLink(Collections.singletonList(message), mdMap);
            if (!result.isEmpty()) {
                DownLinkData downLink = result.get(0); // 设计上只支持单个指令下发
                if (!downLink.isEmpty()) {
                    Map<String, String> metadata = downLink.getMetadata();

                    String identifier = metadata.get("identifier");
                    JsonNode jsonNode = mapper.readTree(downLink.getData());
                    // 过滤 array 数组的问题。
                    if (null != jsonNode.get("array")){
                        ObjectNode objectNode = jsonNode.deepCopy();
                        JsonNode arrayNode = mapper.readTree(objectNode.get("array").asText());
                        objectNode.set("array",arrayNode);
                        jsonNode = mapper.readTree(String.valueOf(objectNode));
                    }
                    ChargingPointProtocol obj = packetObjectFromJson(message.getIntegrateNo(), identifier, jsonNode);
                    KhChargingPointChannelManager.sendMessageToSN(message.getIntegrateNo(),obj);

                    if(callback != null){
                        // 存放请求事件的唯一标识，为了下发回调响应( 桩编号+下发命令 ， 根据判断额外会加入其他信息，例如 充电流水号)
                        String key = getCallbackKey(message.getIntegrateNo(), identifier, jsonNode);
                        callback.waitForResponse(key, downLink);
                        log.info("科华--平台下发指令---写入回调---key:{}", key);
                    }
                    log.info("科华--平台下发指令---"+message.getIntegrateNo()+"---"+identifier+"---"+ JSONObject.toJSONString(obj));
                }
            }
        } catch (Exception e) {
            log.warn("Failed to process downLink message", e);
        }
    }


    public ChargingPointProtocol packetObjectFromJson(String integrateNo, String identifier, JsonNode json) {


        MsgHeader header = new MsgHeader();
        header.setPreamble(0x4b48);
        header.setTerminator(0x68);

        try {

            if (identifier.equals("get")){

                //TODO 获取查询的属性名称，
                // 判断属性是终端、或者是 主动上报、或者是充电实时数据
                // 再决定转换成某个终端的下行指令。

                int  cmd = 0;
                boolean isFirst = true;
                List<Short> list = new ArrayList<>();
                Iterator<Map.Entry<String, JsonNode>> fieldsIterator = json.fields();
                while (fieldsIterator.hasNext()) {
                    Map.Entry<String, JsonNode> field = fieldsIterator.next();
                    //System.out.println("Key: " + field.getKey() + ", Value: " + field.getValue());
                    list.add((short) Objects.requireNonNull(TerminalData.keyOf(field.getKey())).getId());

                    if (isFirst){
                        isFirst = false;
                        if (TerminalData.keyOf(field.getKey()) != null){
                            cmd = MsgType.GET_DATA.getType();

                        }else if (RealtimeData.keyOf(field.getKey()) != null){
                            cmd = MsgType.GET_REAL_DATA.getType();
                            list.add((short) Objects.requireNonNull(TerminalData.keyOf(field.getKey())).getId());
                        }
                    }

                }

                if (list.size() == 0 || cmd == 0){
                    throw  new NullPointerException();
                }

                if (cmd == MsgType.GET_REAL_DATA.getType()){
                    ChargingPointProtocol<GetRealtimeChargingDataRequest> chargingPointProtocol = new ChargingPointProtocol<>();
                    GetRealtimeChargingDataRequest getRealtimeChargingDataRequest = new GetRealtimeChargingDataRequest(
                            (byte) list.size(),
                            list.toArray(Short[]::new)
                    );
                    chargingPointProtocol.setBody(getRealtimeChargingDataRequest);
                    header.setCmd(cmd);
                    chargingPointProtocol.setHeader(header);
                    return chargingPointProtocol;

                }else {
                    ChargingPointProtocol<GetTerminalDataRequest> chargingPointProtocol = new ChargingPointProtocol<>();
                    GetTerminalDataRequest getTerminalDataRequest = new GetTerminalDataRequest(
                            (byte) list.size(),
                            list.toArray(Short[]::new)
                    );
                    chargingPointProtocol.setBody(getTerminalDataRequest);

                    header.setCmd(cmd);

                    chargingPointProtocol.setHeader(header);
                    return chargingPointProtocol;
                }



            }
            else if (identifier.equals("set")){

                //TODO 获取查询的属性名称，
                // 判断属性是终端、或者是 主动上报、或者是充电实时数据
                // 再决定转换成某个终端的下行指令。

            }
            /*启动停止充电*/
            else if(identifier.equals("ChargingRequest")){

                ChargingPointProtocol<ChargingRequest> chargingPointProtocol = new ChargingPointProtocol<>();
                ChargingRequest startStopChargingRequest = new ChargingRequest(
                        json.get("optType").asInt(),
                        json.get("voltage").asInt(),
                        json.get("chargingStrategy").asInt(),
                        json.get("params").asInt(),
                        json.get("account").asText(),
                        json.get("chargingSN").asInt(),
                        json.get("balance").asInt(),
                        json.get("verifyCode").asInt(),
                        json.get("power").asInt()
                        );
                chargingPointProtocol.setBody(startStopChargingRequest);
                header.setCmd(MsgType.START_STOP_CHARGING.getType());
                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }
            /*启动停止充电结果*/
            else if(identifier.equals("StartStopChargingResultResponse")){

                ChargingPointProtocol<ChargingResultResponse> chargingPointProtocol = new ChargingPointProtocol<>();
                ChargingResultResponse startStopChargingResultResponse = new ChargingResultResponse(
                        json.get("account").asText(),
                        json.get("chargingSN").asInt()
                );
                chargingPointProtocol.setBody(startStopChargingResultResponse);
                header.setCmd(MsgType.START_STOP_CHARGING.getType());
                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }
            /*刷卡鉴权响应*/
            else if (identifier.equals("CardAuthResponse")) {

                ChargingPointProtocol<CardAuthResponse> chargingPointProtocol = new ChargingPointProtocol<>();
                CardAuthResponse cardAuthResponse = new CardAuthResponse(
                        json.get("cardNumber").asText(),
                        (byte) json.get("cardType").asInt(),
                        json.get("balances").asInt(),
                        json.get("cardState").asInt(),
                        json.get("chargingSN").asInt(),
                        (short) json.get("verifyCode").asInt()
                );
                chargingPointProtocol.setBody(cardAuthResponse);

                header.setCmd(MsgType.CARD_AUTH_ACK.getType());
                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }
            /*充电实时上报应答*/
            else if (identifier.equals("RealtimeChargingDataResponse")) {

                ChargingPointProtocol<RealtimeChargingDataResponse> chargingPointProtocol = new ChargingPointProtocol<>();
                RealtimeChargingDataResponse realtimeChargingDataResponse = new RealtimeChargingDataResponse(
                        (byte) json.get("ack").asInt()
                );
                chargingPointProtocol.setBody(realtimeChargingDataResponse);

                header.setCmd(MsgType.REAL_TIME_DATA_ACK.getType());
                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }
            /*充电历史记录上报应答*/
            else if (identifier.equals("ChargingHistoryResponse")) {

                ChargingPointProtocol<ChargingHistoryResponse> chargingPointProtocol = new ChargingPointProtocol<>();
                ChargingHistoryResponse chargingHistoryResponse = new ChargingHistoryResponse(
                        (byte) json.get("ack").asInt(),
                        json.get("cardNumber").asText(),
                        json.get("chargingSN").asInt(),
                        (byte) json.get("chargingType").asInt()
                );
                chargingPointProtocol.setBody(chargingHistoryResponse);

                header.setCmd(MsgType.CHARGING_HISTORY_RECORD_ACK.getType());
                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }
            /*报警历史记录应答*/
            else if (identifier.equals("AlarmHistoryResponse")) {

                ChargingPointProtocol<AlarmHistoryResponse> chargingPointProtocol = new ChargingPointProtocol<>();
                AlarmHistoryResponse alarmHistoryResponse = new AlarmHistoryResponse(
                        (byte) json.get("ack").asInt(),
                        (byte) json.get("type").asInt(),
                        json.get("startTime").asText()
                );
                chargingPointProtocol.setBody(alarmHistoryResponse);
                header.setCmd(MsgType.ALARM_HISTORY_RECORD_ACK.getType());
                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }
            /*操作历史记录应答*/
            else if (identifier.equals("OptHistoryResponse")) {

                ChargingPointProtocol<OptHistoryResponse> chargingPointProtocol = new ChargingPointProtocol<>();
                OptHistoryResponse optHistoryResponse = new OptHistoryResponse(
                        (byte) json.get("ack").asInt(),
                        (byte) json.get("modifyType").asInt(),
                        json.get("modifyTime").asInt()
                );
                chargingPointProtocol.setBody(optHistoryResponse);
                header.setCmd(MsgType.OPT_HISTORY_RECORD_ACK.getType());
                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }
            /*FTP升级指令*/
            else if (identifier.equals("FtpOTARequest")) {
                ChargingPointProtocol<FtpOTARequest> chargingPointProtocol = new ChargingPointProtocol<>();
                FtpOTARequest ftpOTARequest = new FtpOTARequest(
                        (byte) json.get("softType").asInt(),
                        json.get("ipAddress").asText(),
                        (short) json.get("port").asInt(),
                        json.get("userName").asText(),
                        json.get("password").asText(),
                        json.get("fileName").asText(),
                        json.get("version").asText(),
                        json.get("totalSize").asInt(),
                        json.get("verifyCode").asInt()
                );
                chargingPointProtocol.setBody(ftpOTARequest);
                header.setCmd(MsgType.FTP_OTA.getType());
                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }
            /*FTP升级指令*/
            else if (identifier.equals("FtpOTAResultResponse")) {
                ChargingPointProtocol<FtpOTAResultResponse> chargingPointProtocol = new ChargingPointProtocol<>();
                FtpOTAResultResponse ftpOTAResultResponse = new FtpOTAResultResponse(
                        (byte) json.get("ack").asInt(),
                        json.get("version").asText()
                );
                chargingPointProtocol.setBody(ftpOTAResultResponse);
                header.setCmd(MsgType.FTP_OTA_RESULT_ACK.getType());
                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }
            /*TCP升级指令*/
            else if (identifier.equals("TcpOTARequest")) {
                ChargingPointProtocol<TcpOTARequest> chargingPointProtocol = new ChargingPointProtocol<>();
                TcpOTARequest tcpOTARequest = new TcpOTARequest(
                        (byte) json.get("softType").asInt(),
                        json.get("fileName").asText(),
                        json.get("totalSize").asInt(),
                        json.get("verifyCode").asInt()
                );
                chargingPointProtocol.setBody(tcpOTARequest);
                header.setCmd(MsgType.TCP_OTA.getType());
                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }
            /*TCP下发文件指令*/
            //TODO 升级文件用base64编码
            else if (identifier.equals("TcpOTATransferFileRequest")) {
                ChargingPointProtocol<TcpOTATransferFileRequest> chargingPointProtocol = new ChargingPointProtocol<>();
                TcpOTATransferFileRequest tcpOTATransferFileRequest = new TcpOTATransferFileRequest(
                        (short) json.get("sn").asInt(),
                        json.get("data").asText()
                );
                chargingPointProtocol.setBody(tcpOTATransferFileRequest);
                header.setCmd(MsgType.TCP_OTA_FILE.getType());
                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }
            /*平台响应终端上报TCP升级结果*/
            else if (identifier.equals("TcpOTAResultResponse")) {
                ChargingPointProtocol<TcpOTAResultResponse> chargingPointProtocol = new ChargingPointProtocol<>();
                TcpOTAResultResponse tcpOTAResultResponse = new TcpOTAResultResponse(
                        (byte) json.get("ack").asInt()
                );
                chargingPointProtocol.setBody(tcpOTAResultResponse);
                header.setCmd(MsgType.TCP_OTA_RESULT_ACK.getType());
                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }
            /*充电桩校时*/
            else if (identifier.equals("TimingRequest")) {
                ChargingPointProtocol<TimingRequest> chargingPointProtocol = new ChargingPointProtocol<>();
                TimingRequest timingRequest = new TimingRequest(
                        json.get("currentTime").asText()
                );
                chargingPointProtocol.setBody(timingRequest);
                header.setCmd(MsgType.TIMING.getType());
                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }
            /*充电金额补发（变化）*/
            else if (identifier.equals("ChargingAmountChangedRequest")) {
                ChargingPointProtocol<ChargingAmountChangedRequest> chargingPointProtocol = new ChargingPointProtocol<>();
                ChargingAmountChangedRequest chargingAmountChangedRequest = new ChargingAmountChangedRequest(
                        json.get("cardNumber").asText(),
                        json.get("amount").asInt()
                );
                chargingPointProtocol.setBody(chargingAmountChangedRequest);
                header.setCmd(MsgType.CHARGING_AMOUNT_CHANGED.getType());
                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }
            /*VIN码启动充电*/
            else if (identifier.equals("ChargingWithVINResponse")) {
                ChargingPointProtocol<ChargingWithVINResponse> chargingPointProtocol = new ChargingPointProtocol<>();
                ChargingWithVINResponse chargingWithVINResponse = new ChargingWithVINResponse(
                        (byte)json.get("ack").asInt(),
                        json.get("cardNumber").asText(),
                        json.get("balance").asInt(),
                        json.get("sn").asInt(),
                        (short) json.get("verifyCode").asInt(),
                        (byte)json.get("reason").asInt()
                );
                chargingPointProtocol.setBody(chargingWithVINResponse);
                header.setCmd(MsgType.VIN_REQ_CHARGING_ACK.getType());
                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }
            /*平台应答手动输入账号充电*/
            else if (identifier.equals("ChargingWithAccountResponse")) {
                ChargingPointProtocol<ChargingWithAccountResponse> chargingPointProtocol = new ChargingPointProtocol<>();
                ChargingWithAccountResponse chargingWithAccountResponse = new ChargingWithAccountResponse(
                        (byte)json.get("ack").asInt(),
                        json.get("cardNumber").asText(),
                        json.get("balance").asInt(),
                        json.get("sn").asInt(),
                        (short) json.get("verifyCode").asInt(),
                        (byte)json.get("reason").asInt()
                );
                chargingPointProtocol.setBody(chargingWithAccountResponse);
                header.setCmd(MsgType.PWD_REQ_CHARGING_ACK.getType());
                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }

            /*尖峰平谷计费规则下发*/
            else if (identifier.equals("SetRateRequest")) {

                ArrayList<TimeIntervalInfo> array = new ArrayList<>();
                JsonNode arrNode = json.get("array");
                if (arrNode.isArray()) {
                    for (JsonNode objNode : arrNode) {
                        String startTime = objNode.get("startTime").asText();
                        byte index = (byte)objNode.get("index").asInt();
                        TimeIntervalInfo timeIntervalInfo = new TimeIntervalInfo(startTime,index);
                        array.add(timeIntervalInfo);
                    }
                }
                ChargingPointProtocol<SetRateRequest> chargingPointProtocol = new ChargingPointProtocol<>();
                SetRateRequest setRateRequest = new SetRateRequest(
                        json.get("version").asText(),
                        (byte)json.get("timeIntervalCount").asInt(),
                        array,
                        (short) json.get("jianRate").asInt(),
                        (short) json.get("fengRate").asInt(),
                        (short) json.get("pingRate").asInt(),
                        (short) json.get("guRate").asInt(),
                        (short) json.get("jianServiceRate").asInt(),
                        (short) json.get("fengServiceRate").asInt(),
                        (short) json.get("pingServiceRate").asInt(),
                        (short) json.get("guServiceRate").asInt()
                );
                chargingPointProtocol.setBody(setRateRequest);
                header.setCmd(MsgType.SET_TIME_INTERVAL_RATE.getType());
                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }
            /*尖峰平谷计费规则查询*/
            else if (identifier.equals("GetRateRequest")) {

                ChargingPointProtocol<GetRateRequest> chargingPointProtocol = new ChargingPointProtocol<>();
                GetRateRequest getRateRequest = new GetRateRequest(
                        (byte) json.get("type").asInt()
                );
                chargingPointProtocol.setBody(getRateRequest);
                header.setCmd(MsgType.GET_TIME_INTERVAL_RATE.getType());
                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }
            /*服务器下发限制功率命令*/
            else if (identifier.equals("LimitPowerRequest")) {

                ChargingPointProtocol<LimitPowerRequest> chargingPointProtocol = new ChargingPointProtocol<>();
                LimitPowerRequest limitPowerRequest = new LimitPowerRequest(
                        (byte) json.get("controlType").asInt(),
                        json.get("maxPower").asInt()
                );
                chargingPointProtocol.setBody(limitPowerRequest);
                header.setCmd(MsgType.SET_POWER_OUT_LIMIT.getType());
                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }
            /*二维码设置*/
            else if (identifier.equals("QRCodeRequest")) {

                ChargingPointProtocol<QRCodeRequest> chargingPointProtocol = new ChargingPointProtocol<>();
                QRCodeRequest limitPowerRequest = new QRCodeRequest(
                        (byte) json.get("len").asInt(),
                        json.get("code").asText()
                );
                chargingPointProtocol.setBody(limitPowerRequest);
                header.setCmd(MsgType.SET_QR_FORMAT.getType());
                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }
            /*系统重启*/
            else if (identifier.equals("RebootRequest")) {

                ChargingPointProtocol<RebootRequest> chargingPointProtocol = new ChargingPointProtocol<>();
                RebootRequest rebootRequest = new RebootRequest(
                        (byte)json.get("reset").asInt(),
                        json.get("account").asText()
                );
                chargingPointProtocol.setBody(rebootRequest);

                header.setCmd(MsgType.REBOOT.getType());
                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }
            /*下线注销*/
            else if (identifier.equals("LogoutRequest")) {

                ChargingPointProtocol<LogoutRequest> chargingPointProtocol = new ChargingPointProtocol<>();
                LogoutRequest logoutRequest = new LogoutRequest(
                        (byte)json.get("byte").asInt()
                );
                chargingPointProtocol.setBody(logoutRequest);

                header.setCmd(MsgType.LOGOUT.getType());
                chargingPointProtocol.setHeader(header);
                return chargingPointProtocol;
            }

        } catch (Exception e) {
            log.error(e.toString());
        }
        return null;
    }

    @Override
    public void destroy() {

    }

    /**
     * 获取 下发命令的 回调 key
     *
     * @param integrateNo 桩编号
     * @param identifier  下发命令
     * @param jsonNode    下发内容
     * @return
     */
    private String getCallbackKey(String integrateNo, String identifier, JsonNode jsonNode) {
        String callBackKey = integrateNo.concat(identifier);
        switch (identifier) {
            case "ChargingRequest": {
                // 平台控制启停充电(增加充电流水号)
                if (null == jsonNode.get("chargingSN")) {
                    break;
                }
                String serialNumber = jsonNode.get("chargingSN").asText();
                callBackKey = callBackKey.concat(serialNumber);
                break;
            }
            default:
        }
        return callBackKey;
    }
}
