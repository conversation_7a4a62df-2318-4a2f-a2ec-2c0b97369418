package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import io.netty.buffer.ByteBufUtil;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * @description: 服务器下发尖峰平谷计费规则
 * @author: zhonghx
 * @create: 2023-02-15 15:08
 **/
@Data
@AllArgsConstructor
public class SetRateRequest implements MessageEncoder{

    /*长度10字节 ASCII码，计费规则版本号*/
    private String version;

    /*时段数量，至少1条，最多10条。时段必须按顺序排列，覆盖00：00 - 24：00*/
    private byte timeIntervalCount;

    private ArrayList<TimeIntervalInfo> array = new ArrayList<>();

    /*尖时段单价*/
    private short jianRate;
    /*峰时段单价*/
    private short fengRate;
    /*平时段单价*/
    private short pingRate;
    /*谷时段单价*/
    private short guRate;

    /*尖时段服务费*/
    private short jianServiceRate;

    /*峰时段服务费*/
    private short fengServiceRate;

    /*平时段服务费*/
    private short pingServiceRate;

    /*谷时段服务费*/
    private short guServiceRate;

    @Override
    public ByteBuf encode() {

        ByteBuf buf = ByteBufAllocator.DEFAULT.buffer();
        buf.writeBytes(version.getBytes(StandardCharsets.US_ASCII));
        buf.writeByte(timeIntervalCount);
        for (TimeIntervalInfo t: array) {
            buf.writeBytes(t.encode());
        }
        buf.writeShort(jianRate);
        buf.writeShort(fengRate);
        buf.writeShort(pingRate);
        buf.writeShort(guRate);

        buf.writeShort(jianServiceRate);
        buf.writeShort(fengServiceRate);
        buf.writeShort(pingServiceRate);
        buf.writeShort(guServiceRate);

        return buf;
    }

}
