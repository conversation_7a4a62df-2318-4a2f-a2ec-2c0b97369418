package com.yuchen.iot.gateway.connecotr.tcp;

import com.yuchen.iot.gateway.connector.api.AbstractConnector;
import com.yuchen.iot.gateway.connector.api.ConnectorInitParams;
import io.netty.bootstrap.Bootstrap;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import lombok.extern.slf4j.Slf4j;

/**
 * TODO
 *
 * <AUTHOR>
 * @version 2.0.0
 * @date 2022/11/23 下午3:40
 */
@Slf4j
public abstract class AbstractTcpServerConnector<T extends TcpConnectorMessage<?>> extends AbstractConnector<T> {

	protected ServerBootstrap bootstrap;

	private EventLoopGroup eventLoopGroup;

	private EventLoopGroup workerGroup;

	@Override
	public void init(ConnectorInitParams params) {
		super.init(params);
		// 构建 netty 必须部件
		this.eventLoopGroup = new NioEventLoopGroup(1);
		this.workerGroup = new NioEventLoopGroup();

		try {
			bootstrap = new ServerBootstrap();
			bootstrap.group(this.eventLoopGroup, workerGroup).channel(NioServerSocketChannel.class)
					.childHandler(this.getChannelInitializer());
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 组织消息handle链
	 */
	protected abstract ChannelInitializer<?> getChannelInitializer();

	@Override
	public void process(T message) {
		try {
			this.doProcess(message);
			this.connectorStatistics.incMessagesProcessed();
		} catch (Exception e) {
			log.debug("Failed to apply data converter function: {}", e.getMessage(), e);
			this.connectorStatistics.incErrorsOccurred();
		}
	}

	protected abstract void doProcess(T message) throws Exception;

	@Override
	public void destroy() {
		if (eventLoopGroup != null) {
			this.eventLoopGroup.shutdownGracefully();
		}
	}
}
