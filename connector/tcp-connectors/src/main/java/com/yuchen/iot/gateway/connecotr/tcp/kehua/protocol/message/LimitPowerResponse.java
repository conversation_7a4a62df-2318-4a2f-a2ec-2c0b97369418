package com.yuchen.iot.gateway.connecotr.tcp.kehua.protocol.message;

import io.netty.buffer.ByteBuf;
import lombok.Data;

/**
 * @description: 充电桩应答服务器功率限制指令
 * @author: zhonghx
 * @create: 2023-02-16 10:35
 **/

@Data
public class LimitPowerResponse implements MessageDecoder{
    /*响应结果
    * 0失败
    * 1成功
    * */
    private byte ack;
    @Override
    public void decode(ByteBuf in) {
        ack = in.readByte();
    }
}
