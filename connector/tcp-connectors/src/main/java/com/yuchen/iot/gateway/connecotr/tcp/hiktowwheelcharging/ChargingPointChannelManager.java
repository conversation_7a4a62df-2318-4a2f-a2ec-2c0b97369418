package com.yuchen.iot.gateway.connecotr.tcp.hiktowwheelcharging;

import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;

/**
 * @description: 管理充电桩的channel
 * @author: zhonghx
 * @create: 2022-12-30 15:07
 **/
public class ChargingPointChannelManager {

    private static final Logger log = LoggerFactory.getLogger(ChargingPointChannelManager.class);


    //private static ConcurrentHashMap<String, Channel> channelHashMap=null;
    private static final ConcurrentHashMap<String, Channel> channelHashMap = new ConcurrentHashMap<>();

    private ChargingPointChannelManager(){}

    public static ConcurrentHashMap<String, Channel> getChannelHashMap() {
        return channelHashMap;
    }

    public static Channel getChannelByName(String name){
        if (name == null || name.isEmpty()){
            return null;
        }
        return channelHashMap.get(name);
    }


    public static void addChannel(String name,Channel channel){
        if (name == null || name.isEmpty()){
            return;
        }
        channelHashMap.put(name,channel);
    }


    public static void removeChannelByName(String name){
        if (name == null || name.isEmpty()){
            return;
        }
        channelHashMap.remove(name);
    }


    // 向指定 SN 的设备发送消息
    public static boolean sendMessageToSN(String sn, Object message) {
        log.info("获取通道============："+sn);
        Channel channel = getChannelByName(sn);
        if (channel != null) {
            log.info("获取通道============："+channel.isActive());
            log.info("获取通道isOpen()============："+channel.isOpen());
            channel.writeAndFlush(message);
        } else {
            log.info("获取通道为空============："+sn);
            // 处理设备不存在或未连接的情况
            return false;
        }
        return true;
    }
}
