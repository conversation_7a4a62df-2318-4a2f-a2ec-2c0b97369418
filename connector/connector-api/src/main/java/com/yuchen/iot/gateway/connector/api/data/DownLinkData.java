package com.yuchen.iot.gateway.connector.api.data;

import java.beans.ConstructorProperties;
import java.util.Map;

/**
 * 下行连接数据
 * <AUTHOR>
 * @date 2022/10/27 下午2:14
 * @version 2.0.0
 */
public class DownLinkData {

    private final String contentType;
    private final byte[] data;
    private final Map<String, String> metadata;

    public boolean isEmpty() {
        return this.data == null || this.data.length == 0;
    }

    @ConstructorProperties({"contentType", "data", "metadata"})
    DownLinkData(String contentType, byte[] data, Map<String, String> metadata) {
        this.contentType = contentType;
        this.data = data;
        this.metadata = metadata;
    }

    public static DownLinkDataBuilder builder() {
        return new DownLinkDataBuilder();
    }

    public String getContentType() {
        return this.contentType;
    }

    public byte[] getData() {
        return this.data;
    }

    public Map<String, String> getMetadata() {
        return this.metadata;
    }

    public static class DownLinkDataBuilder {
        private String contentType;
        private byte[] data;
        private Map<String, String> metadata;

        DownLinkDataBuilder() {
        }

        public DownLinkDataBuilder contentType(String contentType) {
            this.contentType = contentType;
            return this;
        }

        public DownLinkDataBuilder data(byte[] data) {
            this.data = data;
            return this;
        }

        public DownLinkDataBuilder metadata(Map<String, String> metadata) {
            this.metadata = metadata;
            return this;
        }

        public DownLinkData build() {
            return new DownLinkData(this.contentType, this.data, this.metadata);
        }
    }
}
