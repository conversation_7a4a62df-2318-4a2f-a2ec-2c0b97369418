package com.yuchen.iot.gateway.connector.api;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.TextNode;
import com.google.common.util.concurrent.ListenableFuture;
import com.yuchen.iot.gateway.connector.api.converter.DownLinkDataConverter;
import com.yuchen.iot.gateway.connector.api.converter.UpLinkDataConverter;
import com.yuchen.iot.gateway.connector.api.data.DownLinkData;
import com.yuchen.iot.gateway.connector.api.data.UpLinkContentType;
import com.yuchen.iot.gateway.connector.api.data.UpLinkData;
import com.yuchen.iot.gateway.connector.api.data.UpLinkMetaData;
import com.yuchen.iot.gateway.connector.api.message.ThingMessage;
import com.yuchen.iot.gateway.dao.integration.Connector;
import com.yuchen.iot.gateway.dao.integration.MetaConfig;
import com.yuchen.iot.gateway.util.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Base64Utils;

import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;

/**
 * 插件抽象实现
 *
 * <AUTHOR>
 * @date 2022/10/26 下午5:19
 * @version 2.0.0
 */
@Slf4j
public abstract class AbstractConnector<T> implements SouthConnector<T> {

    protected final ObjectMapper mapper = new ObjectMapper();

    protected Connector connector;

    protected UpLinkDataConverter upLinkConverter;

    protected DownLinkDataConverter downLinkConverter;

    protected UpLinkMetaData metadataTemplate;

    protected ConnectorStatistics connectorStatistics;

    protected ConnectorContext connectorContext;

    protected Map<String, String> endpointMeta;

    public Map<String, String> getEndpointMeta() {
        return endpointMeta;
    }

    // Endpoint注册的监听
    public Map<String, UpLinkDataListener> upLinkDataListeners = new ConcurrentHashMap<>();

    public Connector getConfiguration() {
        return this.connector;
    }

    @Override
    public void init(ConnectorInitParams params) {
        log.debug("ConnectorInitParams ========> {}", params);
        this.connectorContext = params.getConnectorContext();
        this.connector = params.getConfiguration();
        this.upLinkConverter = params.getUpLinkConverter();
        this.downLinkConverter = params.getDownLinkConverter();

        Map<String, String> mdMap = new HashMap<>();
        mdMap.put("connectorName", this.connector.getName());
        if (this.connector.getConfiguration().containsKey("metadata")) {
            JsonNode metadata = JacksonUtil.valueToTree(this.connector.getConfiguration().get("metadata"));
            if (metadata != null) {
                Iterator<Map.Entry<String, JsonNode>> it = metadata.fields();
                while (it.hasNext()) {
                    Map.Entry<String, JsonNode> md = it.next();
                    if (md.getValue().isValueNode()) {
                        mdMap.put(md.getKey(), md.getValue().asText());
                    } else {
                        mdMap.put(md.getKey(), md.getValue().toString());
                    }
                }
            }
        }
        this.metadataTemplate = new UpLinkMetaData(this.getDefaultUpLinkContentType(), mdMap);
        this.connectorStatistics = new ConnectorStatistics();


    }

    @Override
    public void update(ConnectorInitParams params) {
        this.init(params);
    }

    @Override
    public void listenOnEndpoint(String deviceKey, Map<String, String> endpointMeta,
                                 UpLinkDataListener endpointListener) {
        this.endpointMeta=endpointMeta;
        this.upLinkDataListeners.put(deviceKey, endpointListener);
        this.doListenOnEndpoint(deviceKey, endpointMeta);
    }

    @Override
    public void onDownLinkMessage(ThingMessage message, WaitCallResponseCallback callback) {

    }

    @Override
    public void listenOffEndpoint(String deviceKey, Map<String, String> endpointMeta) {
        this.doListenOffEndpoint(deviceKey, endpointMeta);
        this.upLinkDataListeners.remove(deviceKey);
    }

    protected UpLinkDataListener getUpLinkDataListener(String deviceKey) {
        return this.upLinkDataListeners.get(deviceKey);
    }

    protected abstract void doListenOnEndpoint(String deviceKey, Map<String, String> endpointMeta);

    protected abstract void doListenOffEndpoint(String deviceKey, Map<String, String> endpointMeta);

    protected UpLinkContentType getDefaultUpLinkContentType() {
        return UpLinkContentType.JSON;
    }

    public void validateConfiguration(Connector configuration, boolean allowLocalNetworkHosts) {
        if (configuration != null && configuration.getConfiguration() != null) {
            this.doValidateConfiguration(configuration.getConfiguration(), allowLocalNetworkHosts);
        } else {
            throw new IllegalArgumentException("配置不能为空!");
        }
    }

    protected <T> T getClientConfiguration(Connector connectorInstance, Class<T> clazz) {
        return this.getClientConfiguration(connectorInstance.getConfiguration(), clazz);
    }

    protected <T> T getClientConfiguration(MetaConfig clientConfiguration, Class<T> clazz) {
        if (clientConfiguration == null) {
            throw new IllegalArgumentException("clientConfiguration field is missing!");
        } else {
            return JacksonUtil.convertValue(clientConfiguration, clazz);
        }
    }

    protected void doValidateConfiguration(MetaConfig jsonConfiguration, boolean allowLocalNetworkHosts) {

    }

    public void checkConnection(Connector connector) {
        if (connector != null && connector.getConfiguration() != null) {
            this.doCheckConnection(connector);
        } else {
            throw new IllegalArgumentException("Connector configuration is empty!");
        }
    }

    protected static boolean isLocalNetworkHost(String host) {
        try {
            InetAddress address = InetAddress.getByName(host);
            return address.isAnyLocalAddress() || address.isLoopbackAddress() || address.isLinkLocalAddress() || address
                    .isSiteLocalAddress();
        } catch (UnknownHostException var2) {
            throw new IllegalArgumentException("Unable to resolve provided hostname: " + host);
        }
    }

    protected void doCheckConnection(Connector connector) {

    }

    @Override
    public ConnectorStatistics popStatistics() {
        ConnectorStatistics statistics = this.connectorStatistics;
        //this.connectorStatistics = new ConnectorStatistics();
        return statistics;
    }

    protected void processUpLinkData(UpLinkData data) throws Exception {
        //端点初始化时,会增加一个监听器,监听器可以处理上行数据
        UpLinkDataListener listener = this.upLinkDataListeners.get(data.getIntegrateNo());
        log.debug("上传IntegrateNo" +
                "" +
                ": {}", data.getIntegrateNo());
        if (listener != null) {
            listener.onSouthMessage(data);
        }else {
            log.warn("监听器为空: {}", data.getIntegrateNo());
        }
    }

    protected void processResponseUpLinkData(UpLinkData data) {

    }

    protected ListenableFuture<List<UpLinkData>> convertToUpLinkDataListAsync(ExecutorService context, byte[] data,
                                                                              UpLinkMetaData md) throws Exception {
        try {
            return this.upLinkConverter.convertUpLink(data, md, context);
        } catch (Exception e) {
            if (log.isDebugEnabled()) {
                log.debug("[{}][{}] Failed to apply upLink data converter function for data: {} and metadata: {}",
                        this.connector.getId(), this.connector.getName(),
                        Base64Utils.encodeToString(data), md);
            }

            throw e;
        }
    }

    protected List<UpLinkData> convertToUpLinkDataList(ExecutorService context, byte[] data,
                                                       UpLinkMetaData md) throws Exception {
        return this.convertToUpLinkDataListAsync(context, data, md).get();
    }

    protected JsonNode getDownLinkPayloadJson(DownLinkData data) throws IOException {
        String contentType = data.getContentType();
        if ("JSON".equals(contentType)) {
            return this.mapper.readTree(data.getData());
        } else {
            return "TEXT".equals(contentType) ? new TextNode(
                    new String(data.getData(), StandardCharsets.UTF_8)) : new TextNode(
                    Base64Utils.encodeToString(data.getData()));
        }
    }
}
