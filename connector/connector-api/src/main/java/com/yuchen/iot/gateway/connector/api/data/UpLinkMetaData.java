package com.yuchen.iot.gateway.connector.api.data;

import java.beans.ConstructorProperties;
import java.util.Map;

/**
 * 上行连接元数据
 * <AUTHOR>
 * @date 2022/10/27 下午2:15
 * @version 2.0.0
 */
public class UpLinkMetaData {
    private final UpLinkContentType contentType;
    private final Map<String, String> kvMap;

    @ConstructorProperties({"contentType", "kvMap"})
    public UpLinkMetaData(UpLinkContentType contentType, Map<String, String> kvMap) {
        this.contentType = contentType;
        this.kvMap = kvMap;
    }

    public UpLinkContentType getContentType() {
        return this.contentType;
    }

    public Map<String, String> getKvMap() {
        return this.kvMap;
    }
}
