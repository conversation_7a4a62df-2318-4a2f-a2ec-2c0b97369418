package com.yuchen.iot.gateway.connector.api.data.message;

/**
 * 事件:物模型的事件
 * <AUTHOR>
 * @date 2022/11/17 上午11:14
 * @version 2.0.0
 */
public class Event {

    private String identifier;

    private EventValue value;

    private long time;

    public Event() {

    }

    public Event(EventValue value) {
        this.value = value;
        this.time = System.currentTimeMillis() / 1000L;
    }

    public Event(EventValue value, long time) {
        this.value = value;
        this.time = time;
    }

    public String getIdentifier() {
        return identifier;
    }

    public void setIdentifier(String identifier) {
        this.identifier = identifier;
    }

    public EventValue getValue() {
        return value;
    }

    public void setValue(EventValue value) {
        this.value = value;
    }

    public long getTime() {
        return time;
    }

    public void setTime(long time) {
        this.time = time;
    }
}
