package com.yuchen.iot.gateway.connector.api.data.message;

/**
 * 文件
 *
 * key = file param key
 * value = file contents
 *
 * <AUTHOR>
 * @date 2022/11/17 下午5:16
 * @version 2.0.0
 */
public class UploadFile {

    private String fileName;

    private String fileContent;

    private String ossName;

    private String ossBucket;

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getOssName() {
        return ossName;
    }

    public void setOssName(String ossName) {
        this.ossName = ossName;
    }

    public String getFileContent() {
        return fileContent;
    }

    public void setFileContent(String fileContent) {
        this.fileContent = fileContent;
    }

    public String getOssBucket() {
        return ossBucket;
    }

    public void setOssBucket(String ossBucket) {
        this.ossBucket = ossBucket;
    }
}
