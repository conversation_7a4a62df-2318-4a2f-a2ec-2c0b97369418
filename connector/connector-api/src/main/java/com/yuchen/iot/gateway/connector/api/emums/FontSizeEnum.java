package com.yuchen.iot.gateway.connector.api.emums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/14 16:18
 * @description 字体大小:16-16*16,32-32*32
 */
@Getter
public enum FontSizeEnum {
    SIZE_16(16,"16*16"),
    SIZE_32(32,"32*32");


    private Integer size;

    private String description;

    FontSizeEnum(Integer size, String description){
        this.size = size;
        this.description = description;
    }
}
