package com.yuchen.iot.gateway.connector.api;

import lombok.Data;

import java.util.concurrent.atomic.AtomicLong;

/**
 * 连接器消息处理统计
 * <AUTHOR>
 * @date 2022/10/27 下午4:48
 * @version 2.0.0
 */
@Data
public class ConnectorStatistics {

    private AtomicLong messagesProcessed = new AtomicLong(0L);

    private AtomicLong errorsOccurred = new AtomicLong(0L);

    public void incMessagesProcessed() {
        this.messagesProcessed.incrementAndGet();
    }

    public void incErrorsOccurred() {
        this.errorsOccurred.incrementAndGet();
    }

    public long getMessagesProcessed() {
        return this.messagesProcessed.get();
    }

    public long getErrorsOccurred() {
        return this.errorsOccurred.get();
    }
}
