package com.yuchen.iot.gateway.connector.api.emums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/14 16:22
 * @description 颜色 rgb 值:1-红, 2-绿, 4-黄(红+绿), 8-蓝, 16-青(绿+蓝), 32-紫(红+蓝), 64-白(红+绿+蓝)
 *
 */
@Getter
public enum FontColorEnum {
    RGB_RED(1,"红"),
    RGB_GREEN(2,"绿"),
    RGB_YELLOW(4,"黄(红+绿)"),
    RGB_BLUE(8,"蓝"),
    RGB_CYAN(16,"青(绿+蓝)"),
    RGB_PURPLE(32,"紫(红+蓝)"),
    RGB_WHITE(64,"白(红+绿+蓝)");

    private Integer code;

    private String description;

    FontColorEnum(Integer code, String description){
        this.code = code;
        this.description = description;
    }
}
