package com.yuchen.iot.gateway.connector.api.emums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/14 16:35
 * @description 显示车牌-true,false
 */
@Getter
public enum ShowPlateEnum {
    DISPLAY(true,"显示车牌"),
    NOT_DISPLAY(false,"不显示车牌");

    private Boolean hasDisplay;

    private String description;

    ShowPlateEnum(Boolean hasDisplay, String description){
        this.hasDisplay = hasDisplay;
        this.description = description;
    }
}
