package com.yuchen.iot.gateway.connector.api.util;

import cn.hutool.extra.spring.SpringUtil;
import org.springframework.beans.factory.config.AutowireCapableBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.util.ClassUtils;
import org.springframework.util.ReflectionUtils;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import javax.validation.constraints.NotNull;
import java.lang.reflect.Method;

/**
 * 特殊bean的处理方式, 诸如controller
 * 手动将controller 注入beanFactory
 */
public class SpecialBeanUtil {

    public static ApplicationContext applicationContext;

    static {
        applicationContext = SpringUtil.getApplicationContext();
    }

    /**
     * 手动注册bean
     *
     * @param clazz
     */
    public static void registerBean(@NotNull Class<?> clazz) {
        AutowireCapableBeanFactory autowireCapableBeanFactory = applicationContext.getAutowireCapableBeanFactory();
        // 手动注入
        Object autowire = autowireCapableBeanFactory.autowire(clazz, AutowireCapableBeanFactory.AUTOWIRE_CONSTRUCTOR, false);
        // 后置处理... postProcess...等等 完成spring bean的生命周期管理
        autowireCapableBeanFactory.autowireBean(autowire);
        // 将对象注入beanFactory
        SpringUtil.registerBean(clazz.getSimpleName(), autowire);
    }


    /**
     * 注册Controller
     *
     * @param controllerClazz
     * @return
     * @throws Exception
     */
    public static Object registerController(@NotNull Class<?> controllerClazz) throws Exception {

        final RequestMappingHandlerMapping requestMappingHandlerMapping =
                applicationContext.getBean(RequestMappingHandlerMapping.class);
        registerBean(controllerClazz);
        //注册Controller
        Method method = requestMappingHandlerMapping.getClass().getSuperclass().getSuperclass().
                getDeclaredMethod("detectHandlerMethods", Object.class);
        //将private改为可使用
        method.setAccessible(true);
        method.invoke(requestMappingHandlerMapping, applicationContext.getBean(controllerClazz));
        return applicationContext.getBean(controllerClazz);
    }

    /**
     * 去掉Controller的Mapping
     *
     * @param controllerClazz
     */
    public static void unregisterController(@NotNull Class<?> controllerClazz) {
        final RequestMappingHandlerMapping requestMappingHandlerMapping = (RequestMappingHandlerMapping)
                applicationContext.getBean("requestMappingHandlerMapping");
        if (requestMappingHandlerMapping != null) {
            Object controller = applicationContext.getBean(controllerClazz);
            ReflectionUtils.doWithMethods(controllerClazz, method -> {
                Method specificMethod = ClassUtils.getMostSpecificMethod(method, controllerClazz);
                try {
                    Method createMappingMethod = RequestMappingHandlerMapping.class.
                            getDeclaredMethod("getMappingForMethod", Method.class, Class.class);
                    createMappingMethod.setAccessible(true);
                    RequestMappingInfo requestMappingInfo = (RequestMappingInfo)
                            createMappingMethod.invoke(requestMappingHandlerMapping, specificMethod, controllerClazz);
                    if (requestMappingInfo != null) {
                        requestMappingHandlerMapping.unregisterMapping(requestMappingInfo);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }, ReflectionUtils.USER_DECLARED_METHODS);
        }
    }
}
