package com.yuchen.iot.gateway.connector.api.emums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/14 15:51
 * @description 显示方式:左移-left,右移-right,立即显示-immediate
 */
@Getter
public enum DisplayModeEnum {
    LEFT("left","左移"),
    RIGHT("right","右移"),
    IMMEDIATE("immediate","立即显示");

    private String name;

    private String description;

    DisplayModeEnum(String name, String description){
        this.name = name;
        this.description = description;
    }
}
