package com.yuchen.iot.gateway.connector.api.data;

import com.yuchen.iot.gateway.connector.api.data.message.CallResponse;
import com.yuchen.iot.gateway.connector.api.data.message.Event;
import com.yuchen.iot.gateway.connector.api.data.message.Property;
import com.yuchen.iot.gateway.connector.api.data.message.UploadFile;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 上行数据包（经过转换器处理之后）
 *
 * <AUTHOR>
 * @version 2.0.0
 * @date 2022/10/27 下午2:16
 */
public class UpLinkData {

	private final String integrateNo;

	// TODO 当为服务调用的响应消息时，设置与请求消息相等的唯一标识
	private String callId;

	// 事件上报
	private final Event event;

	// 属性上报
	private final Property property;

	// 服务调用响应
	private final CallResponse callResponse;

	// 文件上传
	private final List<UploadFile> uploadFile;

	public UpLinkData(String integrateNo, Event event, Property property,
					  CallResponse callResponse, List<UploadFile> uploadFile) {
		this.integrateNo = integrateNo;
		this.event = event;
		this.property = property;
		this.callResponse = callResponse;
		this.uploadFile = uploadFile;
	}

	public UpLinkData(String integrateNo, Event event, Property property,
					  CallResponse callResponse, String callId, List<UploadFile> uploadFile) {
		this.integrateNo = integrateNo;
		this.event = event;
		this.property = property;
		this.callResponse = callResponse;
		this.callId = callId;
		this.uploadFile = uploadFile;
	}

	public static UpLinkDataBuilder builder() {
		return new UpLinkDataBuilder();
	}

	public String getIntegrateNo() {
		return this.integrateNo;
	}

	public boolean hasEvent() {
		return this.event != null && !CollectionUtils.isEmpty(this.event.getValue());
	}

	public boolean hasCallResponse() {
		return !CollectionUtils.isEmpty(this.callResponse);
	}

	public boolean hasProperties() {
		return !CollectionUtils.isEmpty(this.property);
	}

	public boolean hasUploadFile() {
		return !CollectionUtils.isEmpty(this.uploadFile);
	}

	public Event getEvent() {
		return this.event;
	}

	public CallResponse getCallResponse() {
		return this.callResponse;
	}

	public String getCallId() {
		return callId;
	}

	public Property getProperty() {
		return this.property;
	}

	public List<UploadFile> getUploadFile() {
		return this.uploadFile;
	}

	public static class UpLinkDataBuilder {
		private String integrateNo;
		private Event event;
		private Property property;
		private CallResponse callResponse;
		private String callId;
		private List<UploadFile> uploadFile;

		public UpLinkDataBuilder integrateNo(String integrateNo) {
			this.integrateNo = integrateNo;
			return this;
		}

		public UpLinkDataBuilder callId(String callId) {
			this.callId = callId;
			return this;
		}

		public UpLinkDataBuilder event(Event event) {
			this.event = event;
			return this;
		}

		public UpLinkDataBuilder properties(Property property) {
			this.property = property;
			return this;
		}

		public UpLinkDataBuilder callResponse(CallResponse callResponse) {
			this.callResponse = callResponse;
			return this;
		}

		public UpLinkDataBuilder uploadFile(List<UploadFile> uploadFile) {
			this.uploadFile = uploadFile;
			return this;
		}

		public UpLinkData build() {
			return new UpLinkData(this.integrateNo, this.event, this.property, this.callResponse, callId,
					this.uploadFile);
		}
	}
}
