package com.yuchen.iot.gateway.connector.api.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/14 11:51
 * @description 屏幕显示参数配置信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LedVo {

    /**
     * led id
     */
    private Integer id;

    /**
     * LED 显示内容
     */
    private String content;

    /**
     * 显示方式:左移-left,右移-right,立即显示
     * -immediate
     */
    private String displayMode;

    /**
     * 速度类型:快-fast,中-medium,慢-slow
     */
    private String speedType;

    /**
     * 显示时长,1~60 秒
     */
    private Integer showTime;

    /**
     * 显示车牌-true,false
     */
    private Boolean showPlate;

    /**
     * 字体大小:16-16*16,32-32*32
     */
    private Integer fontSize;

    /**
     * 颜色 rgb 值:1-红, 2-绿, 4-黄(红+绿), 8-蓝, 16-青(绿+蓝), 32-紫(红+
     * 蓝), 64-白(红+绿+蓝)
     */
    private Integer fontColor;
}
