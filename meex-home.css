body {
  font-family: 'Inter', 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
  background: #00151D;
  color: #fff;
  margin: 0;
  padding: 0;
}

.header {
  background: #00151D;
  height: 64px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #222;
}
.nav {
  display: flex;
  gap: 32px;
  list-style: none;
  margin: 0 auto;
  padding: 0;
  font-size: 16px;
  color: #CFCFCF;
}
.nav li {
  cursor: pointer;
  transition: color 0.2s;
}
.nav li.register {
  color: #22B173;
  font-weight: bold;
}
.nav li:hover {
  color: #CAFF00;
}

.banner {
  background: linear-gradient(90deg, #022D3A 0%, #00151D 100%);
  text-align: center;
  padding: 80px 0 40px 0;
}
.banner h1 {
  font-size: 56px;
  font-weight: 700;
  color: #CAFF00;
  margin-bottom: 32px;
}
.btn-primary {
  background: #22B173;
  color: #fff;
  font-size: 22px;
  border: none;
  border-radius: 8px;
  padding: 18px 48px;
  margin-bottom: 24px;
  cursor: pointer;
  font-weight: bold;
  box-shadow: 0 4px 24px 0 rgba(34,177,115,0.15);
  transition: background 0.2s;
}
.btn-primary:hover {
  background: #1a8c5a;
}
.banner .desc {
  color: #CFCFCF;
  font-size: 20px;
  margin: 16px 0 8px 0;
}
.banner .risk {
  color: #E91C24;
  font-size: 14px;
  margin: 0;
}

.market {
  display: flex;
  justify-content: center;
  gap: 32px;
  background: #022D3A;
  padding: 32px 0;
}
.market-item {
  background: #fff;
  color: #00151D;
  border-radius: 16px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.08);
  padding: 24px 48px;
  min-width: 220px;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 18px;
}
.market-item .pair {
  font-size: 20px;
  font-weight: bold;
  color: #22B173;
  margin-bottom: 8px;
}
.market-item .price {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}
.market-item .volume {
  color: #959595;
  margin-bottom: 8px;
}
.market-item .change {
  font-size: 18px;
  font-weight: bold;
}
.market-item .change.up {
  color: #22B173;
}
.market-item .change.down {
  color: #E91C24;
}

.features {
  background: #fff;
  color: #00151D;
  padding: 64px 0 48px 0;
  text-align: center;
}
.features h2 {
  font-size: 36px;
  color: #22B173;
  margin-bottom: 32px;
}
.features ul {
  list-style: none;
  padding: 0;
  margin: 0 auto;
  max-width: 800px;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 32px;
}
.features li {
  background: #CAFF00;
  color: #00151D;
  border-radius: 12px;
  padding: 18px 36px;
  font-size: 20px;
  font-weight: 500;
  min-width: 220px;
  box-shadow: 0 2px 8px 0 rgba(202,255,0,0.08);
}

footer {
  background: #022D3A;
  color: #CFCFCF;
  padding: 48px 0 24px 0;
  text-align: center;
}
.footer-links {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 32px;
  font-size: 16px;
  margin-bottom: 24px;
}
.footer-links span {
  cursor: pointer;
  transition: color 0.2s;
}
.footer-links span:hover {
  color: #CAFF00;
}
.copyright {
  font-size: 14px;
  color: #959595;
  line-height: 1.8;
} 