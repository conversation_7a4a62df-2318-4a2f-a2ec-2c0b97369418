网关运维版本，包含停车场业务
===========================
```
application // 启动和管理功能

connector // 可扩展连接器

dao // 数据库entity定义

message // 消息结构常量

things // 北向连接，对象IoT平台

util // 工具类

plugin // 插件 基础支持, 将开发者开发的插件注入到网关应用, 实现热插拔
```

目前针对连接器进行开发, 主要暴露了两个类. 开发者 适配新协议的时候, 需要实现com.yuchen.iot.gateway.connector.api.SouthConnector 
和com.yuchen.iot.gateway.connector.api.converter.evaluator.Evaluator 接口
完成连接器开发, 适配新的设备.

后续如果要抽象出更多的网关开发流程时, 可以定义新的接口, 继承org.pf4j.ExtensionPoint.
然后在新工程下 对该接口增加是实现类, 并且头部增加org.pf4j.Extension 注解

开发流程:
1. 在[connector](connector) 模块下创建对应的connect连接器

![img.png](screen/img9.png)

并且在pom文件中引入[connector-api](connector%2Fconnector-api)

![img.png](screen/img0.png)

2. 连接器类头部增加org.pf4j.Extension 注解,将该连接器注入到 application 应用的spring 容器中

![img_1.png](screen/img_1.png)

3. 该连接器模块的pom.xml 增加配置. 其中Plugin-Id 不要重复, 尽量定义为该连接器适配的设备及协议的命名方式
    ```
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifestEntries>
                            <Plugin-Id>hik-sdk-connect-plugin</Plugin-Id>
                            <Plugin-Version>0.0.1</Plugin-Version>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
        </plugins>
    </build>
   ```
4. 在该模块根目录下执行mvn clean package, 得到connect.jar文件

5. 如果需要开发新的协议的话, 需要新增对应的上行转换器和下行转换器,并且在该模块目录下执行 mvn clean package

![img_2.png](screen%2Fimg_2.png)

6. 在[application.yml](application%2Fsrc%2Fmain%2Fresources%2Fapplication.yml) 在server.pf4j.path添加你的connect.jar的实际地址 

以上...完整的网关开发流程
...具体可以参考http-connect工程
参考点为 pom.xml 需要增加

![img.png](screen/img.png)


当然,不排除一些对接一些设备过程中, 需要在插件中提供接口能力等
采用jar 插件部署的方式会导致插件中自定义的controller或者其他bean 无法被sprig识别注入

[connector-api](connector%2Fconnector-api) 工程也提供了处理方式,com.yuchen.iot.gateway.connector.api.util.SpecialBeanUtil
在插件接口的生命周期中调用该类, 可以实现将 这些特殊的bean注入到spring 中进行管理

参考[hik-door-connector](connector%2Fhik-door-connector):

![img_3.png](screen%2Fimg_3.png)


当开发者开发一个新的connect连接器时,在该连接器上依赖了其他的外部jar, 在ide中可以正常执行, 但是实际部署中可能会出现一些找不到jar的情况.

应按照以下部署方式:
在你的服务器上面创建以下的目录结构和上传对应的文件
![img.png](screen/img4.png)

其中plugin目录下为开发者开发的连接器jar包

![img.png](screen/img5.png)

lib目录下存放依赖的jar包,如在连接器中引入了hutool这个工具类

![img.png](screen/img6.png)

执行    sh [restart.sh](cmd/restart.sh) 文件

注意, 如果引入的jar 依赖了其他jar, 也要一同引入到lib目录下

注意2: 如果外部jar中引入了 诸如spring等框架的包, 也要一同加入到lib目录下.
或者去掉springboot-maven-plugin 插件, 采用maven-compile插件的打包方式
![img.png](screen/img7.png)

当采用maven的默认打包方式时, 需要修改 [restart.sh](cmd/restart.sh) 文件, 将启动类改为项目中真实的类路径com.yuchen.iot.gateway.application.Application

![img.png](screen/img8.png)